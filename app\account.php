<?php
class account
{
    private $project_id;

    function __construct($project_id)
    {
        $this->project_id = $project_id;
    }

    function add($user_id, $id, $name, $data = null, $platform = "facebook", $type = 'report')
    {
        global $keya, $c_time, $app;
        $db = $app->db2;
        $tb_account = $this->project_id;
        $hash = md5(strtolower($id . "@@@" . $type));
        $insert["account_id"] = $id;
        $insert["account_hash"] = $db->func("UNHEX(?)", [$hash]);
        $insert["name"] = $name;
        $insert["platform"] = $platform;
        $insert["type"] = $type;
        $insert["user_id"] = $user_id;
        $insert["project_id"] = $this->project_id;
        if ($data != null)
        {
            $insert["data"] = serialize($data);
        }

        $acc = $this->get_detail($id, $type);
        if ($acc != null)
        {
            $db->where("account_hash = UNHEX(?)", [$hash]);
            if ($db->update("integration", $insert))
            {
                $ret["code"] = 1;
                $ret["result"]["msg"] = "Success update account " . $type;
            }
            else
            {
                $ret["code"] = 0;
                $ret["result"]["msg"] = "Error update account " . $type;
            }
        }
        else
        {
            $id = $db->insert("integration", $insert);
            if ($id)
            {                
                $ret["code"] = 1;
                $ret["result"]["msg"] = "Success add account " . $type;
                $ret["id"] = $id;
            }
            else
            {
                $ret["code"] = 0;
                $ret["result"]["msg"] = "Error add account " . $type;
            }
        }

        return $ret;
    }

    function get_list($platform)
    {
        global $keya, $c_time, $app;
        $db = $app->db2;
        if($platform !=''){
            $db->where("project_id = ?  and status = 1 and platform = ?", [$this->project_id, $platform]);
        }else{
            $db->where("project_id = ?  and status = 1", [$this->project_id]);
        }
        
        $x = $db->get("integration", null, "account_id, name, platform, type, message");
        foreach ($x as $a => $v)
        {
            $x[$a]["name_short"] = getAbbreviation($v["name"]);
        }
        return $x;
    }

    function get_detail($id, $type)
    {
        global $keya, $c_time, $app;
        $db = $app->db2;
        $account_hash = md5(strtolower($id . "@@@" . $type));
        $db->where("account_hash = UNHEX(?) and status = 1", [$account_hash]);
        $res = $db->getone("integration", "HEX(account_hash) as account_id, name, type");
        return $res;
    }

    function get_detail_id($id)
    {
        global $keya, $c_time, $app;
        $db = $app->db2;
        $db->where("id = ? and status = 1", [$id]);
        $res = $db->getone("integration", "HEX(account_hash) as account_hash,account_id,platform, name, type, data");
        if($db->count >0){
            $res['data'] = unserialize($res['data']);
        }
        return $res;
    }

    function get_detail_id_by_account_id($account_id)
    {
        global $keya, $c_time, $app;
        $db = $app->db2;
        $db->where("account_id = ? and status = 1", [$account_id]);
        $res = $db->getone("integration", "HEX(account_hash) as account_hash,account_id,platform, name, type, data");
        if($db->count >0){
            $res['data'] = unserialize($res['data']);
        }
        return $res;
    }

    function reset_account($account_hash)
    {
        global $keya, $c_time, $app;
        $db = $app->db2;
        //$db->where("account_hash = UNHEX(?)", [$account_hash]);
        $db->where("account_id = ?", [$account_hash]);
        $accs = $db->getone("integration");
        if ($db->count > 0)
        {
            //$db->where("account_hash = UNHEX(?)", [$account_hash]);
            $db->where("account_id = ?", [$account_hash]);
            $db->delete("integration");
            $ret["code"] = 1;
            $ret["result"]["msg"] = "Success delete integration account";
        }
        else
        {
            $ret["code"] = 1;
            $ret["result"]["msg"] = "Data not found";
        }
        return $ret;
    }

    function grab_fb($res){
        global $app;
        if ($res != null) {
            //$res["data"] = unserialize($res["data"]);
            if ($res["platform"] == "facebook" || $res["type"] == "ctwa"){   
                $fb = new \Facebook\Facebook(["app_id" => $app
                ->config->fb_app_id, "app_secret" => $app
                ->config->fb_app_secret, "default_graph_version" => "v20.0", ]);
                /*
                if($res["type"] == "facebook"){
                $fb = new \Facebook\Facebook(["app_id" => $app
                    ->config->fb_app_id, "app_secret" => $app
                    ->config->fb_app_secret, "default_graph_version" => "v20.0", ]);
                }
                else{
                    $fb = new \Facebook\Facebook(["app_id" => $app
                    ->config->fb_app_id_ctwa, "app_secret" => $app
                    ->config->fb_app_secret_ctwa, "default_graph_version" => "v20.0", ]);
                }
                */
//                try {
//                    // Get the App Token
//                    $appAccessToken = $fb->getApp()->getAccessToken();
//                    // Use the App Token to debug the User Access Token
//                    $response = $fb->get('/debug_token?input_token='.$res["data"]["access_token"] , $appAccessToken);
//                    $tokenData = $response->getDecodedBody()['data'];
//
//                    // Check if the token is expired
//                    if (isset($tokenData['is_valid']) && $tokenData['is_valid']) {
//                        if (isset($tokenData['expires_at'])) {
//                            $expiryTime = $tokenData['expires_at'];
//                            echo "Token is valid and will expire on: " . date('Y-m-d H:i:s', $expiryTime);
//                            
//                        } else {
//                            echo "Token is valid but does not have an expiry time.";
//                        }
//                    } else {
//                        return  "Token has expired or is invalid.";
//                    }
//
//                } catch(Facebook\Exceptions\FacebookResponseException $e) {
//                    // When Graph returns an error
//                    return 'Graph returned an error: ' . $e->getMessage();
//                } catch(Facebook\Exceptions\FacebookSDKException $e) {
//                    // When validation fails or other local issues
//                    return 'Facebook SDK returned an error: ' . $e->getMessage();
//                }
                try {
                    $yesterday = date('Y-m-d', strtotime('0 day'));
                    $today = date('Y-m-d');
                    $data_grab = $res["account_id"] . "/insights?level=ad&fields=ad_id,spend,ad_name,adset_id,adset_name,campaign_id,campaign_name,impressions,outbound_clicks&time_range={'since':'$yesterday','until':'$today'}";

                    //$data_grab = $res["account_id"] . "/insights?level=ad&date_preset=today&fields=ad_id,spend,ad_name,adset_id,adset_name,campaign_id,campaign_name,impressions,outbound_clicks";
                    $response = $fb->get($data_grab, $res["data"]["access_token"]);
                    $tmp = $response->getDecodedBody();
                    $result = [];
                    if (count($tmp["data"]) > 0){
                        foreach ($tmp["data"] as $key => $value){
                            array_push($result, $value);
                        }
                    }
                    if (isset($tmp["paging"]["cursors"]["after"])){
                        $next_page = $tmp["paging"]["cursors"]["after"];
                    }else{
                        $next_page = false;
                    }

                    $i = 0;
                    while ($next_page != false){
                        $data_grab = $res["account_id"] . "/insights?level=ad&fields=ad_id,spend,ad_name,adset_id,adset_name,campaign_id,campaign_name,impressions,outbound_clicks&time_range={'since':'$yesterday','until':'$today'}&after=" . $next_page;

                        //$data_grab = $res["account_id"] . "/insights?level=ad&date_preset=today&fields=ad_id,spend,ad_name,adset_id,adset_name,campaign_id,campaign_name,impressions,outbound_clicks&after=" . $next_page;
                        $response = $fb->get($data_grab, $res["data"]["access_token"]);
                        $tmp = $response->getDecodedBody();
                        if (count($tmp["data"]) > 0){
                            foreach ($tmp["data"] as $key => $value){
                                array_push($result, $value);
                            }
                        }

                        if (!isset($tmp["paging"]["cursors"]["after"])){
                            $next_page = false;
                        }else{
                            $next_page = $tmp["paging"]["cursors"]["after"];
                        }
                    }
                } catch (Facebook\Exceptions\FacebookResponseException $e) {
                    // When Graph returns an error (e.g., expired token)
                    $msg["result"]["msg"] = 'Facebook Graph returned an error: ' . $e->getMessage();
                    $msg["code"] = 0;
                    return $msg;
                } catch (Facebook\Exceptions\FacebookSDKException $e) {
                    // When validation fails or other local issues
                    $msg["result"]["msg"] = 'Facebook SDK returned an error: ' . $e->getMessage();
                    $msg["code"] = 0;
                    return $msg;
                }
            }
            if(count($result)==0){
                $msg["result"]["msg"] = "result null";
                $msg["code"] = 1;
                return $msg;
            }
            $campaigns = array();
            $r = new report();
            $source = "meta";
            $data_campaign = $result;
            foreach ($data_campaign as $key => $value){
                $cid = $value["campaign_id"];
                $adset_id = $value["adset_id"];
                $ad_id = $value["ad_id"];
                $campaigns[$cid]["name"] = $value["campaign_name"];
                $campaigns[$cid]["adset"][$adset_id]["name"] = $value["adset_name"];
                $campaigns[$cid]["adset"][$adset_id]["adcopy"][$ad_id]["name"] = $value["ad_name"];
            }
            foreach ($campaigns as $key => $campaign){
                $campaign_id = $r->add_report_kolom($source, $campaign["name"], "campaign", $key, null, true) ["data"];
                if (count($campaign["adset"]) > 0){
                    if ($campaign_id != NULL){
                        foreach ($campaign["adset"] as $key2 => $adset){
                            $adset_id = $r->add_report_kolom($source, $adset["name"], "adset", $key2, $campaign_id, true) ["data"];
                            if ($adset_id != NULL)
                            {

                                foreach ($adset["adcopy"] as $key3 => $adcopy)
                                {
                                    $adcopy_id = $r->add_report_kolom($source, $adcopy["name"], "adcopy", $key3, $adset_id, true) ["data"];
                                }
                            }
                        }
                    }
                }
            }

           
            foreach ($data_campaign as $key => $data_campaign_child){
                $tmp = [];
                $date = $data_campaign_child["date_start"];
                foreach ($data_campaign_child as $key => $value){
                    if ($key == "spend" || $key == "impressions") {
                        $tmp[$key] = $value;
                    }elseif ($key == "outbound_clicks"){
                        $tmp[$key] = (int)$value[0]["value"];
                    }
                }

                foreach ($tmp as $key2 => $value2){
                    $report_id = $r->get_report_id($data_campaign_child["ad_id"]);
                    if ($report_id){
                        $r->update_report_data($report_id, $key2, $value2, $date);
                    }
                }
            }

            $msg["result"]["msg"] = "done";
            $msg["code"] = 1;
        }else{
            $msg["result"]["msg"] = "not found";
            $msg["code"] = 0;
        }
        return $msg;
    }
}

