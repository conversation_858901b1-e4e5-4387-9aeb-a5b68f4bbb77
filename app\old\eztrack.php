<?php
use FacebookAds\Api;
use FacebookAds\Logger\CurlLogger;
use FacebookAds\Object\ServerSide\Event;
use FacebookAds\Object\ServerSide\EventRequest;
use FacebookAds\Object\ServerSide\UserData;
use FacebookAds\Object\ServerSide\CustomData;
use FacebookAds\Object\ServerSide\ActionSource;

class eztrack{
	private $table_visitor = "";
    private $table_event = "";
    private $table_cs = "";
    private $table_cs_log = "";
	private $table_log = "";
	private $table_hash = "";
	private $table_campaign = "";
    private $db = NULL;
    private $db2 = NULL ;

	function __construct($site_id)
	{
		global $wpdb;
		$this->table_visitor = $site_id . '_visitor';
		$this->table_event = $site_id . '_event';
		$this->table_cs = $site_id . '_cs';
		$this->table_cs_log = $site_id . '_cs_log';
		$this->table_log = $site_id . '_log';
		$this->table_hash = $site_id . '_hash';
		$this->table_campaign = $site_id . '_campaign';

		date_default_timezone_set('Asia/Jakarta');
	}

	function visit($param){
		global $app;
		$db = $app->db;
		$db2 = $app->db2;
		if (isset($param["email"])) {
			$data_insert["email"] = $param["email"];
		}
		if (isset($param["nama"])) {
			$data_insert["nama"] = $param["nama"];
		}
		if (isset($param["phone"])) {
			$data_insert["phone"] = $param["phone"];
		}
		if (isset($param["fbp"])) {
			$data_insert["fbp"] = $param["fbp"];
		}
		if (isset($param["fbc"])) {
			$data_insert["fbc"] = $param["fbc"];
		}
		if (isset($param["fbclid"])) {
			$data_insert["fbclid"] = $param["fbclid"];
		}
		if (isset($param["gclid"])) {
			$data_insert["gclid"] = $param["gclid"];
		}
		if (isset($param["ttclid"])) {
			$data_insert["ttclid"] = $param["ttclid"];
		}
		if (isset($param["ttclid2"])) {
			$data_insert["ttclid2"] = $param["ttclid2"];
		}
		if (isset($param["ip"])) {
			$data_insert["ip"] = $param["ip"];
		}
		if (isset($param["useragent"])) {
			$data_insert["useragent"] = $param["useragent"];
		}
		if (isset($param["lp"])) {
			$data_insert["lp"] = $param["lp"];
		}
		$param["campaign_id"] = preg_replace('/[^0-9]/', '', $param["campaign_id"]);
		if (isset($param["campaign_id"])) {
			$data_insert["campaign_id"] =  $param["campaign_id"];
		}
		$table_visitor = $this->table_visitor;
/*

		if (isset($param["vid"])) {
			$where = array("vid" => $param["vid"]);

			if (count($data_insert) < 1 or $data_insert == NULL) {
				$ret['id'] = $param["vid"];
				$ret["code"] = 200;
			} elseif ($wpdb->update($table_visitor, $data_insert, $where)) {
				$ret['id'] = $param["vid"];
				$ret["code"] = 200;
			} else {
				$ret["msg"] = "unknown error";
				$ret["code"] = 500;
			}
		} else {
			if ($wpdb->insert($table_visitor, $data_insert)) {

				$ret['id'] = $wpdb->insert_id;
			}
		}
*/
/*
		try {
			$this->sent_pixel($param["pixel"], $param["apikey"], $ret['id'], "ViewContent");
		} catch (Exception $e) {
		}
*/
		$ret["code"] = 200;
		return $ret;
	}

	function contact($site, $param, $custom = NULL){
		global $app;
		$db = $app->db;
		$db2 = $app->db2;
		$tanggal = date('Y-m-d H:i:s', time());
		if (isset($param["vid"])) {
			$param["vid"] =  preg_replace('/[^0-9]/', '', $param["vid"]);
			$tanggal = date("Y-m-d");
			$table_event = $this->table_event;
			$table_cs = $this->table_cs;
			$table_cs_log = $this->table_cs_log;
			$table_visitor = $this->table_visitor;
			$table_hash = $this->table_hash;
			$table_campaign = $this->table_campaign;
			//var_dump($param["vid"]);
			$db2->where("vid", $param["vid"]);
			$visitor = $db2->getone($table_visitor);
			if($db2->count==0){
				return "vid not found";
			}
			$campaign_id = $visitor["campaign_id"];
			$db2->where("campaign_id", $campaign_id);
			$campaign = $db2->getone($table_campaign);
			$param["pixel"] = $campaign["fb_pixel"];
			$param["apikey"] = $campaign["fb_token"];
			$site["tiktok_token"] = $campaign["tiktok_token"];
			$site["tiktok_pixel"] = $campaign["tiktok_pixel"];
			$hash_data = md5($param["vid"] . ";" . "contact");
			$db2->where("hash = UNHEX(?)", array($hash_data));
			$hash = $db2->get($table_hash);
			$debug = 0;
			if (isset($custom["debug"])) {
				if ($custom["debug"] == 1) {
					$debug = 1;
				}
			}
			if ($hash != NULL) {

				$hash  = $hash[0];
				if ($debug == 0) {
					return "alerady exist";
				}
			}
			$custom = array();
			if (isset($param["phone"])) {
				$custom["phone"] = $param["phone"];
			}
			if (isset($param["email"])) {
				$custom["email"] = $param["email"];
			}
			$event_triggerd = $site["fb_event_contact"];
			$tmpx = $this->sent_pixel($param["pixel"], $param["apikey"], $param["vid"], $event_triggerd, $custom);
			///////// send tiktok
			$tiktok = $site["tiktok_token"];
			$tiktok_pixel = $site["tiktok_pixel"];
			$table_visitor = $this->table_visitor;
			$db2->where("vid", $param["vid"]);
			$visitor = $db2->getone($table_visitor);
			//$tanggal_created = $visitor["created"];
			//$myDateTime = DateTime::createFromFormat('Y-m-d H:i:s', $tanggal_created);
			//$tanggal_created = $myDateTime->format('Y-m-d');
			$tanggal_created = date('Y-m-d', time());
			if ($visitor["ttclid"] != "" && $tiktok != "" && $tiktok_pixel != "") {
				// Generated by curl-to-PHP: http://incarnate.github.io/curl-to-php/
				$ch = curl_init();

				$timestamp = date('Y-m-d\TH:i:s.Z\Z', time());

				$post["pixel_code"] = $tiktok_pixel;
				$post["event"] = "Contact";
				$post["event_id"] = "3_" . $param["vid"];
				$post["timestamp"] = $timestamp;
				$post["context"]["user_agent"] = $visitor['useragent'];
				$post["context"]["ip"] = $visitor['ip'];
				$post["context"]["page"]["url"] = "https://" . $site["domain"] . "/contact";
				$post["context"]["ad"]["callback"] = $visitor["ttclid"];
				$post["context"]["user"]["external_id"] = md5($param["vid"]);
				$post["context"]["user"]["phone_number"] = hash('sha256', "+" . $visitor["phone"]);

				if (isset($param["test_event_code"])) {
					$post["test_event_code"] = $param["test_event_code"];
				}




				curl_setopt($ch, CURLOPT_URL, 'https://business-api.tiktok.com/open_api/v1.2/pixel/track/');
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
				curl_setopt($ch, CURLOPT_POST, 1);
				curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-type: multipart/form-data"));
				curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post));

				$headers = array();
				$headers[] = 'Access-Token: ' . $tiktok;
				$headers[] = 'Content-Type: application/json';
				curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

				$result = curl_exec($ch);


				/*
		  $log["pixel_id"] = $tiktok_pixel;
		  $log["access_token"] = $tiktok;
		  $log["vid"] = $vid;
		  $log["type"] = "Contact";
		  $log["msg"] = $result;

		  

		  $data_insert = array();
		  $data_insertx["source"] = "FB";
		  $data_insertx["type"] = $type;
		  $data_insertx["error"] = 0;
		  $data_insertx["result"] = json_encode($log);
*/
				//var_dump($result);
				if (curl_errno($ch)) {
					echo 'Error:' . curl_error($ch);
				}
				curl_close($ch);
			}
			//////// end send tiktok
			if (isset($param["phone"]) && isset($param["nope_cs"])) {
				/*
table cs  diganti cs log
*/
				$tanggal = date('Y-m-d', time());
				$nope_cs = hp_x($param["nope_cs"]);
				$hash = md5($tanggal_created . ";" . $nope_cs);
				////////////////// get cs id
				$db2->where("nope", $nope_cs);
				$cs = $db2->getone($table_cs);
				///////////////////////////
				if ($db2->count > 0 and $cs != NULL) {
					$hash_campaign = md5($tmpx["campaign_id"] . ";" . $tmpx["lp"] . ";" . $tanggal_created);
					$q = "INSERT INTO {$table_event} (campaign_id,lp,tanggal,contact,hash) VALUES (?,?,?,1,UNHEX(?)) ON DUPLICATE KEY UPDATE contact = contact + 1;";
					$db2->rawQuery($q, array($tmpx["campaign_id"], $tmpx["lp"], $tanggal_created, $hash_campaign));
					$q = "INSERT INTO {$table_cs_log} (tanggal,cs_id,contact,hash) 
					VALUES 
					(?,?,1,UNHEX(?)) ON DUPLICATE KEY UPDATE contact = contact + 1;";
					$db2->rawQuery($q, array($tanggal_created, $cs["id"], $hash));
					@$q = "UPDATE {$table_cs} SET `last_contact` = NOW() WHERE `id` = ?; ";
					@$db2->rawQuery($q, array($cs["id"]));
				}else{
					return "cs not exist";
				}
				$data_insert["contact"] = 1;
				$data_insert["waktu_contact"] = date('Y-m-d H:i:s', time());
				$data_insert["cs_id"] = $cs["id"];
				$data_insert["phone"] = hp_x($param["phone"]);
				$db2->where("vid", $param["vid"]);
				$db2->update($table_visitor, $data_insert);
			}
			/*
langsung tambahkan table campaign + campaign detail + lp + lp detail
*/

			$lp = new landing_page($site["id"], $db, $db2);
			if (count($visitor) > 0 and $visitor != NULL) {
				$campaign_id = $visitor["campaign_id"];
				$db2->where("campaign_id", $campaign_id);
				$campaign = $db2->getone($table_campaign);
				if ($campaign["direct_link"] == 1) {
					$lp_id = NULL;
				} else {
					$lp_id = $visitor["lp"];
				}
				$lp->inc_convertion($campaign_id, $lp_id, "contact");
			}
			/////////
			$data["hash"] = $db2->func("UNHEX(?)", array($hash_data));
			$db2->insert($table_hash, $data);
		}
		return $tmpx["result"];
	}

	function checkout($site, $param, $custom = NULL){
		global $app;
		$db = $app->db;
		$db2 = $app->db2;
		$tanggal = date('Y-m-d H:i:s', time());
		$table_event = $this->table_event;
		$table_cs = $this->table_cs;
		$table_cs_log = $this->table_cs_log;
		$table_visitor = $this->table_visitor;
		$table_hash = $this->table_hash;
		$table_campaign = $this->table_campaign;
		$db2->where("vid", $param["vid"]);
		$visitor = $db2->getone($table_visitor);
		$campaign_id = $visitor["campaign_id"];
		$db2->where("campaign_id", $campaign_id);
		$campaign = $db2->getone($table_campaign);
		$param["pixel"] = $campaign["fb_pixel"];
		$param["apikey"] = $campaign["fb_token"];
		$site["tiktok_token"] = $campaign["tiktok_token"];
		$site["tiktok_pixel"] = $campaign["tiktok_pixel"];
		$hash_data = md5($param["vid"] . ";" . $param["value"] . ";purchase");
		$db2->where("hash = UNHEX(?)", array($hash_data));
		$hash = $db2->get($table_hash);
		if ($hash != NULL) {
			$hash  = $hash[0];
			return "alerady exist";
		}
		if (isset($param["currency"])) {
			$custom["currency"]  = $param["currency"];
		} else {
			$custom["currency"] = "IDR";
		}
		if (isset($param["value"])) {
			$custom["value"]  = $param["value"];
		} else {
			$custom["value"]  = 0;
		}
		if (isset($param["vid"])) {
			$tanggal = date('Y-m-d', time());
			$table_visitor = $this->table_visitor;
			$db2->where("vid", $param["vid"]);
			$visitor = $db2->getone($table_visitor);
			if ($visitor == NULL) {
				return "visitor not found";
			}
			//$tanggal_created = $visitor["created"];
			//$myDateTime = DateTime::createFromFormat('Y-m-d H:i:s', $tanggal_created);
			//$tanggal_created = $myDateTime->format('Y-m-d');
			$tanggal_created = date('Y-m-d', time());
			$event_triggerd = $site["fb_event_ic"];
			$tmpx = $this->sent_pixel($param["pixel"], $param["apikey"], $param["vid"], $event_triggerd, $custom);
			///////// send tiktok
			$tiktok_pixel = $site["tiktok_pixel"];
			$tiktok = $site["tiktok_token"];
			if ($visitor["ttclid"] != "" && $tiktok != "" && $tiktok_pixel != "") {
				// Generated by curl-to-PHP: http://incarnate.github.io/curl-to-php/
				$ch = curl_init();
				$timestamp = date('Y-m-d\TH:i:s.Z\Z', time());
				$post["pixel_code"] = $tiktok_pixel;
				$post["event"] = "InitiateCheckout";
				$post["event_id"] = "4_" . $param["vid"];
				$post["timestamp"] = $timestamp;
				$post["context"]["user_agent"] = $visitor['useragent'];
				$post["context"]["ip"] = $visitor['ip'];
				$post["context"]["page"]["url"] = "https://" . $site["domain"] . "/contact";
				$post["context"]["ad"]["callback"] = $visitor["ttclid"];
				$post["context"]["user"]["external_id"] = md5($param["vid"]);
				$post["context"]["user"]["phone_number"] = hash('sha256', "+" . $visitor["phone"]);
				$post["properties"]["currency"] = $custom["currency"];
				$post["properties"]["value"] = $custom["value"];
				if (isset($param["test_event_code"])) {
					$post["test_event_code"] = $param["test_event_code"];
				}
				curl_setopt($ch, CURLOPT_URL, 'https://business-api.tiktok.com/open_api/v1.2/pixel/track/');
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
				curl_setopt($ch, CURLOPT_POST, 1);
				curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-type: multipart/form-data"));
				curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post));
				$headers = array();
				$headers[] = 'Access-Token: ' . $tiktok;
				$headers[] = 'Content-Type: application/json';
				curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
				$result = curl_exec($ch);
				if (curl_errno($ch)) {
					echo 'Error:' . curl_error($ch);
				}
				curl_close($ch);
			}
			//////// end send tiktok
			/*
			$hash = md5($visitor["campaign_id"] . ";" . $visitor["lp"] . ";" . $tanggal_created);
			$value = preg_replace("/[^0-9]/", "", $param["value"]);

			$q = "INSERT INTO {$table_event} (campaign_id,lp,tanggal,purchase,value,hash) VALUES (?,?,?,1,?,UNHEX(?)) ON DUPLICATE KEY UPDATE purchase = purchase + 1 , value = value + ?;";
			$db2->rawQuery($q, array($visitor["campaign_id"], $visitor["lp"], $tanggal_created, $value, $hash, $value));
			$cs_id = $visitor["cs_id"];
			if ($cs_id != 0) {
				$db2->where("id", $cs_id);
				$cs = $db2->getone($table_cs);
				$hash = md5($tanggal_created . ";" . $cs['id']);
				if (count($cs) > 0 and $cs != NULL) {
					$q = "INSERT INTO {$table_cs_log} (tanggal,cs_id,purchase,hash,value) VALUES (?,?,1,UNHEX(?),?) ON DUPLICATE KEY UPDATE purchase = contact + 1 , value = value + ?;";
					$db2->rawQuery($q, array($tanggal_created, $cs["id"], $hash, $value, $value));
				}
			}
			$datax["purchase"] = 1;
			$datax["value"] =  $value;
			$datax["waktu_purchase"] = date('Y-m-d H:i:s', time());
			$db2->where("vid", $param["vid"]);
			$db2->update($table_visitor, $datax);
			*/
		}
		/*
		$lp = new landing_page($site["id"], $db, $db2);
		if (count($visitor) > 0 and $visitor != NULL) {
			$campaign_id = $visitor["campaign_id"];
			$db2->where("campaign_id", $campaign_id);
			$campaign = $db2->getone($table_campaign);
			if ($campaign["direct_link"] == 1) {
				$lp_id = NULL;
			} else {
				$lp_id = $visitor["lp"];
			}
			$lp->inc_convertion($campaign_id, $lp_id, "purchase", $tanggal_created, $value);
		}
		*/
		$data = array();
		$data["hash"] = $db2->func("UNHEX(?)", array($hash_data));
		$db2->insert($table_hash, $data);
		return $tmpx["result"];
	}

	function purchase($site, $param, $custom = NULL){
		global $app;
		$db = $app->db;
		$db2 = $app->db2;
		$tanggal = date('Y-m-d H:i:s', time());
		$table_event = $this->table_event;
		$table_cs = $this->table_cs;
		$table_cs_log = $this->table_cs_log;
		$table_visitor = $this->table_visitor;
		$table_hash = $this->table_hash;
		$table_campaign = $this->table_campaign;
		$db2->where("vid", $param["vid"]);
		$visitor = $db2->getone($table_visitor);
		$campaign_id = $visitor["campaign_id"];
		$db2->where("campaign_id", $campaign_id);
		$campaign = $db2->getone($table_campaign);
		$param["pixel"] = $campaign["fb_pixel"];
		$param["apikey"] = $campaign["fb_token"];
		$site["tiktok_token"] = $campaign["tiktok_token"];
		$site["tiktok_pixel"] = $campaign["tiktok_pixel"];
		$hash_data = md5($param["vid"] . ";" . $param["value"] . ";purchase");
		$db2->where("hash = UNHEX(?)", array($hash_data));
		$hash = $db2->get($table_hash);
		if ($hash != NULL) {
			$hash  = $hash[0];
			return "alerady exist";
		}
		if (isset($param["currency"])) {
			$custom["currency"]  = $param["currency"];
		} else {
			$custom["currency"] = "IDR";
		}
		if (isset($param["value"])) {
			$custom["value"]  = $param["value"];
		} else {
			$custom["value"]  = 0;
		}
		if (isset($param["vid"])) {
			$tanggal = date('Y-m-d', time());
			$table_visitor = $this->table_visitor;
			$db2->where("vid", $param["vid"]);
			$visitor = $db2->getone($table_visitor);
			if ($visitor == NULL) {
				return "visitor not found";
			}
			//$tanggal_created = $visitor["created"];
			//$myDateTime = DateTime::createFromFormat('Y-m-d H:i:s', $tanggal_created);
			//$tanggal_created = $myDateTime->format('Y-m-d');
			$tanggal_created = date('Y-m-d', time());
			$event_triggerd = $site["fb_event_purchase"];
			$tmpx = $this->sent_pixel($param["pixel"], $param["apikey"], $param["vid"], $event_triggerd, $custom);
			///////// send tiktok
			$tiktok_pixel = $site["tiktok_pixel"];
			$tiktok = $site["tiktok_token"];
			if ($visitor["ttclid"] != "" && $tiktok != "" && $tiktok_pixel != "") {
				// Generated by curl-to-PHP: http://incarnate.github.io/curl-to-php/
				$ch = curl_init();
				$timestamp = date('Y-m-d\TH:i:s.Z\Z', time());
				$post["pixel_code"] = $tiktok_pixel;
				$post["event"] = "CompletePayment";
				$post["event_id"] = "4_" . $param["vid"];
				$post["timestamp"] = $timestamp;
				$post["context"]["user_agent"] = $visitor['useragent'];
				$post["context"]["ip"] = $visitor['ip'];
				$post["context"]["page"]["url"] = "https://" . $site["domain"] . "/contact";
				$post["context"]["ad"]["callback"] = $visitor["ttclid"];
				$post["context"]["user"]["external_id"] = md5($param["vid"]);
				$post["context"]["user"]["phone_number"] = hash('sha256', "+" . $visitor["phone"]);
				$post["properties"]["currency"] = $custom["currency"];
				$post["properties"]["value"] = $custom["value"];
				if (isset($param["test_event_code"])) {
					$post["test_event_code"] = $param["test_event_code"];
				}
				curl_setopt($ch, CURLOPT_URL, 'https://business-api.tiktok.com/open_api/v1.2/pixel/track/');
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
				curl_setopt($ch, CURLOPT_POST, 1);
				curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-type: multipart/form-data"));
				curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post));
				$headers = array();
				$headers[] = 'Access-Token: ' . $tiktok;
				$headers[] = 'Content-Type: application/json';
				curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
				$result = curl_exec($ch);
				if (curl_errno($ch)) {
					echo 'Error:' . curl_error($ch);
				}
				curl_close($ch);
			}
			//////// end send tiktok
			$hash = md5($visitor["campaign_id"] . ";" . $visitor["lp"] . ";" . $tanggal_created);
			$value = preg_replace("/[^0-9]/", "", $param["value"]);
			$q = "INSERT INTO {$table_event} (campaign_id,lp,tanggal,purchase,value,hash) VALUES (?,?,?,1,?,UNHEX(?)) ON DUPLICATE KEY UPDATE purchase = purchase + 1 , value = value + ?;";
			$db2->rawQuery($q, array($visitor["campaign_id"], $visitor["lp"], $tanggal_created, $value, $hash, $value));
			$cs_id = $visitor["cs_id"];
			if ($cs_id != 0) {
				$db2->where("id", $cs_id);
				$cs = $db2->getone($table_cs);
				$hash = md5($tanggal_created . ";" . $cs['nope']);
				if (count($cs) > 0 and $cs != NULL) {
					$q = "INSERT INTO {$table_cs_log} (tanggal,cs_id,purchase,hash,value) VALUES (?,?,1,UNHEX(?),?) ON DUPLICATE KEY UPDATE purchase = purchase + 1 , value = value + ?;";
					$db2->rawQuery($q, array($tanggal_created, $cs["id"], $hash, $value, $value));
				}
			}
			$datax["purchase"] = 1;
			$datax["value"] =  $value;
			$datax["waktu_purchase"] = date('Y-m-d H:i:s', time());
			$db2->where("vid", $param["vid"]);
			$db2->update($table_visitor, $datax);
			$lp = new landing_page($site["id"], $db, $db2);
			if (count($visitor) > 0 and $visitor != NULL) {
				$campaign_id = $visitor["campaign_id"];
				$db2->where("campaign_id", $campaign_id);
				$campaign = $db2->getone($table_campaign);
				if ($campaign["direct_link"] == 1) {
					$lp_id = NULL;
				} else {
					$lp_id = $visitor["lp"];
				}
				$lp->inc_convertion($campaign_id, $lp_id, "purchase", $tanggal_created, $value);
			}
		}
		$data = array();
		$data["hash"] = $db2->func("UNHEX(?)", array($hash_data));
		$db2->insert($table_hash, $data);
		return $tmpx["result"];
	}

	function other($event, $site, $param, $custom = NULL){
		global $app;
		$db = $app->db;
		$db2 = $app->db2;
		$tanggal = date('Y-m-d H:i:s', time());
		if (isset($param["vid"])) {
			$param["vid"] =  preg_replace('/[^0-9]/', '', $param["vid"]);
			$tanggal = date("Y-m-d");
			$table_event = $this->table_event;
			$table_cs = $this->table_cs;
			$table_cs_log = $this->table_cs_log;
			$table_visitor = $this->table_visitor;
			$table_hash = $this->table_hash;
			$table_campaign = $this->table_campaign;
			//var_dump($param["vid"]);
			$db2->where("vid", $param["vid"]);
			$visitor = $db2->getone($table_visitor);
			$campaign_id = $visitor["campaign_id"];
			$db2->where("campaign_id", $campaign_id);
			$campaign = $db2->getone($table_campaign);
			$param["pixel"] = $campaign["fb_pixel"];
			$param["apikey"] = $campaign["fb_token"];
			$site["tiktok_token"] = $campaign["tiktok_token"];
			$site["tiktok_pixel"] = $campaign["tiktok_pixel"];
			$hash_data = md5($param["vid"] . ";" . $event);
			$db2->where("hash = UNHEX(?)", array($hash_data));
			$hash = $db2->get($table_hash);
			if ($hash != NULL) {
				$hash  = $hash[0];
				return "alerady exist";
			}
			$custom = array();
			if (isset($param["phone"])) {
				$custom["phone"] = $param["phone"];
			}
			if (isset($param["email"])) {
				$custom["email"] = $param["email"];
			}
			$tmpx = $this->sent_pixel($param["pixel"], $param["apikey"], $param["vid"], $event, $custom);
			///////// send tiktok
			$tiktok = $site["tiktok_token"];
			$tiktok_pixel = $site["tiktok_pixel"];
			$table_visitor = $this->table_visitor;
			$db2->where("vid", $param["vid"]);
			$visitor = $db2->getone($table_visitor);
			//$tanggal_created = $visitor["created"];
			//$myDateTime = DateTime::createFromFormat('Y-m-d H:i:s', $tanggal_created);
			//$tanggal_created = $myDateTime->format('Y-m-d');
			$tanggal_created = date('Y-m-d', time());
			if ($visitor["ttclid"] != "" && $tiktok != "" && $tiktok_pixel != "") {
				// Generated by curl-to-PHP: http://incarnate.github.io/curl-to-php/
				$ch = curl_init();
				$timestamp = date('Y-m-d\TH:i:s.Z\Z', time());
				$post["pixel_code"] = $tiktok_pixel;
				$post["event"] = $event;
				$post["event_id"] = "3_" . $param["vid"];
				$post["timestamp"] = $timestamp;
				$post["context"]["user_agent"] = $visitor['useragent'];
				$post["context"]["ip"] = $visitor['ip'];
				$post["context"]["page"]["url"] = "https://" . $site["domain"] . "/contact";
				$post["context"]["ad"]["callback"] = $visitor["ttclid"];
				$post["context"]["user"]["external_id"] = md5($param["vid"]);
				$post["context"]["user"]["phone_number"] = hash('sha256', "+" . $visitor["phone"]);
				if (isset($param["test_event_code"])) {
					$post["test_event_code"] = $param["test_event_code"];
				}
				curl_setopt($ch, CURLOPT_URL, 'https://business-api.tiktok.com/open_api/v1.2/pixel/track/');
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
				curl_setopt($ch, CURLOPT_POST, 1);
				curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-type: multipart/form-data"));
				curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post));
				$headers = array();
				$headers[] = 'Access-Token: ' . $tiktok;
				$headers[] = 'Content-Type: application/json';
				curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

				$result = curl_exec($ch);


				if (curl_errno($ch)) {
					echo 'Error:' . curl_error($ch);
				}
				curl_close($ch);
			}
			//////// end send tiktok
			$data["hash"] = $db2->func("UNHEX(?)", array($hash_data));
			$db2->insert($table_hash, $data);
		}

		return $tmpx["result"];
	}


	function sent_pixel($pixel_id, $access_token, $vid, $type, $custom = NULL){
		global $app;
		$db = $app->db;
		$db2 = $app->db2;
		$table_visitor = $this->table_visitor;
		$table_log = $this->table_log;
		if ($pixel_id == "" or $access_token == "") {
			return;
		}

		$waktu = time();
		$api = Api::init(null, null, $access_token);
		$api->setLogger(new CurlLogger());
		$db2->where("vid", $vid);
		$data = $db2->getone($this->table_visitor);
		if ($data == NULL) {
			return;
		}
		///////// save send pixel
		$dx['pixel_id'] = $pixel_id;
		$dx['access_token'] = $access_token;
		$dx['vid'] = $vid;
		$dx['type'] = $type;
		$dx['custom'] = json_encode($custom);
		$db->insert('x_send_pixel', $dx);

		$user_data = new UserData();
		if (isset($data["fbc"]) && $data["fbc"] != "") {
			$user_data->setFbc($data["fbc"]);
		} else {
			if (isset($data["fbclid"]) && $data["fbclid"] != "") {
				$fbc = "fb.1." . $waktu . "." . $data["fbclid"];
				$user_data->setFbc($fbc);
			}
		}
		if (isset($data["ip"]) && $data["ip"] != "") {
			$user_data->setClientIpAddress($data["ip"]);
		}
		if (isset($data["agent"]) && $data["agent"] != "") {
			$user_data->setClientUserAgent($data["agent"]);
		}
		if (isset($data["fbp"]) && $data["fbp"] != "") {
			$user_data->setFbp($data["fbp"]);
		}
		if (isset($data["email"]) && $data["email"] != "") {
			$user_data->setEmail($data["email"]);
		}
		if (isset($data["phone"]) && $data["phone"] != "") {
			$user_data->setPhone($data["phone"]);
		}
		if (isset($data["useragent"]) && $data["useragent"] != "") {
			$user_data->setClientUserAgent($data["useragent"]);
		}
		$user_data->setExternalId($data["vid"]);
		if (isset($custom["phone"])) {
			$phone = preg_replace('/[^0-9.]+/', '', $custom["phone"]);
			$data_insert = array();
			$data_insert["phone"] = $phone;
			$db2->where("vid", $data["vid"]);
			$db2->update($table_visitor, $data_insert);
			$user_data->setPhone($phone);
		}
		if (isset($custom["email"])) {
			$data_insert["email"] = $custom["email"];
			$db2->where("vid", $data["vid"]);
			$db2->update($table_visitor, $data_insert);
			$user_data->setEmail(trim($custom["email"]));
		}
		if ($type == 'ViewContent') {
			$data_insert["type"] = "ViewContent";
			$event = new Event();
			$event->setEventId("ViewContent-" . $data["vid"]);
			$event->setEventName('ViewContent');
			$event->setEventTime($waktu);
			$event->setUserData($user_data);
			$event->setActionSource(ActionSource::WEBSITE);
		}
		if ($type == "Lead") {
			$data_insert["type"] = "Lead";
			$event = new Event();
			$event->setEventId("Lead-" . $data["vid"]);
			$event->setEventName('Lead');
			$event->setEventTime($waktu);
			$event->setUserData($user_data);
			$event->setActionSource(ActionSource::WEBSITE);
		}
		if ($type == "AddPaymentInfo") {
			$data_insert["type"] = "AddPaymentInfo";
			$event = new Event();
			$event->setEventId("AddPaymentInfo-" . $data["vid"]);
			$event->setEventName('AddPaymentInfo');
			$event->setEventTime($waktu);
			$event->setUserData($user_data);
			$event->setActionSource(ActionSource::WEBSITE);
		}
		if ($type == "AddToCart") {
			$data_insert["type"] = "AddToCart";
			$event = new Event();
			$event->setEventId("AddToCart-" . $data["vid"]);
			$event->setEventName('AddToCart');
			$event->setEventTime($waktu);
			$event->setUserData($user_data);
			$event->setActionSource(ActionSource::WEBSITE);
		}
		if ($type == "Contact") {
			$data_insert["type"] = "Contact";
			$event = new Event();
			$event->setEventId("Contact-" . $data["vid"]);
			$event->setEventName('Contact');
			$event->setEventTime($waktu);
			$event->setUserData($user_data);
			$event->setActionSource(ActionSource::CHAT);
		}

		if ($type == "InitiateCheckout") {
			$data_insert["type"] = "InitiateCheckout";
			$event = new Event();
			$event->setEventId("InitiateCheckout-" . $data["vid"]);
			$event->setEventName('InitiateCheckout');
			$event->setEventTime($waktu);
			$event->setUserData($user_data);
			$event->setActionSource(ActionSource::CHAT);
		}

		if ($type == "Purchase") {
			$data_insert["type"] = "Purchase";
			$custom_data = new CustomData();
			if (isset($custom["currency"])) {
				$custom_data->setCurrency($custom["currency"]);
			}
			if (isset($custom["value"])) {
				$custom_data->setValue($custom["value"]);
			}
			$event = new Event();
			$event->setEventId("Purchase-" . $data["vid"]);
			$event->setEventName('Purchase');
			$event->setEventTime($waktu);
			$event->setUserData($user_data);
			$event->setCustomData($custom_data);
			$event->setActionSource(ActionSource::CHAT);
		}
		$events = array();
		array_push($events, $event);

		$save_log = false;
		try {
			///////////////////////////////// kirim ke facebook //////////////////////////////////////
			$request = (new EventRequest($pixel_id))->setEvents($events);
			$response = @$request->execute();
			///////////////////////////////////////////////////////////////////////////////////////////
		} catch (Exception $e) {
			$save_log = true;
			$xmsg = $e->getMessage();
			$log["pixel_id"] = $pixel_id;
			$log["access_token"] = $access_token;
			$log["vid"] = $vid;
			$log["type"] = $type;
			$log["custom"] = $custom;
			$data_insert = array();
			$data_insertx["source"] = "FB";
			$data_insertx["type"] = $type;
			$data_insertx["error"] = 1;
			$data_insertx["param"] = json_encode($log);
			$data_insertx["result"] = $xmsg;
			$format = array('%s', '%s');
			@$db2->insert($table_log, $data_insertx);
		}
		ob_start(); //Start buffering
		print_r($response); //print the result
		$output = ob_get_contents(); //get the result from buffer
		ob_end_clean(); //close buffer
		if (strpos($output, 'fbtrace_id') !== false) {
			if ($save_log == false) {
				$data_insert = array();
				$log["pixel_id"] = $pixel_id;
				$log["access_token"] = $access_token;
				$log["vid"] = $vid;
				$log["type"] = $type;
				$log["custom"] = $custom;
				$data_insertx["source"] = "FB";
				$data_insertx["type"] = $type;
				$data_insertx["error"] = 0;
				$data_insertx["param"] = json_encode($log);
				$data_insertx["result"] = $output;
				$format = array('%s');
				@$db2->insert($table_log, $data_insertx);
			}
		} else {
			if ($save_log == false) {
				$data_insert = array();
				$log["pixel_id"] = $pixel_id;
				$log["access_token"] = $access_token;
				$log["vid"] = $vid;
				$log["type"] = $type;
				$log["custom"] = $custom;
				$data_insertx["source"] = "FB";
				$data_insertx["type"] = $type;
				$data_insertx["error"] = 1;
				$data_insert["param"] = json_encode($log);
				$data_insert["result"] = $output;
				$format = array('%s');
				@$db2->insert($table_log, $data_insertx);
			}
		}
		$data["result"] = $output;
		return $data;
	}
}
