<?php 





class aff

{

	function register()

	{



	}



	function login()

	{



	}



	function get_parent($phone)

	{

		global $keya, $c_time, $app;

		$db = $app->db;

		$phone = $this->hp($phone) ;

		$db->join("x_aff a","a.user_id = u.user_id");

		$db->join("x_user p","p.user_id = a.parent");

		$db->where("u.phone",$phone);

		$parent = $db->getone("x_user u","p.nama");



		if(count($parent) > 0)

		{

			return $parent["nama"];

		}

		else

		{

			return $false;	

		}

		

	}



	function get_downline($user_id,$start=0,$end=10)

	{



		global $keya, $c_time, $app;

		$db = $app->db;





		$db->join("x_aff a","a.user_id = u.user_id");

		$db->where("a.parent = UNHEX(?)",array($user_id));

		$downline = $db->get("x_user u",array($start,$end),"u.nama,u.email,u.phone");



		if(count($downline) > 0)

		{

			$res["count"] = count($downline);

			$res["data"] = $downline;

			return $res;

		}

		else

		{

			$res["count"] = 0;

			

			return $res;	

		}

	}



	function visit($code,$site_id = 0,$type="visit")

	{

		/*  ---- type -----

		visit

		coming_soon

		menunggu_pembayaran

		purchase

		*/



		global $keya, $c_time, $app;

		$db = $app->db;



		$db->where('aff_invite', $code);

        $user = $db->getone('x_user');



        if(count($user) > 0)

        {

	        $user_id = bin2hex($user["user_id"]);



	        $tanggal = date("Y-m-d");

	        $hash = md5($tanggal.";".$user_id.";".$site_id);



	        if($type == "visit")

	        {

	        	$q = "INSERT INTO x_aff_traffic (tanggal,user_id,site_id,visit,hash) VALUES ('{$tanggal}',UNHEX('{$user_id}'),'{$site_id}',1,UNHEX('{$hash}')) ON DUPLICATE KEY UPDATE visit = visit + 1;";

	        }elseif ($type="coming_soon") {

	        	$q = "INSERT INTO x_aff_traffic (tanggal,user_id,site_id,coming_soon,hash) VALUES ('{$tanggal}',UNHEX('{$user_id}'),'{$site_id}',1,UNHEX('{$hash}')) ON DUPLICATE KEY UPDATE coming_soon = coming_soon + 1;";

	        }elseif ($type="menunggu_pembayaran") {

	        	$q = "INSERT INTO x_aff_traffic (tanggal,user_id,site_id,menunggu_pembayaran,hash) VALUES ('{$tanggal}',UNHEX('{$user_id}'),'{$site_id}',1,UNHEX('{$hash}')) ON DUPLICATE KEY UPDATE menunggu_pembayaran = menunggu_pembayaran + 1;";

	        }elseif ($type="purchase") {

	        	$q = "INSERT INTO x_aff_traffic (tanggal,user_id,site_id,purchase,hash) VALUES ('{$tanggal}',UNHEX('{$user_id}'),'{$site_id}',1,UNHEX('{$hash}')) ON DUPLICATE KEY UPDATE purchase = purchase + 1;";

	        }

	        

	      

	        $db->rawQuery($q);

	        

	        return true;

	        

    	}

    	else

    	{

    		return false;

    	}

	}



	function komisi_parent($downline_user_id,$komisi)

	{



	}





	



	





	function hp($nohp){

    $hp = "";

    if ($nohp[0] != '0' && $nohp[0] != '+' && $nohp[0] != '6') {

        $nohp = "0" . $nohp;

    }

    $nohp = str_replace(" ", "", $nohp);

    $nohp = str_replace("-", "", $nohp);

    $nohp = str_replace(" ", "", $nohp);

    $nohp = str_replace("(", "", $nohp);

    $nohp = str_replace(")", "", $nohp);

    $nohp = str_replace(".", "", $nohp);

    if (!preg_match('/[^+0-9]/', trim($nohp))) {

        if (substr(trim($nohp), 0, 3) == '+62') {

            $hp = trim($nohp);

        }

        if (substr(trim($nohp), 0, 2) == '62') {

            $hp = '+' . trim($nohp);

        }elseif (substr(trim($nohp), 0, 1) == '0') {

            $hp = '+62' . substr(trim($nohp), 1);

        }

    }

    $hp = str_replace("+", "", $hp);

    return $hp;

}







}