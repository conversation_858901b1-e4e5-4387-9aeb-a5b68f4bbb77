<?php

namespace DynamicWebhook\DataExtractor;

use DynamicWebhook\Abstracts\AbstractDataExtractor;

/**
 * Generic data extractor for standard webhook formats
 */
class GenericDataExtractor extends AbstractDataExtractor
{
    /**
     * Get supported platforms
     */
    public function getSupportedPlatforms(): array
    {
        return [
            'default', 'botsailor', 'kommo', 'otoklix', 'qiscus-omnichannel',
            'rasayel', 'respondio', 'wabahalosis', 'hs'
        ];
    }

    /**
     * Extract platform-specific data
     */
    protected function extractPlatformSpecificData(array $data, array $config, string $platform): array
    {
        $result = $this->getEmptyResult();

        // Extract type value first
        $typeValue = $this->normalizer->extractNestedValue($data, $config['type_field']);
        $result['message_type'] = $this->determineMessageType($typeValue, $config, $platform);

        // Extract phone number based on message type
        if ($result['message_type'] === 'message_out' && isset($config['phone_field_out'])) {
            $result['phone'] = $this->normalizer->extractNestedValue($data, $config['phone_field_out']);
        } else {
            $result['phone'] = $this->normalizer->extractNestedValue($data, $config['phone_field']);
            // Try alternative phone field if primary failed
            if (!$result['phone'] && isset($config['phone_field_alt'])) {
                $result['phone'] = $this->normalizer->extractNestedValue($data, $config['phone_field_alt']);
            }
        }

        // Extract message content based on message type
        if ($result['message_type'] === 'message_out' && isset($config['message_field_out'])) {
            $result['message'] = $this->normalizer->extractNestedValue($data, $config['message_field_out']);
        } else {
            $result['message'] = $this->normalizer->extractNestedValue($data, $config['message_field']);
            // Try alternative message field if primary failed
            if (!$result['message'] && isset($config['message_field_alt'])) {
                $result['message'] = $this->normalizer->extractNestedValue($data, $config['message_field_alt']);
            }
        }

        // Clean phone number
        $result['phone'] = $this->normalizer->cleanPhoneNumber($result['phone']);
        
        // Normalize message
        $result['message'] = $this->normalizer->normalizeMessage($result['message']);

        // Store raw data for debugging
        $result['raw_data'] = $data;

        return $result;
    }
}
