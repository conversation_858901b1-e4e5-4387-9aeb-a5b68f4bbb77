<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/resources/local_services_lead.proto

namespace Google\Ads\GoogleAds\V18\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Represents the credit details of a lead.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.resources.CreditDetails</code>
 */
class CreditDetails extends \Google\Protobuf\Internal\Message
{
    /**
     * Output only. Credit state of the lead.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.LocalServicesCreditStateEnum.CreditState credit_state = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $credit_state = 0;
    /**
     * Output only. The date time when the credit state of the lead was last
     * updated. The format is "YYYY-MM-DD HH:MM:SS" in the Google Ads account's
     * timezone. Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
     *
     * Generated from protobuf field <code>string credit_state_last_update_date_time = 2 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $credit_state_last_update_date_time = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $credit_state
     *           Output only. Credit state of the lead.
     *     @type string $credit_state_last_update_date_time
     *           Output only. The date time when the credit state of the lead was last
     *           updated. The format is "YYYY-MM-DD HH:MM:SS" in the Google Ads account's
     *           timezone. Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Resources\LocalServicesLead::initOnce();
        parent::__construct($data);
    }

    /**
     * Output only. Credit state of the lead.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.LocalServicesCreditStateEnum.CreditState credit_state = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getCreditState()
    {
        return $this->credit_state;
    }

    /**
     * Output only. Credit state of the lead.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.LocalServicesCreditStateEnum.CreditState credit_state = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setCreditState($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\LocalServicesCreditStateEnum\CreditState::class);
        $this->credit_state = $var;

        return $this;
    }

    /**
     * Output only. The date time when the credit state of the lead was last
     * updated. The format is "YYYY-MM-DD HH:MM:SS" in the Google Ads account's
     * timezone. Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
     *
     * Generated from protobuf field <code>string credit_state_last_update_date_time = 2 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return string
     */
    public function getCreditStateLastUpdateDateTime()
    {
        return $this->credit_state_last_update_date_time;
    }

    /**
     * Output only. The date time when the credit state of the lead was last
     * updated. The format is "YYYY-MM-DD HH:MM:SS" in the Google Ads account's
     * timezone. Examples: "2018-03-05 09:15:00" or "2018-02-01 14:34:30"
     *
     * Generated from protobuf field <code>string credit_state_last_update_date_time = 2 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param string $var
     * @return $this
     */
    public function setCreditStateLastUpdateDateTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->credit_state_last_update_date_time = $var;

        return $this;
    }

}

