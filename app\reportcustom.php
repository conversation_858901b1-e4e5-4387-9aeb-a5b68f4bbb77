<?php

class reportcustom {
	private $__access_token;
/*
function __construct($access_token){
$this->access_token = $access_token;
}
 */

	public function get_report_data($report_group_id,$convertion_type,$column, $start, $end,$type,$source,$parent_id,$sort_by,$sort_type)
    {
		global $app;
		$db2   = $app->db2;
		$error = false;

        $r = new report();
        $return = $r->get_overview_custom($report_group_id,$convertion_type,$column, $start, $end,$type,$source,$parent_id,$sort_by,$sort_type);

        return $return;
	}

	public function get_report_list() {
		global $app;
		$db2 = $app->db2;

/*
		$db2->join("report r1", "r1.report_id = r2.parent_id");
		$db2->where("r1.parent_id", 0);
		$res = $db2->get("report r2", NULL, "r2.source,r2.report_id,r2.name");
		*/
		
		$db2->where("r1.parent_id", 0);
		$res = $db2->get("report r1", NULL, "r1.source,r1.report_id,r1.name");

		$return["code"] = 1;
		$return["msg"]  = "sukses";
		$return["data"] = $res;

		return $return;
	}

	public function get_campaign_detail($campaign_id) {
		global $app;
		$db2 = $app->db2;

		$db2->join("report_group", "report_group.report_group_id = report_group_rel.report_group_id");
		$db2->join("report", "report.report_id = report_group_rel.report_id");
		$db2->where("report_group_rel.report_group_id", $campaign_id);
		$res = $db2->get("report_group_rel", NULL, "report.report_id,report.name,report_group_rel.report_group_rel_id");

		$return["code"] = 1;
		$return["msg"]  = "sukses";
		$return["data"] = $res;

		return $return;
	}
	public function get_campaigns($parent_id) {
		global $app;
		$db2 = $app->db2;

		$db2->where("report_group_id_parent", $parent_id);
		$res = $db2->get("report_group");

		$return["code"] = 1;
		$return["msg"]  = "sukses";
		$return["data"] = $res;

		return $return;
	}

	public function get_reportcustom() {
		global $app;
		$db2 = $app->db2;

		$db2->where("report_group_id_parent", 0);
		$res = $db2->get("report_group");

		$return["code"] = 1;
		$return["msg"]  = "sukses";
		$return["data"] = $res;

		return $return;
	}

	public function create($name, $data, $logo_url = NULL) {
		global $app;
		$db2 = $app->db2;

		$data_insert["report_group_name"]      = $name;
		$data_insert["report_group_id_parent"] = 0;
		if (isset($logo_url)) {
			$data_insert["logo_url"] = $logo_url;
		}

		$parent_id = $db2->insert("report_group", $data_insert);

		if (NULL != $data) {
			if (is_array($data)) {
				if (count($data) > 0) {
					foreach ($data as $key => $value) {
						$campaign_id = $this->add_group($parent_id, $key);

						$value = explode(",", $value);
						if (is_array($value)) {
							foreach ($value as $k => $v) {
								$this->add_report_to_group($campaign_id, $v);
							}
						} else {
							$this->add_report_to_group($campaign_id, $value);
						}
					}
				}
			}
		}

		return true;
	}

	public function delete($report_custom_id) {
		global $app;
		$db2 = $app->db2;

		$db2->where("report_group_id_parent", $report_custom_id);
		$report_groups = $db2->get("report_group");

		foreach ($report_groups as $key => $value) {
			$this->delete_group($value["report_group_id"]);
		}

		$db2->where("report_group_id", $report_custom_id);
		$db2->delete("report_group");

		$return["code"] = 1;
		$return["msg"]  = "sukses";

		return $return;
	}

	public function add_group($parent_id, $name) {
		global $app;
		$db2 = $app->db2;

		$data_insert["report_group_name"]      = $name;
		$data_insert["report_group_id_parent"] = $parent_id;

		$id = $db2->insert("report_group", $data_insert);

		return $id;
	}

	public function delete_group($group_id) {
		global $app;
		$db2 = $app->db2;

		$db2->where("report_group_id ", $group_id);
		$db2->delete("report_group_rel");

		$db2->where("report_group_id", $group_id);
		$db2->delete("report_group");

		$return["code"] = 1;
		$return["msg"]  = "sukses";

		return $return;
	}

	public function add_report_to_group($group_id, $report_id) {
		global $app;
		$db2 = $app->db2;

		$data_insert["report_group_id"] = $group_id;
		$data_insert["report_id"]       = $report_id;

		$db2->insert("report_group_rel", $data_insert);
	}

	public function delete_report_from_group($rel_id) {
		global $app;
		$db2 = $app->db2;

		$db2->where("report_group_rel_id", $rel_id);
		$db2->delete("report_group_rel");

		$return["code"] = 1;
		$return["msg"]  = "sukses";

		return $return;
	}

	/*
		$reportCustom = new reportcustom();
		$result = $reportCustom->edit(
			123, // report_group_id
			"New Name",
			array(
				"Campaign 1" => "1,2,3",
				"Campaign 2" => "4,5,6"
			),
			"http://example.com/logo.png" // optional
		);

	*/
	public function edit($report_group_id, $name, $data, $logo_url = NULL) {
		global $app;
		$db2 = $app->db2;
	
		// Update main report group
		$data_update["report_group_name"] = $name;
		if (isset($logo_url)) {
			$data_update["logo_url"] = $logo_url;
		}
		
		$db2->where("report_group_id", $report_group_id);
		$db2->update("report_group", $data_update);
	
		// Delete existing child groups and relations
		$db2->where("report_group_id_parent", $report_group_id);
		$report_groups = $db2->get("report_group");
		
		foreach ($report_groups as $group) {
			$this->delete_group($group["report_group_id"]);
		}
	
		// Create new child groups and relations
		if (NULL != $data) {
			if (is_array($data)) {
				if (count($data) > 0) {
					foreach ($data as $key => $value) {
						$campaign_id = $this->add_group($report_group_id, $key);
	
						$value = explode(",", $value);
						if (is_array($value)) {
							foreach ($value as $k => $v) {
								$this->add_report_to_group($campaign_id, $v);
							}
						} else {
							$this->add_report_to_group($campaign_id, $value);
						}
					}
				}
			}
		}
	
		$return["code"] = 1;
		$return["msg"] = "sukses";
		return $return;
	}



	// Sample response structure:
	/*
	{
		"code": 1,
		"msg": "sukses",
		"data": {
			"main_group": {
				"report_group_id": 123,
				"report_group_name": "Main Campaign",
				"logo_url": "http://example.com/logo.png"
			},
			"campaigns": {
				"Campaign 1": {
					"group_id": 456,
					"reports": [
						{"report_id": 1, "name": "Report 1"},
						{"report_id": 2, "name": "Report 2"}
					]
				},
				"Campaign 2": {
					"group_id": 789,
					"reports": [
						{"report_id": 3, "name": "Report 3"}
					]
				}
			}
		}
	}
	*/
	public function get_campaign_data($report_group_id) {
		global $app;
		$db2 = $app->db2;
		
		// Get main report group data
		$db2->where("report_group_id", $report_group_id);
		$main_group = $db2->getOne("report_group", "report_group_id, report_group_name, logo_url");
		
		// Get child campaigns
		$db2->where("report_group_id_parent", $report_group_id);
		$child_groups = $db2->get("report_group", null, "report_group_id, report_group_name");
		
		$campaigns = array();
		foreach ($child_groups as $group) {
			// Get reports for each child group
			$db2->join("report", "report.report_id = report_group_rel.report_id");
			$db2->where("report_group_rel.report_group_id", $group['report_group_id']);
			$reports = $db2->get("report_group_rel", null, "report.report_id, report.name");
			
			$campaigns[$group['report_group_name']] = array(
				'group_id' => $group['report_group_id'],
				'reports' => $reports
			);
		}
		
		$return = array(
			"code" => 1,
			"msg" => "sukses",
			"data" => array(
				"main_group" => $main_group,
				"campaigns" => $campaigns
			)
		);
		
		return $return;
	}
	
}

