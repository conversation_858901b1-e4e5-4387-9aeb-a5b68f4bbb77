<?php 

function get_list_download($param)
{
    extract($param);
    assign_child_db($project_id);
    global $app;
    $db2 = $app->db2;
    if(isset($filter)){
        if($filter == "purchase"){
            $db2->where("purchase",1);      
        }elseif($filter == "prospek"){
            $db2->where("prospek",1);   
        }elseif($filter == "mql"){
            $db2->where("mql",1);   
        }else{
            $db2->where("lead",1);
        }
    }else{
        $db2->where("lead",1);
    }
    if(isset($source) && $source != ''){
        $db2->where("visitor_source.source", $source);
    }
   ////////////////////////////////////
   $db2->join("visitor_source","visitor_source.visitor_id = visitor.visitor_id","Left");
   $db2->join("visitor_cs","visitor_cs.visitor_id = visitor.visitor_id","Left");
   
   $db2->join("cs", "visitor_cs.cs_key = cs.cs_key","Left");
   $db2->join("cs_divisi", "cs.cs_key = cs_divisi.cs_key","Left");
   $db2->join("divisi", "cs_divisi.divisi_id = divisi.divisi_id","Left"); 

    if(isset($date_start) && isset($date_end)){
        $db2->where("DATE(visitor.waktu_contact) BETWEEN ? AND ?", array($date_start, $date_end)); 
    }else{
        if(isset($date_start)){
            $db2->where("waktu_contact",$date_start,">=");
        }
        if(isset($date_end)){
            if($date_start == $date_end){
                $date = new DateTime($date_end);
                $date->modify('+1 day');
                $date_end = $date->format('Y-m-d');
                $db2->where("visitor.waktu_contact",$date_end,"<");
            }else{
                $db2->where("visitor.waktu_contact",$date_end,"<=");
            }
        }
    }
    $db2->where("CHAR_LENGTH(visitor.phone) <= ?", array(14));
    $db2->orderBy("visitor.waktu_contact","desc");
    $db2->groupBy("visitor.phone,visitor_cs.cs_key");
    $data = $db2->get("visitor",$pagination,"GROUP_CONCAT(COALESCE(visitor_source.source, 'Unknown') SEPARATOR ', ') as source
    ,visitor.visitor_id,IFNULL(visitor.created,'') as visit,IFNULL(visitor.waktu_contact,'') as waktu_contact,visitor.phone as phone,
    GROUP_CONCAT(cs.phone SEPARATOR ', ') as cs_nope,
    GROUP_CONCAT(cs.name SEPARATOR ', ') as cs_name,GROUP_CONCAT(divisi.name SEPARATOR ', ') as divisi_name,
    visitor.mql as mql,visitor.prospek as prospek,visitor.purchase as purchase,visitor.value as value,visitor.page_url as page_url");
    /*
     file_put_contents('log/sql.txt', '[' . date('Y-m-d H:i:s') . "]\n", FILE_APPEND);	
     file_put_contents('log/sql.txt', $db2->getLastQuery(), FILE_APPEND);	
    file_put_contents('log/sql.txt', "\n\n", FILE_APPEND);
    */

    foreach($data as $key=>$value){
        $campaign_name = "";
        $adcopy_name = "";
        $adset_name = "";
        $url = $value["page_url"];
        $parts = parse_url($url);
        if ($parts !== false) {
            parse_str($parts['query'], $query);
            if(isset($query["gcn"])){
                $data[$key]['campaign_name'] = $query["gcn"];
            }else{$data[$key]['campaign_name'] = "";}
            if(isset($query["gadsn"])){
                $data[$key]['adset_name'] = $query["gadsn"];
            }else{$data[$key]['adset_name'] = "";}
            if(isset($query["gadcn"])){
                $data[$key]['adcopy_name'] = $query["gadcn"];
            }else{$data[$key]['adcopy_name'] = "";}
        }
        unset($data[$key]["page_url"]);
        $visitor_id = convBase($value["visitor_id"], "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        $visitor_id = str_split($visitor_id, 4);
        $visitor_id = implode(".", $visitor_id);
        $data[$key]["Id"] = $visitor_id;
        unset($data[$key]["visitor_id"]);
    }
    if($data != NULL){
    $head = ['Source','First Visit','Lead Time','Phone','CS','CS Name', 'Divisi','MQL',"Prospek","Purchase","Purchase Value","Campaign Name","Adset Name","Adcopy Name",'Id'];
    array_unshift($data , $head);
   //   echo '<pre>'; print_r($head); echo '</pre>';
  // echo json_encode($data);
   // echo '<pre>'; print_r($data); echo '</pre>';
         return $data;
    
    }
    exit();
}


function get_list($param)
{

    extract($param);
    assign_child_db($project_id);
    $pagination = NULL;
    if(isset($page))
    {
    	if($page > 1)
        {
            $start = $page * 20;
        }else{
            $start = 0;
        }
        $pagination = [$start,20];
    }
    global $app;
    $db2 = $app->db2;
    if(isset($filter)){
    	if($filter == "purchase")
    	{
    		$db2->where("purchase",1);		
    	}elseif($filter == "prospek"){
    		$db2->where("prospek",1);	
    	}elseif($filter == "mql"){
    		$db2->where("mql",1);	
    	}
    	else{
    		$db2->where("lead",1);
    	}
    }else{$db2->where("lead",1);}

    if(isset($source) && $source != ''){
        $db2->where("visitor_source.source", $source);
    }
    
    if(isset($phone) && $phone != ''){
        // Validasi format nomor telepon
        $phone = preg_replace('/[^0-9]/', '', $phone); // Hapus karakter non-digit
        if(strlen($phone) >= 10 && strlen($phone) <= 15){
            $db2->where("visitor.phone", $phone);
        }else{
            $result["code"] = 0;
            $result["result"]["msg"] = "Format nomor telepon tidak valid. Gunakan 10-15 digit angka.";
            return $result;
        }
    }
    
    if(isset($date_start) && isset($date_end)){
        $db2->where("DATE(visitor.waktu_contact) BETWEEN ? AND ?", array($date_start, $date_end)); 
    }else{
        if(isset($date_start)){
            $db2->where("waktu_contact",$date_start,">=");
        }
        if(isset($date_end)){
            if($date_start == $date_end){
                $date = new DateTime($date_end);
                $date->modify('+1 day');
                $date_end = $date->format('Y-m-d');
                $db2->where("visitor.waktu_contact",$date_end,"<");
            }else{
                $db2->where("visitor.waktu_contact",$date_end,"<=");
            }
        }
    }
    
    $db2->where("CHAR_LENGTH(visitor.phone) <= ?", array(14));
    $db2->orderBy("waktu_contact","DESC");
    $db2->join("visitor_source","visitor_source.visitor_id = visitor.visitor_id","Left");
    //$data = $db2->get("visitor",$pagination,"visitor.created, IFNULL(visitor.created,'') as visit,IFNULL(visitor.waktu_contact,'') as LeadTime, visitor.phone, visitor.mql, visitor.prospek, visitor.purchase, visitor.value, visitor.visitor_id");

    // cs
    $db2->join("visitor_cs","visitor_cs.visitor_id = visitor.visitor_id","Left");
    $db2->join("cs", "visitor_cs.cs_key = cs.cs_key","Left");
    $db2->join("cs_divisi", "cs.cs_key = cs_divisi.cs_key","Left");
    $db2->join("divisi", "cs_divisi.divisi_id = divisi.divisi_id","Left"); 
    $db2->groupBy("visitor.phone,visitor_cs.cs_key");
    $data = $db2->get("visitor",$pagination,"visitor.created, IFNULL(visitor.created,'') as visit,IFNULL(visitor.waktu_contact,'') as LeadTime, visitor.phone, visitor.mql, visitor.prospek, visitor.purchase, visitor.value, visitor.visitor_id, cs.phone as cs_phone");
    foreach($data as $key=>$value) {
        $visitor_id = convBase($value["visitor_id"], "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        $visitor_id = str_split($visitor_id, 4);
        $visitor_id = implode(".", $visitor_id);
        $data[$key]["Id"] = $visitor_id;
        unset($data[$key]["visitor_id"]);
    }
    $result["code"]  = 1;
    $result["result"]["data"]= $data;
    $result["result"]["msg"] = "sukses";

    return $result;
}


function get_history($param){
    extract($param);
    $chatHistory = new ChatHistory($project_id);  
    $data = $chatHistory->getLimit($phone);
    $result["code"]  = 1;
    $result["result"]["data"]= $data;
    $result["result"]["msg"] = "success get history client";
    return $result;
}

function reset_project($param){
    extract($param);
    $chatHistory = new ChatHistory($project_id);  
    $data = $chatHistory->resetProjectFolder();
    $result["code"]  = 1;
    $result["result"]["data"]= $data;
    $result["result"]["project_id"]= $project_id;
    $result["result"]["msg"] = "success reset history client";
    return $result;
}