<?php 
function add_waba($param){
	$rules = array(
      'name' => 'required',
      'phone_id' => 'required',
      'waba_id' => 'required',
      'access_token' => 'required',
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $divisi_key = NULL;
    if(isset($divisi)){
      $divisi_key = $divisi;
    }
    preg_match_all('/\(([^)]*?)\)/', $name, $matches);
    if (!empty($matches[1])) {
        $candidate = '';
        foreach (array_reverse($matches[1]) as $group) {
            if (preg_match('/\d/', $group)) {
                $candidate = $group;
                break;
            }
        }
        if ($candidate !== '') {
            $phone = preg_replace('/\D+/', '', $candidate);
        } else {
            $msg["code"] = 0;
            $msg["result"]["msg"] = "Phone empty";
            return $msg;
        }
    } else {
        $msg["code"] = 0;
        $msg["result"]["msg"] = "Phone empty";
        return $msg;
    }
    $name_cleaned = preg_replace('/\s*\(.*?\)\s*/', '', $name);
    $cs = new cs();
    $msg = $cs->add_cs($project_id, $name_cleaned, $phone, 0, 1, $divisi_key);
    return $msg;
}

function add_waba_halosis($param){
	$rules = array(
      'name' => 'required',
      'phone_id' => 'required',
      'waba_id' => 'required',
      'access_token' => 'required',
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $divisi_key = NULL;
    if(isset($divisi)){
      $divisi_key = $divisi;
    }
    preg_match('/\((.*?)\)/', $name, $matches);
    if (!empty($matches[1])) {
        $name_cleaned = preg_replace('/\s*\(.*?\)\s*/', '', $name);
        $phone = preg_replace('/\D/', '', $matches[1]);
        $cs = new cs();
        $msg = $cs->add_cs($project_id, $name_cleaned, $phone, 0, 1, $divisi_key);
    } else {
        $msg["code"] = 0;
        $msg["result"]["msg"] = "Phone empty";
    }
    return $msg;
}

function update_status($param){
    $rules = array(
        'phone' => 'required',
        'status' => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $res = cs::edit_cs_status($phone,$status);

    return $res;
}

function get_log_download($param){
    extract($param);
    assign_child_db($project_id);
    if(isset($start)){
        $date_range["start"] = $start;
    }else{
        $date_range["start"] = date('Y-m-d', strtotime('today - 30 days'));
    }

    if(isset($end)){
        $date_range["end"] = $end;
    }else{
        $date_range["end"] = date("Y-m-d");
    }
    return cs_log::get_log_download($date_range);
}

function get_log($param){
    extract($param);
    assign_child_db($project_id);

    if(isset($start)){
        $date_range["start"] = $start;
    }else{
        $date_range["start"] = date('Y-m-d', strtotime('today - 30 days'));
    }

    if(isset($end)){
        $date_range["end"] = $end;
    }else{
        $date_range["end"] = date("Y-m-d");
    }

    if(!isset($page)){
        $page = 1;
    }
    $return["code"] = 1;
    $return["result"]["data"] = cs_log::get_log($date_range,$page);
    $return["result"]["msg"] = "sukses";
    return $return;

}

function add_setting($param){
    extract($param);
    $return["tos"] ="SYARAT DAN KETENTUAN PENGGUNAAN LAYANAN GASS WHATSAPP TRACKING 

<b>1. Pendahuluan</b>
Terima kasih telah menggunakan layanan GASS WhatsApp Tracking. Dengan mengakses dan menggunakan layanan kami, Anda setuju untuk terikat oleh syarat dan ketentuan berikut. Jika Anda tidak setuju dengan syarat-syarat ini, mohon untuk tidak menggunakan layanan kami.


<b>2. Deskripsi Layanan</b>
GASS WhatsApp Tracking menyediakan layanan pelacakan aktivitas pengguna di WhatsApp dengan menggunakan teknologi serupa dengan WhatsApp Web.


<b>3. Kebijakan Privasi WhatsApp</b>
Layanan ini akan diatur oleh kebijakan privasi WhatsApp yang berlaku. Kami merekomendasikan Anda untuk membaca dan memahami kebijakan tersebut, yang dapat diakses melalui tautan berikut: \nhttps://faq.whatsapp.com/1325842477576427/?locale=en_US.


<b>4. Integrasi Otomatis</b>
Dengan memilih Integrasi Otomatis, Anda memberikan izin kepada kami untuk membaca dan menganalisis semua aktivitas di akun WhatsApp Anda demi tujuan pelacakan yang telah dijelaskan.


<b>5. Rekomendasi Penggunaan WhatsApp Business</b>
Untuk hasil yang lebih maksimal, kami merekomendasikan penggunaan layanan WhatsApp Business dari penyedia API resmi seperti Ezchat,Qontak, Qisqus, 360Dialog, yang telah terbukti dapat terintegrasi secara optimal dengan layanan GASS WhatsApp Tracking.


<b>6. Ketentuan Pengguna</b>
Pemahaman Risiko: Pengguna memahami dan menerima risiko yang terkait dengan penggunaan layanan ini. 


Terima kasih atas pemahaman dan kerjasama Anda. Jika Anda memiliki pertanyaan atau klarifikasi lebih lanjut, jangan ragu untuk menghubungi kami.";

  $return["tos_enable"] = false;
  
  $return["waba"]["barantum"]["access_token"] = false;
  $return["waba"]["barantum"]["webhook_url"] = "https://".$server_subdomain."/webhook.html?provider=barantum&p=".$project_key."&cs=";  

  $return["waba"]["pancake"]["access_token"] = true;
  $return["waba"]["pancake"]["pages_id"] = true;
  $return["waba"]["pancake"]["webhook_url"] = "https://".$server_subdomain."/webhook.html?provider=pancake&p=".$project_key."&cs=";

  $return["waba"]["qontak"]["access_token"] = true;
  $return["waba"]["qontak"]["integration_id"] = true;
  $return["waba"]["qontak"]["webhook_url"] = false;

  $return["waba"]["qiscus_omnichannel"]["channel"] = true;
  $return["waba"]["qiscus_omnichannel"]["app_id"] = true;
  $return["waba"]["qiscus_omnichannel"]["secret_key"] = true;

  $return["waba"]["qiscus"]["access_token"] = true;
  $return["waba"]["qiscus"]["webhook_url"] = "https://".$server_subdomain."/webhook.html?provider=qiscus&p=".$project_key."&cs=";

  $return["waba"]["otoklix"]["access_token"] = false;
  $return["waba"]["otoklix"]["webhook_url"] = "https://".$server_subdomain."/webhook.html?provider=otoklix&p=".$project_key."&cs=";

  $return["waba"]["respond.io"]["access_token"] = true;
  $return["waba"]["respond.io"]["webhook_url"] = "https://".$server_subdomain."/webhook.html?provider=respondio&p=".$project_key."&cs=";

  $return["waba"]["sleekflow"]["access_token"] = false;
  $return["waba"]["sleekflow"]["webhook_url"] = "https://".$server_subdomain."/webhook.html?provider=sleekflow&p=".$project_key."&cs=";

  $return["waba"]["konekwa"]["access_token"] = false;
  //$return["waba"]["konekwa"]["webhook_url"] = "https://".$server_subdomain."/webhook.html?provider=konekwa&p=".$project_key."&cs=";
  $return["waba"]["konekwa"]["webhook_url"] = false;

  $return["waba"]["fonnte"]["access_token"] = true;
  $return["waba"]["fonnte"]["webhook_url"] = "https://".$server_subdomain."/webhook.html?provider=fonnte&p=".$project_key."&cs=";
  
  $return["waba"]["rasayel"]["access_token"] = false;
  $return["waba"]["rasayel"]["webhook_url"] = "https://".$server_subdomain."/webhook.html?provider=rasayel&p=".$project_key."&cs=";
  
  $return["waba"]["halosis"]["access_token"] = false;
  $return["waba"]["halosis"]["webhook_url"] = "https://".$server_subdomain."/webhook.html?provider=halosis&p=".$project_key."&cs=";

  $return["waba"]["smartchat"]["access_token"] = false;
  $return["waba"]["smartchat"]["webhook_url"] = "https://".$server_subdomain."/webhook.html?provider=smartchat&p=".$project_key."&cs=";

  $return["waba"]["kommo"]["access_token"] = false;
  $return["waba"]["kommo"]["webhook_url"] = "https://".$server_subdomain."/webhook.html?provider=kommo&p=".$project_key."&cs=";

  //$return["waba"]["chatdaddy"]["access_token"] = false;
  //$return["waba"]["chatdaddy"]["webhook_url"] = "https://".$server_subdomain."/webhook.html?provider=chatdaddy&p=".$project_key."&cs=";
  return $return;
}

function add($param){

    
	$rules = array(
      'name' => 'required',
      'phone' => 'required',
      'autodc' => 'required',
      'without_scan' => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $divisi_key = NULL;
    if(isset($divisi)){
      $divisi_key = $divisi;
    }
    $cs = new cs();

    if(isset($wa_type))
    {
        if($wa_type == "waba")
        {
            if(isset($provider))
            {
                if($provider == "qontak")
                {
                    
                    if(isset($access_token)){
                        $curl = curl_init();
                        $url_webhook = "https://".$server_subdomain."/webhook.html?provider=qontak&cs=".$phone."&p=".$project_key;
                        curl_setopt_array($curl, [
                        CURLOPT_URL => "https://service-chat.qontak.com/api/open/v1/message_interactions",
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => "",
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 30,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => "PUT",
                        CURLOPT_POSTFIELDS => "-----011000010111000001101001\r\nContent-Disposition: form-data; name=\"broadcast_log_status\"\r\n\r\n0\r\n-----011000010111000001101001\r\nContent-Disposition: form-data; name=\"receive_message_from_agent\"\r\n\r\n1\r\n-----011000010111000001101001\r\nContent-Disposition: form-data; name=\"receive_message_from_customer\"\r\n\r\n1\r\n-----011000010111000001101001\r\nContent-Disposition: form-data; name=\"status_message\"\r\n\r\n1\r\n-----011000010111000001101001\r\nContent-Disposition: form-data; name=\"url\"\r\n\r\n".$url_webhook."\r\n-----011000010111000001101001--\r\n",
                        CURLOPT_HTTPHEADER => [
                            "Authorization: Bearer ".$access_token,
                            "Content-Type: multipart/form-data; boundary=---011000010111000001101001"
                        ],
                        ]);

                        $response = curl_exec($curl);
                        $err = curl_error($curl);

                        curl_close($curl);

                        if ($err) {
                            $msg["code"] = 0;
                            $ret["msg"] = $err;
                            return $msg;
                        } else {
                            $response = json_decode($response,true);
                          
                            if($response["status"] != "error")
                            {   
                                if (!empty($integration_id)) {
                                    $p = new qontak();
                                    $p->add_cs($integration_id, $phone);
                                }
                                
                                
                                $msg = $cs->add_cs($project_id,$name, $phone, 0, 1, $divisi_key);
                                return $msg;
                            }else{
                                $msg["code"] = 0;
                                $msg["msg"] = "Qontak ".$response["error"]["messages"][0];
                                return $msg;
                            }
                            
                        }
                       
                    }
                    
                }elseif($provider == "pancake"){
                    if(!isset($pages_id))
                    {
                        $msg["code"] = 0;
                        $msg["msg"] = "Page ID empty";
                        return $msg;
                    }

                    $p = new pancake();
                    $p->add_cs($pages_id,$phone,$access_token);

                    $msg = $cs->add_cs($project_id,$name, $phone, 0, 1, $divisi_key);
                    return $msg;
                }elseif($provider == "qiscus_omnichannel") {
                    if(empty($channel)){
                        $msg["code"] = 0;
                        $msg["msg"] = "Channel Empty";
                        return $msg;
                    }
                    if(empty($app_id))
                    {
                        $msg["code"] = 0;
                        $msg["msg"] = "App ID Empty";
                        return $msg;
                    }
                    if(empty($secret_key))
                    {
                        $msg["code"] = 0;
                        $msg["msg"] = "Secret Key Empty";
                        return $msg;
                    }
                    $url = "https://omnichannel.qiscus.com/whatsapp/".$app_id."/".$channel."/settings";
                    $webhookUrl = "https://".$server_subdomain."/webhook.html?provider=qiscus-omnichannel&cs=".$phone."&p=".$project_key;
                    $data = array();
                    $data = [
                        "webhooks" => [
                            "url" => $webhookUrl
                        ]
                    ];

                    $ch = curl_init($url);

                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        "Qiscus-App-Id: $app_id",
                        "Qiscus-Secret-Key: $secret_key",
                        "Content-Type: application/json"
                    ]);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

                    $response = curl_exec($ch);

                    curl_close($ch);

                    if (curl_errno($ch)) {
                        $error =  'Error:' . curl_error($ch);
                        $msg["code"] = 0;
                        $msg["msg"] = "Qiscus ".$error;
                        return $msg;
                    } else {
                        $response = json_decode($response,true);
                          
                        if($response["status"] == 200)
                        {   
                            $msg = $cs->add_cs($project_id,$name, $phone, 0, 1, $divisi_key);
                            return $msg;
                        }else{
                            $msg["code"] = 0;
                            $msg["msg"] = "Qiscus  ".$response["errors"]['message'];
                            $msg["response"] = $response;
                            return $msg;
                        }
                    }

                    
                }
                else{
                    $msg = $cs->add_cs($project_id,$name, $phone, 0, 1, $divisi_key);
                    return $msg;
                }
            }
        }
        else{
            $msg = $cs->add_cs($project_id,$name, $phone, $autodc, $without_scan, $divisi_key);
            return $msg;
        }
    }
    else{
        $msg = $cs->add_cs($project_id,$name, $phone, $autodc, $without_scan, $divisi_key);
        return $msg;
    }

   
}

function status($param){
    $rules = array(
      'phone' => 'required',
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $cs = new cs();
    $msg = $cs->status($phone);
    return $msg;
}

function delete($param){
    $rules = array(
      'phone' => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $cs = new cs();
    $msg = $cs->delete_cs($phone);
    return $msg;
}


function edit($param){
    $rules = array(
      'phone' => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    if(isset($name)){$update["name"] = $name;}
    if(isset($autodc)){$update["autodc"] = $autodc;}
    if(isset($without_scan)){$update["without_scan"] = $without_scan;}
    if(isset($status)){$update["status"] = $status;}
    if(count($update) > 0){
        $cs = new cs();
        if(!isset($divisi)){
            $divisi = NULL;
        }
        if(isset($provider)) {
            if($provider == "qontak") {
                
            }elseif($provider == "pancake"){
                $p = new pancake();
                $p->edit_cs($pages_id, $phone, $access_token);
            }elseif($provider == "qiscus_omnichannel") {
                
            }
        }
        $msg = $cs->edit_cs($phone,$update,$divisi);
    }else{
        $return["code"] = 0;
        $return["result"]["msg"] = "error, failed to update no data";
    }
    return $msg;
}

function get($param){
    extract($param);
    assign_child_db($project_id);
    if(isset($param["divisi_key"])){
        $divisi_key = $param["divisi_key"];
    }else{
        $divisi_key = NULL;
    }
    $cs = new cs();
    $msg = $cs->get_cs(NULL, $divisi_key);
    return $msg;
}

