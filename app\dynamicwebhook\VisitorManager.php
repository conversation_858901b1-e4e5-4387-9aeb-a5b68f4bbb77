<?php

namespace DynamicWebhook;

use DynamicWebhook\Interfaces\VisitorManagerInterface;

/**
 * Visitor manager for handling visitor creation and tracking
 */
class VisitorManager implements VisitorManagerInterface
{
    private $trackService;
    private $db2Service;
    private $platformDetector;

    public function __construct($trackService, $db2Service = null, $platformDetector = null)
    {
        $this->trackService = $trackService;
        $this->db2Service = $db2Service;
        $this->platformDetector = $platformDetector;
    }

    /**
     * Process visitor creation and tracking
     */
    public function processVisitor(array $data, array $context): ?array
    {
        $visitorId = $context['visitor_id'] ?? null;
        $phone = $data['phone'] ?? null;
        $isNewContact = $context['is_new_contact'] ?? false;
        $csPhone = $context['cs_phone'] ?? null;
        $platform = $context['platform'] ?? 'default';

        if (!$phone || !$this->trackService) {
            return null;
        }

        // Try to get existing visitor
        $visitor = $this->getVisitor($visitorId, $phone);
        
        if ($visitor === false || $visitor === null) {
            // Create new visitor if phone is available
            if ($isNewContact) {
                $context['trigger_contact'] = true;
            }
            
            $newVisitorId = $this->createVisitor($phone, $isNewContact);
            if ($newVisitorId) {
                $tmpVisitorId = $newVisitorId;
                
                // Convert visitor ID to formatted string
                $formattedVisitorId = $this->formatVisitorId($newVisitorId);
                $visitor = $this->getVisitor($formattedVisitorId, $phone);
                
                // Insert CS tracking
                $this->insertCsTracking($tmpVisitorId, $csPhone, $data['message_type'] ?? 'message_in');
                
                // Check for CTWA based on platform
                $ctwaResult = $this->checkCTWA($phone, $data, $platform, $isNewContact);
                if ($ctwaResult) {
                    $this->insertVisitorSource($formattedVisitorId, "meta");
                    $visitor = $this->getVisitor($formattedVisitorId, $phone);
                }
            }
        }

        return $visitor;
    }

    /**
     * Create new visitor
     */
    public function createVisitor(string $phone, bool $isNewContact): ?array
    {
        if (!$this->trackService) {
            return null;
        }

        return $this->trackService->create_visitor($phone, $isNewContact);
    }

    /**
     * Get existing visitor
     */
    public function getVisitor(?string $visitorId, string $phone): ?array
    {
        if (!$this->trackService) {
            return null;
        }

        $visitor = $this->trackService->get_visitor($visitorId, $phone);
        return $visitor === false ? null : $visitor;
    }

    /**
     * Update visitor with CTWA data
     */
    public function updateVisitorWithCTWA(array $visitor, array $ctwaData): bool
    {
        if (!$this->db2Service || empty($ctwaData['ctwa_clid'])) {
            return false;
        }

        $visitorCtwaData = [
            "last_campaign" => [
                "source" => "meta",
                "data" => [
                    "adcopy_id" => $ctwaData["adcopy_id"],
                    "ctwa_clid" => $ctwaData['ctwa_clid'],
                    "source_id" => $ctwaData['source_id'],
                    "campaign_data" => $ctwaData['campaign_data'],
                    "extraction_method" => "dynamic_webhook",
                    "extraction_timestamp" => date('Y-m-d H:i:s')
                ]
            ]
        ];

        $ctwaClid = $ctwaData['ctwa_clid'];
        $key = hash("sha256", "ctwa;".$ctwaClid.";lead;", true);
        
        $this->db2Service->where("hash", $key);
        $this->db2Service->get("log_connector_hash");
        
        if ($this->db2Service->count == 0) {
            $this->db2Service->insert("log_connector_hash", ["hash" => $key, "value" => "ctwa;".$ctwaClid.";lead;"]);
            
            $this->db2Service->where("visitor_id", $visitor["visitor_id"]);
            $this->db2Service->update("visitor", ["data" => serialize($visitorCtwaData)]);
            
            return true;
        }

        return false;
    }

    /**
     * Process new contact creation
     */
    public function processNewContact(string $phone): bool
    {
        if (!$this->db2Service) {
            return false;
        }

        $newContact = [
            "phone" => $phone,
            "phone_hash" => $this->db2Service->func("UNHEX(?)", [md5($phone)]),
            "created" => date("Y-m-d H:i:s")
        ];
        
        return $this->db2Service->setQueryOption('IGNORE')->insert("new_kontak", $newContact);
    }

    /**
     * Format visitor ID to string representation
     */
    private function formatVisitorId(int $visitorId): string
    {
        if (!function_exists('convBase')) {
            return (string)$visitorId;
        }

        $formatted = convBase($visitorId, "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        $formatted = str_split($formatted, 4);
        return implode(".", $formatted);
    }

    /**
     * Insert CS tracking
     */
    private function insertCsTracking(int $visitorId, ?string $csPhone, string $messageType): void
    {
        if (!$this->trackService || !$csPhone) {
            return;
        }

        $this->trackService->insert_cs($visitorId, $csPhone, $messageType);
    }

    /**
     * Check CTWA based on platform
     */
    private function checkCTWA(string $phone, array $data, string $platform, bool $isNewContact): bool
    {
        if (!$this->trackService) {
            return false;
        }

        switch ($platform) {
            case 'waba':
                return $this->trackService->fbwa_waba($phone, ["raw" => json_encode($data)], $isNewContact, false);
            case 'konekwa':
                return $this->trackService->fbwa_personal($phone, $data);
            case 'qontak':
                return $this->trackService->fbwa_qontak($phone, $data);
            default:
                return $this->trackService->fbwa_personal($phone, $data);
        }
    }

    /**
     * Insert visitor source
     */
    private function insertVisitorSource(string $visitorId, string $source): void
    {
        if (!$this->db2Service) {
            return;
        }

        $this->db2Service->setQueryOption(array('IGNORE'))->insert("visitor_source", [
            "visitor_id" => $visitorId, 
            "source" => $source
        ]);
    }
}
