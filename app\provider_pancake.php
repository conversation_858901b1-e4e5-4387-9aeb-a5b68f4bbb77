<?php

class pancake {

	public function add_cs($page, $nope, $token) {
		global $app;
		$db2                     = $app->db2;
		$page_key                = sha1($page);
		$nope                    = preg_replace("/[^0-9]/", "", $nope);
		$data["pancake_cs_id"]   = $page;
		$data["pancake_cs_key"]  = $db2->func("UNHEX(?)", [$page_key]);
		$data["pancake_cs_nope"] = $nope;
		$data["token"]           = $token;
		$db2->setQueryOption(Array('LOW_PRIORITY', 'IGNORE'))->insert("pancake_waba", $data);
	}
	public function edit_cs($page, $nope, $token) {
		global $app;
		$db2                     = $app->db2;
		$data["token"]           = $token;
		$db2->where("pancake_cs_id = ?",[$page]);
		$db2->update("pancake_waba", $data);
	}
	public function get_history($token, $page_id) {
		global $app;
		$db2    = $app->db2;
		$recent = $this->get_recent($token, $page_id);

		foreach ($recent as $conv) {

			if (substr($conv["from"]["id"], 0, 3) === "wa_") {
				$phone = substr($conv["from"]["id"], 3);
			} else {
				$phone = null;
			}

			$customer_id = $conv["customers"][0]["id"];
			$acc_id      = $conv["id"];
			$last_update = $conv["updated_at"];

			if (extension_loaded('memcached')) {
				$project_id = $app->project_id;
				$memcached  = new Memcached();
				$memcached->addServer('127.0.0.1', 11211);
				// 'Check Connected to Memcached server.';
				if ($memcached->getVersion()) {
					$mem_key         = $project_id . '_pancake_conv_out_' . $customer_id;
					$mem_value       = $memcached->get($mem_key);
					$memcached_exist = true;
				}
			}

			if (false == $mem_value) {
				$get_detail = true;
			} else {
				if ($mem_value != $last_update) {
					$get_detail = true;
				} else {
					$get_detail = false;
				}
			}

			if ($get_detail) {
				if ($memcached_exist) {
					$expiration = 3600 * 24 * 30;
					$memcached->set($mem_key, $last_update, $expiration);
				}

				$last_grab = $mem_value;

				$detail = $this->get_detail($token, $page_id, $acc_id, $customer_id);

				foreach ($detail as $d) {

					$data_msg = null;
					if ($d["from"]["id"] == $page_id) {
						$last_update_date_time = new DateTime($last_update);
						$last_grab_date_time   = new DateTime($last_grab);

						if ($last_update_date_time > $last_grab_date_time) {

							$msg_type = "message_out";

							if (isset($d["message"])) {
								$data_msg["msg"] = $d["message"];
							} else {
								$cleanedText     = preg_replace("/\s+|\r?\n/", " ", serialize($d["attachments"]));
								$data_msg["msg"] = $cleanedText;
							}

							if ('<div></div>' != $data_msg["msg"]) {
								$key                     = $customer_id . ";" . $d["inserted_at"] . ";" . $d["id"];
								$key_hash                = sha1($key);
								$data_msg["pancake_key"] = $db2->func("UNHEX(?)", [$key_hash]);
								$data_msg["phone"]       = $phone;
								$data_msg["cs"]          = $this->get_phone($page_id);
								
								if(isset($d["id"])){
									$data_msg["msg_id"]       = $d["id"];
								}

								$db2->setQueryOption(Array('LOW_PRIORITY', 'IGNORE'))->insert("pancake_out", $data_msg);
							}

						}
					}

				}

			}

		}
	}

	public function get_phone($page) {
		global $app;
		$project_id = $app->project_id;

		$mem_key         = $project_id . '_pancake_get_phone_' . $page;
		$memcached_exist = false;
		$memcached       = new Memcached();
		$memcached->addServer('127.0.0.1', 11211);
		// 'Check Connected to Memcached server.';
		if ($memcached->getVersion()) {

			$mem_value = $memcached->get($mem_key);
			if ($memcached->getResultCode() == Memcached::RES_SUCCESS) {
				if (isset($mem_value)) {
					return $mem_value;
				}
			}
			$memcached_exist = true;
		}

		global $app;
		$db2 = $app->db2;

		$page_key = sha1($page);

		$db2->where("pancake_cs_key = UNHEX(?)", [$page_key]);
		$cs = $db2->getone("pancake_waba");

		if (null != $cs) {
			$expiration = 3600 * 24 * 30;
			$memcached->set($mem_key, $cs["pancake_cs_nope"], $expiration);
			return $cs["pancake_cs_nope"];
		} else {
			return false;
		}

	}
	public function get_recent($token, $page_id) {
		$unixTimeNow = time();
		$unixTimeYesterday = $unixTimeNow - 86400;

		$url    = "https://pages.fm/api/public_api/v1/pages/{$page_id}/conversations?page_id={$page_id}&page_access_token={$token}&page_number=1&order_by=updated_at&until={$unixTimeNow}&since={$unixTimeYesterday}";

		//$url = "https://pages.fm/api/public_api/v1/pages/waba_238581549345576/conversations?page_id=waba_238581549345576&page_access_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aW1lc3RhbXAiOjE3MzQ2ODY1MjcsImlkIjoid2FiYV8yMzg1ODE1NDkzNDU1NzYifQ.JV7fAS5qIZT5Ky5y44BGCef62fiM0GZiZp10CrX5hpM&until=1734683739&since=1734597339&page_number=1&order_by=updated_at";
		$recent = file_get_contents($url);

		$data = json_decode($recent, true);
		if (isset($data["conversations"])) {
			return $data["conversations"];
		} else {
			return false;
		}

	}

	public function get_detail($token, $page_id, $acc_id, $customer_id) {

		$url = "https://pancake.vn/api/public_api/v1/pages/{$page_id}/conversations/{$acc_id}/messages?page_access_token={$token}&customer_id={$customer_id}";
		$ret = json_decode(file_get_contents($url), true);
		if (isset($ret["messages"])) {
			return $ret["messages"];
		} else {
			return null;
		}
	}
}
