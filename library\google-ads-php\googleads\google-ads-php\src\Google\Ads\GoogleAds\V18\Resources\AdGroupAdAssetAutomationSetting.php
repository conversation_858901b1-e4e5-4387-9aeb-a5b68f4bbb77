<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/resources/ad_group_ad.proto

namespace Google\Ads\GoogleAds\V18\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Asset automation setting for an AdGroupAd.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.resources.AdGroupAdAssetAutomationSetting</code>
 */
class AdGroupAdAssetAutomationSetting extends \Google\Protobuf\Internal\Message
{
    /**
     * The asset automation type that this setting configures.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AssetAutomationTypeEnum.AssetAutomationType asset_automation_type = 1;</code>
     */
    protected $asset_automation_type = null;
    /**
     * The opt-in/out status for the specified asset automation type.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AssetAutomationStatusEnum.AssetAutomationStatus asset_automation_status = 2;</code>
     */
    protected $asset_automation_status = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $asset_automation_type
     *           The asset automation type that this setting configures.
     *     @type int $asset_automation_status
     *           The opt-in/out status for the specified asset automation type.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Resources\AdGroupAd::initOnce();
        parent::__construct($data);
    }

    /**
     * The asset automation type that this setting configures.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AssetAutomationTypeEnum.AssetAutomationType asset_automation_type = 1;</code>
     * @return int
     */
    public function getAssetAutomationType()
    {
        return isset($this->asset_automation_type) ? $this->asset_automation_type : 0;
    }

    public function hasAssetAutomationType()
    {
        return isset($this->asset_automation_type);
    }

    public function clearAssetAutomationType()
    {
        unset($this->asset_automation_type);
    }

    /**
     * The asset automation type that this setting configures.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AssetAutomationTypeEnum.AssetAutomationType asset_automation_type = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setAssetAutomationType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\AssetAutomationTypeEnum\AssetAutomationType::class);
        $this->asset_automation_type = $var;

        return $this;
    }

    /**
     * The opt-in/out status for the specified asset automation type.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AssetAutomationStatusEnum.AssetAutomationStatus asset_automation_status = 2;</code>
     * @return int
     */
    public function getAssetAutomationStatus()
    {
        return isset($this->asset_automation_status) ? $this->asset_automation_status : 0;
    }

    public function hasAssetAutomationStatus()
    {
        return isset($this->asset_automation_status);
    }

    public function clearAssetAutomationStatus()
    {
        unset($this->asset_automation_status);
    }

    /**
     * The opt-in/out status for the specified asset automation type.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AssetAutomationStatusEnum.AssetAutomationStatus asset_automation_status = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setAssetAutomationStatus($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\AssetAutomationStatusEnum\AssetAutomationStatus::class);
        $this->asset_automation_status = $var;

        return $this;
    }

}

