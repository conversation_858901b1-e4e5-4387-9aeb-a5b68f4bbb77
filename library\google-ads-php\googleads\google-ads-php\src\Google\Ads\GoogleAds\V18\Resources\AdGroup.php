<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/resources/ad_group.proto

namespace Google\Ads\GoogleAds\V18\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * An ad group.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.resources.AdGroup</code>
 */
class AdGroup extends \Google\Protobuf\Internal\Message
{
    /**
     * Immutable. The resource name of the ad group.
     * Ad group resource names have the form:
     * `customers/{customer_id}/adGroups/{ad_group_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     */
    protected $resource_name = '';
    /**
     * Output only. The ID of the ad group.
     *
     * Generated from protobuf field <code>optional int64 id = 34 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $id = null;
    /**
     * The name of the ad group.
     * This field is required and should not be empty when creating new ad
     * groups.
     * It must contain fewer than 255 UTF-8 full-width characters.
     * It must not contain any null (code point 0x0), NL line feed
     * (code point 0xA) or carriage return (code point 0xD) characters.
     *
     * Generated from protobuf field <code>optional string name = 35;</code>
     */
    protected $name = null;
    /**
     * The status of the ad group.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupStatusEnum.AdGroupStatus status = 5;</code>
     */
    protected $status = 0;
    /**
     * Immutable. The type of the ad group.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupTypeEnum.AdGroupType type = 12 [(.google.api.field_behavior) = IMMUTABLE];</code>
     */
    protected $type = 0;
    /**
     * The ad rotation mode of the ad group.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupAdRotationModeEnum.AdGroupAdRotationMode ad_rotation_mode = 22;</code>
     */
    protected $ad_rotation_mode = 0;
    /**
     * Output only. For draft or experiment ad groups, this field is the resource
     * name of the base ad group from which this ad group was created. If a draft
     * or experiment ad group does not have a base ad group, then this field is
     * null.
     * For base ad groups, this field equals the ad group resource name.
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional string base_ad_group = 36 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     */
    protected $base_ad_group = null;
    /**
     * The URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 37;</code>
     */
    protected $tracking_url_template = null;
    /**
     * The list of mappings used to substitute custom parameter tags in a
     * `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.CustomParameter url_custom_parameters = 6;</code>
     */
    private $url_custom_parameters;
    /**
     * Immutable. The campaign to which the ad group belongs.
     *
     * Generated from protobuf field <code>optional string campaign = 38 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     */
    protected $campaign = null;
    /**
     * The maximum CPC (cost-per-click) bid.
     *
     * Generated from protobuf field <code>optional int64 cpc_bid_micros = 39;</code>
     */
    protected $cpc_bid_micros = null;
    /**
     * Output only. Value will be same as that of the CPC (cost-per-click) bid
     * value when the bidding strategy is one of manual cpc, enhanced cpc, page
     * one promoted or target outrank share, otherwise the value will be null.
     *
     * Generated from protobuf field <code>optional int64 effective_cpc_bid_micros = 57 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $effective_cpc_bid_micros = null;
    /**
     * The maximum CPM (cost-per-thousand viewable impressions) bid.
     *
     * Generated from protobuf field <code>optional int64 cpm_bid_micros = 40;</code>
     */
    protected $cpm_bid_micros = null;
    /**
     * The target CPA (cost-per-acquisition). If the ad group's campaign
     * bidding strategy is TargetCpa or MaximizeConversions (with its target_cpa
     * field set), then this field overrides the target CPA specified in the
     * campaign's bidding strategy.
     * Otherwise, this value is ignored.
     *
     * Generated from protobuf field <code>optional int64 target_cpa_micros = 41;</code>
     */
    protected $target_cpa_micros = null;
    /**
     * The CPV (cost-per-view) bid.
     *
     * Generated from protobuf field <code>optional int64 cpv_bid_micros = 42;</code>
     */
    protected $cpv_bid_micros = null;
    /**
     * Average amount in micros that the advertiser is willing to pay for every
     * thousand times the ad is shown.
     *
     * Generated from protobuf field <code>optional int64 target_cpm_micros = 43;</code>
     */
    protected $target_cpm_micros = null;
    /**
     * The target ROAS (return-on-ad-spend) override. If the ad group's campaign
     * bidding strategy is TargetRoas or MaximizeConversionValue (with its
     * target_roas field set), then this field overrides the target ROAS specified
     * in the campaign's bidding strategy.
     * Otherwise, this value is ignored.
     *
     * Generated from protobuf field <code>optional double target_roas = 44;</code>
     */
    protected $target_roas = null;
    /**
     * The percent cpc bid amount, expressed as a fraction of the advertised price
     * for some good or service. The valid range for the fraction is [0,1) and the
     * value stored here is 1,000,000 * [fraction].
     *
     * Generated from protobuf field <code>optional int64 percent_cpc_bid_micros = 45;</code>
     */
    protected $percent_cpc_bid_micros = null;
    /**
     * The fixed amount in micros that the advertiser pays for every thousand
     * impressions of the ad.
     *
     * Generated from protobuf field <code>optional int64 fixed_cpm_micros = 64;</code>
     */
    protected $fixed_cpm_micros = null;
    /**
     * Average amount in micros that the advertiser is willing to pay for every ad
     * view.
     *
     * Generated from protobuf field <code>optional int64 target_cpv_micros = 65;</code>
     */
    protected $target_cpv_micros = null;
    /**
     * True if optimized targeting is enabled. Optimized Targeting is the
     * replacement for Audience Expansion.
     *
     * Generated from protobuf field <code>bool optimized_targeting_enabled = 59;</code>
     */
    protected $optimized_targeting_enabled = false;
    /**
     * When this value is true, demographics will be excluded from the types of
     * targeting which are expanded when optimized_targeting_enabled is true.
     * When optimized_targeting_enabled is false, this field is ignored. Default
     * is false.
     *
     * Generated from protobuf field <code>bool exclude_demographic_expansion = 67;</code>
     */
    protected $exclude_demographic_expansion = false;
    /**
     * Allows advertisers to specify a targeting dimension on which to place
     * absolute bids. This is only applicable for campaigns that target only the
     * display network and not search.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.TargetingDimensionEnum.TargetingDimension display_custom_bid_dimension = 23;</code>
     */
    protected $display_custom_bid_dimension = 0;
    /**
     * URL template for appending params to Final URL.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 46;</code>
     */
    protected $final_url_suffix = null;
    /**
     * Setting for targeting related features.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.TargetingSetting targeting_setting = 25;</code>
     */
    protected $targeting_setting = null;
    /**
     * Immutable. Setting for audience related features.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.resources.AdGroup.AudienceSetting audience_setting = 56 [(.google.api.field_behavior) = IMMUTABLE];</code>
     */
    protected $audience_setting = null;
    /**
     * Output only. The effective target CPA (cost-per-acquisition).
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional int64 effective_target_cpa_micros = 47 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $effective_target_cpa_micros = null;
    /**
     * Output only. Source of the effective target CPA.
     * This field is read-only.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_target_cpa_source = 29 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $effective_target_cpa_source = 0;
    /**
     * Output only. The effective target ROAS (return-on-ad-spend).
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional double effective_target_roas = 48 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $effective_target_roas = null;
    /**
     * Output only. Source of the effective target ROAS.
     * This field is read-only.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_target_roas_source = 32 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $effective_target_roas_source = 0;
    /**
     * Output only. The resource names of labels attached to this ad group.
     *
     * Generated from protobuf field <code>repeated string labels = 49 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     */
    private $labels;
    /**
     * The asset field types that should be excluded from this ad group. Asset
     * links with these field types will not be inherited by this ad group from
     * the upper levels.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.enums.AssetFieldTypeEnum.AssetFieldType excluded_parent_asset_field_types = 54;</code>
     */
    private $excluded_parent_asset_field_types;
    /**
     * The asset set types that should be excluded from this ad group. Asset set
     * links with these types will not be inherited by this ad group from the
     * upper levels.
     * Location group types (GMB_DYNAMIC_LOCATION_GROUP,
     * CHAIN_DYNAMIC_LOCATION_GROUP, and STATIC_LOCATION_GROUP) are child types of
     * LOCATION_SYNC. Therefore, if LOCATION_SYNC is set for this field, all
     * location group asset sets are not allowed to be linked to this ad group,
     * and all Location Extension (LE) and Affiliate Location Extensions (ALE)
     * will not be served under this ad group.
     * Only LOCATION_SYNC is currently supported.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.enums.AssetSetTypeEnum.AssetSetType excluded_parent_asset_set_types = 58;</code>
     */
    private $excluded_parent_asset_set_types;
    /**
     * Output only. Provides aggregated view into why an ad group is not serving
     * or not serving optimally.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupPrimaryStatusEnum.AdGroupPrimaryStatus primary_status = 62 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $primary_status = 0;
    /**
     * Output only. Provides reasons for why an ad group is not serving or not
     * serving optimally.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.enums.AdGroupPrimaryStatusReasonEnum.AdGroupPrimaryStatusReason primary_status_reasons = 63 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    private $primary_status_reasons;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $resource_name
     *           Immutable. The resource name of the ad group.
     *           Ad group resource names have the form:
     *           `customers/{customer_id}/adGroups/{ad_group_id}`
     *     @type int|string $id
     *           Output only. The ID of the ad group.
     *     @type string $name
     *           The name of the ad group.
     *           This field is required and should not be empty when creating new ad
     *           groups.
     *           It must contain fewer than 255 UTF-8 full-width characters.
     *           It must not contain any null (code point 0x0), NL line feed
     *           (code point 0xA) or carriage return (code point 0xD) characters.
     *     @type int $status
     *           The status of the ad group.
     *     @type int $type
     *           Immutable. The type of the ad group.
     *     @type int $ad_rotation_mode
     *           The ad rotation mode of the ad group.
     *     @type string $base_ad_group
     *           Output only. For draft or experiment ad groups, this field is the resource
     *           name of the base ad group from which this ad group was created. If a draft
     *           or experiment ad group does not have a base ad group, then this field is
     *           null.
     *           For base ad groups, this field equals the ad group resource name.
     *           This field is read-only.
     *     @type string $tracking_url_template
     *           The URL template for constructing a tracking URL.
     *     @type array<\Google\Ads\GoogleAds\V18\Common\CustomParameter>|\Google\Protobuf\Internal\RepeatedField $url_custom_parameters
     *           The list of mappings used to substitute custom parameter tags in a
     *           `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     *     @type string $campaign
     *           Immutable. The campaign to which the ad group belongs.
     *     @type int|string $cpc_bid_micros
     *           The maximum CPC (cost-per-click) bid.
     *     @type int|string $effective_cpc_bid_micros
     *           Output only. Value will be same as that of the CPC (cost-per-click) bid
     *           value when the bidding strategy is one of manual cpc, enhanced cpc, page
     *           one promoted or target outrank share, otherwise the value will be null.
     *     @type int|string $cpm_bid_micros
     *           The maximum CPM (cost-per-thousand viewable impressions) bid.
     *     @type int|string $target_cpa_micros
     *           The target CPA (cost-per-acquisition). If the ad group's campaign
     *           bidding strategy is TargetCpa or MaximizeConversions (with its target_cpa
     *           field set), then this field overrides the target CPA specified in the
     *           campaign's bidding strategy.
     *           Otherwise, this value is ignored.
     *     @type int|string $cpv_bid_micros
     *           The CPV (cost-per-view) bid.
     *     @type int|string $target_cpm_micros
     *           Average amount in micros that the advertiser is willing to pay for every
     *           thousand times the ad is shown.
     *     @type float $target_roas
     *           The target ROAS (return-on-ad-spend) override. If the ad group's campaign
     *           bidding strategy is TargetRoas or MaximizeConversionValue (with its
     *           target_roas field set), then this field overrides the target ROAS specified
     *           in the campaign's bidding strategy.
     *           Otherwise, this value is ignored.
     *     @type int|string $percent_cpc_bid_micros
     *           The percent cpc bid amount, expressed as a fraction of the advertised price
     *           for some good or service. The valid range for the fraction is [0,1) and the
     *           value stored here is 1,000,000 * [fraction].
     *     @type int|string $fixed_cpm_micros
     *           The fixed amount in micros that the advertiser pays for every thousand
     *           impressions of the ad.
     *     @type int|string $target_cpv_micros
     *           Average amount in micros that the advertiser is willing to pay for every ad
     *           view.
     *     @type bool $optimized_targeting_enabled
     *           True if optimized targeting is enabled. Optimized Targeting is the
     *           replacement for Audience Expansion.
     *     @type bool $exclude_demographic_expansion
     *           When this value is true, demographics will be excluded from the types of
     *           targeting which are expanded when optimized_targeting_enabled is true.
     *           When optimized_targeting_enabled is false, this field is ignored. Default
     *           is false.
     *     @type int $display_custom_bid_dimension
     *           Allows advertisers to specify a targeting dimension on which to place
     *           absolute bids. This is only applicable for campaigns that target only the
     *           display network and not search.
     *     @type string $final_url_suffix
     *           URL template for appending params to Final URL.
     *     @type \Google\Ads\GoogleAds\V18\Common\TargetingSetting $targeting_setting
     *           Setting for targeting related features.
     *     @type \Google\Ads\GoogleAds\V18\Resources\AdGroup\AudienceSetting $audience_setting
     *           Immutable. Setting for audience related features.
     *     @type int|string $effective_target_cpa_micros
     *           Output only. The effective target CPA (cost-per-acquisition).
     *           This field is read-only.
     *     @type int $effective_target_cpa_source
     *           Output only. Source of the effective target CPA.
     *           This field is read-only.
     *     @type float $effective_target_roas
     *           Output only. The effective target ROAS (return-on-ad-spend).
     *           This field is read-only.
     *     @type int $effective_target_roas_source
     *           Output only. Source of the effective target ROAS.
     *           This field is read-only.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $labels
     *           Output only. The resource names of labels attached to this ad group.
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $excluded_parent_asset_field_types
     *           The asset field types that should be excluded from this ad group. Asset
     *           links with these field types will not be inherited by this ad group from
     *           the upper levels.
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $excluded_parent_asset_set_types
     *           The asset set types that should be excluded from this ad group. Asset set
     *           links with these types will not be inherited by this ad group from the
     *           upper levels.
     *           Location group types (GMB_DYNAMIC_LOCATION_GROUP,
     *           CHAIN_DYNAMIC_LOCATION_GROUP, and STATIC_LOCATION_GROUP) are child types of
     *           LOCATION_SYNC. Therefore, if LOCATION_SYNC is set for this field, all
     *           location group asset sets are not allowed to be linked to this ad group,
     *           and all Location Extension (LE) and Affiliate Location Extensions (ALE)
     *           will not be served under this ad group.
     *           Only LOCATION_SYNC is currently supported.
     *     @type int $primary_status
     *           Output only. Provides aggregated view into why an ad group is not serving
     *           or not serving optimally.
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $primary_status_reasons
     *           Output only. Provides reasons for why an ad group is not serving or not
     *           serving optimally.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Resources\AdGroup::initOnce();
        parent::__construct($data);
    }

    /**
     * Immutable. The resource name of the ad group.
     * Ad group resource names have the form:
     * `customers/{customer_id}/adGroups/{ad_group_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getResourceName()
    {
        return $this->resource_name;
    }

    /**
     * Immutable. The resource name of the ad group.
     * Ad group resource names have the form:
     * `customers/{customer_id}/adGroups/{ad_group_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setResourceName($var)
    {
        GPBUtil::checkString($var, True);
        $this->resource_name = $var;

        return $this;
    }

    /**
     * Output only. The ID of the ad group.
     *
     * Generated from protobuf field <code>optional int64 id = 34 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int|string
     */
    public function getId()
    {
        return isset($this->id) ? $this->id : 0;
    }

    public function hasId()
    {
        return isset($this->id);
    }

    public function clearId()
    {
        unset($this->id);
    }

    /**
     * Output only. The ID of the ad group.
     *
     * Generated from protobuf field <code>optional int64 id = 34 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int|string $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt64($var);
        $this->id = $var;

        return $this;
    }

    /**
     * The name of the ad group.
     * This field is required and should not be empty when creating new ad
     * groups.
     * It must contain fewer than 255 UTF-8 full-width characters.
     * It must not contain any null (code point 0x0), NL line feed
     * (code point 0xA) or carriage return (code point 0xD) characters.
     *
     * Generated from protobuf field <code>optional string name = 35;</code>
     * @return string
     */
    public function getName()
    {
        return isset($this->name) ? $this->name : '';
    }

    public function hasName()
    {
        return isset($this->name);
    }

    public function clearName()
    {
        unset($this->name);
    }

    /**
     * The name of the ad group.
     * This field is required and should not be empty when creating new ad
     * groups.
     * It must contain fewer than 255 UTF-8 full-width characters.
     * It must not contain any null (code point 0x0), NL line feed
     * (code point 0xA) or carriage return (code point 0xD) characters.
     *
     * Generated from protobuf field <code>optional string name = 35;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * The status of the ad group.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupStatusEnum.AdGroupStatus status = 5;</code>
     * @return int
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * The status of the ad group.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupStatusEnum.AdGroupStatus status = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setStatus($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\AdGroupStatusEnum\AdGroupStatus::class);
        $this->status = $var;

        return $this;
    }

    /**
     * Immutable. The type of the ad group.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupTypeEnum.AdGroupType type = 12 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Immutable. The type of the ad group.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupTypeEnum.AdGroupType type = 12 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\AdGroupTypeEnum\AdGroupType::class);
        $this->type = $var;

        return $this;
    }

    /**
     * The ad rotation mode of the ad group.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupAdRotationModeEnum.AdGroupAdRotationMode ad_rotation_mode = 22;</code>
     * @return int
     */
    public function getAdRotationMode()
    {
        return $this->ad_rotation_mode;
    }

    /**
     * The ad rotation mode of the ad group.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupAdRotationModeEnum.AdGroupAdRotationMode ad_rotation_mode = 22;</code>
     * @param int $var
     * @return $this
     */
    public function setAdRotationMode($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\AdGroupAdRotationModeEnum\AdGroupAdRotationMode::class);
        $this->ad_rotation_mode = $var;

        return $this;
    }

    /**
     * Output only. For draft or experiment ad groups, this field is the resource
     * name of the base ad group from which this ad group was created. If a draft
     * or experiment ad group does not have a base ad group, then this field is
     * null.
     * For base ad groups, this field equals the ad group resource name.
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional string base_ad_group = 36 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getBaseAdGroup()
    {
        return isset($this->base_ad_group) ? $this->base_ad_group : '';
    }

    public function hasBaseAdGroup()
    {
        return isset($this->base_ad_group);
    }

    public function clearBaseAdGroup()
    {
        unset($this->base_ad_group);
    }

    /**
     * Output only. For draft or experiment ad groups, this field is the resource
     * name of the base ad group from which this ad group was created. If a draft
     * or experiment ad group does not have a base ad group, then this field is
     * null.
     * For base ad groups, this field equals the ad group resource name.
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional string base_ad_group = 36 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setBaseAdGroup($var)
    {
        GPBUtil::checkString($var, True);
        $this->base_ad_group = $var;

        return $this;
    }

    /**
     * The URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 37;</code>
     * @return string
     */
    public function getTrackingUrlTemplate()
    {
        return isset($this->tracking_url_template) ? $this->tracking_url_template : '';
    }

    public function hasTrackingUrlTemplate()
    {
        return isset($this->tracking_url_template);
    }

    public function clearTrackingUrlTemplate()
    {
        unset($this->tracking_url_template);
    }

    /**
     * The URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 37;</code>
     * @param string $var
     * @return $this
     */
    public function setTrackingUrlTemplate($var)
    {
        GPBUtil::checkString($var, True);
        $this->tracking_url_template = $var;

        return $this;
    }

    /**
     * The list of mappings used to substitute custom parameter tags in a
     * `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.CustomParameter url_custom_parameters = 6;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getUrlCustomParameters()
    {
        return $this->url_custom_parameters;
    }

    /**
     * The list of mappings used to substitute custom parameter tags in a
     * `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.CustomParameter url_custom_parameters = 6;</code>
     * @param array<\Google\Ads\GoogleAds\V18\Common\CustomParameter>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUrlCustomParameters($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Ads\GoogleAds\V18\Common\CustomParameter::class);
        $this->url_custom_parameters = $arr;

        return $this;
    }

    /**
     * Immutable. The campaign to which the ad group belongs.
     *
     * Generated from protobuf field <code>optional string campaign = 38 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getCampaign()
    {
        return isset($this->campaign) ? $this->campaign : '';
    }

    public function hasCampaign()
    {
        return isset($this->campaign);
    }

    public function clearCampaign()
    {
        unset($this->campaign);
    }

    /**
     * Immutable. The campaign to which the ad group belongs.
     *
     * Generated from protobuf field <code>optional string campaign = 38 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setCampaign($var)
    {
        GPBUtil::checkString($var, True);
        $this->campaign = $var;

        return $this;
    }

    /**
     * The maximum CPC (cost-per-click) bid.
     *
     * Generated from protobuf field <code>optional int64 cpc_bid_micros = 39;</code>
     * @return int|string
     */
    public function getCpcBidMicros()
    {
        return isset($this->cpc_bid_micros) ? $this->cpc_bid_micros : 0;
    }

    public function hasCpcBidMicros()
    {
        return isset($this->cpc_bid_micros);
    }

    public function clearCpcBidMicros()
    {
        unset($this->cpc_bid_micros);
    }

    /**
     * The maximum CPC (cost-per-click) bid.
     *
     * Generated from protobuf field <code>optional int64 cpc_bid_micros = 39;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCpcBidMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->cpc_bid_micros = $var;

        return $this;
    }

    /**
     * Output only. Value will be same as that of the CPC (cost-per-click) bid
     * value when the bidding strategy is one of manual cpc, enhanced cpc, page
     * one promoted or target outrank share, otherwise the value will be null.
     *
     * Generated from protobuf field <code>optional int64 effective_cpc_bid_micros = 57 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int|string
     */
    public function getEffectiveCpcBidMicros()
    {
        return isset($this->effective_cpc_bid_micros) ? $this->effective_cpc_bid_micros : 0;
    }

    public function hasEffectiveCpcBidMicros()
    {
        return isset($this->effective_cpc_bid_micros);
    }

    public function clearEffectiveCpcBidMicros()
    {
        unset($this->effective_cpc_bid_micros);
    }

    /**
     * Output only. Value will be same as that of the CPC (cost-per-click) bid
     * value when the bidding strategy is one of manual cpc, enhanced cpc, page
     * one promoted or target outrank share, otherwise the value will be null.
     *
     * Generated from protobuf field <code>optional int64 effective_cpc_bid_micros = 57 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int|string $var
     * @return $this
     */
    public function setEffectiveCpcBidMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->effective_cpc_bid_micros = $var;

        return $this;
    }

    /**
     * The maximum CPM (cost-per-thousand viewable impressions) bid.
     *
     * Generated from protobuf field <code>optional int64 cpm_bid_micros = 40;</code>
     * @return int|string
     */
    public function getCpmBidMicros()
    {
        return isset($this->cpm_bid_micros) ? $this->cpm_bid_micros : 0;
    }

    public function hasCpmBidMicros()
    {
        return isset($this->cpm_bid_micros);
    }

    public function clearCpmBidMicros()
    {
        unset($this->cpm_bid_micros);
    }

    /**
     * The maximum CPM (cost-per-thousand viewable impressions) bid.
     *
     * Generated from protobuf field <code>optional int64 cpm_bid_micros = 40;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCpmBidMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->cpm_bid_micros = $var;

        return $this;
    }

    /**
     * The target CPA (cost-per-acquisition). If the ad group's campaign
     * bidding strategy is TargetCpa or MaximizeConversions (with its target_cpa
     * field set), then this field overrides the target CPA specified in the
     * campaign's bidding strategy.
     * Otherwise, this value is ignored.
     *
     * Generated from protobuf field <code>optional int64 target_cpa_micros = 41;</code>
     * @return int|string
     */
    public function getTargetCpaMicros()
    {
        return isset($this->target_cpa_micros) ? $this->target_cpa_micros : 0;
    }

    public function hasTargetCpaMicros()
    {
        return isset($this->target_cpa_micros);
    }

    public function clearTargetCpaMicros()
    {
        unset($this->target_cpa_micros);
    }

    /**
     * The target CPA (cost-per-acquisition). If the ad group's campaign
     * bidding strategy is TargetCpa or MaximizeConversions (with its target_cpa
     * field set), then this field overrides the target CPA specified in the
     * campaign's bidding strategy.
     * Otherwise, this value is ignored.
     *
     * Generated from protobuf field <code>optional int64 target_cpa_micros = 41;</code>
     * @param int|string $var
     * @return $this
     */
    public function setTargetCpaMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->target_cpa_micros = $var;

        return $this;
    }

    /**
     * The CPV (cost-per-view) bid.
     *
     * Generated from protobuf field <code>optional int64 cpv_bid_micros = 42;</code>
     * @return int|string
     */
    public function getCpvBidMicros()
    {
        return isset($this->cpv_bid_micros) ? $this->cpv_bid_micros : 0;
    }

    public function hasCpvBidMicros()
    {
        return isset($this->cpv_bid_micros);
    }

    public function clearCpvBidMicros()
    {
        unset($this->cpv_bid_micros);
    }

    /**
     * The CPV (cost-per-view) bid.
     *
     * Generated from protobuf field <code>optional int64 cpv_bid_micros = 42;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCpvBidMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->cpv_bid_micros = $var;

        return $this;
    }

    /**
     * Average amount in micros that the advertiser is willing to pay for every
     * thousand times the ad is shown.
     *
     * Generated from protobuf field <code>optional int64 target_cpm_micros = 43;</code>
     * @return int|string
     */
    public function getTargetCpmMicros()
    {
        return isset($this->target_cpm_micros) ? $this->target_cpm_micros : 0;
    }

    public function hasTargetCpmMicros()
    {
        return isset($this->target_cpm_micros);
    }

    public function clearTargetCpmMicros()
    {
        unset($this->target_cpm_micros);
    }

    /**
     * Average amount in micros that the advertiser is willing to pay for every
     * thousand times the ad is shown.
     *
     * Generated from protobuf field <code>optional int64 target_cpm_micros = 43;</code>
     * @param int|string $var
     * @return $this
     */
    public function setTargetCpmMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->target_cpm_micros = $var;

        return $this;
    }

    /**
     * The target ROAS (return-on-ad-spend) override. If the ad group's campaign
     * bidding strategy is TargetRoas or MaximizeConversionValue (with its
     * target_roas field set), then this field overrides the target ROAS specified
     * in the campaign's bidding strategy.
     * Otherwise, this value is ignored.
     *
     * Generated from protobuf field <code>optional double target_roas = 44;</code>
     * @return float
     */
    public function getTargetRoas()
    {
        return isset($this->target_roas) ? $this->target_roas : 0.0;
    }

    public function hasTargetRoas()
    {
        return isset($this->target_roas);
    }

    public function clearTargetRoas()
    {
        unset($this->target_roas);
    }

    /**
     * The target ROAS (return-on-ad-spend) override. If the ad group's campaign
     * bidding strategy is TargetRoas or MaximizeConversionValue (with its
     * target_roas field set), then this field overrides the target ROAS specified
     * in the campaign's bidding strategy.
     * Otherwise, this value is ignored.
     *
     * Generated from protobuf field <code>optional double target_roas = 44;</code>
     * @param float $var
     * @return $this
     */
    public function setTargetRoas($var)
    {
        GPBUtil::checkDouble($var);
        $this->target_roas = $var;

        return $this;
    }

    /**
     * The percent cpc bid amount, expressed as a fraction of the advertised price
     * for some good or service. The valid range for the fraction is [0,1) and the
     * value stored here is 1,000,000 * [fraction].
     *
     * Generated from protobuf field <code>optional int64 percent_cpc_bid_micros = 45;</code>
     * @return int|string
     */
    public function getPercentCpcBidMicros()
    {
        return isset($this->percent_cpc_bid_micros) ? $this->percent_cpc_bid_micros : 0;
    }

    public function hasPercentCpcBidMicros()
    {
        return isset($this->percent_cpc_bid_micros);
    }

    public function clearPercentCpcBidMicros()
    {
        unset($this->percent_cpc_bid_micros);
    }

    /**
     * The percent cpc bid amount, expressed as a fraction of the advertised price
     * for some good or service. The valid range for the fraction is [0,1) and the
     * value stored here is 1,000,000 * [fraction].
     *
     * Generated from protobuf field <code>optional int64 percent_cpc_bid_micros = 45;</code>
     * @param int|string $var
     * @return $this
     */
    public function setPercentCpcBidMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->percent_cpc_bid_micros = $var;

        return $this;
    }

    /**
     * The fixed amount in micros that the advertiser pays for every thousand
     * impressions of the ad.
     *
     * Generated from protobuf field <code>optional int64 fixed_cpm_micros = 64;</code>
     * @return int|string
     */
    public function getFixedCpmMicros()
    {
        return isset($this->fixed_cpm_micros) ? $this->fixed_cpm_micros : 0;
    }

    public function hasFixedCpmMicros()
    {
        return isset($this->fixed_cpm_micros);
    }

    public function clearFixedCpmMicros()
    {
        unset($this->fixed_cpm_micros);
    }

    /**
     * The fixed amount in micros that the advertiser pays for every thousand
     * impressions of the ad.
     *
     * Generated from protobuf field <code>optional int64 fixed_cpm_micros = 64;</code>
     * @param int|string $var
     * @return $this
     */
    public function setFixedCpmMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->fixed_cpm_micros = $var;

        return $this;
    }

    /**
     * Average amount in micros that the advertiser is willing to pay for every ad
     * view.
     *
     * Generated from protobuf field <code>optional int64 target_cpv_micros = 65;</code>
     * @return int|string
     */
    public function getTargetCpvMicros()
    {
        return isset($this->target_cpv_micros) ? $this->target_cpv_micros : 0;
    }

    public function hasTargetCpvMicros()
    {
        return isset($this->target_cpv_micros);
    }

    public function clearTargetCpvMicros()
    {
        unset($this->target_cpv_micros);
    }

    /**
     * Average amount in micros that the advertiser is willing to pay for every ad
     * view.
     *
     * Generated from protobuf field <code>optional int64 target_cpv_micros = 65;</code>
     * @param int|string $var
     * @return $this
     */
    public function setTargetCpvMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->target_cpv_micros = $var;

        return $this;
    }

    /**
     * True if optimized targeting is enabled. Optimized Targeting is the
     * replacement for Audience Expansion.
     *
     * Generated from protobuf field <code>bool optimized_targeting_enabled = 59;</code>
     * @return bool
     */
    public function getOptimizedTargetingEnabled()
    {
        return $this->optimized_targeting_enabled;
    }

    /**
     * True if optimized targeting is enabled. Optimized Targeting is the
     * replacement for Audience Expansion.
     *
     * Generated from protobuf field <code>bool optimized_targeting_enabled = 59;</code>
     * @param bool $var
     * @return $this
     */
    public function setOptimizedTargetingEnabled($var)
    {
        GPBUtil::checkBool($var);
        $this->optimized_targeting_enabled = $var;

        return $this;
    }

    /**
     * When this value is true, demographics will be excluded from the types of
     * targeting which are expanded when optimized_targeting_enabled is true.
     * When optimized_targeting_enabled is false, this field is ignored. Default
     * is false.
     *
     * Generated from protobuf field <code>bool exclude_demographic_expansion = 67;</code>
     * @return bool
     */
    public function getExcludeDemographicExpansion()
    {
        return $this->exclude_demographic_expansion;
    }

    /**
     * When this value is true, demographics will be excluded from the types of
     * targeting which are expanded when optimized_targeting_enabled is true.
     * When optimized_targeting_enabled is false, this field is ignored. Default
     * is false.
     *
     * Generated from protobuf field <code>bool exclude_demographic_expansion = 67;</code>
     * @param bool $var
     * @return $this
     */
    public function setExcludeDemographicExpansion($var)
    {
        GPBUtil::checkBool($var);
        $this->exclude_demographic_expansion = $var;

        return $this;
    }

    /**
     * Allows advertisers to specify a targeting dimension on which to place
     * absolute bids. This is only applicable for campaigns that target only the
     * display network and not search.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.TargetingDimensionEnum.TargetingDimension display_custom_bid_dimension = 23;</code>
     * @return int
     */
    public function getDisplayCustomBidDimension()
    {
        return $this->display_custom_bid_dimension;
    }

    /**
     * Allows advertisers to specify a targeting dimension on which to place
     * absolute bids. This is only applicable for campaigns that target only the
     * display network and not search.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.TargetingDimensionEnum.TargetingDimension display_custom_bid_dimension = 23;</code>
     * @param int $var
     * @return $this
     */
    public function setDisplayCustomBidDimension($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\TargetingDimensionEnum\TargetingDimension::class);
        $this->display_custom_bid_dimension = $var;

        return $this;
    }

    /**
     * URL template for appending params to Final URL.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 46;</code>
     * @return string
     */
    public function getFinalUrlSuffix()
    {
        return isset($this->final_url_suffix) ? $this->final_url_suffix : '';
    }

    public function hasFinalUrlSuffix()
    {
        return isset($this->final_url_suffix);
    }

    public function clearFinalUrlSuffix()
    {
        unset($this->final_url_suffix);
    }

    /**
     * URL template for appending params to Final URL.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 46;</code>
     * @param string $var
     * @return $this
     */
    public function setFinalUrlSuffix($var)
    {
        GPBUtil::checkString($var, True);
        $this->final_url_suffix = $var;

        return $this;
    }

    /**
     * Setting for targeting related features.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.TargetingSetting targeting_setting = 25;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\TargetingSetting|null
     */
    public function getTargetingSetting()
    {
        return $this->targeting_setting;
    }

    public function hasTargetingSetting()
    {
        return isset($this->targeting_setting);
    }

    public function clearTargetingSetting()
    {
        unset($this->targeting_setting);
    }

    /**
     * Setting for targeting related features.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.TargetingSetting targeting_setting = 25;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\TargetingSetting $var
     * @return $this
     */
    public function setTargetingSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\TargetingSetting::class);
        $this->targeting_setting = $var;

        return $this;
    }

    /**
     * Immutable. Setting for audience related features.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.resources.AdGroup.AudienceSetting audience_setting = 56 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Resources\AdGroup\AudienceSetting|null
     */
    public function getAudienceSetting()
    {
        return $this->audience_setting;
    }

    public function hasAudienceSetting()
    {
        return isset($this->audience_setting);
    }

    public function clearAudienceSetting()
    {
        unset($this->audience_setting);
    }

    /**
     * Immutable. Setting for audience related features.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.resources.AdGroup.AudienceSetting audience_setting = 56 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Resources\AdGroup\AudienceSetting $var
     * @return $this
     */
    public function setAudienceSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Resources\AdGroup\AudienceSetting::class);
        $this->audience_setting = $var;

        return $this;
    }

    /**
     * Output only. The effective target CPA (cost-per-acquisition).
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional int64 effective_target_cpa_micros = 47 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int|string
     */
    public function getEffectiveTargetCpaMicros()
    {
        return isset($this->effective_target_cpa_micros) ? $this->effective_target_cpa_micros : 0;
    }

    public function hasEffectiveTargetCpaMicros()
    {
        return isset($this->effective_target_cpa_micros);
    }

    public function clearEffectiveTargetCpaMicros()
    {
        unset($this->effective_target_cpa_micros);
    }

    /**
     * Output only. The effective target CPA (cost-per-acquisition).
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional int64 effective_target_cpa_micros = 47 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int|string $var
     * @return $this
     */
    public function setEffectiveTargetCpaMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->effective_target_cpa_micros = $var;

        return $this;
    }

    /**
     * Output only. Source of the effective target CPA.
     * This field is read-only.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_target_cpa_source = 29 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getEffectiveTargetCpaSource()
    {
        return $this->effective_target_cpa_source;
    }

    /**
     * Output only. Source of the effective target CPA.
     * This field is read-only.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_target_cpa_source = 29 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setEffectiveTargetCpaSource($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\BiddingSourceEnum\BiddingSource::class);
        $this->effective_target_cpa_source = $var;

        return $this;
    }

    /**
     * Output only. The effective target ROAS (return-on-ad-spend).
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional double effective_target_roas = 48 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return float
     */
    public function getEffectiveTargetRoas()
    {
        return isset($this->effective_target_roas) ? $this->effective_target_roas : 0.0;
    }

    public function hasEffectiveTargetRoas()
    {
        return isset($this->effective_target_roas);
    }

    public function clearEffectiveTargetRoas()
    {
        unset($this->effective_target_roas);
    }

    /**
     * Output only. The effective target ROAS (return-on-ad-spend).
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional double effective_target_roas = 48 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param float $var
     * @return $this
     */
    public function setEffectiveTargetRoas($var)
    {
        GPBUtil::checkDouble($var);
        $this->effective_target_roas = $var;

        return $this;
    }

    /**
     * Output only. Source of the effective target ROAS.
     * This field is read-only.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_target_roas_source = 32 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getEffectiveTargetRoasSource()
    {
        return $this->effective_target_roas_source;
    }

    /**
     * Output only. Source of the effective target ROAS.
     * This field is read-only.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_target_roas_source = 32 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setEffectiveTargetRoasSource($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\BiddingSourceEnum\BiddingSource::class);
        $this->effective_target_roas_source = $var;

        return $this;
    }

    /**
     * Output only. The resource names of labels attached to this ad group.
     *
     * Generated from protobuf field <code>repeated string labels = 49 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getLabels()
    {
        return $this->labels;
    }

    /**
     * Output only. The resource names of labels attached to this ad group.
     *
     * Generated from protobuf field <code>repeated string labels = 49 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setLabels($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->labels = $arr;

        return $this;
    }

    /**
     * The asset field types that should be excluded from this ad group. Asset
     * links with these field types will not be inherited by this ad group from
     * the upper levels.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.enums.AssetFieldTypeEnum.AssetFieldType excluded_parent_asset_field_types = 54;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getExcludedParentAssetFieldTypes()
    {
        return $this->excluded_parent_asset_field_types;
    }

    /**
     * The asset field types that should be excluded from this ad group. Asset
     * links with these field types will not be inherited by this ad group from
     * the upper levels.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.enums.AssetFieldTypeEnum.AssetFieldType excluded_parent_asset_field_types = 54;</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setExcludedParentAssetFieldTypes($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::ENUM, \Google\Ads\GoogleAds\V18\Enums\AssetFieldTypeEnum\AssetFieldType::class);
        $this->excluded_parent_asset_field_types = $arr;

        return $this;
    }

    /**
     * The asset set types that should be excluded from this ad group. Asset set
     * links with these types will not be inherited by this ad group from the
     * upper levels.
     * Location group types (GMB_DYNAMIC_LOCATION_GROUP,
     * CHAIN_DYNAMIC_LOCATION_GROUP, and STATIC_LOCATION_GROUP) are child types of
     * LOCATION_SYNC. Therefore, if LOCATION_SYNC is set for this field, all
     * location group asset sets are not allowed to be linked to this ad group,
     * and all Location Extension (LE) and Affiliate Location Extensions (ALE)
     * will not be served under this ad group.
     * Only LOCATION_SYNC is currently supported.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.enums.AssetSetTypeEnum.AssetSetType excluded_parent_asset_set_types = 58;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getExcludedParentAssetSetTypes()
    {
        return $this->excluded_parent_asset_set_types;
    }

    /**
     * The asset set types that should be excluded from this ad group. Asset set
     * links with these types will not be inherited by this ad group from the
     * upper levels.
     * Location group types (GMB_DYNAMIC_LOCATION_GROUP,
     * CHAIN_DYNAMIC_LOCATION_GROUP, and STATIC_LOCATION_GROUP) are child types of
     * LOCATION_SYNC. Therefore, if LOCATION_SYNC is set for this field, all
     * location group asset sets are not allowed to be linked to this ad group,
     * and all Location Extension (LE) and Affiliate Location Extensions (ALE)
     * will not be served under this ad group.
     * Only LOCATION_SYNC is currently supported.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.enums.AssetSetTypeEnum.AssetSetType excluded_parent_asset_set_types = 58;</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setExcludedParentAssetSetTypes($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::ENUM, \Google\Ads\GoogleAds\V18\Enums\AssetSetTypeEnum\AssetSetType::class);
        $this->excluded_parent_asset_set_types = $arr;

        return $this;
    }

    /**
     * Output only. Provides aggregated view into why an ad group is not serving
     * or not serving optimally.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupPrimaryStatusEnum.AdGroupPrimaryStatus primary_status = 62 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getPrimaryStatus()
    {
        return $this->primary_status;
    }

    /**
     * Output only. Provides aggregated view into why an ad group is not serving
     * or not serving optimally.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupPrimaryStatusEnum.AdGroupPrimaryStatus primary_status = 62 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setPrimaryStatus($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\AdGroupPrimaryStatusEnum\AdGroupPrimaryStatus::class);
        $this->primary_status = $var;

        return $this;
    }

    /**
     * Output only. Provides reasons for why an ad group is not serving or not
     * serving optimally.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.enums.AdGroupPrimaryStatusReasonEnum.AdGroupPrimaryStatusReason primary_status_reasons = 63 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getPrimaryStatusReasons()
    {
        return $this->primary_status_reasons;
    }

    /**
     * Output only. Provides reasons for why an ad group is not serving or not
     * serving optimally.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.enums.AdGroupPrimaryStatusReasonEnum.AdGroupPrimaryStatusReason primary_status_reasons = 63 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setPrimaryStatusReasons($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::ENUM, \Google\Ads\GoogleAds\V18\Enums\AdGroupPrimaryStatusReasonEnum\AdGroupPrimaryStatusReason::class);
        $this->primary_status_reasons = $arr;

        return $this;
    }

}

