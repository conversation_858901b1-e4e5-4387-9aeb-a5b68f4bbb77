<?php

namespace DynamicWebhook\Interfaces;

/**
 * Interface for message and data normalization
 */
interface MessageNormalizerInterface
{
    /**
     * Clean and normalize phone number
     *
     * @param string|null $phone Raw phone number
     * @return string|null Cleaned phone number
     */
    public function cleanPhoneNumber(?string $phone): ?string;

    /**
     * Normalize message content
     *
     * @param string|null $message Raw message
     * @return string Normalized message
     */
    public function normalizeMessage(?string $message): string;

    /**
     * Validate phone number format
     *
     * @param string|null $phone Phone number
     * @return bool
     */
    public function isValidPhoneNumber(?string $phone): bool;

    /**
     * Extract nested value from array using dot notation
     *
     * @param array $array Source array
     * @param string|null $path Dot notation path
     * @return mixed
     */
    public function extractNestedValue(array $array, ?string $path);
}
