<?php

function get_report_custom_detail($param){
    $msg = "eeq custom";
    return $msg;
}

function delete_report_custom($param)
{
    $rules = [
        "project_id" => "required",
        "report_group_id" => "required",
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $rg = new reportcustom();
    $msg = $rg->delete($report_group_id);
 
    return $msg;
}

function delete_report_custom_group($param)
{
    $rules = [
        "project_id" => "required",
        "group_id" => "required",
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $rg = new reportcustom();
    $msg = $rg->delete_group($group_id);

    return $msg;
}

function delete_report_from_group($param)
{
    $rules = [
        "project_id" => "required",
        "rel_id" => "required",
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $rg = new reportcustom();
    $msg = $rg->delete_report_from_group($rel_id);

    return $msg;
}


function create($param)
{
    $rules = [
        "project_id" => "required",
        "name" => "required",
        "group" => "required",
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    
   
    $logo_url = $logo_url ?? NULL;

    $rg = new reportcustom();
    $msg = $rg->create($name,$group,$logo_url);

    return $msg;
}

function get_report_list($param)
{
    $rules = [
        "project_id" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $rg = new reportcustom();
    $msg = $rg->get_report_list();

    return $msg;
}

function get_reportcustom($param)
{
    $rules = [
        "project_id" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $rg = new reportcustom();
    $msg = $rg->get_reportcustom();
   
    return $msg;
}

function get_campaigns($param)
{
    $rules = [
        "project_id" => "required",
        "parent_id" => "required",
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $rg = new reportcustom();
    $msg = $rg->get_campaigns($parent_id);

    return $msg;
}

function get_campaign_detail($param)
{
    $rules = [
        "project_id" => "required",
        "campaign_id" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $rg = new reportcustom();
    $msg = $rg->get_campaign_detail($campaign_id);

    return $msg;
}