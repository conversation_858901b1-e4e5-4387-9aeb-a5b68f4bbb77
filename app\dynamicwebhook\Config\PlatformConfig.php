<?php

namespace DynamicWebhook\Config;

/**
 * Platform configuration manager
 */
class PlatformConfig
{
    private static array $platformConfigs = [
        'konekwa' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out',
            'raw_field' => 'raw'
        ],
        'waba' => [
            'phone_field' => 'entry.0.changes.0.value.messages.0.from',
            'phone_field_out' => 'entry.0.changes.0.value.messages.0.to',
            'message_field' => 'entry.0.changes.0.value.messages.0.text.body',
            'type_field' => 'entry.0.changes.0.value.messages.0.type',
            'incoming_condition' => 'from_exists',
            'outgoing_condition' => 'to_exists',
            'nested_structure' => true,
            'echo_field' => 'entry.0.changes.0.value.message_echoes'
        ],
        'halosis' => [
            'phone_field' => 'data.from_phone_number',
            'phone_field_out' => 'data.to',
            'message_field' => 'data.message',
            'message_field_out' => 'data.text.body',
            'type_field' => 'type',
            'incoming_condition' => 'message.received',
            'outgoing_condition' => 'message.sent',
            'cs_field' => 'data.to_phone_number',
            'cs_field_out' => 'from_phone_number'
        ],
        'sleekflow' => [
            'phone_field' => 'from',
            'phone_field_alt' => 'contact.PhoneNumber',
            'phone_field_out' => 'to',
            'message_field' => 'messageContent',
            'message_field_alt' => 'message.message_content',
            'type_field' => 'status',
            'type_field_alt' => 'message.message_status',
            'incoming_condition' => 'Received',
            'outgoing_condition' => 'Sent'
        ],
        'barantum' => [
            'phone_field' => 'message_users_id',
            'message_field' => 'message_text',
            'type_field' => 'company_uuid',
            'incoming_condition' => 'exists',
            'outgoing_condition' => 'not_exists'
        ],
        'chatdaddy' => [
            'phone_field' => 'data.0.senderContactId',
            'message_field' => 'data.0.text',
            'type_field' => 'data.0.fromMe',
            'incoming_condition' => false,
            'outgoing_condition' => true,
            'event_field' => 'event',
            'event_required' => 'message-insert'
        ],
        'fonnte' => [
            'phone_field' => 'sender',
            'message_field' => 'message',
            'type_field' => 'quick',
            'incoming_condition' => 0,
            'outgoing_condition' => 1
        ],
        'freshchat' => [
            'phone_field' => 'data.customer.phone',
            'message_field' => 'data.message.message_parts.0.text.content',
            'type_field' => 'data.message.actor_type',
            'incoming_condition' => 'user',
            'outgoing_condition' => 'agent'
        ],
        'smartchat' => [
            'phone_field' => 'no_customer',
            'message_field' => 'message',
            'type_field' => 'message_type',
            'incoming_condition' => 'inbound',
            'outgoing_condition' => 'outbound',
            'form_data' => true
        ],
        'botsailor' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'kommo' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'otoklix' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'pancake' => [
            'phone_field' => 'entry.0.changes.0.value.contacts.0.wa_id',
            'message_field' => 'entry.0.changes.0.value.messages.0.text.body',
            'type_field' => 'entry.0.changes.0.value.statuses',
            'incoming_condition' => 'not_exists',
            'outgoing_condition' => 'exists',
            'nested_structure' => true,
            'special_handling' => 'pancake_waba'
        ],
        'qiscus-omnichannel' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'rasayel' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'respondio' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'wabahalosis' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'hs' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'qontak' => [
            'phone_field' => 'room.account_uniq_id',
            'message_field' => 'text',
            'type_field' => 'sender_type',
            'incoming_condition' => 'Models::Contact',
            'outgoing_condition' => 'not_Models::Contact',
            'integration_field' => 'room.channel_integration_id',
            'special_handling' => 'qontak_integration'
        ],
        'default' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ]
    ];

    /**
     * Get configuration for a platform
     */
    public static function get(string $platform): array
    {
        return self::$platformConfigs[$platform] ?? self::$platformConfigs['default'];
    }

    /**
     * Get all platform configurations
     */
    public static function getAll(): array
    {
        return self::$platformConfigs;
    }

    /**
     * Check if platform is configured
     */
    public static function exists(string $platform): bool
    {
        return isset(self::$platformConfigs[$platform]);
    }

    /**
     * Add or update platform configuration
     */
    public static function set(string $platform, array $config): void
    {
        self::$platformConfigs[$platform] = $config;
    }

    /**
     * Validate platform configuration
     */
    public static function validate(array $config): array
    {
        $errors = [];
        $required = ['phone_field', 'message_field', 'type_field', 'incoming_condition', 'outgoing_condition'];
        
        foreach ($required as $field) {
            if (!isset($config[$field])) {
                $errors[] = "Missing required field: {$field}";
            }
        }
        
        return $errors;
    }

    /**
     * Get supported platforms
     */
    public static function getSupportedPlatforms(): array
    {
        return array_keys(self::$platformConfigs);
    }
}
