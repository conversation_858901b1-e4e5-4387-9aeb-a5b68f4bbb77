<?php
set_time_limit(0);
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Konekt\PdfInvoice\InvoicePrinter;
use Aws\S3\S3Client;
use Aws\Exception\AwsException;
function generate_inv_lunas($param){
    global $app;
    extract($param);
    assign_child_db($project_id);
    $pos["act"] = 'bill_get_project_user';
    $pos["project_id"] = $project_id;
    $user = json_decode(post_x_contents($pos, 'http://10.104.0.27/api.html'), true);
    if(isset($user['setting_bill'])){
        $db = $app->db2;
        $last_bill =  $start;  
        $yesterday =  $end;
        $db->join("visitor_cs vc", "v.visitor_id = vc.visitor_id", "LEFT");
        $db->join("cs c", "vc.cs_key = c.cs_key", "LEFT");
                
        $db->where("DATE(v.created) BETWEEN ? AND ?", array($last_bill, $yesterday));  
        $site = $db->get("visitor v", NULL, "v.site, v.data, DATE(v.created) as waktu");
        $sites = []; 
        foreach ($site as $item) {
            $sit = $item["site"];
            $sit = str_replace("www.","", $sit);
            $isctwa = false;

            if (strpos($item["data"] ?? '', "ctwaClid") !== false) {
                $isctwa = true;
                $sit = 'ctwa';
                $waktu = $item["waktu"];
                if(!isset($sites[$sit]['total'])){
                    $sites[$sit]['total'] = 0;
                }
                if (!isset($sites[$sit]['date'][$waktu])) {
                    $sites[$sit]['date'][$waktu] = 0;
                    $sites[$sit]['total'] ++;
                }
                $sites[$sit]['date'][$waktu]++; 
                $sites[$sit]['total']++;

            }
            if($sit!='' && $sit != 'b-cdn.net' && $isctwa==false){     ///// disable ctwa
                $waktu = $item["waktu"];
                if($sit==''){
                    $sit = 'ctwa';
                }
                if (!isset($sites[$sit]['date'][$waktu])) {
                    $sites[$sit]['date'][$waktu] = 0;
                    $sites[$sit]['total']++;
                }
                $sites[$sit]['date'][$waktu]++;        
            }
        }
        $total_day_site = 0;$total_day_ctwa = 0;
        foreach ($sites as $k => $v) {
            if($k != 'ctwa'){
                $total_day_site = $v['total'] + $total_day_site;
            }else{
                $total_day_ctwa = $v['total'] + $total_day_ctwa;
            }            
        }
        $tctwa = []; $total_click_ctwa = 0;
        if(isset($sites['ctwa']['date'])){
            foreach ($sites['ctwa']['date'] as $k => $v) {
                //print_r($k);
                //// hitung ctwa
                
                $tctwa[$k]['date'] = $k; 
                $tctwa[$k]['total'] = $v;     
                $total_click_ctwa += $v;  
            }
            $total_day_ctwa = $total_click_ctwa;
        }
        $total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_click_ctwa;
        
        $total_bill_site = $user['setting_bill']['site'] * $total_day_site;
        //$total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_day_ctwa;
        // hitung bill cs
        $db->where("DATE(tanggal) BETWEEN ? AND ?", array($last_bill, $yesterday));  
        $cs = $db->get("cs_log", NULL, "phone, DATE(tanggal) as waktu, impression, contact, mql, prospek, purchase, value");
        $nocs = []; 
        foreach ($cs as $item) {
            $c = $item["phone"];
            $waktu = $item["waktu"];
            if(!isset($nocs[$c]['total'])){
                $nocs[$c]['total'] = 0;
            }
            if (!isset($nocs[$c]['date'][$waktu])) {
                $nocs[$c]['date'][$waktu] = 0;
                $nocs[$c]['total']++;
            }
            $nocs[$c]['date'][$waktu]++;        
        }
        $total_day_cs = 0;
        foreach ($nocs as $k => $v) {
            $total_day_cs = $v['total'] + $total_day_cs;
        }
        $total_bill_cs = $user['setting_bill']['cs_waba']['total_tagihan_cs'];
        // hitung cta
        $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'unik_cta'", array($last_bill, $yesterday));  
        $cta = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
        $tcta = []; $total_click_cta = 0;
        foreach ($cta as $item) {
            $c = $item["report_id"];
            $waktu = $item["waktu"];
            if(!isset($tcta[$c]['total'])){
                $tcta[$c]['total'] = 0;
            }
            if (!isset($tcta[$c]['date'][$waktu])) {
                $tcta[$c]['date'][$waktu] = 0;
                $tcta[$c]['total']++;
            }
            $tcta[$c]['date'][$waktu] += $item["report_value"];   
            $total_click_cta += $item["report_value"]; 
        }
        $total_day_cta = 0;
        foreach ($tcta as $k => $v) {
            $total_day_cta = $v['total'] + $total_day_cta;
        }
        
        // hitung lp_view
        $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'lp_view'", array($last_bill, $yesterday));  
        $lp_view = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
        $tlp_view = []; 
        foreach ($lp_view as $item) {
            $c = $item["report_id"];
            $waktu = $item["waktu"];
            if(!isset($tlp_view[$c]['total'])){
                $tlp_view[$c]['total'] = 0;
            }
            if (!isset($tlp_view[$c]['date'][$waktu])) {
                $tlp_view[$c]['date'][$waktu] = 0;
                $tlp_view[$c]['total']++;
            }
            $tlp_view[$c]['date'][$waktu] += $item["report_value"];        
        }
        $total_day_lp_view = 0;
        foreach ($tlp_view as $k => $v) {
            $total_day_lp_view= $v['total'] + $total_day_lp_view;
        }
        
        $total_bill_cta = $user['setting_bill']['lead'] * $total_click_cta;
        $total['total_bill_site'] = $total_bill_site;
        $total['total_bill_ctwa'] = $total_bill_ctwa;
        $total['total_bill_cs'] = $total_bill_cs;
        $total['total_bill_cta'] = $total_bill_cta;
        $total['total_day_site'] = $total_day_site;
        $total['total_day_ctwa'] = $total_day_ctwa;
        $total['total_day_cs'] = $total_day_cs;
        $total['total_day_cta'] = $total_day_cta;
        $total['total_click_cta'] = $total_click_cta;
        $total['total_click_ctwa'] = $total_click_ctwa;
        $total['total'] = $total_bill_ctwa + $total_bill_site + $total_bill_cs + $total_bill_cta;
        
        $dx['user'] = $user;
        $dx['sites'] = $sites;
        $dx['nocs'] = $nocs;
        $dx['tcta'] = $tcta;
        $dx['tctwa'] = $tctwa;
        $dx['total'] = $total;
        create_inv($dx, $project_id.$bill_id, 1, 1);

        $msg['code']=1;        
        $msg['data']['user'] = $user;
//        $msg['data']['sites'] = $sites;
//        $msg['data']['nocs'] = $nocs;
//        $msg['data']['tcta'] = $tcta;
        $msg['data']['total'] = $total;
        return $msg;
    }else{
        $msg['code']=0;
        $msg['msg']='bill not set';
        return $msg;
    }
    
}

function generate_inv($param){
    global $app;
    extract($param);
    assign_child_db($project_id);
    $pos["act"] = 'bill_get_project_user';
    $pos["project_id"] = $project_id;
    $user = json_decode(post_x_contents($pos, 'http://10.104.0.27/api.html'), true);
    if(isset($user['setting_bill'])){
        $db = $app->db2;
        $last_bill =  $start;  
        $yesterday =  $end;
        $db->join("visitor_cs vc", "v.visitor_id = vc.visitor_id", "LEFT");
        $db->join("cs c", "vc.cs_key = c.cs_key", "LEFT");
                
        $db->where("DATE(v.created) BETWEEN ? AND ?", array($last_bill, $yesterday));  
        $site = $db->get("visitor v", NULL, "v.site, v.data, DATE(v.created) as waktu");
        $sites = []; 
        foreach ($site as $item) {
            $sit = $item["site"];
            $sit = str_replace("www.","", $sit);
            $isctwa = false;

            if (strpos($item["data"] ?? '', "ctwaClid") !== false) {
                $isctwa = true;
                $sit = 'ctwa';
                $waktu = $item["waktu"];
                if(!isset($sites[$sit]['total'])){
                    $sites[$sit]['total'] = 0;
                }
                if (!isset($sites[$sit]['date'][$waktu])) {
                    $sites[$sit]['date'][$waktu] = 0;
                    $sites[$sit]['total'] ++;
                }
                $sites[$sit]['date'][$waktu]++; 
                $sites[$sit]['total']++;

            }
            if($sit!='' && $sit != 'b-cdn.net' && $isctwa==false){   ///// disable ctwa
                $waktu = $item["waktu"];
                if($sit==''){
                    $sit = 'ctwa';
                }
                if (!isset($sites[$sit]['date'][$waktu])) {
                    $sites[$sit]['date'][$waktu] = 0;
                    $sites[$sit]['total']++;
                }
                $sites[$sit]['date'][$waktu]++;        
            }
        }
        $total_day_site = 0;$total_day_ctwa = 0;
        foreach ($sites as $k => $v) {
            if($k != 'ctwa'){
                $total_day_site = $v['total'] + $total_day_site;
            }else{
                $total_day_ctwa = $v['total'] + $total_day_ctwa;
            }            
        }
        $tctwa = []; $total_click_ctwa = 0;
        if(isset($sites['ctwa']['date'])){
            foreach ($sites['ctwa']['date'] as $k => $v) {
                //print_r($k);
                //// hitung ctwa
                
                $tctwa[$k]['date'] = $k; 
                $tctwa[$k]['total'] = $v;     
                $total_click_ctwa += $v;  
            }
            $total_day_ctwa = $total_click_ctwa;
        }
        $total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_click_ctwa;

        $total_bill_site = $user['setting_bill']['site'] * $total_day_site;
        //$total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_day_ctwa;
        // hitung bill cs
        $db->where("DATE(tanggal) BETWEEN ? AND ?", array($last_bill, $yesterday));  
        $cs = $db->get("cs_log", NULL, "phone, DATE(tanggal) as waktu, impression, contact, mql, prospek, purchase, value");
        $nocs = []; 
        foreach ($cs as $item) {
            $c = $item["phone"];
            $waktu = $item["waktu"];
            if(!isset($nocs[$c]['total'])){
                $nocs[$c]['total'] = 0;
            }
            if (!isset($nocs[$c]['date'][$waktu])) {
                $nocs[$c]['date'][$waktu] = 0;
                $nocs[$c]['total']++;
            }
            $nocs[$c]['date'][$waktu]++;        
        }
        $total_day_cs = 0;
        foreach ($nocs as $k => $v) {
            $total_day_cs = $v['total'] + $total_day_cs;
        }
        $total_bill_cs = $user['setting_bill']['cs'] * $total_day_cs;
        // hitung cta
        $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'unik_cta'", array($last_bill, $yesterday));  
        $cta = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
        $tcta = []; $total_click_cta = 0;
        foreach ($cta as $item) {
            $c = $item["report_id"];
            $waktu = $item["waktu"];
            if(!isset($tcta[$c]['total'])){
                $tcta[$c]['total'] = 0;
            }
            if (!isset($tcta[$c]['date'][$waktu])) {
                $tcta[$c]['date'][$waktu] = 0;
                $tcta[$c]['total']++;
            }
            $tcta[$c]['date'][$waktu] += $item["report_value"];   
            $total_click_cta += $item["report_value"]; 
        }
        $total_day_cta = 0;
        foreach ($tcta as $k => $v) {
            $total_day_cta = $v['total'] + $total_day_cta;
        }
        
        // hitung lp_view
        $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'lp_view'", array($last_bill, $yesterday));  
        $lp_view = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
        $tlp_view = []; 
        foreach ($lp_view as $item) {
            $c = $item["report_id"];
            $waktu = $item["waktu"];
            if(!isset($tlp_view[$c]['total'])){
                $tlp_view[$c]['total'] = 0;
            }
            if (!isset($tlp_view[$c]['date'][$waktu])) {
                $tlp_view[$c]['date'][$waktu] = 0;
                $tlp_view[$c]['total']++;
            }
            $tlp_view[$c]['date'][$waktu] += $item["report_value"];        
        }
        $total_day_lp_view = 0;
        foreach ($tlp_view as $k => $v) {
            $total_day_lp_view= $v['total'] + $total_day_lp_view;
        }
        
        $total_bill_cta = $user['setting_bill']['lead'] * $total_click_cta;
        $total['total_bill_site'] = $total_bill_site;
        $total['total_bill_ctwa'] = $total_bill_ctwa;
        $total['total_bill_cs'] = $total_bill_cs;
        $total['total_bill_cta'] = $total_bill_cta;
        $total['total_day_site'] = $total_day_site;
        $total['total_day_ctwa'] = $total_day_ctwa;
        $total['total_day_cs'] = $total_day_cs;
        $total['total_day_cta'] = $total_day_cta;
        $total['total_click_cta'] = $total_click_cta;
        $total['total_click_ctwa'] = $total_click_ctwa;
        $total['total'] = $total_bill_ctwa + $total_bill_site + $total_bill_cs + $total_bill_cta;
        $create_bill = false;
        if($total['total'] > 0){
            $create_bill = true;
        }
        
        // $dx['user'] = $user;
        // $dx['sites'] = $sites;
        // $dx['nocs'] = $nocs;
        // $dx['tcta'] = $tcta;
        // $dx['total'] = $total;
        if($create_bill == true){
            $pos["act"] = 'bill_create';
            $pos["user_id"] = $user['data']['user_id'];
            $pos["project_id"] = $project_id;
            $pos["keterangan"] = "Billing project ".$user['data']['name'];
            $pos["total"] = $total['total'];
            $pos["last_bill"] = $yesterday;
            $pos["data"] = '{}';
            $bill = json_decode(post_x_contents($pos, 'http://10.104.0.27/api.html'), true);
            if($bill['code']==1){
                $lunas = 0; $topup = 0;
                if($total['total'] <= $user['data']['saldo']){
                    //pay bill lunas  
                    $lunas = 1;
                }else{
                    $topup = $total['total']; 
                }
                $dx['user'] = $user;
                $dx['sites'] = $sites;
                $dx['nocs'] = $nocs;
                $dx['tcta'] = $tcta;
                $dx['tctwa'] = $tctwa;
                $dx['total'] = $total;
                create_inv($dx, $project_id.$bill['id'], $lunas, 1, $yesterday);
                
                $dinv[0]['text']= 'Report';
                $dinv[0]['link']= 'https://space.gass.biz.id/v2/invoice/'.$project_id.$bill['id'].'.xlsx';
                $dinv[0]['data'] = array('project_id' => $project_id, 'bill_id'=>$bill['id'], 'start' => $last_bill, 'end'=> $yesterday);
                $dinv[1]['text']= 'Invoice';
                $dinv[1]['link']= 'https://space.gass.biz.id/v2/invoice/invoice-'.$project_id.$bill['id'].'.pdf';
                $pos = array();
                if($lunas == 1){
                    $user['data']['saldo'] = $user['data']['saldo'] - $total['total'];
                    $pos["act"] = 'bill_edit_data';
                    $pos["status"] = 1; 
                    $pos["bill_id"] = $bill['bill_id'];
                    $pos["id"] = $bill['id'];
                    $pos["nominal"] = $topup;
                    $pos["total"] = $total['total'];
                }else{
                    $pos["act"] = 'bill_pay_bill';
                    $pos["nominal"] = $topup;
                    $pos["total"] = $total['total'];
                    $pos["status"] = 0; 
                    $pos["id"] = $bill['bill_id'];
                    $pos["bill_id"] = $bill['id'];
                }
                $pos['user'] = $user['data'];
                $pos["user_id"] = $user['data']['user_id'];
                $pos["invoice_id"] = $project_id.$bill['id'];
                $pos["project_id"] = $project_id;
                $pos["data"] = json_encode($dinv);
                post_x_contents($pos, 'http://10.104.0.27/api.html');
            }else{
                $msg = $bill;
            }
        }

        $msg['code']=1;        
        $msg['data']['user'] = $user;
        $msg['data']['total'] = $total;
        return $msg;
    }else{
        $msg['code']=0;
        $msg['msg']='bill not set';
        return $msg;
    }
    
}


function get_usage_range($param){
    global $app;
        $rules = [
        "project_id" => "required",
        "start" => "required",
        "end" => "required",
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $pos["act"] = 'bill_get_project_user';
    $pos["project_id"] = $project_id;
    $user = json_decode(post_x_contents($pos, 'http://10.104.0.27/api.html'), true);
    if(isset($user['setting_bill'])){
        $db = $app->db2;
        $last_bill =  $start;  
        //$yesterday =  date('Y-m-d ',strtotime('-1 day', strtotime(date("Y-m-d"))));
        $yesterday =  $end;
        $db->join("visitor_cs vc", "v.visitor_id = vc.visitor_id", "LEFT");
        $db->join("cs c", "vc.cs_key = c.cs_key", "LEFT");
                
        $db->where("DATE(v.created) BETWEEN ? AND ?", array($last_bill, $yesterday));  
        $site = $db->get("visitor v", NULL, "v.site, v.data, DATE(v.created) as waktu");
        $sites = []; 
        foreach ($site as $item) {
            $sit = $item["site"];
            $sit = str_replace("www.","", $item["site"] ?? '');
            $isctwa = false;
        
            if (strpos($item["data"] ?? '', "ctwaClid") !== false) {
                $isctwa = true;
                $sit = 'ctwa';
                $waktu = $item["waktu"];
                if(!isset($sites[$sit]['total'])){
                    $sites[$sit]['total'] = 0;
                }
                if (!isset($sites[$sit]['date'][$waktu])) {
                    $sites[$sit]['date'][$waktu] = 0;
                    $sites[$sit]['total'] ++;
                }
                $sites[$sit]['date'][$waktu]++; 
                $sites[$sit]['total']++;

            }
            if($sit!='' && $sit != 'b-cdn.net' && $isctwa==false){   ///// disable ctwa
                $waktu = $item["waktu"];
                if($sit==''){
                    $sit = 'ctwa';
                }
                if(!isset($sites[$sit]['total'])){
                    $sites[$sit]['total'] = 0;
                }
                if (!isset($sites[$sit]['date'][$waktu])) {
                    $sites[$sit]['date'][$waktu] = 0;
                    $sites[$sit]['total']++;
                }
                $sites[$sit]['date'][$waktu]++;        
            }
        }
        $total_day_site = 0;$total_day_ctwa = 0;
        
        foreach ($sites as $k => $v) {
            if($k != 'ctwa'){
                $total_day_site = $v['total'] + $total_day_site;
            }else{
                $total_day_ctwa = $v['total'] + $total_day_ctwa;
            }            
        }
        $tctwa = []; $total_click_ctwa = 0;
        if(isset($sites['ctwa']['date'])){
            foreach ($sites['ctwa']['date'] as $k => $v) {
                //print_r($k);
                //// hitung ctwa
                
                $tctwa[$k]['date'] = $k; 
                $tctwa[$k]['total'] = $v;     
                $total_click_ctwa += $v;  
            }
            $total_day_ctwa = $total_click_ctwa;
        }
        $total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_click_ctwa;
        
        $total_bill_site = $user['setting_bill']['site'] * $total_day_site;
        //$total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_day_ctwa;
        // hitung bill cs
        $db->where("DATE(tanggal) BETWEEN ? AND ?", array($last_bill, $yesterday));  
        $cs = $db->get("cs_log", NULL, "phone, DATE(tanggal) as waktu, impression, contact, mql, prospek, purchase, value");
        $nocs = []; 
        foreach ($cs as $item) {
            $c = $item["phone"];
            $waktu = $item["waktu"];         
            if(!isset($nocs[$c]['total'])){
                $nocs[$c]['total'] = 0;
            }   
            if (!isset($nocs[$c]['date'][$waktu])) {
                $nocs[$c]['date'][$waktu] = 0;
                $nocs[$c]['total']++;
            }
            $nocs[$c]['date'][$waktu]++;        
        }
        $total_day_cs = 0;
        foreach ($nocs as $k => $v) {
            $total_day_cs = $v['total'] + $total_day_cs;
        }
        $total_bill_cs = $user['setting_bill']['cs'] * $total_day_cs;
        // hitung cta
        $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'unik_cta'", array($last_bill, $yesterday));  
        $cta = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
        $tcta = []; $total_click_cta = 0;
        $last_cta = ''; $last_cta_value = 0;
        foreach ($cta as $item) {
            $c = $item["report_id"];
            $waktu = $item["waktu"];
            $last_cta = $waktu;
            if(!isset($tcta[$c]['total'])){
                $tcta[$c]['total'] = 0;
            }
            if (!isset($tcta[$c]['date'][$waktu])) {
                $tcta[$c]['date'][$waktu] = 0;
                $tcta[$c]['total']++;
            }
            $total_click_cta += $item["report_value"];   
            $tcta[$c]['date'][$waktu] += $item["report_value"];    
            $last_cta_value = $tcta[$c]['date'][$waktu];    
        }
        $total_day_cta = 0;
        foreach ($tcta as $k => $v) {
            $total_day_cta = $v['total'] + $total_day_cta;
        }
        
        
        // hitung lp_view
        $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'lp_view'", array($last_bill, $yesterday));  
        $lp_view = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
        $tlp_view = []; $total_lp_view = 0;
        foreach ($lp_view as $item) {
            $c = $item["report_id"];
            $waktu = $item["waktu"];
            if(!isset($tlp_view[$c]['total'])){
                $tlp_view[$c]['total'] = 0;
            }
            if (!isset($tlp_view['date'][$waktu])) {
                $tlp_view['date'][$waktu] = 0;
                $tlp_view['total']++;
            }
            $tlp_view['date'][$waktu] += $item["report_value"];     
            $total_lp_view += $item["report_value"];      
        }
        $total_day_lp_view = 0;
        foreach ($tlp_view as $k => $v) {
            $total_day_lp_view= $v['total'] + $total_day_lp_view;
        }
        
        $total_bill_cta = $user['setting_bill']['lead'] * $total_click_cta;
        $total['total_bill_site'] = $total_bill_site;
        $total['total_bill_ctwa'] = $total_bill_ctwa;
        $total['total_bill_cs'] = $total_bill_cs;
        $total['total_bill_cta'] = $total_bill_cta;
        $total['total_day_site'] = $total_day_site;
        $total['total_day_ctwa'] = $total_day_ctwa;
        $total['total_day_cs'] = $total_day_cs;
        $total['total_day_cta'] = $total_day_cta;
        $total['total_click_cta'] = $total_click_cta;
        $total['total_click_ctwa'] = $total_click_ctwa;
        $total['total'] = $total_bill_ctwa + $total_bill_site + $total_bill_cs + $total_bill_cta;


        $total["project_id"] = $project_id;
        $total["cta"] = $last_cta_value;
        $total["lp_view"] = $total_lp_view;
        $total["last_cta"] = $last_cta;


        $msg['code']=1;        
        //$msg['data']['user'] = $user;
//        $msg['data']['sites'] = $sites;
//        $msg['data']['nocs'] = $nocs;
        $msg['data']['tcta'] = $tcta;
        $msg['data']['tlp_view'] = $tlp_view;
        $msg['data']['total'] = $total;
        $msg['data']['start'] = $last_bill;
        $msg['data']['end'] = $yesterday;
        return $msg;
    }else{
        $msg['code']=0;
        $msg['msg']='bill not set';
        return $msg;
    }
    
}

function get_usage_new($param){
    global $app;
    $rules = [
        "project_id" => "required",
        "token" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $pos["act"] = 'bill_get_project_user';
    $pos["project_id"] = $project_id;
    $user = json_decode(post_x_contents($pos, 'http://10.104.0.27/api.html'), true);
    if(isset($user['setting_bill'])){
        $db = $app->db2;
        $last_bill = date('Y-m-d', strtotime($user['data']['last_bill']));
        $yesterday = date('Y-m-d', strtotime('-1 day'));

        // visitor + ctwa total query
        $sql = "
            SELECT 
                CASE 
                    WHEN v.data LIKE '%ctwaClid%' THEN 'ctwa'
                    WHEN v.site IS NULL OR v.site = '' THEN 'ctwa'
                    ELSE REPLACE(v.site, 'www.', '')
                END as site_name,
                DATE(v.created) as waktu,
                COUNT(*) as total
            FROM visitor v
            LEFT JOIN visitor_cs vc ON v.visitor_id = vc.visitor_id
            LEFT JOIN cs c ON vc.cs_key = c.cs_key
            WHERE DATE(v.created) BETWEEN ? AND ?
            AND (v.site != 'b-cdn.net' OR v.site IS NULL)
            GROUP BY site_name, waktu
        ";

        $siteData = $db->rawQuery($sql, [$last_bill, $yesterday]);

        // proses hasil visitor
        $sites = [];
        $total_click_ctwa = 0;
        $total_day_site = 0;

        foreach ($siteData as $item) {
            $sit = $item['site_name'];
            $waktu = $item['waktu'];
            $total = (int)$item['total'];

            if (!isset($sites[$sit])) {
                $sites[$sit] = ['total' => 0, 'date' => []];
            }

            $sites[$sit]['date'][$waktu] = $total;
            $sites[$sit]['total'] += $total;

            if ($sit === 'ctwa') {
                $total_click_ctwa += $total;
            } else {
                $total_day_site += $total;
            }
        }

        $total_day_ctwa = $total_click_ctwa;

        // cs_log total
        $sql = "
            SELECT phone, DATE(tanggal) as waktu, COUNT(*) as total
            FROM cs_log
            WHERE DATE(tanggal) BETWEEN ? AND ?
            GROUP BY phone, waktu
        ";

        $csData = $db->rawQuery($sql, [$last_bill, $yesterday]);

        $total_day_cs = 0;
        foreach ($csData as $item) {
            $total_day_cs += $item['total'];
        }

        // report_data CTA
        $sql = "
            SELECT report_id, DATE(date) as waktu, SUM(report_value) as total
            FROM report_data
            WHERE DATE(date) BETWEEN ? AND ? AND report_key = 'unik_cta'
            GROUP BY report_id, waktu
        ";

        $ctaData = $db->rawQuery($sql, [$last_bill, $yesterday]);

        $total_click_cta = 0;
        $total_day_cta = 0;
        foreach ($ctaData as $item) {
            $total_click_cta += $item['total'];
            $total_day_cta++;
        }

        // report_data LP View
        $sql = "
            SELECT report_id, DATE(date) as waktu, SUM(report_value) as total
            FROM report_data
            WHERE DATE(date) BETWEEN ? AND ? AND report_key = 'lp_view'
            GROUP BY report_id, waktu
        ";

        $lpViewData = $db->rawQuery($sql, [$last_bill, $yesterday]);

        $total_lp_view = 0;
        $total_day_lp_view = 0;
        foreach ($lpViewData as $item) {
            $total_lp_view += $item['total'];
            $total_day_lp_view++;
        }

        // perhitungan billing
        $total_bill_site = $user['setting_bill']['site'] * $total_day_site;
        $total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_click_ctwa;
        $total_bill_cs = $user['setting_bill']['cs'] * $total_day_cs;
        $total_bill_cta = $user['setting_bill']['lead'] * $total_click_cta;

        $total = [
            'total_bill_site' => $total_bill_site,
            'total_bill_ctwa' => $total_bill_ctwa,
            'total_bill_cs'   => $total_bill_cs,
            'total_bill_cta'  => $total_bill_cta,
            'total_day_site'  => $total_day_site,
            'total_day_ctwa'  => $total_day_ctwa,
            'total_day_cs'    => $total_day_cs,
            'total_day_cta'   => $total_day_cta,
            'total_click_cta' => $total_click_cta,
            'total_click_ctwa'=> $total_click_ctwa,
            'total'           => $total_bill_site + $total_bill_ctwa + $total_bill_cs + $total_bill_cta
        ];

        $alert_nominal = $user['data']['billing_alert'] - 5000;
        $create_bill = false;
        if($user['data']['billing_day'] > 0){
            if((int)date("d") == (int)$user['data']['billing_day']) {
                $date1 = new DateTime($user['data']['last_bill']);
                $date2 = new DateTime(date("Y-m-d"));
                $interval = $date1->diff($date2);
                if ($interval->days > 0) {
                    $create_bill = true;
                }else{
                    $msg['msg']='interval day billing belum memenuhi';
                }
            }else{
                $msg['msg']='day billing belum memenuhi';
            }
        }else{
            if($total['total'] >= $alert_nominal){
                $create_bill = true;
            }else{
                $msg['msg']='minimal billing belum memenuhi';
            }
        }
        if($create_bill == true){
            $db1 = $app->db;
            $db1->where("code = ?", array('bill_'.$project_id));  
            $res = $db1->getone("cron");
            if ($db1->count > 0) {
                $rex['res'] = [];
                $rex['type'] = 'cron';
                $rex['webhook'] = $res['url'] ;
                $pos['msg'] = json_encode($rex);
                $res = post_x_contents($pos, 'https://que-fb1.gass.co.id/add-task');
            }            
        }

        $msg['code']=1;        
        //$msg['data']['user'] = $user;
//        $msg['data']['sites'] = $sites;
//        $msg['data']['nocs'] = $nocs;
        //$msg['data']['tcta'] = $tcta;
        $msg['data']['total'] = $total;
        $msg['data']['start'] = $last_bill;
        $msg['data']['end'] = $yesterday;
    }else{
        $msg['code']=0;
        $msg['msg']='bill not set';
    }
    return $msg;
}

function get_usage($param){
    global $app;
    $rules = [
        "project_id" => "required",
        "token" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    $memcached = new Memcached();
    $memcached->addServer('127.0.0.1', 11211);
    if (extension_loaded('memcached')) {
        $mem_key = 'BillGetByKey_' . $project_id;
        if(isset($disable_cache) && $disable_cache == 1){
            $bill_get = [];
        }else{  
            $bill_get = $memcached->get($mem_key);
            if (!empty($bill_get)) {
                return $bill_get;
            }
        }       
    } 
    assign_child_db($project_id);
    $pos["act"] = 'bill_get_project_user';
    $pos["project_id"] = $project_id;
    $user = json_decode(post_x_contents($pos, 'http://10.104.0.27/api.html'), true);
    if(isset($user['setting_bill'])){
        $db = $app->db2;
        $last_bill =  date('Y-m-d', strtotime($user['data']['last_bill']));  
        //$last_bill =  date('Y-m-d', strtotime('+1 day', strtotime($user['data']['last_bill'])));  
        //$yesterday =  date('Y-m-d ',strtotime('-1 day', strtotime(date("Y-m-d"))));

        $yesterday =  date('Y-m-d', strtotime('-1 day', strtotime(date("Y-m-d"))));
        //$yesterday =  date('Y-m-d', strtotime(date("Y-m-d")));
        $db->join("visitor_cs vc", "v.visitor_id = vc.visitor_id", "LEFT");
        $db->join("cs c", "vc.cs_key = c.cs_key", "LEFT");
                
        $db->where("DATE(v.created) BETWEEN ? AND ?", array($last_bill, $yesterday));  
        $site = $db->get("visitor v", NULL, "v.site, v.data, DATE(v.created) as waktu");
        $sites = []; 
        foreach ($site as $item) {
            $sit = $item["site"];
            $sit = str_replace("www.","", $item["site"] ?? '');
            $isctwa = false;
            if (strpos($item["data"] ?? '', "ctwaClid") !== false) {
                $isctwa = true;
                $sit = 'ctwa';
                $waktu = $item["waktu"];
                if(!isset($sites[$sit]['total'])){
                    $sites[$sit]['total'] = 0;
                }
                if (!isset($sites[$sit]['date'][$waktu])) {
                    $sites[$sit]['date'][$waktu] = 0;
                    $sites[$sit]['total'] ++;
                }
                $sites[$sit]['date'][$waktu]++; 
                $sites[$sit]['total']++;
            }
            if($sit!='' && $sit != 'b-cdn.net' && $isctwa==false){   ///// disable ctwa
                $waktu = $item["waktu"];
                if($sit==''){
                    $sit = 'ctwa';
                }
                if(!isset($sites[$sit]['total'])){
                    $sites[$sit]['total'] = 0;
                }
                if (!isset($sites[$sit]['date'][$waktu])) {
                    $sites[$sit]['date'][$waktu] = 0;
                    $sites[$sit]['total']++;
                }
                $sites[$sit]['date'][$waktu]++;        
            }
        }
        $total_day_site = 0;$total_day_ctwa = 0;
        foreach ($sites as $k => $v) {
            if($k != 'ctwa'){
                $total_day_site = $v['total'] + $total_day_site;
            }else{
                $total_day_ctwa = $v['total'] + $total_day_ctwa;
            }            
        }
        $tctwa = []; $total_click_ctwa = 0;
        if(isset($sites['ctwa']['date'])){
            foreach ($sites['ctwa']['date'] as $k => $v) {
                //print_r($k);
                //// hitung ctwa
                
                $tctwa[$k]['date'] = $k; 
                $tctwa[$k]['total'] = $v;     
                $total_click_ctwa += $v;  
            }
            $total_day_ctwa = $total_click_ctwa;
        }
        $total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_click_ctwa;

        $total_bill_site = $user['setting_bill']['site'] * $total_day_site;
        //$total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_day_ctwa;
        // hitung bill cs
        $db->where("DATE(tanggal) BETWEEN ? AND ?", array($last_bill, $yesterday));  
        $cs = $db->get("cs_log", NULL, "phone, DATE(tanggal) as waktu, impression, contact, mql, prospek, purchase, value");
        $nocs = []; 
        foreach ($cs as $item) {
            $c = $item["phone"];
            $waktu = $item["waktu"];
            if(!isset($nocs[$c]['total'])){
                $nocs[$c]['total'] = 0;
            }
            if (!isset($nocs[$c]['date'][$waktu])) {
                $nocs[$c]['date'][$waktu] = 0;
                $nocs[$c]['total']++;
            }
            $nocs[$c]['date'][$waktu]++;        
        }
        $total_day_cs = 0;
        foreach ($nocs as $k => $v) {
            $total_day_cs = $v['total'] + $total_day_cs;
        }
        $total_bill_cs = $user['setting_bill']['cs'] * $total_day_cs;
        // hitung cta
        $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'unik_cta'", array($last_bill, $yesterday));  
        $cta = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
        $tcta = []; $total_click_cta = 0;
        $last_cta = ''; $last_cta_value = 0;
        foreach ($cta as $item) {
            $c = $item["report_id"];
            $waktu = $item["waktu"];
            $last_cta = $waktu;
            if(!isset($tcta[$c]['total'])){
                $tcta[$c]['total'] = 0;
            }
            if (!isset($tcta[$c]['date'][$waktu])) {
                $tcta[$c]['date'][$waktu] = 0;
                $tcta[$c]['total']++;
            }
            $total_click_cta += $item["report_value"];   
            $tcta[$c]['date'][$waktu] += $item["report_value"];    
            $last_cta_value = $tcta[$c]['date'][$waktu];    
        }
        $total_day_cta = 0;
        foreach ($tcta as $k => $v) {
            $total_day_cta = $v['total'] + $total_day_cta;
        }
        
        // hitung lp_view
        $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'lp_view'", array($last_bill, $yesterday));  
        $lp_view = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
        $tlp_view = []; $total_lp_view = 0;
        foreach ($lp_view as $item) {
            $c = $item["report_id"];
            $waktu = $item["waktu"];
            if(!isset($tlp_view[$c]['total'])){
                $tlp_view[$c]['total'] = 0;
            }
            if (!isset($tlp_view[$c]['date'][$waktu])) {
                $tlp_view[$c]['date'][$waktu] = 0;
                $tlp_view[$c]['total']++;
            }
            $tlp_view[$c]['date'][$waktu] += $item["report_value"];       
            $total_lp_view += $item["report_value"];   
        }
        $total_day_lp_view = 0;
        foreach ($tlp_view as $k => $v) {
            $total_day_lp_view= $v['total'] + $total_day_lp_view;
        }
        
        $total_bill_cta = $user['setting_bill']['lead'] * $total_click_cta;
        $total['total_bill_site'] = $total_bill_site;
        $total['total_bill_ctwa'] = $total_bill_ctwa;
        $total['total_bill_cs'] = $total_bill_cs;
        $total['total_bill_cta'] = $total_bill_cta;
        $total['total_day_site'] = $total_day_site;
        $total['total_day_ctwa'] = $total_day_ctwa;
        $total['total_day_cs'] = $total_day_cs;
        $total['total_day_cta'] = $total_day_cta;
        $total['total_click_cta'] = $total_click_cta;
        $total['total_click_ctwa'] = $total_click_ctwa;
        $total['total'] = $total_bill_ctwa + $total_bill_site + $total_bill_cs + $total_bill_cta;
        $alert_nominal = $user['data']['billing_alert'] - 5000;
        $create_bill = false;
        if($user['data']['billing_day'] > 0){
            if((int)date("d") == (int)$user['data']['billing_day']) {
                $date1 = new DateTime($user['data']['last_bill']);
                $date2 = new DateTime(date("Y-m-d"));
                $interval = $date1->diff($date2);
                if ($interval->days > 0) {
                    $create_bill = true;
                }else{
                    $msg['msg']='interval day billing belum memenuhi';
                }
            }else{
                $msg['msg']='day billing belum memenuhi';
            }
        }else{
            if($total['total'] >= $alert_nominal){
                $create_bill = true;
            }else{
                $msg['msg']='minimal billing belum memenuhi';
            }
        }
        if($create_bill == true){
            $db1 = $app->db;
            $db1->where("code = ?", array('bill_'.$project_id));  
            $res = $db1->getone("cron");
            if ($db1->count > 0) {
                $rex['res'] = [];
                $rex['type'] = 'cron';
                $rex['webhook'] = $res['url'] ;
                $pos['msg'] = json_encode($rex);
                $res = post_x_contents($pos, 'https://que-fb1.gass.co.id/add-task');
            }            
        }

        $msg['code']=1;        
        //$msg['data']['user'] = $user;
//        $msg['data']['sites'] = $sites;
//        $msg['data']['nocs'] = $nocs;
        //$msg['data']['tcta'] = $tcta;
        $msg['data']['total'] = $total;
        $msg['data']['start'] = $last_bill;
        $msg['data']['end'] = $yesterday;
    }else{
        $msg['code']=0;
        $msg['msg']='bill not set';
    }
    if (empty($bill_get)) {
        $expiration = 3600 * 24 * 1;
        if (extension_loaded('memcached')) {
            $memcached->set($mem_key, $msg, $expiration);
        }
    }
    return $msg;
}

function get($param){
    global $app;
    extract($param);
    // $memcached = new Memcached();
    // $memcached->addServer('127.0.0.1', 11211);
    // if (extension_loaded('memcached')) {
    //     $mem_key = 'BillGetByKey_' . $project_id;
    //     $bill_get = $memcached->get($mem_key);
    //     if (!empty($bill_get)) {
    //         return $bill_get;
    //     }
    // }    
    assign_child_db($project_id);
    $pos["act"] = 'bill_get_project_user';
    $pos["project_id"] = $project_id;
    $user = json_decode(post_x_contents($pos, 'http://10.104.0.27/api.html'), true);
    if(isset($user['setting_bill'])){
        $db = $app->db2;
        //$last_bill =  date('Y-m-d', strtotime('+1 day', strtotime($user['data']['last_bill'])));  
        $last_bill =  date('Y-m-d', strtotime($user['data']['last_bill']));  
        $yesterday =  date('Y-m-d', strtotime('-1 day', strtotime(date("Y-m-d"))));
        $db->join("visitor_cs vc", "v.visitor_id = vc.visitor_id", "LEFT");
        $db->join("cs c", "vc.cs_key = c.cs_key", "LEFT");
                
        $db->where("DATE(v.created) BETWEEN ? AND ?", array($last_bill, $yesterday));  
        $site = $db->get("visitor v", NULL, "v.site, v.data, DATE(v.created) as waktu");
        $sites = []; 
        foreach ($site as $item) {
            $sit = $item["site"];
            $sit = str_replace("www.","", $sit);
            if (strpos($item["data"] ?? '', "ctwaClid") !== false) {
                $isctwa = true;
                $sit = 'ctwa';
                $waktu = $item["waktu"];
                if(!isset($sites[$sit]['total'])){
                    $sites[$sit]['total'] = 0;
                }
                if (!isset($sites[$sit]['date'][$waktu])) {
                    $sites[$sit]['date'][$waktu] = 0;
                    $sites[$sit]['total'] ++;
                }
                $sites[$sit]['date'][$waktu]++; 
                $sites[$sit]['total']++;
            }
            if($sit!='' && $sit != 'b-cdn.net' && $isctwa==false){   ///// disable ctwa
                $waktu = $item["waktu"];
                if($sit==''){
                    $sit = 'ctwa';
                }
                if(!isset($sites[$sit]['total'])){
                    $sites[$sit]['total'] = 0;
                }
                if (!isset($sites[$sit]['date'][$waktu])) {
                    $sites[$sit]['date'][$waktu] = 0;
                    $sites[$sit]['total']++;
                }
                $sites[$sit]['date'][$waktu]++;        
            }      
        }
        $total_day_site = 0;$total_day_ctwa = 0;
        foreach ($sites as $k => $v) {
            if($k != 'ctwa'){
                $total_day_site = $v['total'] + $total_day_site;
            }else{
                $total_day_ctwa = $v['total'] + $total_day_ctwa;
            }            
        }
        $tctwa = []; $total_click_ctwa = 0;
        if(isset($sites['ctwa']['date'])){
            foreach ($sites['ctwa']['date'] as $k => $v) {
                //print_r($k);
                //// hitung ctwa
                
                $tctwa[$k]['date'] = $k; 
                $tctwa[$k]['total'] = $v;     
                $total_click_ctwa += $v;  
            }
            $total_day_ctwa = $total_click_ctwa;
        }
        $total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_click_ctwa;
        
        $total_bill_site = $user['setting_bill']['site'] * $total_day_site;
        //$total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_day_ctwa;
        // hitung bill cs
        //$db->where("DATE(tanggal) BETWEEN ? AND ?", array($last_bill, $yesterday));  
        //$cs = $db->get("cs_log", NULL, "phone, DATE(tanggal) as waktu, impression, contact, mql, prospek, purchase, value");
        $nocs = []; 
        // foreach ($cs as $item) {
        //     $c = $item["phone"];
        //     $waktu = $item["waktu"];
        //     if(!isset($nocs[$c]['total'])){
        //         $nocs[$c]['total'] = 0;
        //     }
        //     if (!isset($nocs[$c]['date'][$waktu])) {
        //         $nocs[$c]['date'][$waktu] = 0;
        //         $nocs[$c]['total']++;
        //     }
        //     $nocs[$c]['date'][$waktu]++;        
        // }
        $total_day_cs = 0;
        foreach ($nocs as $k => $v) {
            $total_day_cs = $v['total'] + $total_day_cs;
        }
        $total_bill_cs = $user['setting_bill']['cs'] * $total_day_cs;
        // hitung cta
        $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'unik_cta'", array($last_bill, $yesterday));  
        $db->orderBy("date","Asc");
        $cta = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
        $tcta = []; $total_click_cta = 0;
        $last_cta = ''; $last_cta_value = 0;
        foreach ($cta as $item) {
            $c = $item["report_id"];
            $waktu = $item["waktu"];
            $last_cta = $waktu;
            if(!isset($tcta[$c]['total'])){
                $tcta[$c]['total'] = 0;
            }
            if (!isset($tcta[$c]['date'][$waktu])) {
                $tcta[$c]['date'][$waktu] = 0;
                $tcta[$c]['total']++;
            }
            $tcta[$c]['date'][$waktu] += $item["report_value"];    
            $total_click_cta += $item["report_value"];  
            $last_cta_value = $tcta[$c]['date'][$waktu];
        }
        $total_day_cta = 0;
        foreach ($tcta as $k => $v) {
            $total_day_cta = $v['total'] + $total_day_cta;
        }
        
        // hitung lp_view
        $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'lp_view'", array($last_bill, $yesterday));  
        $lp_view = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
        $tlp_view = []; $total_lp_view = 0;
        foreach ($lp_view as $item) {
            $c = $item["report_id"];
            $waktu = $item["waktu"];
            if(!isset($tlp_view[$c]['total'])){
                $tlp_view[$c]['total'] = 0;
            }
            if (!isset($tlp_view[$c]['date'][$waktu])) {
                $tlp_view[$c]['date'][$waktu] = 0;
                $tlp_view[$c]['total']++;
            }
            $tlp_view[$c]['date'][$waktu] += $item["report_value"];  
            $total_lp_view += $item["report_value"];  
        }
        $total_day_lp_view = 0;
        foreach ($tlp_view as $k => $v) {
            $total_day_lp_view= $v['total'] + $total_day_lp_view;
        }
        
        $total_bill_cs = $user['setting_bill']['cs_waba']['total_tagihan_cs'];

        $total_bill_cta = $user['setting_bill']['lead'] * $total_click_cta;
        $total['total_bill_site'] = $total_bill_site;
        $total['total_bill_ctwa'] = $total_bill_ctwa;
        $total['total_bill_cs'] = $total_bill_cs;
        $total['total_bill_cta'] = $total_bill_cta;
        $total['total_day_site'] = $total_day_site;
        $total['total_day_ctwa'] = $total_day_ctwa;
        $total['total_day_cs'] = $total_day_cs;
        $total['total_day_cta'] = $total_day_cta;
        $total['total_click_cta'] = $total_click_cta;
        $total['total_click_ctwa'] = $total_click_ctwa;
        $total['total'] = $total_bill_ctwa + $total_bill_site + $total_bill_cs + $total_bill_cta;
        $alert_nominal = $user['data']['billing_alert'] - 5000;
        $create_bill = false;
        // cek minimal billing
        if(isset($force_bill) && $force_bill == 1){
            $create_bill = true;
        }else{
            if($user['data']['billing_day'] > 0){
                if((int)date("d") == (int)$user['data']['billing_day']) {
                    if($total['total'] > 0){
                        $create_bill = true;
                    }else{
                        $msg['msg']='minimal billing belum memenuhi';
                    }
                    // $date1 = new DateTime($user['data']['last_bill']);
                    // $date2 = new DateTime(date("Y-m-d"));
                    // $interval = $date1->diff($date2);
                    // if($interval->d > 0){
                    //     if($total['total'] >= 0){
                    //         $create_bill = true;
                    //     }else{
                    //         $msg['msg']='minimal billing belum memenuhi';
                    //     }
                    // }else{
                    //     $msg['interval'] = $interval->d;
                    //     $msg['last_bill'] = $user['data']['last_bill'];
                    //     $msg['billing_day'] = $user['data']['billing_day'];
                    //     $msg['msg']='interval day billing belum memenuhi';
                    // }
                    $msg['last_bill'] = $user['data']['last_bill'];
                    $msg['billing_day'] = $user['data']['billing_day'];
                }else{
                    $msg['msg']='day billing belum memenuhi';
                }
            }else{
                if($total['total'] >= $alert_nominal){
                    $create_bill = true;
                }else{
                    $msg['msg']='minimal billing belum memenuhi';
                }
            }
        }        

        if($create_bill == true){
            $pos["act"] = 'bill_create';
            $pos["user_id"] = $user['data']['user_id'];
            $pos["project_id"] = $project_id;
            $pos["keterangan"] = "Billing project ".$user['data']['name'];
            $pos["total"] = $total['total'];
            $pos["last_bill"] = $yesterday;
            $pos["data"] = '{}';
            $bill = json_decode(post_x_contents($pos, 'http://10.104.0.27/api.html'), true);
            if($bill['code']==1){
                $lunas = 0; $topup = 0;
                if($total['total'] <= $user['data']['saldo']){
                    //pay bill lunas  
                    $lunas = 1;
                }else{
                    $topup = $total['total']; 
//                    if($user['data']['count_bill'] > 0){
//                        $topup = $total['total'];
//                    }else{
//                        $topup = $total['total'] - $user['data']['saldo'];
//                    }
                }
                $dx['user'] = $user;
                $dx['sites'] = $sites;
                $dx['nocs'] = $nocs;
                $dx['tcta'] = $tcta;
                $dx['tctwa'] = $tctwa;
                $dx['total'] = $total;
                create_inv($dx, $project_id.$bill['id'], $lunas, 1);
                
                $dinv[0]['text']= 'Report';
                $dinv[0]['link']= 'https://space.gass.biz.id/v2/invoice/'.$project_id.$bill['id'].'.xlsx';
                $dinv[0]['data'] = array('project_id' => $project_id, 'bill_id'=>$bill['id'], 'start' => $last_bill, 'end'=> $yesterday);
                $dinv[1]['text']= 'Invoice';
                $dinv[1]['link']= 'https://space.gass.biz.id/v2/invoice/invoice-'.$project_id.$bill['id'].'.pdf';
                $pos = array();
                if($lunas == 1){
                    $user['data']['saldo'] = $user['data']['saldo'] - $total['total'];
                    $pos["act"] = 'bill_edit_data';
                    $pos["status"] = 1; 
                    $pos["bill_id"] = $bill['bill_id'];
                    $pos["id"] = $bill['id'];
                    $pos["nominal"] = $topup;
                    $pos["total"] = $total['total'];
                }else{
                    $pos["act"] = 'bill_pay_bill';
                    $pos["nominal"] = $topup;
                    $pos["total"] = $total['total'];
                    $pos["status"] = 0; 
//                    $dinv[2]['button']= 'Topup';
//                    $dinv[2]['bill_id']= $bill['id'];
//                    $dinv[2]['nominal']= $topup;
                    $pos["id"] = $bill['bill_id'];
                    $pos["bill_id"] = $bill['id'];
                }
                $pos['user'] = $user['data'];
                $pos["user_id"] = $user['data']['user_id'];
                $pos["invoice_id"] = $project_id.$bill['id'];
                $pos["project_id"] = $project_id;
                $pos["data"] = json_encode($dinv);
                post_x_contents($pos, 'http://10.104.0.27/api.html');
            }else{
                $msg = $bill;
            }
        }
        $msg['code']=1;        
        $msg['data']['user'] = $user;
//        $msg['data']['sites'] = $sites;
//        $msg['data']['nocs'] = $nocs;
        //$msg['data']['tcta'] = $tcta;
        $msg['data']['total'] = $total;
        $msg['last_cta'] = $last_cta;
        $msg['last_cta_value'] = $last_cta_value;
        
        
        //$pos["act"] = 'internal_grab_stat';
        //$pos["project_id"] = $project_id;
        // $pos["cta"] = $last_cta_value;
        // $pos["lp_view"] = $total_lp_view;
        // $pos["tanggal"] = $last_cta;
        //post_x_contents($pos, 'http://10.104.0.27/api.html');
        if(isset($param['source']) && $param['source'] == 'cloudflare'){
            unset($msg['data']);
        }
    }else{
        $msg['code']=0;
        $msg['msg']='bill not set';
    }
    // if (empty($bill_get)) {
    //     $expiration = 3600 * 24 * 1;
    //     if (extension_loaded('memcached')) {
    //         $memcached->set($mem_key, $msg, $expiration);
    //     }
    // }
    return $msg;
}

function get_usage_range_new($param){
    global $app;
        $rules = [
        "project_id" => "required",
        "start" => "required",
        "end" => "required",
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $pos["act"] = 'bill_get_project_user';
    $pos["project_id"] = $project_id;
    $user = json_decode(post_x_contents($pos, 'http://10.104.0.27/api.html'), true);
    if(isset($user['setting_bill'])){
        $db = $app->db2;
        $last_bill =  $start;  
        $yesterday =  $end;
        $db->join("visitor_cs vc", "v.visitor_id = vc.visitor_id", "LEFT");
        $db->join("cs c", "vc.cs_key = c.cs_key", "LEFT");
                
        $db->where("DATE(v.created) BETWEEN ? AND ?", array($last_bill, $yesterday));  
        $site = $db->get("visitor v", NULL, "v.site, v.data, DATE(v.created) as waktu");
        $sites = []; 
        foreach ($site as $item) {
            $sit = $item["site"];
            $sit = str_replace("www.","", $item["site"] ?? '');
            $isctwa = false;
        
            if (strpos($item["data"] ?? '', "ctwaClid") !== false) {
                $isctwa = true;
                $sit = 'ctwa';
                $waktu = $item["waktu"];
                if(!isset($sites[$sit]['total'])){
                    $sites[$sit]['total'] = 0;
                }
                if (!isset($sites[$sit]['date'][$waktu])) {
                    $sites[$sit]['date'][$waktu] = 0;
                    $sites[$sit]['total'] ++;
                }
                $sites[$sit]['date'][$waktu]++; 
                $sites[$sit]['total']++;
                
                   
            }
            if($sit!='' && $sit != 'b-cdn.net' && $isctwa==false){   ///// disable ctwa
                $waktu = $item["waktu"];
                if($sit==''){
                    $sit = 'ctwa';
                }
                if(!isset($sites[$sit]['total'])){
                    $sites[$sit]['total'] = 0;
                }
                if (!isset($sites[$sit]['date'][$waktu])) {
                    $sites[$sit]['date'][$waktu] = 0;
                    $sites[$sit]['total']++;
                }
                $sites[$sit]['date'][$waktu]++;        
            }
        }
        $total_day_site = 0;$total_day_ctwa = 0;
        
        foreach ($sites as $k => $v) {
            if($k != 'ctwa'){
                $total_day_site = $v['total'] + $total_day_site;
            }else{
                $total_day_ctwa = $v['total'] + $total_day_ctwa;
            }            
        }
        $tctwa = []; $total_click_ctwa = 0;
        if(isset($sites['ctwa']['date'])){
            foreach ($sites['ctwa']['date'] as $k => $v) {
                //print_r($k);
                //// hitung ctwa
                
                $tctwa[$k]['date'] = $k; 
                $tctwa[$k]['total'] = $v;     
                $total_click_ctwa += $v;  
            }
            $total_day_ctwa = $total_click_ctwa;
        }
        $total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_click_ctwa;
        
        $total_bill_site = $user['setting_bill']['site'] * $total_day_site;
        //$total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_day_ctwa;
        // hitung bill cs
        $db->where("DATE(tanggal) BETWEEN ? AND ?", array($last_bill, $yesterday));  
        $cs = $db->get("cs_log", NULL, "phone, DATE(tanggal) as waktu, impression, contact, mql, prospek, purchase, value");
        $nocs = []; 
        foreach ($cs as $item) {
            $c = $item["phone"];
            $waktu = $item["waktu"];
            if(!isset($nocs[$c]['total'])){
                $nocs[$c]['total'] = 0;
            }
            if (!isset($nocs[$c]['date'][$waktu])) {
                $nocs[$c]['date'][$waktu] = 0;
                $nocs[$c]['total']++;
            }
            $nocs[$c]['date'][$waktu]++;        
        }
        $total_day_cs = 0;
        foreach ($nocs as $k => $v) {
            $total_day_cs = $v['total'] + $total_day_cs;
        }
        $total_bill_cs = $user['setting_bill']['cs'] * $total_day_cs;
        // hitung cta
        $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'unik_cta'", array($last_bill, $yesterday));  
        $db->orderBy("date","Asc");
        $cta = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
        $tcta = []; $total_click_cta = 0;$tcta_date = [];
        $last_cta = ''; $last_cta_value = 0;
        foreach ($cta as $item) {
            $c = $item["report_id"];
            $waktu = $item["waktu"];
            $last_cta = $waktu;
            if(!isset($tcta[$c]['total'])){
                $tcta[$c]['total'] = 0;
            }
            if (!isset($tcta[$c]['date'][$waktu])) {
                $tcta[$c]['date'][$waktu] = 0;
                $tcta[$c]['total']++;
            }
            if (!isset($tcta_date[$waktu])) {
                $tcta_date[$waktu] = 0;
            }
            $tcta_date[$waktu] += $item["report_value"]; 
            $tcta[$c]['date'][$waktu] += $item["report_value"];    
            $total_click_cta += $item["report_value"];  
            $last_cta_value = $tcta[$c]['date'][$waktu];
        }
        $total_day_cta = 0;
        foreach ($tcta as $k => $v) {
            $total_day_cta = $v['total'] + $total_day_cta;
        }
        
        // hitung lp_view
        $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'lp_view'", array($last_bill, $yesterday));  
        $lp_view = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
        $tlp_view = []; $total_lp_view = 0;$tlp_view_date = [];
        foreach ($lp_view as $item) {
            $c = $item["report_id"];
            $waktu = $item["waktu"];
            if(!isset($tlp_view[$c]['total'])){
                $tlp_view[$c]['total'] = 0;
            }
            if (!isset($tlp_view[$c]['date'][$waktu])) {
                $tlp_view[$c]['date'][$waktu] = 0;
                $tlp_view[$c]['total']++;
            }
            if (!isset($tlp_view_date[$waktu])) {
                $tlp_view_date[$waktu] = 0;
            }
            $tlp_view_date[$waktu] += $item["report_value"];  
            $tlp_view[$c]['date'][$waktu] += $item["report_value"];  
            $total_lp_view += $item["report_value"];  
        }
        $total_day_lp_view = 0;
        foreach ($tlp_view as $k => $v) {
            $total_day_lp_view= $v['total'] + $total_day_lp_view;
        }
        
        $total_bill_cta = $user['setting_bill']['lead'] * $total_click_cta;
        $total['total_bill_site'] = $total_bill_site;
        $total['total_bill_ctwa'] = $total_bill_ctwa;
        $total['total_bill_cs'] = $total_bill_cs;
        $total['total_bill_cta'] = $total_bill_cta;
        $total['total_day_site'] = $total_day_site;
        $total['total_day_ctwa'] = $total_day_ctwa;
        $total['total_day_cs'] = $total_day_cs;
        $total['total_day_cta'] = $total_day_cta;
        $total['total_click_cta'] = $total_click_cta;
        $total['total_click_ctwa'] = $total_click_ctwa;
        $total['total'] = $total_bill_ctwa + $total_bill_site + $total_bill_cs + $total_bill_cta;
        $alert_nominal = $user['data']['billing_alert'] - 5000;
        $create_bill = false;
        // cek minimal billing
        $msg['code']=1;        
        //$msg['data']['user'] = $user;

        $msg['data']['tcta_date'] = $tcta_date;
        $msg['data']['tlp_view_date'] = $tlp_view_date;
        
        $msg['data']['total'] = $total;
        $msg['last_cta'] = $last_cta;
        $msg['last_cta_value'] = $last_cta_value;
        
        return $msg;
    }else{
        $msg['code']=0;
        $msg['msg']='bill not set';
        return $msg;
    }
    
}


function create_inv($data, $inv_id, $lunas=0, $disable_cs=1, $bill_date = ''){    
    if(isset($data['sites'])){
        $spreadsheet = new Spreadsheet();
        $worksheet = $spreadsheet->getActiveSheet();
        $worksheet->setTitle('SITE');
        $worksheet->setCellValue('A1', "Site");
        $worksheet->setCellValue('B1', 'Date');
        $worksheet->setCellValue('C1', 'Visitor');  
        $siteIndex = 2;
        foreach ($data['sites'] as $sheetName => $sheetData) {
            $worksheet->setCellValue('A'.$siteIndex, $sheetName);
            $sheetData = $sheetData['date'];
            foreach ($sheetData as $date => $value) {
                $worksheet->setCellValue('B' . $siteIndex, $date);
                $worksheet->setCellValue('C' . $siteIndex, $value);
                $siteIndex ++;
            }
            $siteIndex ++;
        }
    }
    
    if(isset($data['nocs']) && $disable_cs==0){
        $worksheet1 = $spreadsheet->createSheet();
        $worksheet1->setTitle('CS');
        $worksheet1->setCellValue('A1', "No. CS");
        $worksheet1->setCellValue('B1', 'Date');
        $csIndex = 2;
        foreach ($data['nocs'] as $sheetName => $sheetData) {
            $worksheet1->setCellValue('A'.$csIndex, $sheetName);
            $sheetData = $sheetData['date'];
            foreach ($sheetData as $date => $value) {
                $worksheet1->setCellValue('B' . $csIndex, $date);
                $csIndex ++;
            }
            $csIndex ++;
        }
    }
    
    if(isset($data['tcta'])){
        $worksheet2 = $spreadsheet->createSheet();
        $worksheet2->setTitle('CTA');
        $worksheet2->setCellValue('A1', "Report ID");
        $worksheet2->setCellValue('B1', 'Date');
        $worksheet2->setCellValue('C1', 'Total');
        $tctaIndex = 2;
        foreach ($data['tcta'] as $sheetName => $sheetData) {
            $worksheet2->setCellValue('A'.$tctaIndex, $sheetName);
            $sheetData = $sheetData['date'];
            foreach ($sheetData as $date => $value) {
                $worksheet2->setCellValue('B' . $tctaIndex, $date);
                $worksheet2->setCellValue('C' . $tctaIndex, $value);
                $tctaIndex ++;
            }
            $tctaIndex ++;
        }
    }
    if(isset($data['tctwa'])){
        $worksheet3 = $spreadsheet->createSheet();
        $worksheet3->setTitle('CTWA');
        $worksheet3->setCellValue('A1', "Date");
        $worksheet3->setCellValue('B1', 'Total');
        $siteIndex = 2;
        foreach ($data['tctwa'] as $sheetName => $sheetData) {
            $worksheet3->setCellValue('A'.$siteIndex, $sheetData['date']);
            $worksheet3->setCellValue('B'.$siteIndex, $sheetData['total']);
            $siteIndex ++;
        }
    }
    $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
    $writer->save('datafile/'.$inv_id.'.xlsx');
    
    $user = $data['user'];
    $total = $data['total'];
    //if($lunas==0){
        render_inv_pdf($user, $total, $inv_id, 0, $bill_date );
    //}
    render_inv_pdf($user, $total, $inv_id, 1, $bill_date );    
    upload_inv($inv_id, $lunas);
}

function render_inv_pdf($user, $total, $inv_id, $lunas, $bill_date = ''){
    ob_start();
    $invoice = new InvoicePrinter('a4', 'Rp.');
    $time = time();
    if ($bill_date==''){
        $bill_date = date('d-m-Y', $time);
    }
    $invoice->setLogo("logo.png");   //logo image path
    $invoice->setColor("#007fff");      // pdf color scheme
    $invoice->setType("Invoice Gass");    // Invoice Type
    $invoice->setReference("INV-".$time);   // Reference
    $invoice->setDate($bill_date);   //Billing Date
    $invoice->setTime(date('h:i:s A',$time));   //Billing Time
    $invoice->setDue(date('d-m-Y',strtotime('+14 days')));    // Due Date
    //$invoice->setFrom(array("Gass.co.id","PT Karunia Teknologi Labdagati","TOWER PALEM, Apartment Green Palace, Jl. Raya Kalibata","Kota Jakarta Selatan"));
    $invoice->setFrom(array("Gass.co.id","PT. GASS MARKETING TEKNOLOG","The Mansion Office Fontana, Lantai 17 Unit BF-17 B2", "Jalan Trembesi, Pademangan Timur, DKI Jakarta, 14410"));
    $invoice->setTo(array($user['data']['user_name'], $user['data']['user_phone'], $user['data']['user_email'],""));
    $invoice->addItem("Site", "", $total['total_day_site'], false, $user['setting_bill']['site'], false, $total['total_bill_site']);
    $invoice->addItem("CTWA", "", $total['total_day_ctwa'], false, $user['setting_bill']['ctwa'], false, $total['total_bill_ctwa']);
    //$invoice->addItem("CS", "", $total['total_day_cs'], false, $user['setting_bill']['cs'], false, $total['total_bill_cs']);
    $invoice->addItem("CS WABA", "", $user['setting_bill']['cs_waba']['total_day_cs'], false, $user['setting_bill']['cs_waba']['price'], false, $user['setting_bill']['cs_waba']['total_tagihan_cs']);
    $invoice->addItem("CTA", "", $total['total_click_cta'], false, $user['setting_bill']['lead'], false, $total['total_bill_cta']);
    $invoice->addTotal("Total", $total['total']);
    //$invoice->addTotal("Total due",11446.6,true);
    
    $invoice->addTitle("Important Notice");
    $invoice->addParagraph("Segera lakukan pembayaran sebelum expired invoice");
    $invoice->setFooternote("Gass.co.id");
//    if($lunas==0){
//        $invoice->addBadge("Payment Unpaid", '#FF0000');
//    }else{
//        $invoice->addBadge("Payment Paid", '#008000');
//    }
    if($lunas){
        $invoice->addBadge("Payment Paid", '#008000');
        $invoice->render('datafile/invoice-paid'.$inv_id.'.pdf','F'); 
    }else{
        $invoice->addBadge("Payment Unpaid", '#FF0000');
        $invoice->render('datafile/invoice-unpaid'.$inv_id.'.pdf','F'); 
    }    
    /* I => Display on browser, D => Force Download, F => local path save, S => return document path */
    ob_end_flush();
}

function upload_invoice($param){
    global $app;
    extract($param);
    assign_child_db($project_id);
    return upload_inv($invoice_id, $lunas, false);
}

function upload_inv($inv_id, $lunas, $xlsx=true){
    $accessKeyId = 'DO008UEQDK8PV4387R3E';
    $secretAccessKey = 'brg+I77djtsmUyUXaWjviA7ut3IfW+5FqQqniRjJm7Y';
    $region = 'sgp1'; // e.g., 'nyc3'
    $bucket = 'gass';
    // Array of file paths to upload
    if($lunas){
        if($xlsx){
            $filePaths = [
                'datafile/'.$inv_id.'.xlsx',
                'datafile/invoice-paid'.$inv_id.'.pdf',
                // Add more file paths as needed
            ];
        }else{
            $filePaths = [
                'datafile/invoice-paid'.$inv_id.'.pdf',
                // Add more file paths as needed
            ];
        }
        
    }else{
        if($xlsx){
            $filePaths = [
                'datafile/'.$inv_id.'.xlsx',
                'datafile/invoice-unpaid'.$inv_id.'.pdf',
                // Add more file paths as needed
            ];
        }else{
            $filePaths = [
                'datafile/invoice-unpaid'.$inv_id.'.pdf',
                // Add more file paths as needed
            ];
        }
        
    }    

    $s3Client = new S3Client([
        'version' => 'latest',
        'region' => $region,
        'endpoint' => 'https://sgp1.digitaloceanspaces.com',
        'credentials' => [
            'key' => $accessKeyId,
            'secret' => $secretAccessKey,
        ],
    ]);
    $uploadsukses = false;
    foreach ($filePaths as $filePath) {
        // Generate a unique object key for each file
        $namefile = str_replace("invoice-paid", "invoice-", $filePath);
        $namefile = str_replace("invoice-unpaid", "invoice-", $namefile);
        $objectKey = 'v2/invoice/'.basename($namefile);
        if($lunas){
            try {
                // Delete the object
                $result = $s3Client->deleteObject([
                    'Bucket' => $bucket,
                    'Key'    => $objectKey,
                ]);
            } catch (AwsException $e) {
                // Catch an error
                //echo "Error: " . $e->getMessage() . "\n";
            }
        }
        //$objectKey = $namefile;
        try {
            // Upload the file
            $result = $s3Client->putObject([
                'Bucket' => $bucket,
                'Key' => $objectKey,
                'SourceFile' => $filePath,
                'ACL' => 'public-read', // Set the appropriate ACL
            ]);            
            // Output the URL of the uploaded file
            //echo 'File uploaded successfully: ' . $result['ObjectURL'] . PHP_EOL;
            $uploadsukses = true;
            //unlink($filePath);
        } catch (AwsException $e) {
            // Output any errors that occurred
            //echo 'Error uploading file ' . $filePath . ': ' . $e->getMessage() . PHP_EOL;
        }
    }
    return $uploadsukses;
}


function get_site($param){
    global $app;
    extract($param);
    assign_child_db($project_id);
    $db = $app->db2;
    $yesterday =  date('Y-m-d ',strtotime('-1 day', strtotime(date("Y-m-d"))));    
    $db->where("DATE(created) BETWEEN ? AND ?", array('2023-09-01', $yesterday));  
    $site = $db->get("visitor", NULL, "site, DATE(created) as waktu");
    print_r($param);
    $counts = [];
    foreach ($site as $item) {
        $site = $item["site"];
        $waktu = $item["waktu"];
        if (!isset($counts[$site][$waktu])) {
            $counts[$site][$waktu] = 0;
        }
        $counts[$site][$waktu]++;
    }
    return $counts;
}


function get_cs($param){
    global $app;
    extract($param);
    assign_child_db($project_id);
    $db2 = $app->db2;
    $db2->where("DATE(tanggal) BETWEEN ? AND ?", array($start, $end));  
    $cs = $db2->get("cs_log", NULL, "phone, DATE(tanggal) as waktu, impression, contact, mql, prospek, purchase, value");
    return $cs;
}


function get_cta($param){
    global $app;
    extract($param);
    assign_child_db($project_id);
    $db = $app->db2;
    
    $yesterday =  date('Y-m-d ',strtotime('-1 day', strtotime(date("Y-m-d"))));    
    $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'unik_cta'", array($start, $end));  
    $cta = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_key, report_value");
    $counts = []; $val = 0;
    foreach ($cta as $item) {
        $val += $item["report_value"];
    }
    $res['data'] = $cta;
    $res['total_cta'] = $val;    
    return $res;
}

function test($param){
    global $app;
    extract($param);
    assign_child_db($project_id);
    $db = $app->db2;
    $fb = new fb('EAAL49DVgF5MBOzAv6FJetx07psBCGqnw0bbQQRaA0Vk9DZAlPDuJl5m1wpJFOIswmYVJZAkYwS8TsMZBTyvHe0kmhsmbb5YZBTeg7BtKqbFZC7fgn2NRoSuJGhZCuG6IlHXZBWI6kGCYnGOyG7cregmpjWwZAWvcWwrBf0m6mJILDbBA4wXxov1nfehEZAYjf5NLh40WKKRZBy');
    $res = $fb->get_campaign('', "ACTIVE");
    return $res;
}


function generate_bill_exec($param){
    global $app;
    extract($param); 
    assign_child_db($project_id);
    $pos["act"] = 'bill_get_project_user';
    $pos["project_id"] = $project_id;
    $user = json_decode(post_x_contents($pos, 'http://10.104.0.27/api.html'), true);
    if(isset($user['setting_bill'])){
        $db = $app->db2;
        //$last_bill =  date('Y-m-d', strtotime('+1 day', strtotime($user['data']['last_bill'])));  
        $last_bill =  date('Y-m-d', strtotime($user['data']['last_bill']));  
        $yesterday =  date('Y-m-d', strtotime('-1 day', strtotime(date("Y-m-d"))));
        $db->join("visitor_cs vc", "v.visitor_id = vc.visitor_id", "LEFT");
        $db->join("cs c", "vc.cs_key = c.cs_key", "LEFT");
                
        $db->where("DATE(v.created) BETWEEN ? AND ?", array($last_bill, $yesterday));  
        $site = $db->get("visitor v", NULL, "v.site, v.data, DATE(v.created) as waktu");
        $sites = []; 
        foreach ($site as $item) {
            $sit = $item["site"];
            $sit = str_replace("www.","", $sit);
            if (strpos($item["data"] ?? '', "ctwaClid") !== false) {
                $isctwa = true;
                $sit = 'ctwa';
                $waktu = $item["waktu"];
                if(!isset($sites[$sit]['total'])){
                    $sites[$sit]['total'] = 0;
                }
                if (!isset($sites[$sit]['date'][$waktu])) {
                    $sites[$sit]['date'][$waktu] = 0;
                    $sites[$sit]['total'] ++;
                }
                $sites[$sit]['date'][$waktu]++; 
                $sites[$sit]['total']++;
            }
            if($sit!='' && $sit != 'b-cdn.net' && $isctwa==false){   ///// disable ctwa
                $waktu = $item["waktu"];
                if($sit==''){
                    $sit = 'ctwa';
                }
                if(!isset($sites[$sit]['total'])){
                    $sites[$sit]['total'] = 0;
                }
                if (!isset($sites[$sit]['date'][$waktu])) {
                    $sites[$sit]['date'][$waktu] = 0;
                    $sites[$sit]['total']++;
                }
                $sites[$sit]['date'][$waktu]++;        
            }      
        }
        $total_day_site = 0;$total_day_ctwa = 0;
        foreach ($sites as $k => $v) {
            if($k != 'ctwa'){
                $total_day_site = $v['total'] + $total_day_site;
            }else{
                $total_day_ctwa = $v['total'] + $total_day_ctwa;
            }            
        }
        $tctwa = []; $total_click_ctwa = 0;
        if(isset($sites['ctwa']['date'])){
            foreach ($sites['ctwa']['date'] as $k => $v) {
                //print_r($k);
                //// hitung ctwa
                
                $tctwa[$k]['date'] = $k; 
                $tctwa[$k]['total'] = $v;     
                $total_click_ctwa += $v;  
            }
            $total_day_ctwa = $total_click_ctwa;
        }
        $total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_click_ctwa;
        
        $total_bill_site = $user['setting_bill']['site'] * $total_day_site;
        //$total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_day_ctwa;
        // hitung bill cs
        $db->where("DATE(tanggal) BETWEEN ? AND ?", array($last_bill, $yesterday));  
        $cs = $db->get("cs_log", NULL, "phone, DATE(tanggal) as waktu, impression, contact, mql, prospek, purchase, value");
        $nocs = []; 
        foreach ($cs as $item) {
            $c = $item["phone"];
            $waktu = $item["waktu"];
            if(!isset($nocs[$c]['total'])){
                $nocs[$c]['total'] = 0;
            }
            if (!isset($nocs[$c]['date'][$waktu])) {
                $nocs[$c]['date'][$waktu] = 0;
                $nocs[$c]['total']++;
            }
            $nocs[$c]['date'][$waktu]++;        
        }
        $total_day_cs = 0;
        foreach ($nocs as $k => $v) {
            $total_day_cs = $v['total'] + $total_day_cs;
        }
        $total_bill_cs = $user['setting_bill']['cs'] * $total_day_cs;
        // hitung cta
        $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'unik_cta'", array($last_bill, $yesterday));  
        $db->orderBy("date","Asc");
        $cta = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
        $tcta = []; $total_click_cta = 0;
        $last_cta = ''; $last_cta_value = 0;
        foreach ($cta as $item) {
            $c = $item["report_id"];
            $waktu = $item["waktu"];
            $last_cta = $waktu;
            if(!isset($tcta[$c]['total'])){
                $tcta[$c]['total'] = 0;
            }
            if (!isset($tcta[$c]['date'][$waktu])) {
                $tcta[$c]['date'][$waktu] = 0;
                $tcta[$c]['total']++;
            }
            $tcta[$c]['date'][$waktu] += $item["report_value"];    
            $total_click_cta += $item["report_value"];  
            $last_cta_value = $tcta[$c]['date'][$waktu];
        }
        $total_day_cta = 0;
        foreach ($tcta as $k => $v) {
            $total_day_cta = $v['total'] + $total_day_cta;
        }
        
        // hitung lp_view
        $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'lp_view'", array($last_bill, $yesterday));  
        $lp_view = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
        $tlp_view = []; $total_lp_view = 0;
        foreach ($lp_view as $item) {
            $c = $item["report_id"];
            $waktu = $item["waktu"];
            if(!isset($tlp_view[$c]['total'])){
                $tlp_view[$c]['total'] = 0;
            }
            if (!isset($tlp_view[$c]['date'][$waktu])) {
                $tlp_view[$c]['date'][$waktu] = 0;
                $tlp_view[$c]['total']++;
            }
            $tlp_view[$c]['date'][$waktu] += $item["report_value"];  
            $total_lp_view += $item["report_value"];  
        }
        $total_day_lp_view = 0;
        foreach ($tlp_view as $k => $v) {
            $total_day_lp_view= $v['total'] + $total_day_lp_view;
        }
        
        $total_bill_cta = $user['setting_bill']['lead'] * $total_click_cta;
        $total['total_bill_site'] = $total_bill_site;
        $total['total_bill_ctwa'] = $total_bill_ctwa;
        $total['total_bill_cs'] = $total_bill_cs;
        $total['total_bill_cta'] = $total_bill_cta;
        $total['total_day_site'] = $total_day_site;
        $total['total_day_ctwa'] = $total_day_ctwa;
        $total['total_day_cs'] = $total_day_cs;
        $total['total_day_cta'] = $total_day_cta;
        $total['total_click_cta'] = $total_click_cta;
        $total['total_click_ctwa'] = $total_click_ctwa;
        $total['total'] = $total_bill_ctwa + $total_bill_site + $total_bill_cs + $total_bill_cta;
        $alert_nominal = $user['data']['billing_alert'] - 5000;
        $create_bill = true;
        
        if($create_bill == true){
            $pos["act"] = 'bill_create';
            $pos["user_id"] = $user['data']['user_id'];
            $pos["project_id"] = $project_id;
            $pos["keterangan"] = "Billing project ".$user['data']['name'];
            $pos["total"] = $total['total'];
            $pos["last_bill"] = $yesterday;
            $pos["data"] = '{}';
            $bill = json_decode(post_x_contents($pos, 'http://10.104.0.27/api.html'), true);
            if($bill['code']==1){
                $lunas = 0; $topup = 0;
                if($total['total'] <= $user['data']['saldo']){
                    //pay bill lunas  
                    $lunas = 1;
                }else{
                    $topup = $total['total']; 
                }
                $dx['user'] = $user;
                $dx['sites'] = $sites;
                $dx['nocs'] = $nocs;
                $dx['tcta'] = $tcta;
                $dx['tctwa'] = $tctwa;
                $dx['total'] = $total;
                create_inv($dx, $project_id.$bill['id'], $lunas, 1);
                
                $dinv[0]['text']= 'Report';
                $dinv[0]['link']= 'https://space.gass.biz.id/v2/invoice/'.$project_id.$bill['id'].'.xlsx';
                $dinv[0]['data'] = array('project_id' => $project_id, 'bill_id'=>$bill['id'], 'start' => $last_bill, 'end'=> $yesterday);
                $dinv[1]['text']= 'Invoice';
                $dinv[1]['link']= 'https://space.gass.biz.id/v2/invoice/invoice-'.$project_id.$bill['id'].'.pdf';
                $pos = array();
                if($lunas == 1){
                    $user['data']['saldo'] = $user['data']['saldo'] - $total['total'];
                    $pos["act"] = 'bill_edit_data';
                    $pos["status"] = 1; 
                    $pos["bill_id"] = $bill['bill_id'];
                    $pos["id"] = $bill['id'];
                    $pos["nominal"] = $topup;
                    $pos["total"] = $total['total'];
                }else{
                    $pos["act"] = 'bill_pay_bill';
                    $pos["nominal"] = $topup;
                    $pos["total"] = $total['total'];
                    $pos["status"] = 0; 
                    $pos["id"] = $bill['bill_id'];
                    $pos["bill_id"] = $bill['id'];
                }
                $pos['user'] = $user['data'];
                $pos["user_id"] = $user['data']['user_id'];
                $pos["invoice_id"] = $project_id.$bill['id'];
                $pos["project_id"] = $project_id;
                $pos["data"] = json_encode($dinv);
                post_x_contents($pos, 'http://10.104.0.27/api.html');
            }else{
                $msg = $bill;
            }
        }
        $msg['code']=1;        
        $msg['data']['user'] = $user;
        $msg['data']['total'] = $total;
        $msg['last_cta'] = $last_cta;
        $msg['last_cta_value'] = $last_cta_value;
        if(isset($param['source']) && $param['source'] == 'cloudflare'){
            unset($msg['data']);
        }
    }else{
        $msg['code']=0;
        $msg['msg']='bill not set';
    }
    return $msg;
}


function get_usage_range2($param){
    global $app;
    $rules = [
        "project_id" => "required",
        "start" => "required", 
        "end" => "required",
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    
    $pos = ["act" => 'bill_get_project_user', "project_id" => $project_id];
    $user = json_decode(post_x_contents($pos, 'http://10.104.0.27/api.html'), true);
    
    if(!isset($user['setting_bill'])){
        return ['code' => 0, 'msg' => 'bill not set'];
    }

    $db = $app->db2;
    $last_bill = $start;
    $yesterday = $end;
    $date_grouped_data = []; // New structure for date-grouped data
    
    // Process visitor data
    $db->join("visitor_cs vc", "v.visitor_id = vc.visitor_id", "LEFT");
    $db->join("cs c", "vc.cs_key = c.cs_key", "LEFT");
    $db->where("DATE(v.created) BETWEEN ? AND ?", array($last_bill, $yesterday));
    $site = $db->get("visitor v", NULL, "v.site, v.data, DATE(v.created) as waktu");
    
    $sites = [];
    foreach ($site as $item) {
        $sit = str_replace("www.", "", $item["site"] ?? '');
        $waktu = $item["waktu"];
        $isctwa = strpos($item["data"] ?? '', "ctwaClid") !== false;
        
        if ($isctwa) {
            $sit = 'ctwa';
        }
        
        if($sit != '' && $sit != 'b-cdn.net' && ($isctwa || !$isctwa)) {
            if($sit == '') $sit = 'ctwa';
            
            if(!isset($sites[$sit]['total'])) $sites[$sit]['total'] = 0;
            if(!isset($sites[$sit]['date'][$waktu])) {
                $sites[$sit]['date'][$waktu] = 0;
               
                $sites[$sit]['total']++;
            }
           
            $sites[$sit]['date'][$waktu]++;
            if($isctwa) $sites[$sit]['total']++;

           
        }
    }
    
    // Calculate site totals
    $total_day_site = $total_day_ctwa = 0;
    foreach ($sites as $k => $v) {
        if($k != 'ctwa') {
            $total_day_site += $v['total'];
        } else {
            $total_day_ctwa += $v['total'];
        }
    }
    
    // Process CTWA data
    $tctwa = [];
    
   
    if(isset($sites['ctwa']['date'])){
        foreach ($sites['ctwa']['date'] as $k => $v) {
            $tctwa[$k] = ['date' => $k, 'total' => $v];
            $total_click_ctwa += $v;

            $date_grouped_data[$k]["tctwa"] += $v;
        }
        $total_day_ctwa = $total_click_ctwa;
    }    
    
    // Process CS data
    $db->where("DATE(tanggal) BETWEEN ? AND ?", array($last_bill, $yesterday));
    $cs = $db->get("cs_log", NULL, "phone, DATE(tanggal) as waktu, impression, contact, mql, prospek, purchase, value");
    $nocs = [];
    foreach ($cs as $item) {
        $c = $item["phone"];
        $waktu = $item["waktu"];
        if(!isset($nocs[$c]['total'])) $nocs[$c]['total'] = 0;
        if(!isset($nocs[$c]['date'][$waktu])) {
            $nocs[$c]['date'][$waktu] = 0;
            $nocs[$c]['total']++;
        }
        $nocs[$c]['date'][$waktu]++;
    }
    $total_day_cs = array_sum(array_column($nocs, 'total'));
    
    // Fetch all report data in single query
    $db->where("DATE(date) BETWEEN ? AND ?", array($last_bill, $yesterday));
    $all_reports = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_key, report_value");
     
    // Initialize report data containers
    $tcta = [];
    $tlp_view = [];
    $tunik_wa = [];
    $tprospek = [];
    $tpurchase = [];
    
    
    $total_click_cta = 0;
    $total_lp_view = 0;
    $total_unik_wa = 0;
    $total_prospek = 0;
    $total_purchase = 0;
    
    $last_cta = '';
    $last_cta_value = 0;
    
    // Process all report data in single loop
    foreach ($all_reports as $item) {
        $report_id = $item["report_id"];
        $waktu = $item["waktu"];
        $report_key = $item["report_key"];
        $report_value = $item["report_value"];
        
        // Initialize date group if not exists
        if(!isset($date_grouped_data[$waktu])) {
            $date_grouped_data[$waktu] = [
                'tcta' => 0,
                'tlp_view' => 0,
                'tunik_wa' => 0,
                'tprospek' => 0,
                'tpurchase' => 0
            ];
        }
        
        switch($report_key) {
            case 'unik_cta':
                $last_cta = $waktu;
                if(!isset($tcta[$report_id]['total'])) $tcta[$report_id]['total'] = 0;
                if(!isset($tcta[$report_id]['date'][$waktu])) {
                    $tcta[$report_id]['date'][$waktu] = 0;
                    $tcta[$report_id]['total']++;
                }
                $tcta[$report_id]['date'][$waktu] += $report_value;
                $total_click_cta += $report_value;
                $last_cta_value = $tcta[$report_id]['date'][$waktu];
                $date_grouped_data[$waktu]['tcta'] += $report_value;
                break;
                
            case 'lp_view':
                if(!isset($tlp_view[$report_id]['total'])) $tlp_view[$report_id]['total'] = 0;
                if(!isset($tlp_view['date'][$waktu])) {
                    $tlp_view['date'][$waktu] = 0;
                    $tlp_view['total']++;
                }
                $tlp_view['date'][$waktu] += $report_value;
                $total_lp_view += $report_value;
                $date_grouped_data[$waktu]['tlp_view'] += $report_value;
                break;
                
            case 'unik_wa':
                if(!isset($tunik_wa[$report_id]['total'])) $tunik_wa[$report_id]['total'] = 0;
                if(!isset($tunik_wa[$report_id]['date'][$waktu])) {
                    $tunik_wa[$report_id]['date'][$waktu] = 0;
                    $tunik_wa[$report_id]['total']++;
                }
                $tunik_wa[$report_id]['date'][$waktu] += $report_value;
                $total_unik_wa += $report_value;
                $date_grouped_data[$waktu]['tunik_wa'] += $report_value;
                break;
                
            case 'prospek':
                if(!isset($tprospek[$report_id]['total'])) $tprospek[$report_id]['total'] = 0;
                if(!isset($tprospek[$report_id]['date'][$waktu])) {
                    $tprospek[$report_id]['date'][$waktu] = 0;
                    $tprospek[$report_id]['total']++;
                }
                $tprospek[$report_id]['date'][$waktu] += $report_value;
                $total_prospek += $report_value;
                $date_grouped_data[$waktu]['tprospek'] += $report_value;
                break;
                
            case 'purchase':
                if(!isset($tpurchase[$report_id]['total'])) $tpurchase[$report_id]['total'] = 0;
                if(!isset($tpurchase[$report_id]['date'][$waktu])) {
                    $tpurchase[$report_id]['date'][$waktu] = 0;
                    $tpurchase[$report_id]['total']++;
                }
                $tpurchase[$report_id]['date'][$waktu] += $report_value;
                $total_purchase += $report_value;
                $date_grouped_data[$waktu]['tpurchase'] += $report_value;
                break;
        }
    }
    
    $total_day_cta = array_sum(array_column($tcta, 'total'));
    $total_day_lp_view = array_sum(array_column($tlp_view, 'total'));
    
    // Calculate billing totals
    $total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_click_ctwa;
    $total_bill_site = $user['setting_bill']['site'] * $total_day_site;
    $total_bill_cs = $user['setting_bill']['cs'] * $total_day_cs;
    $total_bill_cta = $user['setting_bill']['lead'] * $total_click_cta;
    
    $total = [
        'total_bill_site' => $total_bill_site,
        'total_bill_ctwa' => $total_bill_ctwa,
        'total_bill_cs' => $total_bill_cs,
        'total_bill_cta' => $total_bill_cta,
        'total_day_site' => $total_day_site,
        'total_day_ctwa' => $total_day_ctwa,
        'total_day_cs' => $total_day_cs,
        'total_day_cta' => $total_day_cta,
        'total_click_cta' => $total_click_cta,
        'total_click_ctwa' => $total_click_ctwa,
        'total' => $total_bill_ctwa + $total_bill_site + $total_bill_cs + $total_bill_cta,
        'project_id' => $project_id,
        'cta' => $last_cta_value,
        'lp_view' => $total_lp_view,
        'last_cta' => $last_cta
    ];
    
    // Merge date_grouped_data with existing structure
   // $response_data = $date_grouped_data;
    
    // Add traditional structure for backward compatibility
   
    $response_data['summary'] = $total;
   // $response_data['start'] = $last_bill;
   // $response_data['end'] = $yesterday;
   ksort($date_grouped_data);
   $response_data["data"] = $date_grouped_data;
    
    return [
        'code' => 1,
        'data' => $response_data
    ];
}