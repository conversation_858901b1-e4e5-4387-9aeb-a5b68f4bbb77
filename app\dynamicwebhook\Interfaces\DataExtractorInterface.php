<?php

namespace DynamicWebhook\Interfaces;

/**
 * Interface for data extraction from webhooks
 */
interface DataExtractorInterface
{
    /**
     * Extract data from webhook payload
     *
     * @param array $data Webhook data
     * @param string $platform Platform name
     * @return array Extracted data containing phone, message, type, etc.
     */
    public function extract(array $data, string $platform): array;

    /**
     * Validate extracted data
     *
     * @param array $extractedData
     * @return bool
     */
    public function validate(array $extractedData): bool;

    /**
     * Get supported platforms for this extractor
     *
     * @return array
     */
    public function getSupportedPlatforms(): array;
}
