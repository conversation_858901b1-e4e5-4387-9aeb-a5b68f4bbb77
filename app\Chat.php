<?php

class Chat {
    private $aiRouter;
    private $systemPrompt;
    private $project_id;
    
    /**
     * Constructor to initialize the Chat class
     * 
     * @param string $project_id Project ID to load system prompt
     * @param AIRouter $aiRouter AIRouter instance
     */
    public function __construct($project_id, AIRouter $aiRouter) {
        $this->project_id = $project_id;
        $this->aiRouter = $aiRouter;
        
        // Load system prompt from file
        $promptFile = 'config/prompt_project/' . $project_id . '.md';
        if (!file_exists($promptFile)) {
            throw new Exception('System prompt file not found: ' . $promptFile);
        }
        
        $this->systemPrompt = file_get_contents($promptFile);
        if ($this->systemPrompt === false) {
            throw new Exception('Failed to read system prompt file: ' . $promptFile);
        }
    }
    
    /**
     * Analyze chat history using AI Router
     * 
     * @param array $chatHistory Array of chat messages
     * @param array $options Additional options for the AI request
     * @return array Analysis result
     */
    public function analyzer($chatHistory, $options = []) {
        // Convert chat history to JSON string for the prompt
        $chatHistoryJson = json_encode($chatHistory, JSON_PRETTY_PRINT);
        
        // Build the prompt for AI analysis
        $prompt = "Analyze the following chat history and determine the appropriate event based on the system prompt rules.\n\n";
        $prompt .= "Chat History:\n" . $chatHistoryJson . "\n\n";
        $prompt .= "Please provide the result in the specified output format from the system prompt.";
        
        // Create messages for AI Router
        $messages = [
            ['role' => 'system', 'content' => $this->systemPrompt],
            ['role' => 'user', 'content' => $prompt]
        ];
        
        // Send request to AI Router
        $response = $this->aiRouter->chatCompletion($messages, $options);
        
        // Extract and parse the response
        if (isset($response['choices'][0]['message']['content'])) {
            $content = $response['choices'][0]['message']['content'];
            
            // Try to extract JSON from the response
            $jsonContent = $this->extractJsonFromResponse($content);
            
            if ($jsonContent) {
                return json_decode($jsonContent, true);
            }
            
            // If no JSON found, return the raw response
            return ['response' => $content];
        }
        
        throw new Exception('Failed to analyze chat history: No response content');
    }
    
    /**
     * Extract JSON content from AI response
     * 
     * @param string $content Response content
     * @return string|null Extracted JSON string
     */
    private function extractJsonFromResponse($content) {
        // Look for JSON content between ```json and ``` markers
        if (preg_match('/```json\s*(.*?)\s*```/s', $content, $matches)) {
            return $matches[1];
        }
        
        // Look for JSON content between ``` and ``` markers
        if (preg_match('/```\s*(\{.*?\})\s*```/s', $content, $matches)) {
            return $matches[1];
        }
        
        // Look for JSON object directly
        if (preg_match('/(\{.*\})/s', $content, $matches)) {
            return $matches[1];
        }
        
        return null;
    }
    
    /**
     * Get the current project ID
     * 
     * @return string Project ID
     */
    public function getProjectId() {
        return $this->project_id;
    }
    
    /**
     * Get the system prompt content
     * 
     * @return string System prompt content
     */
    public function getSystemPrompt() {
        return $this->systemPrompt;
    }
}
