<?php

// Qontak Helper: Functions for processing Qontak webhook data

/**
 * Handle Facebook WhatsApp tracking for Qontak
 * 
 * @param string $phone The phone number
 * @param array $param The parameters from Qontak webhook
 * @return bool Returns true if adcopy_id is found and processed, false otherwise
 */
function fbwa_qontak($phone, $param) {
    if (!is_array($param)) {
        return false;
    }
    
    global $app;
    $db2 = $app->db2;
    $fbwa_ret = false;
    $data = [];
    
    // Check if we have room description with ctwa_clid
    if (isset($param["room"]["description"])) {
        $description = $param["room"]["description"];
        
        // Extract ctwa_clid from description
        if (preg_match('/ctwa_clid=([^;]+)/', $description, $matches)) {
            $data["ctwaClid"] = $matches[1];
        }
        
        // Extract source_id if available (equivalent to adcopy_id)
        if (preg_match('/source_id=([^;]+)/', $description, $matches)) {
            $data["adcopy_id"] = $matches[1];
            $fbwa_ret = true;
        }
        
        // If we have adcopy_id, update visitor data
        if (isset($data["adcopy_id"])) {
            $db2->where("external_key = UNHEX(?)", [sha1($data["adcopy_id"])]);
            $data_report = $db2->getone("report");

            $data_visitor_fbwa["last_campaign"]["source"] = "meta";
            if(isset($data_report["report_id"])){
                $data_visitor_fbwa["last_campaign"]["data"]["adcopy_id"] = $data_report["report_id"];
            }
            if (isset($data["ctwaClid"])) {
                $data_visitor_fbwa["last_campaign"]["data"]["ctwaClid"] = $data["ctwaClid"];
            }
            $db2->where("phone", $phone);
            $db2->update("visitor", ["data" => serialize($data_visitor_fbwa)]);
        }
    }
    return $fbwa_ret;
}

