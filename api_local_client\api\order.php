<?php

function get($param)
{
/*
    $data_order['nama'] = "martin";
    $data_order['value'] = "100000";
$data_order['kota'] = "surabaya";
$data_order['provinsi']  = "jawa timur";
$data_order['alamat'] = "jalan walikota 1";
$data_order['items'] = array();
 $item['qty'] = "1";
$item['sku'] = "item-1";
 array_push($data_order['items'], $item);

 $item['qty'] = "2";
 $item['sku'] = "item-2";
  array_push($data_order['items'], $item);

  echo json_encode($data_order);
  die();
  */

    extract($param);
    assign_child_db($project_id);
    $pagination = NULL;
    if(isset($page)){
        if($page > 1){
            $mulai = ($page - 1) * 20;
        }else{
            $mulai = 0;
        }
        $pagination = [$mulai,20];
    }else{
        $pagination = [0,20];
    }
    global $app;
    $db2 = $app->db2;

    $total_page = 0;
    $count = $db2->rawQuery("SELECT count(*) as c FROM orders");
    if($count != null){
        $count = $count[0]["c"];
        $total_page = ceil($count/20);
    }

    $db2->join("visitor v","o.visitor_id = v.visitor_id");
    $db2->orderby("o.order_id","desc");
    if(isset($start) && isset($end)){
        if($start != '' && $end != ''){  
            $db2->where("DATE(o.created) BETWEEN ? and ?", [$start, $end]);
        }
    }
    $data = $db2->get("orders o",$pagination,"o.*,o.phone as visitor_phone");

    foreach($data as $key => $value){
        $tmp = $value["details"];
        unset($data[$key]["details"]);
        $tmp = json_decode($tmp,true);
        $data[$key]["visitor_id"]  = convBase($data[$key]["visitor_id"], "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        $data[$key]["visitor_id"] = str_split($data[$key]["visitor_id"], 4);
        $data[$key]["visitor_id"] = implode(".", $data[$key]["visitor_id"]);
        if($tmp){
            $data[$key] = array_merge($data[$key],$tmp);
        }
        $data[$key]["phone"] = $value["visitor_phone"];
        unset($data[$key]["visitor_phone"]);
    }

    $data["total_page"] = $total_page;

    $result["code"]  = 1;

    $result["result"]["data"]= $data;
    $result["result"]["msg"] = "sukses";

    return $result;
}
?>