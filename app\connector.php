<?php

class connector
{

    public function trigger($vid, $event, $event_value = 0)
    {
        global $keya, $c_time, $app,$project_id;
        $db = $app->db;
        $db2 = $app->db2;

        $event_value = preg_replace('/\D/', '', $event_value);

        $db2->where("visitor_id", $vid);
        $visitor = $db2->getone("visitor");

        // Check if this is a CTWA scenario
        $ctwa_detection = $this->detectCTWA($db2, $vid, $visitor);

        /*
        if(isset($project_id)){
            if($project_id == 2){
                if($event=="AddToCart" || $event=="cta"){
                   if($visitor["site"]!= "lp.gass.co.id"){
                        return true;
                   }
                }
            }
        }
        */

        

        if ($visitor != null) {

            $connector = null;

            $meta = new meta();

            $db2->where("visitor_id", $vid);
            $visitor_source = $db2->getone("visitor_source");

            if (!empty($visitor_source)) {
                if ($visitor_source["source"] == "google") {
                    $val = $meta->get_meta("google_connector");
                    if (!$val["code"] == 0) {
                        $connector = $val["result"]["data"];
                        if (isset($connector[0])) {
                            if ($connector[0] == false) {
                                return "";
                            } else {
                                $connector = $val["result"]["data"];
                            }
                        }

                    }
                }

                if ($visitor_source["source"] == "") {
                    $val = $meta->get_meta("unknown_connector");
                    if (!$val["code"] == 0) {
                        $connector = $val["result"]["data"];
                        if (isset($connector[0])) {
                            if ($connector[0] == false) {
                                return "";
                            } else {
                                $connector = $val["result"]["data"];
                            }
                        }
                    }
                }

                if ($visitor_source["source"] == "organic") {
                    $val = $meta->get_meta("organic_connector");
                    if (!$val["code"] == 0) {
                        $connector = $val["result"]["data"];
                        if (isset($connector[0])) {
                            if ($connector[0] == false) {
                                return "";
                            } else {
                                $connector = $val["result"]["data"];
                            }
                        }
                    }
                }

                if ($visitor_source["source"] == "shortlink") {
                    $val = $meta->get_meta("shortlink_connector");
                    if (!$val["code"] == 0) {
                        $connector = $val["result"]["data"];
                        if (isset($connector[0])) {
                            if ($connector[0] == false) {
                                return "";
                            } else {
                                $connector = $val["result"]["data"];
                            }
                        }
                    }
                }
            } else {
                $db2->where("visitor_id", $vid);
                $visitor_temp = $db2->getone("visitor");
                if (empty($visitor_temp["page_url"])) {
                    $val = $meta->get_meta("unknown_connector");
                    if (!$val["code"] == 0) {
                        $connector = $val["result"]["data"];
                        if (isset($connector[0])) {
                            if ($connector[0] == false) {
                                return "";
                            } else {
                                $connector = $val["result"]["data"];
                            }
                        }
                    }
                }

            }

            $connector = $this->get($connector);

            if (isset($connector["result"]["data"])) {
                $connector = $connector["result"]["data"];
                if (is_array($connector)) {

                    // If CTWA is detected and we have a dedicated CTWA connector, prioritize it
                    if ($ctwa_detection['is_ctwa'] && $ctwa_detection['use_ctwa_connector'] && $ctwa_detection['ctwa_connector']) {
                        // Use the CTWA connector that was already found in detectCTWA
                        $ctwa_con = $ctwa_detection['ctwa_connector'];
                        $con_data = $ctwa_con["data"];
                        
                        if (!empty($con_data["event"][$event])) {
                            $fb = new trigger_fb();
                            $fb->sent_ctwa_pixel($ctwa_con["connector_key"],
                                $event_value,
                                $con_data["pixel_id"],
                                $con_data["access_token"],
                                $vid,
                                $con_data["event"][$event]
                            );
                        }
                    }

                    foreach ($connector as $key => $con) {
                        //////////////////////////////
                        $con_data = $con["data"];
                        /*
                        if(empty($con_data["event"][$event])){
                            return false;
                        }
                            */
 
                        if (strtolower($con["type"]) == "snackvideo") {
                            $con_data = $con["data"];
                            $snack = new trigger_snack();

                            $snack->sent_pixel($con["connector_key"],
                                $event_value,
                                $con_data["pixel_id"],
                                $con_data["access_token"],
                                $vid,
                                $con_data["event"][$event]
                            );

                        }

                        if (strtolower($con["type"]) == "google analytic" && $visitor["data"] != null) {
                            $con_data = $con["data"];
                            $visitor_data = unserialize($visitor["data"]);

                            if (isset($visitor_data["google_analytic"]["clientId"])) {
                                $utm = null;
                                $gclid = null;

                                $ga = new GAnalytic();
                                $ga->send(
                                    $con_data["event"][$event],
                                    $con_data['api_secret'],
                                    $con_data['measurement_id'],
                                    $visitor_data["google_analytic"]["clientId"],
                                    $event_value,
                                    $utm,
                                    $gclid
                                );
                            }

                        }

                        if (strtolower($con["type"]) == "facebook") {

                            $con_data = $con["data"];

                            $fb = new trigger_fb();
                            
                            // Always use regular pixel method for Facebook connector
                            $fb->sent_pixel($con["connector_key"],
                                $event_value,
                                $con_data["pixel_id"],
                                $con_data["access_token"],
                                $vid,
                                $con_data["event"][$event]
                            );

                        }

                        if (strtolower($con["type"]) == "facebook_ctwa") {
                            // Skip - already handled in the priority section above
                            continue;
                        }

                        if (strtolower($con["type"]) == "tiktok") {
                            $con_data = $con["data"];

                            $tiktok = new trigger_tiktok();
                            $tiktok->sent_pixel($con["connector_key"],
                                $event_value,
                                $con_data["pixel_id"],
                                $con_data["access_token"],
                                $vid,
                                $con_data["event"][$event]
                            );
                        }

                        if (strtolower($con["type"]) == "mgid") {
                            $con_data = $con["data"];

                            if(!empty($con_data["event"][$event])){
                                $mgid = new trigger_mgid();
                                $mgid->sent_pixel($con["connector_key"],
                                    $event_value,
                                    $con_data["endpoint"],
                                    $vid,
                                    $con_data["event"][$event]
                                );
                            }

                            
                        }

                        if (strtolower($con["type"]) == "googleads") {
                            $con_data = $con["data"];
                            if(!empty($con_data["event"][$event])){
                                $trigger = new trigger_gads();
                                $trigger->sent_pixel($con["connector_key"], $event_value, $vid, $event) ;
                            }
                        }

                    }
                }
            }else{
                if ($ctwa_detection['is_ctwa'] && $ctwa_detection['use_ctwa_connector'] && $ctwa_detection['ctwa_connector']) {
                    // Use the CTWA connector that was already found in detectCTWA
                    $ctwa_con = $ctwa_detection['ctwa_connector'];
                    $con_data = $ctwa_con["data"];
                    
                    if (!empty($con_data["event"][$event])) {
                        $fb = new trigger_fb();
                        $rex = $fb->sent_ctwa_pixel($ctwa_con["connector_key"],
                            $event_value,
                            $con_data["pixel_id"],
                            $con_data["access_token"],
                            $vid,
                            $con_data["event"][$event]
                        );
                        //print_r($rex);
                    }
                }
            }
        }
        /////////////////////////////

    }

    public function get_type($project_id=null)
    {
        global $keya, $c_time, $app;
        $db = $app->db;
        $db2 = $app->db2;
        
        $type["Facebook"]["Req"][0]["name"] = "Pixel ID";
        $type["Facebook"]["Req"][0]["var_name"] = "pixel_id";
        $type["Facebook"]["Req"][0]["type"] = "Integer";
        $type["Facebook"]["Req"][1]["name"] = "Access Token";
        $type["Facebook"]["Req"][1]["var_name"] = "access_token";
        $type["Facebook"]["Req"][1]["type"] = "String";

        $type["Facebook"]["Event"]["visit"]["name"] = "Event Visit LP";
        $type["Facebook"]["Event"]["visit"]["var_name"] = "visit";
        $type["Facebook"]["Event"]["visit"]["type"] = "String";

        $type["Facebook"]["Event"]["cta"]["name"] = "Event Click CTA";
        $type["Facebook"]["Event"]["cta"]["var_name"] = "cta";
        $type["Facebook"]["Event"]["cta"]["type"] = "String";

        $type["Facebook"]["Event"]["form"]["name"] = "Event Submit Form";
        $type["Facebook"]["Event"]["form"]["var_name"] = "submit_form";
        $type["Facebook"]["Event"]["form"]["type"] = "String";

        $type["Facebook"]["Event"]["realwa"]["name"] = "Event Masuk WA";
        $type["Facebook"]["Event"]["realwa"]["var_name"] = "lead";
        $type["Facebook"]["Event"]["realwa"]["type"] = "String";

        $type["Facebook"]["Event"]["mql"]["name"] = "Event MQL (Marketing Qualified Lead)";
        $type["Facebook"]["Event"]["mql"]["var_name"] = "mql";
        $type["Facebook"]["Event"]["mql"]["type"] = "String";

        $type["Facebook"]["Event"]["prospek"]["name"] = "Event Prospek";
        $type["Facebook"]["Event"]["prospek"]["var_name"] = "prospek";
        $type["Facebook"]["Event"]["prospek"]["type"] = "String";

        $type["Facebook"]["Event"]["purchase"]["name"] = "Event Purchase";
        $type["Facebook"]["Event"]["purchase"]["var_name"] = "purchase";
        $type["Facebook"]["Event"]["purchase"]["type"] = "String";


        $type["Facebook_ctwa"]["Req"][0]["name"] = "Pixel ID";
        $type["Facebook_ctwa"]["Req"][0]["var_name"] = "pixel_id";
        $type["Facebook_ctwa"]["Req"][0]["type"] = "Integer";
        $type["Facebook_ctwa"]["Req"][1]["name"] = "Access Token";
        $type["Facebook_ctwa"]["Req"][1]["var_name"] = "access_token";
        $type["Facebook_ctwa"]["Req"][1]["type"] = "String";
        $type["Facebook_ctwa"]["Req"][2]["name"] = "WhatsApp Business Account ID";
        $type["Facebook_ctwa"]["Req"][2]["var_name"] = "waba_id";
        $type["Facebook_ctwa"]["Req"][2]["type"] = "String";

        $type["Facebook_ctwa"]["Event"]["realwa"]["name"] = "Event Masuk WA";
        $type["Facebook_ctwa"]["Event"]["realwa"]["var_name"] = "lead";
        $type["Facebook_ctwa"]["Event"]["realwa"]["type"] = "String";  

        $type["Facebook_ctwa"]["Event"]["mql"]["name"] = "Event MQL (Marketing Qualified Lead)";
        $type["Facebook_ctwa"]["Event"]["mql"]["var_name"] = "mql";
        $type["Facebook_ctwa"]["Event"]["mql"]["type"] = "String";

        $type["Facebook_ctwa"]["Event"]["prospek"]["name"] = "Event Prospek";
        $type["Facebook_ctwa"]["Event"]["prospek"]["var_name"] = "prospek";
        $type["Facebook_ctwa"]["Event"]["prospek"]["type"] = "String";

        $type["Facebook_ctwa"]["Event"]["purchase"]["name"] = "Event Purchase";
        $type["Facebook_ctwa"]["Event"]["purchase"]["var_name"] = "purchase";
        $type["Facebook_ctwa"]["Event"]["purchase"]["type"] = "String";

        $type["Tiktok"]["Req"][0]["name"] = "Pixel ID";
        $type["Tiktok"]["Req"][0]["var_name"] = "pixel_id";
        $type["Tiktok"]["Req"][0]["type"] = "String";
        $type["Tiktok"]["Req"][1]["name"] = "Access Token";
        $type["Tiktok"]["Req"][1]["var_name"] = "access_token";
        $type["Tiktok"]["Req"][1]["type"] = "String";

        $type["Tiktok"]["Event"]["visit"]["name"] = "Event Visit LP";
        $type["Tiktok"]["Event"]["visit"]["var_name"] = "visit";
        $type["Tiktok"]["Event"]["visit"]["type"] = "String";

        $type["Tiktok"]["Event"]["form"]["name"] = "Event Submit Form";
        $type["Tiktok"]["Event"]["form"]["var_name"] = "submit_form";
        $type["Tiktok"]["Event"]["form"]["type"] = "String";

        $type["Tiktok"]["Event"]["cta"]["name"] = "Event Click CTA";
        $type["Tiktok"]["Event"]["cta"]["var_name"] = "cta";
        $type["Tiktok"]["Event"]["cta"]["type"] = "String";

        

        $type["Tiktok"]["Event"]["realwa"]["name"] = "Event Masuk WA";
        $type["Tiktok"]["Event"]["realwa"]["var_name"] = "lead";
        $type["Tiktok"]["Event"]["realwa"]["type"] = "String";

        $type["Tiktok"]["Event"]["mql"]["name"] = "Event MQL (Marketing Qualified Lead)";
        $type["Tiktok"]["Event"]["mql"]["var_name"] = "mql";
        $type["Tiktok"]["Event"]["mql"]["type"] = "String";

        $type["Tiktok"]["Event"]["prospek"]["name"] = "Event Prospek";
        $type["Tiktok"]["Event"]["prospek"]["var_name"] = "prospek";
        $type["Tiktok"]["Event"]["prospek"]["type"] = "String";

        $type["Tiktok"]["Event"]["purchase"]["name"] = "Event Purchase";
        $type["Tiktok"]["Event"]["purchase"]["var_name"] = "purchase";
        $type["Tiktok"]["Event"]["purchase"]["type"] = "String";

        $type["SnackVideo"]["Req"][0]["name"] = "Pixel ID";
        $type["SnackVideo"]["Req"][0]["var_name"] = "pixel_id";
        $type["SnackVideo"]["Req"][0]["type"] = "Integer";
        $type["SnackVideo"]["Req"][1]["name"] = "Access Token";
        $type["SnackVideo"]["Req"][1]["var_name"] = "access_token";
        $type["SnackVideo"]["Req"][1]["type"] = "String";
        $type["SnackVideo"]["Req"][2]["name"] = "Test Server Events";
        $type["SnackVideo"]["Req"][2]["var_name"] = "test_clickid";
        $type["SnackVideo"]["Req"][2]["type"] = "String";

        $type["SnackVideo"]["Event"]["visit"]["name"] = "Event Visit LP";
        $type["SnackVideo"]["Event"]["visit"]["var_name"] = "visit";
        $type["SnackVideo"]["Event"]["visit"]["type"] = "String";

        $type["SnackVideo"]["Event"]["cta"]["name"] = "Event Click CTA";
        $type["SnackVideo"]["Event"]["cta"]["var_name"] = "cta";
        $type["SnackVideo"]["Event"]["cta"]["type"] = "String";

        $type["SnackVideo"]["Event"]["form"]["name"] = "Event Submit Form";
        $type["SnackVideo"]["Event"]["form"]["var_name"] = "submit_form";
        $type["SnackVideo"]["Event"]["form"]["type"] = "String";

        $type["SnackVideo"]["Event"]["realwa"]["name"] = "Event Masuk WA";
        $type["SnackVideo"]["Event"]["realwa"]["var_name"] = "lead";
        $type["SnackVideo"]["Event"]["realwa"]["type"] = "String";

        $type["SnackVideo"]["Event"]["mql"]["name"] = "Event MQL (Marketing Qualified Lead)";
        $type["SnackVideo"]["Event"]["mql"]["var_name"] = "mql";
        $type["SnackVideo"]["Event"]["mql"]["type"] = "String";

        $type["SnackVideo"]["Event"]["prospek"]["name"] = "Event Prospek";
        $type["SnackVideo"]["Event"]["prospek"]["var_name"] = "prospek";
        $type["SnackVideo"]["Event"]["prospek"]["type"] = "String";

        $type["SnackVideo"]["Event"]["purchase"]["name"] = "Event Purchase";
        $type["SnackVideo"]["Event"]["purchase"]["var_name"] = "purchase";
        $type["SnackVideo"]["Event"]["purchase"]["type"] = "String";


        $type["MGID"]["Req"][0]["name"] = "Endpoint";
        $type["MGID"]["Req"][0]["var_name"] = "endpoint";

        $type["MGID"]["Event"]["visit"]["name"] = "Event Visit LP";
        $type["MGID"]["Event"]["visit"]["var_name"] = "visit";
        $type["MGID"]["Event"]["visit"]["type"] = "String";

        $type["MGID"]["Event"]["cta"]["name"] = "Event Click CTA";
        $type["MGID"]["Event"]["cta"]["var_name"] = "cta";
        $type["MGID"]["Event"]["cta"]["type"] = "String";

        $type["MGID"]["Event"]["form"]["name"] = "Event Submit Form";
        $type["MGID"]["Event"]["form"]["var_name"] = "submit_form";
        $type["MGID"]["Event"]["form"]["type"] = "String";

        $type["MGID"]["Event"]["realwa"]["name"] = "Event Masuk WA";
        $type["MGID"]["Event"]["realwa"]["var_name"] = "lead";
        $type["MGID"]["Event"]["realwa"]["type"] = "String";

        $type["MGID"]["Event"]["mql"]["name"] = "Event MQL (Marketing Qualified Lead)";
        $type["MGID"]["Event"]["mql"]["var_name"] = "mql";
        $type["MGID"]["Event"]["mql"]["type"] = "String";

        $type["MGID"]["Event"]["prospek"]["name"] = "Event Prospek";
        $type["MGID"]["Event"]["prospek"]["var_name"] = "prospek";
        $type["MGID"]["Event"]["prospek"]["type"] = "String";

        $type["MGID"]["Event"]["purchase"]["name"] = "Event Purchase";
        $type["MGID"]["Event"]["purchase"]["var_name"] = "purchase";
        $type["MGID"]["Event"]["purchase"]["type"] = "String";



        $type["Linkedin"]["Req"][0]["name"] = "partner ID";
        $type["Linkedin"]["Req"][0]["var_name"] = "partner_id";
        $type["Linkedin"]["Req"][0]["type"] = "Integer";
        $type["Linkedin"]["Req"][1]["name"] = "Access Token";
        $type["Linkedin"]["Req"][1]["var_name"] = "access_token";
        $type["Linkedin"]["Req"][1]["type"] = "String";

        $type["Linkedin"]["Event"]["visit"]["name"] = "Event Visit LP";
        $type["Linkedin"]["Event"]["visit"]["var_name"] = "visit";
        $type["Linkedin"]["Event"]["visit"]["type"] = "String";

        $type["Linkedin"]["Event"]["cta"]["name"] = "Event Click CTA";
        $type["Linkedin"]["Event"]["cta"]["var_name"] = "cta";
        $type["Linkedin"]["Event"]["cta"]["type"] = "String";

        $type["Linkedin"]["Event"]["form"]["name"] = "Event Submit Form";
        $type["Linkedin"]["Event"]["form"]["var_name"] = "submit_form";
        $type["Linkedin"]["Event"]["form"]["type"] = "String";

        $type["Linkedin"]["Event"]["realwa"]["name"] = "Event Masuk WA";
        $type["Linkedin"]["Event"]["realwa"]["var_name"] = "lead";
        $type["Linkedin"]["Event"]["realwa"]["type"] = "String";

        $type["Linkedin"]["Event"]["mql"]["name"] = "Event MQL (Marketing Qualified Lead)";
        $type["Linkedin"]["Event"]["mql"]["var_name"] = "mql";
        $type["Linkedin"]["Event"]["mql"]["type"] = "String";

        $type["Linkedin"]["Event"]["prospek"]["name"] = "Event Prospek";
        $type["Linkedin"]["Event"]["prospek"]["var_name"] = "prospek";
        $type["Linkedin"]["Event"]["prospek"]["type"] = "String";

        $type["Linkedin"]["Event"]["purchase"]["name"] = "Event Purchase";
        $type["Linkedin"]["Event"]["purchase"]["var_name"] = "purchase";
        $type["Linkedin"]["Event"]["purchase"]["type"] = "String";



        //////////////////////////////////////////////////////////////////////////////////
        
        $type["Google Analytic"]["Req"][0]["name"] = "Measurement ID" ;
        $type["Google Analytic"]["Req"][0]["var_name"] = "measurement_id" ;
        $type["Google Analytic"]["Req"][0]["type"] = "String" ;

        $type["Google Analytic"]["Req"][1]["name"] = "Api Secret" ;
        $type["Google Analytic"]["Req"][1]["var_name"] = "api_secret" ;
        $type["Google Analytic"]["Req"][1]["type"] = "String" ;

        $type["Google Analytic"]["Event"]["visit"]["name"] = "Event Visit LP" ;
        $type["Google Analytic"]["Event"]["visit"]["var_name"] = "visit" ;
        $type["Google Analytic"]["Event"]["visit"]["type"] = "String" ;

        $type["Google Analytic"]["Event"]["cta"]["name"] = "Event Click CTA" ;
        $type["Google Analytic"]["Event"]["cta"]["var_name"] = "cta" ;
        $type["Google Analytic"]["Event"]["cta"]["type"] = "String" ;

        $type["Google Analytic"]["Event"]["realwa"]["name"] = "Event Masuk WA" ;
        $type["Google Analytic"]["Event"]["realwa"]["var_name"] = "lead" ;
        $type["Google Analytic"]["Event"]["realwa"]["type"] = "String" ;

        $type["Google Analytic"]["Event"]["mql"]["name"] = "Event MQL (Marketing Qualified Lead)" ;
        $type["Google Analytic"]["Event"]["mql"]["var_name"] = "mql" ;
        $type["Google Analytic"]["Event"]["mql"]["type"] = "String" ;

        $type["Google Analytic"]["Event"]["prospek"]["name"] = "Event Prospek" ;
        $type["Google Analytic"]["Event"]["prospek"]["var_name"] = "prospek" ;
        $type["Google Analytic"]["Event"]["prospek"]["type"] = "String" ;

        $type["Google Analytic"]["Event"]["purchase"]["name"] = "Event Purchase" ;
        $type["Google Analytic"]["Event"]["purchase"]["var_name"] = "purchase" ;
        $type["Google Analytic"]["Event"]["purchase"]["type"] = "String" ;

        //////////////////////////////////////////////////////////////////
        
        if($project_id!=null){
            $acc = new account($project_id);
            $type["GoogleAds"]["Req"][0]["name"] = "Account ID" ;
            $type["GoogleAds"]["Req"][0]["var_name"] = "account_id" ;
            $type["GoogleAds"]["Req"][0]["type"] = "String" ;
            $type["GoogleAds"]["Req"][0]["input_type"] = "select";
            $type["GoogleAds"]["Req"][0]["data"] = $acc->get_list('google');
            $type["GoogleAds"]["Req"][0]["required"] = true;
        }

        $type["GoogleAds"]["Req"][1]["name"] = "Global Tag" ;
        $type["GoogleAds"]["Req"][1]["var_name"] = "global_tag" ;
        $type["GoogleAds"]["Req"][1]["type"] = "String" ;
        $type["GoogleAds"]["Req"][1]["input_type"] = "textbox"; 
        $type["GoogleAds"]["Req"][1]["required"] = false;

        $type["GoogleAds"]["Event"]["visit"]["name"] = "Event Visit LP" ;
        $type["GoogleAds"]["Event"]["visit"]["var_name"] = "visit" ;
        $type["GoogleAds"]["Event"]["visit"]["type"] = "String" ;
        $type["GoogleAds"]["Event"]["visit"]["input_type"] = "select";

        $type["GoogleAds"]["Event"]["cta"]["name"] = "Event CTA" ;
        $type["GoogleAds"]["Event"]["cta"]["var_name"] = "cta" ;
        $type["GoogleAds"]["Event"]["cta"]["type"] = "String" ;
        $type["GoogleAds"]["Event"]["cta"]["input_type"] = "select";
        
        $type["GoogleAds"]["Event"]["realwa"]["name"] = "Event Masuk WA" ;
        $type["GoogleAds"]["Event"]["realwa"]["var_name"] = "lead" ;
        $type["GoogleAds"]["Event"]["realwa"]["type"] = "String" ;
        $type["GoogleAds"]["Event"]["realwa"]["input_type"] = "select";

        $type["GoogleAds"]["Event"]["mql"]["name"] = "Event MQL (Marketing Qualified Lead)" ;
        $type["GoogleAds"]["Event"]["mql"]["var_name"] = "mql" ;
        $type["GoogleAds"]["Event"]["mql"]["type"] = "String" ;
        $type["GoogleAds"]["Event"]["mql"]["input_type"] = "select";

        $type["GoogleAds"]["Event"]["prospek"]["name"] = "Event Prospek" ;
        $type["GoogleAds"]["Event"]["prospek"]["var_name"] = "prospek" ;
        $type["GoogleAds"]["Event"]["prospek"]["type"] = "String" ;
        $type["GoogleAds"]["Event"]["prospek"]["input_type"] = "select";
        
        $type["GoogleAds"]["Event"]["purchase"]["name"] = "Event Purchase" ;
        $type["GoogleAds"]["Event"]["purchase"]["var_name"] = "purchase" ;
        $type["GoogleAds"]["Event"]["purchase"]["type"] = "String" ;
        $type["GoogleAds"]["Event"]["purchase"]["input_type"] = "select";

        $type["GoogleAds"]["Event"]["form"]["name"] = "Event Submit Form";
        $type["GoogleAds"]["Event"]["form"]["var_name"] = "submit_form";
        $type["GoogleAds"]["Event"]["form"]["type"] = "String";
        $type["GoogleAds"]["Event"]["form"]["input_type"] = "select";


        /*
        $type["GoogleAds"]["Req"][0]["name"] = "Conversion Action ID" ;
        $type["GoogleAds"]["Req"][0]["var_name"] = "ctId" ;
        $type["GoogleAds"]["Req"][0]["type"] = "String" ;

        /*
        $type["Google"]["Req"][0]["name"] = "Global Tag" ;
        $type["Google"]["Req"][0]["var_name"] = "global_tag" ;
        $type["Google"]["Req"][0]["type"] = "String" ;
         */
        return $type;
    }

    public function add($type, $name, $data)
    {
        global $keya, $c_time, $app;
        $db = $app->db;
        $db2 = $app->db2;

        $type_data = $this->get_type();

        $valid = true;
        foreach ($type_data as $key => $value) {

            if (strtolower($key) == strtolower($type)) {
                $req = $value["Req"];
                foreach ($req as $key2 => $value2) {
                    if (!isset($data[$value2["var_name"]]) && $value2["required"]== true) {
                        $valid = false;
                    } else {
                        if ($data[$value2["var_name"]] == "" && $value2["required"]== true) {
                            $valid = false;
                        }
                    }
                }  
                $event = $value["Event"];
                foreach ($event as $key2 => $value2) {
                    if (!isset($data['event'][$value2["var_name"]])) {
                        $valid = false;
                    } else {
                        if ($data['event'][$value2["var_name"]] == "") {
                            $valid = false;
                        }
                    }
                }
            }

        }

        if ($valid == false) {
            $return["code"] = 0;
            $return["result"]["msg"] = "type data not found";
            return $return;
        }

        if (isset($data['account_id']) && $type == "googleads") {
            $db2->where("account_hash = UNHEX(?) and status = 1", [$data['account_id']]);
            $x = $db2->getone("integration");
            if($db2->count >0){
                $x["data"] = unserialize($x["data"]);
                $data['account_id'] = $x["account_id"];
                $data['access_token'] = $x["data"]['access_token'];
                $data['refresh_token'] = $x["data"]['refresh_token'];
            }
        }

        $insert["name"] = $name;
        $insert["type"] = strtolower($type);
        $insert["data"] = json_encode($data);
        
        if ($connector_id = $db2->insert("connector", $insert)) {
            $connector_key = md5($keya . ";connector;" . $connector_id);
            $update["connector_key"] = $db2->func("UNHEX(?)", array($connector_key));
            $db2->where("connector_id", $connector_id);
            $db2->update("connector", $update);

            $return["code"] = 1;
            $return["result"]["data"]["id"] = $connector_id;
            $return["result"]["data"]["key"] = $connector_key;
            $return["result"]["msg"] = "sukses";
        } else {
            $return["code"] = 1;
            $return["result"]["msg"] = "error, failed to insert";
        }

        if ($type == "snackvideo") {
            if (isset($data["test_clickid"])) {
                if ($data["test_clickid"] != "") {
                    $test_clickid = $data["test_clickid"];
                    $events = $data["event"];
                    $access_token = "";
                    $pixel_id = "";
                    if (isset($data["access_token"])) {if ($data["access_token"] != "") {$access_token = $data["access_token"];}}
                    if (isset($data["pixel_id"])) {if ($data["pixel_id"] != "") {$pixel_id = $data["pixel_id"];}}

                    if (count($events) > 0 && $access_token != "" && $pixel_id != "") {
                        $snack = new trigger_snack();
                        foreach ($events as $key => $event) {
                            $snack->test_event($access_token, $pixel_id, $test_clickid, $event);
                        }
                    }
                }
            }
        }

        return $return;
    }

    public function get($connector_key = null)
    {
        global $keya, $c_time, $app, $project_id;
        $db = $app->db;
        $db2 = $app->db2;

        $memcached = new Memcached();
        $memcached->addServer('127.0.0.1', 11211);
        $mem_key = "getconnectorxxxxx \n\n";
        if (!empty($project_id)) {
            if (extension_loaded('memcached')) {
                if (is_array($connector_key)) {
                    $mem_key = $project_id . '_GetConnectorByKey_' . md5(json_encode($connector_key));
                } elseif (empty($connector_key)) {
                    $mem_key = $project_id . '_GetConnectorByKeyxxx';
                } else {
                    $mem_key = $project_id . '_GetConnectorByKey_' . md5($connector_key);
                }
                $connector = $memcached->get($mem_key);
            } else {
                //file_put_contents('log/memcached_key.log', "memcached not found" . "\n", FILE_APPEND);
            }
        }
        //file_put_contents('log/memcached_key.log', $mem_key . "\n", FILE_APPEND);
        // if (empty($connector)) {

        if (isset($connector_key)) {
            if (is_array($connector_key)) {
                foreach ($connector_key as $key => $value) {
                    $db2->orwhere("connector_key = UNHEX(?)", [$value]);
                }
            } else {
                $db2->orwhere("connector_key = UNHEX(?)", [$connector_key]);
            }
        }
        $connector = $db2->get("connector", null, "HEX(connector_key) as connector_key,type,name,data,created");

        foreach ($connector as $key => $value) {
            $connector[$key]["data"] = json_decode($value["data"], true);
        }

        if (extension_loaded('memcached')) {
            if ($mem_key != "") {
                $expiration = 3600;
                $memcached->set($mem_key, $connector, $expiration);
            }
        }
        // }

        $return["code"] = 1;
        $return["result"]["data"] = $connector;
        $return["result"]["msg"] = "sukses";
        return $return;
    }

    public function update($connector_key, $type, $name, $data)
    {
        global $keya, $c_time, $app;
        $db = $app->db;
        $db2 = $app->db2;

        $type_data = $this->get_type();

        $valid = true;
        foreach ($type_data as $key => $value) {

            if (strtolower($key) == strtolower($type)) {
                $req = $value["Req"];
                foreach ($req as $key2 => $value2) {

                    if (!isset($data[$value2["var_name"]])) {
                        $valid = false;
                    } else {
                        if ($data[$value2["var_name"]] == "") {
                            $valid = false;
                        }
                    }
                }
            }

        }

        if ($valid == false) {return false;}
        if (isset($data['account_id']) && $type == "googleads") {
            
            $db2->where("account_hash = UNHEX(?) and status = 1", [$data['account_id']]);
            $x = $db2->getone("integration");
            if($db2->count >0){
                $x["data"] = unserialize($x["data"]);
                $data['account_id'] = $x["account_id"];
                $data['access_token'] = $x["data"]['access_token'];
                $data['refresh_token'] = $x["data"]['refresh_token'];
            }
        }
        $update["name"] = $name;
        $update["type"] = strtolower($type);
        $update["data"] = json_encode($data);

        $db2->where("connector_key = UNHEX(?)", [$connector_key]);
        if ($id = $db2->update("connector", $update)) {
            $return["code"] = 1;

            $return["result"]["data"]["key"] = $connector_key;
            $return["result"]["msg"] = "sukses";
        } else {
            $return["code"] = 1;
            $return["result"]["msg"] = "error, failed to update";
        }

        if ($type == "snackvideo") {
            if (isset($data["test_clickid"])) {
                if ($data["test_clickid"] != "") {
                    $test_clickid = $data["test_clickid"];
                    $events = $data["event"];
                    $access_token = "";
                    $pixel_id = "";
                    if (isset($data["access_token"])) {if ($data["access_token"] != "") {$access_token = $data["access_token"];}}
                    if (isset($data["pixel_id"])) {if ($data["pixel_id"] != "") {$pixel_id = $data["pixel_id"];}}

                    if (count($events) > 0 && $access_token != "" && $pixel_id != "") {
                        $snack = new trigger_snack();
                        foreach ($events as $key => $event) {
                            $snack->test_event($access_token, $pixel_id, $test_clickid, $event);
                        }
                    }
                }
            }
        }

        return $return;

    }

    public function get_detail($connector_id)
    {
        global $keya, $c_time, $app;
        $db = $app->db;
        $db2 = $app->db2;
        $tb_connector = "project_connector";

        $db2->where("id", $connector_id);
        return $db2->getone("connector");
    }

    public function get_detail_key($connector_key)
    {
        global $keya, $c_time, $app;
        $db = $app->db;
        $db2 = $app->db2;

        $db2->where("connector_key = UNHEX(?)", [$connector_key]);
        $result = $db2->getone("connector", "HEX(connector_key) as connector_key,type,name,data,created");
        if($db2->count > 0){
            $result["data"] = json_decode($result["data"], true);
        }
        return $result;
    }

    public function delete($connector_key)
    {
        global $keya, $c_time, $app;
        $db = $app->db;
        $db2 = $app->db2;

        $db2->where("connector_key = UNHEX(?)", [$connector_key]);
        $con = $db2->getone("connector");
        if ($con != null) {
            $id = $con["connector_id"];

            $db2->where("connector_key = UNHEX(?)", [$connector_key]);
            if ($db2->delete("connector")) {
                $db2->where("connector_key = UNHEX(?)", [$connector_key]);
                $db2->delete("log_connector");
                $return["code"] = 1;
                $return["result"]["msg"] = "sukses";
            } else {
                $return["code"] = 0;
                $return["result"]["msg"] = "unknown error";
            }
        } else {
            $return["code"] = 0;
            $return["result"]["msg"] = "connector not found";
        }

        return $return;

    }

    public function get_log_type($connector_key)
    {
        global $keya, $c_time, $app;
        $db = $app->db;
        $db2 = $app->db2;

        $db2->groupBy("event");
        $db2->orderBy("event", "ASC");
        $res = $db2->get("log_connector", null, "event");

        if ($res != null) {
            $return["code"] = 1;
            $return["result"]["msg"] = "sukses";
            $return["result"]["data"] = $res;
        } else {
            $return["code"] = 0;
            $return["result"]["msg"] = "empty";
        }

        return $return;
    }

    public function get_log($connector_key, $start = 1, $type = null)
    {
        global $keya, $c_time, $app;
        $db = $app->db;
        $db2 = $app->db2;

        if ($start == 1) {
            $start = 0;
        } else {
            $start = $start * 20;
        }

        $db2->join("connector", "connector.connector_key = log_connector.connector_key");
        $db2->where("connector.connector_key = UNHEX(?)", [$connector_key]);
        if ($type != null) {
            $db2->where("event", $type);
        }
        $db2->orderBy("log_connector.waktu", "DESC");
        if ($res = $db2->get("log_connector", [$start, 20], "log_connector.waktu,log_connector.vid,log_connector.error,log_connector.event,log_connector.result")) {

            $return["code"] = 1;

            foreach ($res as $key => $value) {
                $raw_vid = vid_to_raw($value["vid"]);
                $res[$key]["vid"] = $raw_vid;
                $res[$key]["result"] = json_decode($res[$key]["result"], true);
                $res[$key]["result"] = $res[$key]["result"]["msg"];
            }

            $db2->join("connector", "connector.connector_key = log_connector.connector_key");
            $db2->where("connector.connector_key = UNHEX(?)", [$connector_key]);
            if ($type != null) {$db2->where("event", $type);}
            $total = @$db2->getone("log_connector", "count(*) as c")["c"];

            $return["result"]["data"] = $res;
            $return["result"]["total"] = $total;
            $return["result"]["msg"] = "sukses";
        } else {
            //echo $db2->getLastQuery();
            $return["code"] = 0;

            $return["result"]["msg"] = "error";
        }

        return $return;
    }

    /**
     * Detect if visitor should use CTWA (Click to WhatsApp) tracking
     * @param object $db2 Database connection
     * @param string $vid Visitor ID
     * @param array $visitor Visitor data
     * @return array Detection result with is_ctwa and use_ctwa_connector flags
     */
    private function detectCTWA($db2, $vid, $visitor)
    {
        $result = [
            'is_ctwa' => false,
            'use_ctwa_connector' => false,
            'ctwa_clid' => null,
            'waba_id' => null,
            'ctwa_connector' => null
        ];

        if (!$visitor || !isset($visitor["data"])) {
            return $result;
        }

        $visitor_data = unserialize($visitor["data"]);
        if (!$visitor_data) {
            return $result;
        }

        // Check for WABA ID in visitor data - this is the only criteria for CTWA
        $waba_id = null;
        if (isset($visitor_data["last_campaign"]["data"]["waba_id"]) && !empty($visitor_data["last_campaign"]["data"]["waba_id"])) {
            $waba_id = $visitor_data["last_campaign"]["data"]["waba_id"];
        }

        // Extract waba_id from URL if not found in visitor data
        if (!$waba_id && isset($visitor["page_url"]) && !empty($visitor["page_url"])) {
            $url = $visitor["page_url"];
            if (strpos($url, 'waba_id=') !== false) {
                parse_str(parse_url($url, PHP_URL_QUERY), $query_params);
                if (isset($query_params['waba_id']) && !empty($query_params['waba_id'])) {
                    $waba_id = $query_params['waba_id'];
                }
            }
        }

        // Mark as CTWA only if we have WABA ID
        if ($waba_id) {
            $result['is_ctwa'] = true;
            $result['waba_id'] = $waba_id;

            // Check if we have a dedicated CTWA connector configured
            $db2->where("type", ["facebook_ctwa", "Facebook_ctwa"], "IN");
            $ctwa_connectors = $db2->get("connector");
            
            foreach ($ctwa_connectors as $connector) {
                $connector_data = json_decode($connector["data"], true);
                if ($connector_data && isset($connector_data["waba_id"]) && 
                    $connector_data["waba_id"] == $waba_id) {
                    $result['use_ctwa_connector'] = true;
                    $result['ctwa_connector'] = [
                        'connector_key' => bin2hex($connector["connector_key"]),
                        'type' => $connector["type"],
                        'name' => $connector["name"],
                        'data' => $connector_data,
                        'created' => $connector["created"]
                    ];
                    break;
                }
            }
        }

        return $result;
    }

}
