<?php 

class meta{

	function set_meta($name,$value)
	{
		global $app;
		$db = $app->db;
		$db2 = $app->db2;
		$name = strtolower(trim($name));
		$meta_key = md5($name);

		if(is_array($value))
		{
			$value = json_encode($value);
		}

		$q = "INSERT INTO meta (meta_key, name, value) VALUES(UNHEX(?), ?, ?) ON DUPLICATE KEY UPDATE value=?";

		if($meta_id = $db2->rawQuery($q,array($meta_key,$name,$value,$value)))
		{
			$return["code"] = 1;
			$return["result"]["data"] = $meta_id;
			$return["result"]["msg"] = "sukses";
		}
		else{
			$return["code"] = 0;
			$return["result"]["msg"] = "error";
		}

		if (extension_loaded('memcached')) {
			$project_id = $app->project_id;
			$memcached = new Memcached();
			$memcached->addServer('127.0.0.1', 11211); 
			// 'Check Connected to Memcached server.';
			if ($memcached->getVersion()) {
				$mem_key = $project_id.'_meta_key_'.$name;
				$result = $memcached->delete($mem_key);
			}
		}

		return $return;

	}


	function get_meta($name)
	{
		global $app;
		$db = $app->db;
		$db2 = $app->db2;
		$memcached_exist = false;
		if (extension_loaded('memcached')) {
			$project_id = $app->project_id;
			$memcached = new Memcached();
			$memcached->addServer('127.0.0.1', 11211); 
			// 'Check Connected to Memcached server.';
			if ($memcached->getVersion()) {
				$mem_key = $project_id.'_meta_key_'.$name;
				$mem_value = $memcached->get($mem_key);
				if ($memcached->getResultCode() == Memcached::RES_SUCCESS) {
					if(isset($mem_value))
					{
						if($mem_value != ""){
							$cachedData = unserialize($mem_value);
							if($cachedData["code"]==1){
							return $cachedData; // Return cached data if found
							}
						}
					}
				}
				$memcached_exist = true;
			}
		}

		$name = strtolower(trim($name));
		$meta_key = md5($name);
	
		$db2->where("meta_key = UNHEX(?)",[$meta_key]);
		$meta = $db2->getone("meta");


		if($meta != NULL)
		{
			$value = $meta["value"];
			$tmp = json_decode($value,true);

			if($tmp != NULL)
			{
				$return["result"]["data"] = $tmp;
			}
			else
			{
				$return["result"]["data"] = $value;
			}

			if($return["result"]["data"] == "")
			{
				$return["code"] = 0;
				$return["result"]["msg"] = "error";

			}else{
				$return["code"] = 1;
			
				$return["result"]["msg"] = "sukses";
			}

			
		}
		else
		{
			$return["code"] = 0;
			$return["result"]["msg"] = "error";
			return $return;
		}

		if($memcached_exist){
			$value = serialize($return);
			$expiration = 3600 * 1;
			$memcached->set($mem_key, $value, $expiration);
		}

		return $return;
	}

}

?>