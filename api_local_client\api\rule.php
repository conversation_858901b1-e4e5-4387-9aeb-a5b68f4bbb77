<?php 

function update_rule($param)
{
  
    $rules = array(
        'token'      => 'required',
        'rule_key'      => 'required',
        'name'      => 'required',
        'formula'      => 'required',
        'raw'      => 'required',
        'task'      => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    if(!isset($freq)){
        $freq = 0;
    }

    $rule_fb = new RuleFacebook();
    $x = $rule_fb->update_rule($rule_key,$param);

    return $x;
}
function get_rule($param)
{
  
    $rules = array(
        'token'      => 'required',
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    if(!isset($freq)){
        $freq = 0;
    }

    $rule_fb = new RuleFacebook();
    $x = $rule_fb->get_rule();

    return $x;
}
function create_rule($param)
{
  
    $rules = array(
        'token'      => 'required',
        'name'      => 'required',
        'formula'      => 'required',
        'raw'      => 'required',
        'task'      => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    if(!isset($freq)){
        $freq = 0;
    }

    $rule_fb = new RuleFacebook();
    $x = $rule_fb->add_rule($name,$formula,$raw,$task,$freq,$target);

    return $x;
}

function create_target($param)
{
    $rules = array(
        'token'      => 'required',
        'target_name'      => 'required',
        'level'      => 'required',
        'method'      => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $target_rule["method"] = $method;

    if($method != "specific" && $method != "selection"){$msg['code'] = 0;$msg["result"]['msg'] = 'invalid method';return $msg;}

    if($method == "specific")
    {
        if(!isset($item)){$msg['code'] = 0;$msg["result"]['msg'] = 'Item not found';return $msg;}
        if(!is_array($item)){$msg['code'] = 0;$msg["result"]['msg'] = 'Invalid Item';return $msg;}

        $target_rule["item"] = $item;
    }
    if($method == "selection")
    {
        if(!isset($filter_operator)){$msg['code'] = 0;$msg["result"]['msg'] = 'Filter Operator not found';return $msg;}
        if(!isset($term)){$msg['code'] = 0;$msg["result"]['msg'] = 'Term not found';return $msg;}
        
        $target_rule["operator"] = $filter_operator;
        $target_rule["term"] = $term;
    }

    $target_rule["level"] = $level;

    $rule_fb = new RuleFacebook();
    $ret["code"] = 1;
    $ret["msg"] = "sukses";
    $ret["data"] = $rule_fb->create_target($target_name,$target_rule);

    return $ret;
}

function get_target($param)
{
    $rules = array(
        'token'      => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    if(!isset($term)){
        $term = null;
    }
    $rule_fb = new RuleFacebook();

    $msg['code'] = 1;
    $msg['result']['msg'] = 'sukses';
    $msg['result']["data"] = $rule_fb->get_target($term);

 

    return $msg;
}

function get_target_fb($param){
    global $app;
    $rules = array(
        'token'      => 'required',
        "level" => "required"
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $name = null;
    if(isset($param['name'])){
        $name = $param['name'];
    }

    $r = new RuleFacebook();
    $x = $r->get_target_fb($level);

    if($x == false){
        $msg['code'] = 0;
        $msg["result"]['msg'] = 'Error';
    }else{
        if(count($x) > 0){
            $msg['code'] = 1;
            $msg['result']['msg'] = 'sukses';
            $msg['result']["data"] = $x;
        }else{
            $msg['code'] = 1;
            $msg['result']['msg'] = 'empty';
        }
    }

    return $msg;
}

function get_metric($param){

    extract($param);
    assign_child_db($project_id);

    $r = new rule();
    $data = $r->get_metric();

    foreach($data as $key=>$value)
    {
        $data[$key]["id"] = preg_replace('/\s+/', '_', $value["id"]);
        if( str_contains($value["id"],"v-") )
        {
            unset($data[$key]);
        }
    }
    if($data)
    {
        $msg['code'] = 1;
        $msg["result"]['msg'] = 'Sukses';
        $msg["result"]['data'] = array_values($data);
    }
    else{
        $msg['code'] = 0;
        $msg["result"]['msg'] = 'Error';
    }
    
    return $msg;
}