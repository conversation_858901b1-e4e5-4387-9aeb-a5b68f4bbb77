<?php
class stok
{
	// status = 1 = masuk / 0 = keluar
	public function update($user_id, $produk_id, $stok, $status = 1){
		global $keya, $c_time, $app;
		$app->db->where("agen_id = UNHEX(?) and produk_id = ?", array($user_id, $produk_id));
        $stoks = $app->db->getone("x_agen_stok");
        if ($app->db->count > 0) {
			if($status == 1){
				$data["stok"] = $stoks['stok'] + $stok;
			}else{
				$data["stok"] = $stoks['stok'] - $stok;
			}			
            $app->db->where("agen_id = UNHEX(?) and produk_id = ?", array($user_id, $produk_id));
            if($app->db->update("x_agen_stok", $data)){
				$this->add_history($user_id, $produk_id, $stok, $status);
				$msg['code']=1;
				$msg['msg']= 'Sukses update stok produk';
			}else{
				$msg['code']=0;
				$msg['msg']= 'Gagal update stok produk ';
			}
		}else{
			$data["agen_id"]   = $app->db->func("UNHEX(?)", array($user_id));
            $data["produk_id"] = $produk_id;
            $data["stok"] = $stok;
            if($app->db->insert("x_agen_stok", $data)){
				$this->add_history($user_id, $produk_id, $stok, $status);
				$msg['code']=1;
				$msg['msg']= 'Sukses tambah stok produk';
			}else{
				$msg['code']=0;
				$msg['msg']= 'Gagal tambah stok produk';
			}
			//echo $app->db->getLastQuery();
		}
		return $msg;
	}
	
	// status = 1 = masuk / 0 = keluar
	public function add_history($user_id, $produk_id, $stok, $status = 1){
		global $keya, $c_time, $app;
		$data["agen_id"]   = $app->db->func("UNHEX(?)", array($user_id));
		$data["produk_id"] = $produk_id;
		$data["jumlah"] = $stok;
		$data["status"] = $status;
		$app->db->insert("x_agen_stok_history", $data);
	}
	
	public function get($user_id){
    	global $keya, $c_time, $app;
		$app->db->join("x_produk p", "s.produk_id=p.id", "LEFT");
		$app->db->join("x_agen a", "s.agen_id=a.user_id", "LEFT");
		$app->db->where("s.agen_id = UNHEX(?)", array($user_id));
		$app->db->orderBy("p.id", "asc");
		$res = $app->db->get ("x_agen_stok s", array(0, 10), "s.*, p.nama as produk, p.image, p.harga");
		$pos = array();$i = 0;
		foreach($res as $r){
			$pos[$i]["id"] = $r['id'];
			$pos[$i]["nama"] = $r['produk'];
			$pos[$i]["image"] = $r['image'];
			$pos[$i]["harga"] = $r['harga'];
			$pos[$i]["stok"] = $r['stok'];
			$i++;
		}
    	return $pos;
    }
	
}