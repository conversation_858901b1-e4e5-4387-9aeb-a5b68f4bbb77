<?php

class ManualReport
{

	public function getFormat()
	{
		$ret["lead"] = "ID,Phone Number,CS Phone Number";
		$ret["prospek"] = "ID,Phone Number,CS Phone Number";
		$ret["purchase"] = "ID,Phone Number,CS Phone Number,Value";

		return $ret;
	}

	
	function getAllFilesWithoutProjectId()
	{
		$directory = 'manual_report';
		$files = [];

		if (file_exists($directory)) {
			$files = glob($directory . '/*/*'); // Assuming files are in subdirectories
		}

		return $files;
	}

 
	function prosess_quee()
	{
		$files = $this->getAllFilesWithoutProjectId();
		$i = 0;
		$ret = [];
		foreach ($files as $file) {
			$file_name = basename($file);
			$file_tmp = explode("_", $file_name);
			
			$project_id = $file_tmp[0];
			$event = $file_tmp[1];
			
		
			$ret[] = $this->prosess_quee_file($project_id,$event,$file);

			if($i > 1){
				break;
			}
			$i++;
		}
		return $ret;
	}

	function prosess_quee_file($project_id,$event,$file_name)
	{
		$file_handle = fopen($file_name, 'r');
		while (($line = fgetcsv($file_handle)) !== false) {
			$jumlah_kolom = count($line);
			$line[$jumlah_kolom] = $event;
			$data[] = $line;
		}
		fclose($file_handle);

		assign_child_db($project_id);
		$mr = new ManualReport();
		foreach($data as $key => $value){
			unset($value[count($value)-1]);
			
			if($event == "prospek"){
				$mr->addProspek(implode("\n", $value));
			}else if($event == "lead"){
				$mr->addLead(implode("\n", $value));
			}else if($event == "purchase"){
				$mr->addPurchase(implode("\n", $value));
			}
				
			
		}
		unlink($file_name);
		return $file_name;
	}

	public function get_project_quee($project_id, $limit = null)
	{
		$directory = 'manual_report/' . $project_id;
		$data = [];

		if (!file_exists($directory)) {
		
			return $data; // Return empty if directory does not exist
		}

		$files = glob($directory . '/*.csv');
		$count = 0;
		
		foreach ($files as $file) {
			if ($limit !== null && $count >= $limit) {
				break;
			}

			$file_name = basename($file);
			$file_name = explode("_", $file_name);
			$event = $file_name[1];
		
		

			$file_handle = fopen($file, 'r');
			while (($line = fgetcsv($file_handle)) !== false) {
				$jumlah_kolom = count($line);
				$line[$jumlah_kolom] = $event;
				$data[] = $line;
				$count++;

				if ($limit !== null && $count >= $limit) {
					break 2; // Break out of both loops
				}
			}
			fclose($file_handle);
		}

		return $data;
	}

	public function addquee($project_id, $data,$event)
	{
		global $app;

		$directory = 'manual_report/' . $project_id;
		if (!file_exists($directory)) {
			mkdir($directory, 0755, true);
		}

		$lines = explode("\n", $data);
		$chunked_lines = array_chunk($lines, 10);
		$file_index = 1;

		foreach ($chunked_lines as $chunk) {
			$file_name = $directory . '/'.$project_id.'_'.$event.'_' . $file_index . '.csv';
			
			// Cek apakah file sudah ada, jika ada, tambahkan index
			while (file_exists($file_name)) {
				$file_index++;
				$file_name = $directory . '/'.$project_id.'_'.$event.'_' . $file_index . '.csv';
			}

			$file_handle = fopen($file_name, 'w');

			foreach ($chunk as $line) {
				
				fwrite($file_handle, $line . PHP_EOL);
			}

			fclose($file_handle);
			$file_index++;
		}

		return true;
	}

	public function addLead($param, $connector = null)
	{
		global $app;
		$db2 = $app->db2;

		$t = new track();
		$data = array();

		$param = explode("\n", $param);

		$ret = array();

		foreach ($param as $key => $value) {
			$param2 = explode(",", $value);

			$is_new_kontak = false;

			$log["event"] = "Lead";
			$log["input"] = trim(preg_replace('/\s+/', ' ', implode(",", $param2)));
			$log["time"] = date("Y-m-d H:i:s");

			$phone = 0;
			$nope_cs = 0;
			$visitor_id = 0;

			if (isset($param2[2]) && trim($param2[2]) != "") {
				//$nope_cs = $param2[2];
				$nope_cs = preg_replace('/\D/', '', $param2[2]);

			}
			if (isset($param2[1]) && trim($param2[1]) != "") {
				$phone = $param2[1];
				$phone = preg_replace('/\D/', '', $param2[1]);
			}
			if (isset($param2[0]) && trim($param2[0]) != "") {
				$visitor_id = $param2[0];
			}

			if ($phone == 0) {
				$log["error"] = 1;
				$log["result"] = "Error,Phone empty";
				$db2->insert("manual_report_history", $log);
				$ret[] = $log;
				continue;
			}

			if ($nope_cs == 0) {
				$log["error"] = 1;
				$log["result"] = "Error, CS Phone empty";
				$db2->insert("manual_report_history", $log);
				$ret[] = $log;
				continue;
			}
			if ($visitor_id == 0) {
				$log["error"] = 1;
				$log["result"] = "Error, Visitor Id Empty";
				$db2->insert("manual_report_history", $log);
				$ret[] = $log;
				continue;
			} else {
				$raw_visitor_id = $visitor_id;
				$visitor_id = str_replace(".", "", $visitor_id);
				$visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");
				$db2->where("visitor_id", $visitor_id);
				$visitor = $db2->getone("visitor");
				if ($visitor == null) {
					$log["error"] = 1;
					$log["result"] = "Error, Visitor Id Not Found";
					$db2->insert("manual_report_history", $log);
					$ret[] = $log;
					continue;
				}
			}

			$new_kontak["phone"] = $phone;
			$new_kontak["phone_hash"] = $db2->func("UNHEX(?)", [md5($phone)]);
			$new_kontak["created"] = date("Y-m-d H:i:s");

			if ($db2->setQueryOption('IGNORE')->insert("new_kontak", $new_kontak)) {
				$is_new_kontak = true;
			}

			//echo $db2->getLastQuery();

			//if ($is_new_kontak) {

			$t->lead($is_new_kontak, $visitor, $nope_cs, $phone);
			//}

			$log["error"] = 0;
			$log["result"] = $raw_visitor_id . " Success Add Lead";
			$db2->insert("manual_report_history", $log);
			$ret[] = $log;
		}
		return $ret;
	}

	public function addProspek($param, $connector = null)
	{
		global $app;
		$db2 = $app->db2;

		$is_new_kontak = false;

		$t = new track();
		$data = array();

		$param = explode("\n", $param);

		$ret = array();

		foreach ($param as $key => $value) {
			$param2 = explode(",", $value);

			$is_new_kontak = false;

			$log["event"] = "Prospek";
			$log["input"] = trim(preg_replace('/\s+/', ' ', implode(",", $param2)));
			$log["time"] = date("Y-m-d H:i:s");

			$phone = 0;
			$nope_cs = 0;
			$visitor_id = 0;

			if (isset($param2[2]) && trim($param2[2]) != "") {
				$nope_cs = $param2[2];
				$nope_cs = preg_replace('/\D/', '', $param2[2]);
			}
			if (isset($param2[1]) && trim($param2[1]) != "") {
				$phone = $param2[1];
				$phone = preg_replace('/\D/', '', $param2[1]);
			}
			if (isset($param2[0]) && trim($param2[0]) != "") {
				$visitor_id = $param2[0];
			}

			if ($phone == 0) {
				$log["error"] = 1;
				$log["result"] = "Error,Phone empty";
				$db2->insert("manual_report_history", $log);
				$ret[] = $log;
				continue;
			}

			if ($nope_cs == 0) {
				$log["error"] = 1;
				$log["result"] = "Error, CS Phone empty";
				$db2->insert("manual_report_history", $log);
				$ret[] = $log;
				continue;
			}
			if ($visitor_id == 0) {
				$log["error"] = 1;
				$log["result"] = "Error, Visitor Id Empty";
				$db2->insert("manual_report_history", $log);
				$ret[] = $log;
				continue;
			} else {
				$raw_visitor_id = $visitor_id;
				$visitor_id = str_replace(".", "", $visitor_id);
				$visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");
				$db2->where("visitor_id", $visitor_id);
				$visitor = $db2->getone("visitor");
				if ($visitor == null) {
					$log["error"] = 1;
					$log["result"] = "Error, Visitor Id Not Found";
					$db2->insert("manual_report_history", $log);
					$ret[] = $log;
					continue;
				}
			}

			//////////////////////////// cek udah ke triger kontak belum ///////////////////////////////
			if ($visitor["lead"] == 0) {
				$new_kontak["phone"] = $phone;
				$new_kontak["phone_hash"] = $db2->func("UNHEX(?)", [md5($phone)]);
				$new_kontak["created"] = date("Y-m-d H:i:s");

				if ($db2->setQueryOption('IGNORE')->insert("new_kontak", $new_kontak)) {
					$is_new_kontak = true;
				}
				if ($is_new_kontak) {

					$t->lead($is_new_kontak, $visitor, $nope_cs, $phone);
				}
			}
			//////////////////////////////////////// end ///////////////////////////////////////////////

			$t->prospek($nope_cs, $visitor, $phone);

			$log["error"] = 0;
			$log["result"] = $raw_visitor_id . " Success Add Prospek";
			$db2->insert("manual_report_history", $log);
			$ret[] = $log;
		}
		return $ret;

	}

	public function addPurchase($param, $connector = null)
	{
		global $app;
		$db2 = $app->db2;

		$is_new_kontak = false;

		$t = new track();
		$data = array();

		$param = explode("\n", $param);

		$ret = array();

		foreach ($param as $key => $value) {
			$param2 = explode(",", $value);

			$is_new_kontak = false;

			$log["event"] = "Purchase";
			$log["input"] = trim(preg_replace('/\s+/', ' ', implode(",", $param2)));
			$log["time"] = date("Y-m-d H:i:s");

			$phone = 0;
			$nope_cs = 0;
			$visitor_id = 0;
			$value = 0;

			if (isset($param2[3]) && trim($param2[3]) != "") {
				$value = $param2[3];
				$value = preg_replace('/\D/', '', $param2[3]);
			}
			if (isset($param2[2]) && trim($param2[2]) != "") {
				$nope_cs = $param2[2];
				$nope_cs = preg_replace('/\D/', '', $param2[2]);
			}
			if (isset($param2[1]) && trim($param2[1]) != "") {
				$phone = $param2[1];
				$phone = preg_replace('/\D/', '', $param2[1]);
			}
			if (isset($param2[0]) && trim($param2[0]) != "") {
				$visitor_id = $param2[0];
			}

			if ($phone == 0) {
				$log["error"] = 1;
				$log["result"] = "Error,Phone empty";
				$db2->insert("manual_report_history", $log);
				$ret[] = $log;
				continue;
			}

			if ($nope_cs == 0) {
				$log["error"] = 1;
				$log["result"] = "Error, CS Phone empty";
				$db2->insert("manual_report_history", $log);
				$ret[] = $log;
				continue;
			}
			if ($visitor_id == 0) {
				$log["error"] = 1;
				$log["result"] = "Error, Visitor Id Empty";
				$db2->insert("manual_report_history", $log);
				$ret[] = $log;
				continue;
			} else {
				$raw_visitor_id = $visitor_id;
				$visitor_id = str_replace(".", "", $visitor_id);
				$visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");
				$db2->where("visitor_id", $visitor_id);
				$visitor = $db2->getone("visitor");
				if ($visitor == null) {
					$log["error"] = 1;
					$log["result"] = "Error, Visitor Id Not Found";
					$db2->insert("manual_report_history", $log);
					$ret[] = $log;
					continue;
				}
			}

			//////////////////////////// cek udah ke triger kontak belum ///////////////////////////////
			if ($visitor["lead"] == 0) {
				$new_kontak["phone"] = $phone;
				$new_kontak["phone_hash"] = $db2->func("UNHEX(?)", [md5($phone)]);
				$new_kontak["created"] = date("Y-m-d H:i:s");

				if ($db2->setQueryOption('IGNORE')->insert("new_kontak", $new_kontak)) {
					$is_new_kontak = true;
				}
				if ($is_new_kontak) {

					$t->lead($is_new_kontak, $visitor, $nope_cs, $phone);
				}
			}
			//////////////////////////////////////// end ///////////////////////////////////////////////

			$t->purchase($nope_cs, $visitor, $phone, $value);
			$log["error"] = 0;
			$log["result"] = "Success";
			$log["result"] = $raw_visitor_id . " Success Add Purchase";
			$db2->insert("manual_report_history", $log);
			$ret[] = $log;
		}
		return $ret;

	}





	
}
