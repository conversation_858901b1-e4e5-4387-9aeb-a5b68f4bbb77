<?php 
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Jwt\Generator;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Jwt\Parser;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Jwt\Cryptography\Algorithms\Hmac\HS256;

function set_profile($param)
{
    global $app;
    $db = $app->db;
    $u = new user();
    $msg = array();
    $gump = new GUMP(); 
    $msg['code'] = 0 ;
    $gump->validation_rules(array(
        'token'      => 'required',
        'name'      => 'required|max_len,100|min_len,3',
        ));
    $validated = $gump->run($param);
    if($validated === false) {
        $msg['code'] = 0 ;
        $msg['msg'] = $gump->get_readable_errors(true); 
    }else{
        $token = $u->check_token($param['token']);
        if($token){
            $dd["name"]   = $param['name'];
            if(isset($param['email'])){
                $dd["email"]   = $param['email'];
            }
            $db->where("user_id = UNHEX(?)",array($token['user_id']));
            if($db->update("x_user", $dd)){
                $u->set_user_meta($token['user_id'], 'name', $param['name']);
                if(isset($param['email'])){
                    $u->set_user_meta($token['user_id'], 'email', $param['email']);
                }
                $msg['msg'] = 'Success change profile';
                $msg['code'] = 1 ;
            }else{
                $msg['msg'] = 'Error change profile';
                $msg['code'] = 0 ;
            }   
        }else{
            $msg['code'] = 0 ;
            $msg['msg'] = 'Token not valid!!';  
        }
    }
    return $msg;
}


function schedule_add($param)
{
     $rules = array(
        'token'      => 'required',
        'project_key'      => 'required',
        'user_id' => 'required',
        'day' => 'required',
        );
    validate_param($rules,$param);

     //////////////////////////////// cek user token
    $u = new user();
    $token = $u->check_token($param['token']);
    if($token == false) {$msg['code'] = 0 ;$msg['msg'] = 'Token Not Valid'; return $msg;}
    ///////////////////////////////// end cek user token ////////////

    //////////////////////////////// get project id ///////////
    $project = new project();
    $project = $project->get_by_key($param["project_key"]);
    $project_id = $project["data"]["project_id"];
    //////////////////////////////// end get project id ///////////
    extract($param);
    $j = new jadwal_cs($project_id);
    return $j->add($user_id,$day);

}

function schedule_get($param)
{
     $rules = array(
        'token'      => 'required',
        'project_key'      => 'required',
        'user_id' => 'required',
        );
    validate_param($rules,$param);

     //////////////////////////////// cek user token
    $u = new user();
    $token = $u->check_token($param['token']);
    if($token == false) {$msg['code'] = 0 ;$msg['msg'] = 'Token Not Valid'; return $msg;}
    ///////////////////////////////// end cek user token ////////////

    //////////////////////////////// get project id ///////////
    $project = new project();
    $project = $project->get_by_key($param["project_key"]);
    $project_id = $project["data"]["project_id"];
    //////////////////////////////// end get project id ///////////
    extract($param);
   
    $j = new jadwal_cs($project_id);
    return $j->get($user_id);

}

function get_label($param)
{
    $rules = array(
        'token'      => 'required',
        'project_key'      => 'required',
        'user_id' => 'required'
        );
    validate_param($rules,$param);

    //////////////////////////////// cek user token
    $u = new user();
    $token = $u->check_token($param['token']);
    if($token == false) {$msg['code'] = 0 ;$msg['msg'] = 'Token Not Valid'; return $msg;}
    ///////////////////////////////// end cek user token ////////////

    //////////////////////////////// get project id ///////////
    $project = new project();
    $project = $project->get_by_key($param["project_key"]);
    $project_id = $project["data"]["project_id"];
    //////////////////////////////// end get project id ///////////

    $labels = $u->get_label($project_id,$user_id);

}

function get_permission_by_role($param)
{
    return permission::get_role_permission($param["type"]);
}

function get_permission($param)
{

    $u = new user();
    $msg = array();
    $gump = new GUMP(); 
    $msg['code'] = 0 ;
    $gump->validation_rules(array(
        'token'      => 'required',
        'project_key'      => 'required'
        ));
    $validated = $gump->run($param);
    if($validated === false) {
        $msg['code'] = 0 ;
        $msg['msg'] = $gump->get_readable_errors(true); 
    }else{
        $token = $u->check_token($param['token']);
        if($token){
            $project = new project();
            $msg = $project->get_by_key($param["project_key"]);
            if($msg['code']==1){
                $project_id = $msg['data']["project_id"];
                $p = new permission($project_id);
                $permission = $p->get($token["user_id"]);

                if($permission == NULL){
                    $msg['code'] = 0 ;
                    $msg['msg'] = 'Permission Not Found';  
                }else{
                    $msg["data"] = $permission;
                    $msg['code'] = 1 ;
                    $msg['msg'] = 'Success Get Permission';  
                }
            }
        }else{
            $msg['code'] = 0 ;
            $msg['msg'] = 'Token not valid!!';  
        }
    }

     return $msg;
   
}

function set_permission($param)
{
    $u = new user();
    $msg = array();
    $gump = new GUMP(); 
    $msg['code'] = 0 ;
    $gump->validation_rules(array(
        'token'      => 'required',
        'project_key'      => 'required',
        'permission'      => 'required',
        ));
    $validated = $gump->run($param);
    if($validated === false) {
        $msg['code'] = 0 ;
        $msg['msg'] = $gump->get_readable_errors(true); 
    }else{
        $token = $u->check_token($param['token']);
        if($token){
            $project = new project();
            $msg = $project->get_by_key($param["project_key"]);
            if($msg['code']==1){
                $project_id = $msg['data']["project_id"];
                $p = new permission($project_id);
                if(isset($param["user_id"])){
                    $tmp = $p->set($param["user_id"],$param["permission"]);
                }else{
                    $tmp = $p->set($token["user_id"],$param["permission"]);
                }
                if($tmp){
                    $msg['code'] = 1;
                    $msg['msg'] = 'Success update permission';  
                }else{
                    $msg['code'] = 0;
                    $msg['msg'] = 'Failed update permission';  
                }
            }
              
        }else{
            $msg['code'] = 0 ;
            $msg['msg'] = 'Token not valid!!';  
        }
    }

    return $msg;
    
}


function register($param){
    global $keya, $app;
    $db = $app->db1;
    $u = new user();
    $msg = array();
    $gump = new GUMP(); 
    $msg['code'] = 0 ;
    $gump->validation_rules(array(
        //'email'      => 'required|valid_email',
        'phone'      => 'required|max_len,50|min_len,6',
        'name'      => 'required|max_len,100|min_len,3',
        'password'      => 'required|max_len,50|min_len,6',
        'password-retry'      => 'required|max_len,50|min_len,6',
        //'gcm'      => 'required',
        ));
    $validated = $gump->run($param);
    if($validated === false) {
        $msg['code'] = 0 ;
        $msg['msg'] = $gump->get_readable_errors(true); 
    }else{  
        if($param['password'] != $param['password-retry']){
            $msg['code'] = 0 ;
            $msg['msg'] = 'Password retry not same.';   
        }else{
            $gcm = '';
            if(isset($param['gcm'])){
                $gcm = $param['gcm'];
            }
            if(!isset($param['email'])){
                $param['email'] = "";
            }
            parse_str($param['referrer'], $arg);
            $u   = new user();
            $param['phone'] = hp($param['phone']);
            if(isset($arg['ref'])){
                $res = $u->register($param['email'], $param['phone'], $param['name'], $param['password'], $arg['ref']);         
            }else{
                $res = $u->register($param['email'], $param['phone'], $param['name'], $param['password']);
            }
            if($res['code']==1){
                if(isset($param["civ"])){
                   $project = new project();
                    $project->accept_invite($res["user_id"], $param["civ"]); 
                }                
                if(isset($param['referrer'])){
                    if($param['referrer'] !=''){
                        $db->where("user_id = UNHEX(?)", array($res['user_id']));
                        $users = $db->getone("x_referrer");
                        if ($db->count == 0) {                      
                            $data["user_id"] = $db->func("UNHEX(?)", array($res['user_id']));
                            $data["ref"] = $param['referrer'];
                            $data["waktu"] = date('Y-m-d H:i:s');
                            if(isset($arg['ref'])){
                                $data["code"] = $arg['ref'];
                            }else{
                                $data["code"] = '';
                            }                       
                            $db->insert("x_referrer", $data);
                        }
                    }               
                }

            }
            $msg = $res;
        }
    }
    return $msg;
}

function login($param){
    global $keya, $app;
    $db = $app->db;
    $u = new user();
    $msg = array();
    $gump = new GUMP(); 
    $msg['code'] = 0 ;
    $gump->validation_rules(array(
        'phone'      => 'required',
        'password'      => 'required',
        //'gcm'      => 'required',
        ));
    $validated = $gump->run($param);
    if($validated === false) {
        $msg['code'] = 0 ;
        $msg['msg'] = $gump->get_readable_errors(true); 
    }else{  
        $gcm = '';$ip='';$useragent='';
        if(isset($param['gcm'])){
            $gcm = $param['gcm'];
        }
        if(isset($param['ip'])){
            $ip = $param['ip'];
        }
        if(isset($param['useragent'])){
            $useragent = $param['useragent'];
        }
        //file_put_contents('postlogin.json', json_encode($param));
       
       // if(check_access($ip, $useragent)==1){
            $msg = $u->login($param['phone'], $param['password'], $gcm, $ip, $useragent);
           
       // }else{
         //   $msg['code'] = 0 ;
        //    $msg['msg'] = "Try 10 minutes again";   
        //}
    }
    return $msg;
}

function check_access($ip='', $useragent=''){
    global $keya, $c_time, $app;
    if($ip ==''){
        $ip= get_IP_address();
    }
    if($useragent==''){
        $useragent = $_SERVER['HTTP_USER_AGENT'];
    }
    $db= $app->db;
    $db->where("ip = ? and useragent = ?", array($ip, $useragent));
    $access = $db->getone("x_user_access");
    if ($db->count > 0) {
        if($access['retry'] < 3){
            $data["waktu"] = date('Y-m-d H:i:s');
            $data["retry"] = $access['retry'] + 1;
            $db->update("x_user_access", $data);
            return 1;
        }else{
            $cek =  checkTimeDifference($access['waktu'], date('Y-m-d H:i:s'));
            if($cek){
                return 0;
            }else{
                $data["waktu"] = date('Y-m-d H:i:s');
                $data["retry"] = 0;
                $db->update("x_user_access", $data);
                return 1;
            }
        }        
    }else{
        $data["ip"]   = $ip;
        $data["useragent"] = $useragent;
        $data["retry"] = 0;
        $db->insert("x_user_access", $data);
        return 1;
    }
}

function check_token($param){  
    global $keya, $app;
    $db = $app->db;
    $u = new user();
    $msg = array();
    $gump = new GUMP(); 
    $msg['code'] = 0 ;
    $gump->validation_rules(array(
        'token'      => 'required',
        ));
    $validated = $gump->run($param);
    if($validated === false) {
        $msg['code'] = 0 ;
        $msg['msg'] = $gump->get_readable_errors(true); 
    }else{  
        try {
            $signer = new HS256($app->config->secret_jwt);
            $parser = new Parser($signer);
            $claims = $parser->parse($param['token']);
            //print_r($claims);
            if($claims){
                $db->where("id = ?", array($claims['id']));
                $device = $db->getone("x_user_device");
                if($db->count >0){
                    $msg = $u->get_user_meta($claims['user_key']);   
                    $msg['code'] = 1;
                    $msg['msg'] = 'Token valid';
                    $data = array();
                    $data['online_status'] =1;
                    if(isset($param['socket_id'])){
                        $data['socket_id'] = $param['socket_id'];
                    }
                    if(isset($param['fcm']) && strlen($param['fcm']) > 5){
                        $data['gcm'] = $param['fcm'];                                            
                    }
                    
                    if(isset($param['ip'])){
                        $data['ip'] = $param['ip'];
                    }
                    if(isset($param['useragent'])){
                        $data['user_agent'] = $param['useragent'];
                    }
                    $db->where("id = ?", array($claims['id']));
                    $db->update("x_user_device", $data);
                }else{
                    $msg['code'] = 0 ;
                    $msg['msg'] = 'Token not valid!!';  
                }            
            }else{
                $msg['code'] = 0 ;
                $msg['msg'] = 'Token not valid!!';  
            }  
        } catch (Exception $e) {
            $msg['code'] = 0 ;
            $msg['msg'] = 'Token not valid!!';  
        }              
    }
    return $msg;
}

function logout($param){  
    global $keya, $app;
    $db = $app->db;
    $u = new user();
    $msg = array();
    $gump = new GUMP(); 
    $msg['code'] = 0 ;
    $gump->validation_rules(array(
        'token'      => 'required',
        ));
    $validated = $gump->run($param);
    if($validated === false) {
        $msg['code'] = 0 ;
        $msg['msg'] = $gump->get_readable_errors(true); 
    }else{  
        $db->where("token = ?", array($param['token']));
        if ($db->delete('x_user_device')) {
            $msg["code"] = 1;
            $msg["msg"] = "success logout";
        } else {
            $msg["code"] = 0;
            $msg["msg"] = "error logout";
        }
        $msg["code"] = 1;
        $msg["msg"] = "success logout";
    }
    return $msg;
}

function disconnect($param){  
    global $keya, $app;
    $db = $app->db;
    $u = new user();
    $msg = array();
    $gump = new GUMP(); 
    $msg['code'] = 0 ;
    $gump->validation_rules(array(
        'token'      => 'required',
        ));
    $validated = $gump->run($param);
    if($validated === false) {
        $msg['code'] = 0 ;
        $msg['msg'] = $gump->get_readable_errors(true); 
    }else{  
        try {
            $signer = new HS256($app->config->secret_jwt);
            $parser = new Parser($signer);
            $claims = $parser->parse($param['token']);
            if($claims){            
                $db->where("id = ?", array($claims['id']));
                if ($db->update('x_user_device', array('online_status'=>0))) {
                    $msg["code"] = 1;
                    $msg["msg"] = "success disconnect";
                } else {
                    $msg["code"] = 0;
                    $msg["msg"] = "error disconnect";
                }
            }else{
                $msg['code'] = 0 ;
                $msg['msg'] = 'Token not valid!!';  
                return $msg;
            }   
        } catch (Exception $e) {
            $msg['code'] = 0 ;
            $msg['msg'] = 'Token not valid!!';  
        }         
    }
    return $msg;
}

function verif_otp_register($param){
    global $keya, $app;
    $db = $app->db;
    $u = new user();
    $msg = array();
    $gump = new GUMP(); 
    $msg['code'] = 0 ;
    $gump->validation_rules(array(
        'code'      => 'required',
        'otp'      => 'required',
        ));
    $validated = $gump->run($param);
    if($validated === false) {
        $msg['code'] = 0 ;
        $msg['msg'] = $gump->get_readable_errors(true); 
    }else{
        $gcm = '';
        if(isset($param['gcm'])){
            $gcm = $param['gcm'];
        }
        if(isset($param['ip'])){
            $ip = $param['ip'];
        }
        if(isset($param['useragent'])){
            $useragent = $param['useragent'];
        }  
        if(check_access()==1){
            $db->where("code = UNHEX(?)", array($param['code']));
            $verif = $db->getone("x_user_code_verif");
            if($db->count > 0){
                if($param['otp']==$verif['otp']){
                    $db->where("code = UNHEX(?)", array($param['code']));
                    $db->delete('x_user_code_verif');
                    $user_id = strtolower(bin2hex($verif["user_id"]));
                    $dd["verified"]=1;
                    $dd["status"]=1;
                    $db->where('user_id = UNHEX(?)', array($user_id));
                    $db->update("x_user", $dd);
                    
                    $db->where('user_id = UNHEX(?)', array($user_id));
                    $users = $db->getone("x_user"); 
                    $msg["token"]   = $u->add_device($user_id, $users, $gcm, $ip, $useragent);
                    $msg["code"] = 1;
                    $msg["msg"] = "Success verified account";
                }else{
                    $msg["code"] = 0;
                    $msg["msg"] = "Code otp not valid";
                }
            }else{
                $msg["code"] = 0;
                $msg["msg"] = "Code not found";
            }
        }else{
            $msg['code'] = 0 ;
            $msg['msg'] = "Try 10 minutes again";   
        }
    }
    return $msg;
}

function forgot($param){
    global $keya, $app;
    $u = new user();
    $msg = array();
    $gump = new GUMP(); 
    $msg['code'] = 0 ;
    $gump->validation_rules(array(
        'phone'      => 'required',
        ));
    $validated = $gump->run($param);
    if($validated === false) {
        $msg['code'] = 0 ;
        $msg['msg'] = $gump->get_readable_errors(true); 
    }else{  
        if(check_access()==1){
            $msg = $u->request_change_password($param['phone']);
        }else{
            $msg['code'] = 0 ;
            $msg['msg'] = "Try 10 minutes again";   
        }
    }
    return $msg;
}

function verif_otp_forgot($param){
    global $keya, $app;
    $u = new user();
    $msg = array();
    $gump = new GUMP(); 
    $msg['code'] = 0 ;
    $gump->validation_rules(array(
        'code'      => 'required',
        'otp'      => 'required',
        ));
    $validated = $gump->run($param);
    if($validated === false) {
        $msg['code'] = 0 ;
        $msg['msg'] = $gump->get_readable_errors(true); 
    }else{  
        $msg = $u->check_code_otp($param['code'], $param['otp']);
    }
    return $msg;
}

function change_pass($param){
    global $keya, $app;
    $u = new user();
    $msg = array();
    $gump = new GUMP(); 
    $msg['code'] = 0 ;
    $gump->validation_rules(array(
        'id_otp'      => 'required',
        'code_otp'      => 'required',
        'new_password'      => 'required',
        'confirm_new_password'      => 'required',
        ));
    $validated = $gump->run($param);
    if($validated === false) {
        $msg['code'] = 0 ;
        $msg['msg'] = $gump->get_readable_errors(true); 
    }else{  
        if($param['new_password'] == $param['confirm_new_password']){
            $msg = $u->change_password_by_code($param['id_otp'], $param['code_otp'], $param['new_password']);
        }else{
            $msg['code'] = 0 ;
            $msg['msg'] = "Confirm password not same!!";    
        }       
    }
    return $msg;
}

