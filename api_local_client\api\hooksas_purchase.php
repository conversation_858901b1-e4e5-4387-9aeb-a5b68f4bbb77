<?php
if ($param["type"] == "message_out")
{
    ////////////////////////////////////// end cek if prospek
    ///////////////////////////////////// cek if purchase
    $meta_result = $m->get_meta("format_purchase");
    $meta_result2 = $m->get_meta("format_purchase_value");

    if ($meta_result["code"] == 1 && $meta_result2["code"] == 1)
    {

        $rgxFormatPurchase = preg_quote($meta_result["result"]["data"], '/');
        $strregex = str_replace('%ID%', '(\d+)', $rgxFormatPurchase);
        $rgx = '/' . $strregex . '/';
        $res = preg_match($rgx, $pesan, $matches);

        if ($res !== false && $res > 0)
        {
            @file_put_contents('log/hooksas_purchase.txt', '[' . date('Y-m-d H:i:s') . "]\n\n" . json_encode($param)."\n\n\n\n", FILE_APPEND);

            $rgxFormatValuePurchase = preg_quote($meta_result2["result"]["data"], '/');
            $strregex = str_replace('%VALUE%', '([0-9,.]*)', $rgxFormatValuePurchase);
            $rgx = '/' . $strregex . '/';
            $res2 = preg_match($rgx, $pesan, $matches);
            if ($res2 !== false && $res2 > 0)
            {
                $exist_visitor = false;
                $value = preg_replace('/[.,]/', '', $matches[1]);

                $table_visitor = 'visitor';
                $db2->where("phone", $param["phone"]);
                $visitor = $db2->getone($table_visitor);

                if ($visitor != NULL)
                {
                    $exist_visitor = true;
                    $first_purchase = $visitor["first_purchase"];
                    if ($first_purchase == "NULL" || $first_purchase == "" || is_null($first_purchase))
                    {
                        $db2->where("visitor_id", $visitor_id);
                        $db2->update("visitor", ["first_purchase" => date("Y-m-d H:i:s") ]);
                    }

                    $visitor_data = unserialize($visitor["data"]);
                    $visitor_id = $visitor["visitor_id"];
                    $con = new connector();
                    $con->trigger($visitor_id, "purchase", $value);
                    if (isset($visitor_data["last_campaign"]["source"]))
                    {
                        $source = $visitor_data["last_campaign"]["source"];
                        if($source == "meta" || $source == "tiktok" || $source == "google" || $source== "organic"){
                            if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"]))
                            {
                                $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];

                                $campaign = new report();

                                $key = $param["phone"] . ";purchase";
                                $db2->setLockMethod("WRITE")
                                    ->lock(array(
                                    "report_hash",
                                    "meta",
                                    "report_data",
                                    "visitor",
                                    "connector",
                                    "log_connector_hash",
                                    "log_connector"
                                ));
                                if ($campaign->get_hash($key) == null)
                                {
                                    $campaign->add_report_data($visitor, $adcopy_id, "unik_purchase");
                                }

                                $campaign->add_report_data($visitor, $adcopy_id, "purchase");
                                $campaign->add_report_data($visitor, $adcopy_id, "purchase_value", $value);
                                $campaign->add_hash($key);
                                $db2->unlock();
                            }else{
                                hook_purchase_unknown($param["phone"]);
                            }
                        }
                        else
                        {
                            hook_purchase_unknown($param["phone"]);
                        }
                    }
                    else
                    {
                        hook_purchase_unknown($param["phone"]);
                    }
                }
                else
                {
                    hook_purchase_unknown($param["phone"]);
                }


                if ($exist_visitor)
                {
                    $orders["visitor_id"] = $visitor["visitor_id"];
                }else{
                    $orders["visitor_id"] = 0;
                }
                if (isset($param["phone"]))
                {
                    $orders["phone"] = $param["phone"];
                }else{
                    $orders["phone"] = 0;
                }
                if(isset($value)){
                    $orders["value"] = $value;
                }
                else
                {
                    $orders["value"] = 0;
                }
                $orders["created"] = date("Y-m-d H:i:s");
                if ($exist_visitor)
                {
                    $data_visitor = unserialize($visitor["data"]);
                    if(isset($data_visitor["last_campaign"]["source"])){
                        $orders["source"] = $data_visitor["last_campaign"]["source"];
                    }else{
                        $orders["source"] = "unknown";
                    }
                }else{$orders["source"] = "unknown";}
                
                $db2->insert("orders",$orders);


                $cs_log = new cs_log($param["nope_cs"]);
                $cs_log->add_stat("purchase",$value);
                
            }

        }
    }
}

function hook_purchase_unknown($phone){
    global $app;
    $db2 = $app->db2;

    $campaign = new report();

                    $key = $phone . ";purchase";
                    $db2->setLockMethod("WRITE")
                        ->lock(array(
                        "report_hash",
                        "meta",
                        "report_data",
                        "visitor",
                        "connector",
                        "log_connector_hash",
                        "log_connector"
                    ));

                    if ($campaign->get_hash($key) == null)
                    {
                        $con = new connector();
                        $campaign->add_report_data(NULL, 1, "unik_purchase");
                    }

                    $db2->where("phone",$phone);
                    $visitor = $db2->getone("visitor");

                    $campaign->add_report_data($visitor, 1, "purchase");
                    $campaign->add_report_data($visitor, 1, "purchase_value", $value);
                    $campaign->add_hash($key);
                    $db2->unlock();
}
?>
