<?php

function run($param){
    //file_put_contents('sync.txt', '1');
    $file = 'log/sync.txt';
    // The data you want to write to the file
    $data = "1";

    // Open the file for writing ('w' mode overwrites the file)
    $handle = fopen($file, 'w');

    // Check if the file was opened successfully
    if ($handle === false) {
        // Error opening the file
        return("<PERSON>rror opening the file for writing.");
    }

    // Write the data to the file
    if (fwrite($handle, $data) === false) {
        // Error writing to the file
        return("Error writing to the file.");
    }

    // Close the file handle
    if (fclose($handle) === false) {
        // Error closing the file
        return("<PERSON>rror closing the file.");
    }
    return file_get_contents('log/sync.txt');
    // $commands = array(
    //     $param['git_command'],
    // );
    // // exec commands
    // $output = '';
    // foreach($commands AS $command){
    //     $tmp = shell_exec($command);
    //     $output .= htmlentities(trim($tmp));
    // }
    // print_r($param['git_command'].$output);
    // die();
}

function run_sql($param){
    global $app;
    $rules = [
        "query" => "required",
    ];
    validate_param($rules, $param);
    if(isset($param["project_id"])){
        extract($param);
        $project_id = $param["project_id"];
        assign_child_db($project_id);
        $db2 = $app->db2;
        $db3 = $app->db3;
        $res = $db2->rawQuery(urldecode($param["query"]));
        $res = $db3->rawQuery(urldecode($param["query"]));
    }else{
        $db = $app->db;
        $res = $db->rawQuery(urldecode($param["query"]));
    }
    
    return($res);
}