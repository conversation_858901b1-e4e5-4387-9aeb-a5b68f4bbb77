<?php

namespace DynamicWebhook\DataExtractor;

use DynamicWebhook\Abstracts\AbstractDataExtractor;

/**
 * WhatsApp Business API (WABA) data extractor
 */
class WABADataExtractor extends AbstractDataExtractor
{
    /**
     * Get supported platforms
     */
    public function getSupportedPlatforms(): array
    {
        return ['waba', 'pancake'];
    }

    /**
     * Extract platform-specific data
     */
    protected function extractPlatformSpecificData(array $data, array $config, string $platform): array
    {
        $result = $this->getEmptyResult();

        if ($platform === 'pancake') {
            return $this->extractPancakeData($data, $result);
        }

        return $this->extractWABAData($data, $result);
    }

    /**
     * Extract WABA specific data
     */
    private function extractWABAData(array $data, array $result): array
    {
        // Handle messages array
        if (isset($data['entry'][0]['changes'][0]['value']['messages'])) {
            $messages = $data['entry'][0]['changes'][0]['value']['messages'];
            foreach ($messages as $message) {
                if (isset($message['to'])) {
                    $result['phone'] = $message['to'];
                    $result['message_type'] = "message_out";
                } else {
                    $result['phone'] = $message['from'];
                    $result['message_type'] = "message_in";
                }
                
                $type = $message['type'];
                if ($type === 'text') {
                    $result['message'] = $message['text']['body'] ?? '';
                }
                break; // Process first message
            }
        }
        
        // Handle message_echoes array
        if (!$result['phone'] && isset($data['entry'][0]['changes'][0]['value']['message_echoes'])) {
            $messages = $data['entry'][0]['changes'][0]['value']['message_echoes'];
            foreach ($messages as $message) {
                if (isset($message['to'])) {
                    $result['phone'] = $message['to'];
                    $result['message_type'] = "message_out";
                } else {
                    $result['phone'] = $message['from'];
                    $result['message_type'] = "message_in";
                }
                
                $type = $message['type'];
                if ($type === 'text') {
                    $result['message'] = $message['text']['body'] ?? '';
                }
                break; // Process first message
            }
        }

        // Clean phone number
        $result['phone'] = $this->normalizer->cleanPhoneNumber($result['phone']);
        
        // Normalize message
        $result['message'] = $this->normalizer->normalizeMessage($result['message']);

        // Store raw data
        $result['raw_data'] = $data;

        return $result;
    }

    /**
     * Extract Pancake specific data
     */
    private function extractPancakeData(array $data, array $result): array
    {
        // Check if this is status update (outgoing message confirmation)
        if (isset($data['entry'][0]['changes'][0]['value']['statuses'])) {
            $result['message_type'] = 'message_out';
            // For status updates, we need to get data from pancake_out table
            // This will be handled by the main processor
            return $result;
        }
        
        // Regular incoming message
        if (isset($data['entry'][0]['changes'][0]['value']['messages'])) {
            $result['message_type'] = 'message_in';
            $result['phone'] = $data['entry'][0]['changes'][0]['value']['contacts'][0]['wa_id'] ?? null;
            $result['message'] = $data['entry'][0]['changes'][0]['value']['messages'][0]['text']['body'] ?? '';
            
            // Clean phone number
            $result['phone'] = $this->normalizer->cleanPhoneNumber($result['phone']);
            
            // Normalize message
            $result['message'] = $this->normalizer->normalizeMessage($result['message']);
        }

        // Store raw data
        $result['raw_data'] = $data;

        return $result;
    }
}
