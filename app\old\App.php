<?php
class App {
	public $protocol, $hostname, $domain, $basepath, $homepath, $urlpath, $path, $fullpath,
	$config, $param, $params,$permalink, $smarty, $db,
	$prop = [],
	$part = [],
	$rules = ['default' => '404'],
	$rule = 'default',
	$route;
	private $action = [];
	/* magic */
	function __get($name) {
		if(isset($this->prop[$name])) return $this->prop[$name];
	}
	function __call($name, $args) {
		if(stripos($name, 'enable') === 0) {        // $app->enableProp_Name() - set $app->config->prop_name = true
			$key = strtolower(substr($name, 6));    // case insensitive, prop_name always set to lowercase
			$this->config->data($key, true);
		} elseif(stripos($name, 'disable') === 0) { // $app->disableProp_Name() - set $app->config->prop_name = false
			$key = strtolower(substr($name, 7));    // case insensitive, prop_name always set to lowercase
			$this->config->data($key, false);
		} elseif(stripos($name, 'set') === 0) {     // $app->setProp_Name($value) - set $app->config->prop_name = $value
			$key = strtolower(substr($name, 3));
			$this->config->data($key, $args[0]);
		}
	}
	function __construct($config = []) {
		$this->params = new Collection;
		$this->param = $this->params; // alias of params, backward compatibility
		$this->config = new Collection($config);
		$this->protocol = (isset($_SERVER['HTTPS']) || (isset($_SERVER['HTTP_CF_VISITOR']) && strpos($_SERVER['HTTP_CF_VISITOR'], 'https') !== false)) ? 'https' : 'http';
		$this->hostname = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : $_SERVER['SERVER_NAME'];
		$this->domain = stripos($this->hostname, 'www.') === 0 ? substr($this->hostname, 4) : $this->hostname;
		$this->basepath = str_replace('/'.basename($_SERVER['SCRIPT_NAME']), '', $_SERVER['SCRIPT_NAME']);
		$this->homepath = $this->basepath.'/';
		$this->urlpath = rawurldecode(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH));
		$this->path = preg_replace('#^'.preg_quote($this->basepath).'#', '', $this->urlpath);
		if($this->urlpath == $_SERVER['SCRIPT_NAME']) $this->path = dirname($this->path);
		$this->fullpath = $this->protocol.'://'.$this->hostname.$this->urlpath;
		$this->smarty = new Smarty();
		$this->smarty->setCompileDir('./templates/'.$this->domain);
	}  
	function addProperty($name, $value) {
		$this->prop[$name] = $value;
	}
	/* run */
	public function run() {
		$this->action('init');
		$this->wwwRedirector();
		$this->httpsRedirector();
		$this->searchQueryRedirector();
		
		$this->dispatch();
		$this->action('ready');
		$this->invoke($this->rule);
	}
	/* routing */
	public function rule(...$args) {
		$route = array_pop($args); // last args as route
		if(empty($args)){
			$this->rules['default'] = $route;
		}
		else{
			foreach($args as $rule) $this->rules[$rule] = $route;
		}
	}
	public function part($name, $regex) {
		$this->part["%$name%"] = "(?<$name>$regex)";
	}
	private function prepare($rule) {
		return str_replace(array_keys($this->part), array_values($this->part), preg_quote($rule));
	}
	private function dispatch() {
		foreach($this->rules as $rule => $route) {
			if(preg_match('#^'.$this->prepare($rule).'$#', $this->path, $params)) {
				$params = array_filter($params, 'is_string', ARRAY_FILTER_USE_KEY);
				$this->params->data($params);
				$this->rule = $rule;
				$this->route = $route;
				return;
			}
		}
	}
	public function invoke($rule = 'default') {

		$route = $this->rules[$rule];

	
		if(is_callable($route)) {
			call_user_func($route, $this, $this->config, $this->params, ...array_values($this->prop)); // $route is a callback
		} else {
			$route = strval($route);
			//$path = $this->config->core_path('module/').$route.'.php';
			$path = $this->config->core_path($this->config->module_path.'/').$route.'.php';
			if($path != 'index.php' && is_file($path))
				call_user_func(static function($app, $config, $params, $prop) use($path) {
					$param = $params;
					extract($prop);
					include $path;
				}, $this, $this->config, $this->params, $this->prop);
		}
	}
	public function addAction($hook, callable $action) {
		$this->action[$hook][] = $action;
	}
	public function action($hook) {
		if(!empty($this->action[$hook])) {
			foreach($this->action[$hook] as $action) call_user_func($action, $this, $this->config, $this->params, ...array_values($this->prop));
		}
		if(is_string($this->route) && !empty($this->action[$this->route.'_'.$hook])) {
			foreach($this->action[$this->route.'_'.$hook] as $action) call_user_func($action, $this, $this->config, $this->params, ...array_values($this->prop));
		}
	}
	public function theme_build(){	  
		$thms = glob($this->permalink[$this->route]['theme_path']."/*.tpl");
        foreach ($thms as $thm) {
            $th         = str_replace($this->permalink[$this->route]['theme_path'].'/', '', $thm);
            $th         = str_replace('.tpl', '', $th);
            $theme[$th] = $thm;
        }	    
        if(isset($theme))$this->smarty->assign("theme", $theme);
		$this->smarty->assign("app", $this);
		$this->smarty->assign("config", $this->config);
		$this->smarty->assign("tag", $this->param);
		$this->smarty->assign("title", $this->permalink[$this->route]['title']);
		$this->smarty->display($this->permalink[$this->route]['theme_path'].$this->permalink[$this->route]['theme']); 
	}
	/* output */
	public function render($view) {
		die();
		$this->action('render');
		$path = $this->config->theme_path('view/').$view.'.php';
		$path = is_file($path) ? $path : $this->config->theme_path('view/').'index.php';
		if($path != 'index.php' && is_file($path))
			call_user_func(static function($view, $app, $config, $params, $prop) use($path) {
				$param = $params;
				extract($prop);
				include $path;
			}, $view, $this, $this->config, $this->params, $this->prop);
	}
	public function JSONResponse($data = [], $success = true) {
		$json = json_encode(array_merge(['success' => $success], $data));
		header('Cache-Control: no-transform,public,max-age=300,s-maxage=900');
		if($this->config->cors) header('Access-Control-Allow-Origin: *');
		if(isset($_GET['callback']) && $this->config->jsonp) {
			header('Content-Type: text/javascript; charset=utf-8');
			echo $_GET['callback'].'('.$json.');';
		} else {
			header('Content-Type: application/json; charset=utf-8');
			echo $json;
		}
		exit;
	}
	/* url fetching */
	public $curlopt = [];
	public function fetch($method, $url, $fields = null, $options = []) {
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
		curl_setopt($ch, CURLOPT_TIMEOUT, 10);
		switch($method) {
			case 'get': break;
			case 'GET': break;
			case 'post':
			case 'POST': curl_setopt($ch, CURLOPT_POST, true); break;
			default:     curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
		}
		if($fields !== null) curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
		$options = array_merge($this->curlopt, $options);
		foreach($this->curlopt as $key => $value) curl_setopt($ch, $key, $value);
		$result = curl_exec($ch);
		curl_close($ch);
		return $result;
	}
	public function get($url, $options = []) {
		return $this->fetch('GET', $url, null, $options);
	}
	public function post($url, $fields = null, $options = []) {
		return $this->fetch('POST', $url, $fields, $options);
	}
	/* utilities */
	public function slug($str, $options = []) {
		// url_slug by Sean Murphy <<EMAIL>>
		$defaults = ['delimiter' => $this->config->slug_delimiter('-'), 'limit' => $this->config->slug_limit, 'lowercase' => true, 'replacements' => []];	
		$options = array_merge($defaults, $options);                                                       // Merge options	
		$str = mb_convert_encoding((string)$str, 'UTF-8', mb_list_encodings());                            // Make sure string is in UTF-8 and strip invalid UTF-8 characters
		$str = preg_replace(array_keys($options['replacements']), $options['replacements'], $str);         // Make custom replacements
		$str = preg_replace('/[^\p{L}\p{Nd}]+/u', $options['delimiter'], $str);                            // Replace non-alphanumeric characters with our delimiter
		$str = preg_replace('/(' . preg_quote($options['delimiter'], '/') . '){2,}/', '$1', $str);         // Remove duplicate delimiters
		$str = mb_substr($str, 0, ($options['limit'] ? $options['limit'] : mb_strlen($str, 'UTF-8')), 'UTF-8'); // Truncate slug to max. characters
		$str = trim($str, $options['delimiter']);                                                          // Remove delimiter from ends
		return $options['lowercase'] ? mb_strtolower($str, 'UTF-8') : $str;
	}
	public function unslug($slug) {
		return str_replace($this->config->slug_delimiter('-'), ' ', $slug);
	}
	public function cut($str, $start, $end) {
		// an old scraper/grabber script's method to parse desired string inbetween $start and $end by exploding
		$explode = explode($start, $str);
		$explode = array_pad($explode, 2, ''); //
		$result = current(explode($end, $explode[1]));
		return $result;
	}
	public function seeded_shuffle(array &$items, $seed = false) {
		// https://stackoverflow.com/q/24262147/2110506
		$items = array_values($items);
		mt_srand(abs(crc32($seed ?: $this->domain)));
		for ($i = count($items) - 1; $i > 0; $i--) {
			$j = mt_rand(0, $i);
			list($items[$i], $items[$j]) = [$items[$j], $items[$i]];
		}
	}
	public function createURL($pattern, array $replacer = []) {
		// return str_replace(array_keys($replacer), array_map('App::slug', array_values($replacer)), $pattern);
		return preg_replace_callback(
			array_map(function($v) { return '/%('.preg_quote($v).')%/'; }, array_keys($replacer)),
			function ($match) use ($replacer) { return isset($replacer[$match[1]]) && !is_array($replacer[$match[1]]) ? $this->slug($replacer[$match[1]]) : $match[0]; },
			$pattern
		);
	}
		/* Utilities */
	public function wwwRedirector() {
		if($this->config->www) {
			if(stripos($_SERVER['HTTP_HOST'], 'www.') !== 0) {
				header('HTTP/1.1 301 Moved Permanently');
				header('X-Robots-Tag: noindex');
				header('Location: '.$this->protocol.'://www.'.$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']);
				exit;
			}
		} else {
			if(stripos($_SERVER['HTTP_HOST'], 'www.') === 0) {
				header('HTTP/1.1 301 Moved Permanently');
				header('X-Robots-Tag: noindex');
				header('Location: '.$this->protocol.'://'.str_ireplace('www.', '', $_SERVER['HTTP_HOST']).$_SERVER['REQUEST_URI']);
				exit;
			}
		}
	}
	public function httpsRedirector() {
		if($this->config->https && $this->protocol == 'http') {
			header('HTTP/1.1 301 Moved Permanently');
			header('X-Robots-Tag: noindex');
			header('Location: https://'.$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']);
			exit;
		}
	}
	public function searchQueryRedirector() {
		if(empty($_GET['q'])) return;
		$slug = $this->slug($_GET['q']);
		if(!$slug) return;
		header('HTTP/1.1 301 Moved Permanently');
		header('X-Robots-Tag: noindex');
		header('Location: '.$this->createPermalink('search', ['title' => $slug]));
		exit;
	}
}
