<?php
//$param = json_decode($tmp,true);
preg_match("/ID \[(.*?)\]/s", $tmp, $match);
///////////////////////////////////// kontak masuk
if (count($match) > 1)
{
    $visitor_id = $match[1];

    $visitor_id = str_replace(".", "", $visitor_id);
    $visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");

    $data["visitor_id"] = $visitor_id;

    $db2 = $app->db2;

    $db2->where("visitor_id", $visitor_id);
    $visitor = $db2->getone("visitor");

    if ($visitor != null)
    {

        $db2->where("visitor_id", $visitor_id);
        $db2->update("visitor", ["lead" => 1 , "waktu_contact" => date("Y-m-d H:i:s")]);

        if (isset($param["phone"]))
        {
            if ($param["phone"] != "")
            {
                $db2->where("visitor_id", $visitor_id);
                $db2->update("visitor", ["phone" => $param["phone"]]);
            }
        }

        $visitor_data = unserialize($visitor["data"]);

        if (isset($visitor_data["last_campaign"]["source"]))
        {
            $source = $visitor_data["last_campaign"]["source"];

            if ($source == "meta" || $source == "tiktok" || $source == "google" || $source == "organic")
            {
                if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"]))
                {
                    $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];
                    $key = $param["phone"] . ";lead";
                    $campaign = new report();
                    ////////////////////////////// unique by adcopy id
                    $db2->setLockMethod("WRITE")
                        ->lock(array(
                        "report_hash",
                        "meta",
                        "report_data",
                        "visitor",
                        "connector",
                        "log_connector_hash",
                        "log_connector"
                    ));
                    if ($campaign->get_hash($key) == null)
                    {
                        $campaign->add_report_data($visitor, $adcopy_id, "unik_wa");
                    }
                    $campaign->add_report_data($visitor, $adcopy_id, "wa");
                    $campaign->add_hash($key);
                    $db2->unlock();
                }
                else
                {
                    hook_kontak_unknown($is_new_kontak, $param["phone"], $param["nope_cs"]);
                }
            }
            else
            {
                hook_kontak_unknown($is_new_kontak, $param["phone"], $param["nope_cs"]);
            }
        }
        //////////////////// trigger pixel ////////////////
        $key = $param["phone"] . ";lead;" . date("Y-m-d");
        $db2->setLockMethod("WRITE")
            ->lock(array(
            "report_hash",
            "meta",
            "report_data",
            "visitor",
            "connector",
            "log_connector_hash",
            "log_connector"
        ));
        if ($campaign->get_hash($key) == null)
        {
            $con = new connector();
            $con->trigger($visitor_id, "lead");
        }
        $campaign->add_hash($key);
        $db2->unlock();
        //////////////////// trigger pixel ////////////////
        hook_insert_cs($visitor_id, $param["nope_cs"]);
    }
    else
    {
        hook_kontak_unknown($is_new_kontak, $param["phone"], $param["nope_cs"]);
        $data["visitor_id"] = hook_create_visitor($param["phone"]);

        hook_insert_cs($data["visitor_id"], $param["nope_cs"]);
    }
}
else
{

    //
    $is_meta_ctwa = false;

    if ($is_new_kontak)
    {

        $data["visitor_id"] = hook_create_visitor($param["phone"]);

        $is_meta_ctwa = fbwa($param);

        if ($is_meta_ctwa)
        {
            $db2->where("visitor_id", $data["visitor_id"]);
            $visitor = $db2->getone("visitor");
            $visitor_data = unserialize($visitor["data"]);

            if (isset($visitor_data["last_campaign"]["source"]))
            {
                $source = $visitor_data["last_campaign"]["source"];

                if ($source == "meta" || $source == "tiktok" || $source == "google" || $source == "organik")
                {
                    if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"]))
                    {
                        $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];
                        $key = $param["phone"] . ";lead";
                        $campaign = new report();
                        ////////////////////////////// unique by adcopy id
                      
                        if ($campaign->get_hash($key) == null)
                        {
                            $campaign->add_report_data($visitor, $adcopy_id, "unik_wa");
                        }
                        $campaign->add_report_data($visitor, $adcopy_id, "wa");
                        $campaign->add_hash($key);
                       
                    }
                    else
                    {
                        hook_kontak_unknown($is_new_kontak, $param["phone"], $param["nope_cs"]);
                    }
                }
                else
                {
                    hook_kontak_unknown($is_new_kontak, $param["phone"], $param["nope_cs"]);
                }
            }
        }

    }
    else
    {

        $db2->where("phone", $param["phone"]);
        $tmp = $db2->getone("visitor");

        if ($tmp != NULL)
        {
            $data["visitor_id"] = $tmp["visitor_id"];
        }
        else
        {
            $data["visitor_id"] = hook_create_visitor($param["phone"]);
        }
    }

    if ($is_meta_ctwa == false)
    {
        hook_kontak_unknown($is_new_kontak, $param["phone"], $param["nope_cs"]);
    }

    hook_insert_cs($data["visitor_id"], $param["nope_cs"]);
}

function fbwa($param)
{
    $fbwa_ret = false;
    if (isset($param["project_id"]))
    {
        assign_child_db($param["project_id"]);
    }
    else
    {
        return false;
    }

    $tmp = json_encode($param);

    global $app;
    $db = $app->db;
    $db2 = $app->db2;

    $data = [];
    extract($param);

    $raw = json_decode_adv($raw, true);

    if ($raw != null)
    {
        if (count($raw) > 0)
        {

            $tmp = [];
            foreach ($raw as $key => $value)
            {
                $tmp = $value;
                break;
            }

            if (isset($tmp["key"]["remoteJid"]))
            {
                $data["sender"] = $tmp["key"]["remoteJid"];

                $tmp2 = explode("@", $data["sender"]);
                if ($tmp2 != NULL)
                {
                    if (count($tmp2) > 1)
                    {
                        $data["phone"] = $tmp2[0];
                        $data["phone_key"] = $db2->func("UNHEX(?)", [md5($tmp2[0]) ]);
                    }
                }
            }

            if (isset($tmp["message"]["extendedTextMessage"]["contextInfo"]["externalAdReply"]["ctwaClid"]))
            {
                $data["ctwaClid"] = $tmp["message"]["extendedTextMessage"]["contextInfo"]["externalAdReply"]["ctwaClid"];
            }
            if (isset($tmp["message"]["extendedTextMessage"]["contextInfo"]["externalAdReply"]["sourceId"]))
            {
                $data["adcopy_id"] = $tmp["message"]["extendedTextMessage"]["contextInfo"]["externalAdReply"]["sourceId"];
                $fbwa_ret = true;
            }
        }

        if (isset($data["adcopy_id"]))
        {
                $db2->where("external_key = UNHEX(?)",[sha1($data["adcopy_id"])]);
                $data_report = $db2->getone("report");

                if(isset($data_report))
                {
                    $data_visitor_fbwa["last_campaign"]["source"] = "meta";
                    $data_visitor_fbwa["last_campaign"]["data"]["adcopy_id"] = $data_report["report_id"];
                    if(isset($data["ctwaClid"])){
                        $data_visitor_fbwa["last_campaign"]["data"]["ctwaClid"] = $data["ctwaClid"];
                    }
                    $db2->where("phone", $data["phone"]);
                    $db2->update("visitor", ["data"=>serialize($data_visitor_fbwa)]);
                }
                
                
              
        }
    }

    $data_update = null;
    if (isset($data["sender"]))
    {
        $data["sender_key"] = $db2->func("UNHEX(?)", [sha1($data["sender"]) ]);

        $tmp = explode("@", $data["sender"]);
        if ($tmp != NULL)
        {
            if (count($tmp) > 1)
            {
                $data["phone"] = $tmp[0];
                $data["phone_key"] = $db2->func("UNHEX(?)", [md5($tmp[0]) ]);
            }
        }

        if (isset($data["ctwaClid"]))
        {
            $data_update["ctwaClid"] = $data["ctwaClid"];
        }
        if (isset($data["adcopy_id"]))
        {
            $data_update["adcopy_id"] = $data["adcopy_id"];
        }
        if (!is_null($data_update))
        {
            $db2->onDuplicate($data_update);
        }
        if (isset($data["ctwaClid"]))
        {
            $db2->insert("fbwa_ctwa", $data);
        }
    }

    return $fbwa_ret;
}

function hook_kontak_unknown($is_new_kontak, $phone, $nope_cs)
{
    if ($is_new_kontak)
    {
        global $app;
        $db2 = $app->db2;

        $key = $phone . ";lead";
        $campaign = new report();
        ////////////////////////////// unique by adcopy id
        $db2->setLockMethod("WRITE")
            ->lock(array(
            "report_hash",
            "meta",
            "report_data",
            "visitor",
            "connector",
            "log_connector_hash",
            "log_connector"
        ));
        if ($campaign->get_hash($key) == null)
        {
            $campaign->add_report_data(NULL, 1, "unik_wa");
        }
        $campaign->add_report_data(NULL, 1, "wa");
        $campaign->add_hash($key);
        $db2->unlock();

        /////////////////// insert to table visitor /////////////////////
        

        
    }
}

function hook_create_visitor($phone)
{
    global $app;
    $db2 = $app->db2;

    $data_visitor["phone"] = $phone;
    $data_visitor["created"] = date("Y-m-d H:i:s");
    $data_visitor["visit"] = date("Y-m-d H:i:s");
    $data_visitor["waktu_contact"] = date("Y-m-d H:i:s");
    $data_visitor["lead"] = 1;

    $visitor_id = $db2->insert("visitor", $data_visitor);

    return $visitor_id;
}

function hook_insert_cs($visitor_id, $nope_cs)
{
    global $app;
    $db2 = $app->db2;

    $db2->where("visitor_id",$visitor_id);
    $db2->where("cs_key = UNHEX(?)",[md5($nope_cs)]);
    $tmp = $db2->getone("visitor_cs");

    if($tmp == NULL){
        $data_kontak["created"] = date("Y-m-d H:i:s");
        $data_kontak["visitor_id"] = $visitor_id;
        $data_kontak["cs_key"] = $db2->func("UNHEX(?)", [md5($nope_cs) ]);
        $db2->setQueryOption('IGNORE')
            ->insert('visitor_cs', $data_kontak);

        //  @file_put_contents('log/hooksas_kontak.txt', '[' . date('Y-m-d H:i:s') . "]\n" . $db2->getLastQuery(), FILE_APPEND);
        $cs_log = new cs_log($nope_cs);
        $cs_log->add_stat("contact");
    }
}
?>
