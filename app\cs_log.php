<?php 
class cs_log{

	private $cs_nope;

    function __construct($cs_nope) {
        $this->cs_nope = $cs_nope;      
    }

    

	public static function get_log_download($date_range){

		global $app;
    	$db2 = $app->db2;
    	


		$db2->join("cs","cs.cs_key = cs_log.cs_key");
    	if(isset($date_range)){
    		 $db2->where("cs_log.tanggal",[$date_range["start"],$date_range["end"]],"between");

    	}
    	$db2->orderBy("cs_log.tanggal","DESC");
    	$data_cs_log = $db2->get("cs_log",NULL,"cs_log.tanggal,cs.name,cs.phone,cs_log.contact as lead,cs_log.mql,cs_log.prospek,cs_log.purchase,cs_log.value");

    	if($data_cs_log != NULL){
    	$head = ['Tanggal','Name','Phone','Lead','MQL',"Prospek","Purchase","Purchase Value"];
    	array_unshift($data_cs_log , $head);

		return $data_cs_log;
		//Shuchkin\SimpleXLSXGen::fromArray($data_cs_log)->downloadAs('cs_log-'.$date_range['start'].'_'.$date_range['end'].'.xlsx');
		//exit();
		}
	}

	public static function get_log($date_range,$page)
    {
    	global $app;
    	$db2 = $app->db2;

    	$start = 0;

        if($page > 1)
        {
            $start = $page * 20;
        }

    	$db2->join("cs","cs.cs_key = cs_log.cs_key");
    	if(isset($date_range)){
    		 $db2->where("cs_log.tanggal",[$date_range["start"],$date_range["end"]],"between");

    	}
    	$db2->orderBy("cs_log.tanggal","DESC");
    	$data_cs_log = $db2->get("cs_log",[$start,20],"cs_log.tanggal,cs.name,cs.phone,cs_log.contact as lead,cs_log.inbound as inbound,cs_log.outbound as outbound,cs_log.mql,cs_log.prospek,cs_log.purchase,cs_log.value");
    	return $data_cs_log;
    }

	function add_lead_detail($type){
		global $app;
		$db2 = $app->db2;
		$phone =  $this->cs_nope;
		$tanggal = date("Y-m-d");
		$hash = md5($tanggal.";".$phone);
		// $db2->where("hash = UNHEX(?)",[md5($tanggal.";".$phone)]);
		// $current = $db2->getone("cs_log");
		if($type == "inbound")
		{
			//$update["inbound"] = $current["inbound"] + 1;
			$db2->rawQuery("UPDATE cs_log SET inbound = inbound + 1 WHERE hash = UNHEX(?)", array($hash));
		}else{
			//$update["outbound"] = $current["outbound"] + 1;
			$db2->rawQuery("UPDATE cs_log SET outbound = outbound + 1 WHERE hash = UNHEX(?)", array($hash));
		}
		//$db2->where("hash = UNHEX(?)",[md5($tanggal.";".$phone)]);
		//$db2->update("cs_log",$update);
            

	}

	function add_stat($type,$value=0,$msg_type=null)
	{
		global $app;

		$phone =  $this->cs_nope;

		$valid = false;
		if($type == "impression"){ $valid = true;}
		if($type == "contact"){ $valid = true;}
		if($type == "mql"){ $valid = true;}
		if($type == "prospek"){ $valid = true;}
		if($type == "purchase"){ $valid = true;}

		if($valid == false)
		{
			return false;
		}
		
		$db2 = $app->db2;
		$tanggal = date("Y-m-d");
		$data_insert["tanggal"] = $tanggal;
		$data_insert["cs_key"] = $db2->func("UNHEX(?)",[md5($phone)]);
		$data_insert["hash"] = $db2->func("UNHEX(?)",[md5($tanggal.";".$phone)]);
		$data_insert["phone"] = $phone;
		$data_insert[$type] = 1;
		
		$data_dupe[$type] = $db2->inc(1);
		if($value != 0)
		{
			$data_dupe["value"] = $db2->inc($value);
		}
		if($type == "contact"){
			if($msg_type == "message_in"){
				$data_insert["inbound"] = 1;
				$data_dupe["inbound"] = $db2->inc(1);
			}
			if($msg_type == "message_out"){
				$data_insert["outbound"] = 1;
				$data_dupe["outbound"] = $db2->inc(1);
			}
		}
		
		// $db2->where("hash = UNHEX(?)",[md5($tanggal.";".$phone)]);
		// $db2->getone("cs_log");
		$db2->onDuplicate($data_dupe);
		//if($db2->count == 0){
			$db2->insert("cs_log", $data_insert);
		//}		
		
	}
	
}