<?php


function add($param)
{
    
    //@file_put_contents('hooksas_kontak.txt', '[' . date('Y-m-d H:i:s') . "]\n" . serialize($param)."\n\n", FILE_APPEND);
    
    if($param["nope_cs"] == "6281333815212"){
        @file_put_contents('hooksas-6281333815212.txt', '[' . date('Y-m-d H:i:s') . "]\n\n" . serialize($param) . "\n\n\n\n", FILE_APPEND);
    }
    
    
    if($param["nope_cs"] == "628114433222"){
     //   @file_put_contents('hooksas-628114433222.txt', '[' . date('Y-m-d H:i:s') . "]\n\n" . serialize($param). "\n\n\n\n", FILE_APPEND);
    }

    if($param["nope_cs"] == "6281919831666"){
     //   @file_put_contents('hooksas-6281919831666.txt', '[' . date('Y-m-d H:i:s') . "]\n\n" . serialize($param). "\n\n\n\n", FILE_APPEND);
    }
    if($param["nope_cs"] == "6281335673581"){
           @file_put_contents('hooksas-6281335673581.txt', '[' . date('Y-m-d H:i:s') . "]\n\n" . serialize($param). "\n\n\n\n", FILE_APPEND);
    }
    
    if (isset($param["raw"]))
    {
        $param["raw"] = urldecode($param["raw"]);
    }

    if (!is_numeric($param["phone"]))
    {
        return false;
    }

    global $app;
    $db = $app->db;
    $t = new track();

    if (!isset($param["project_id"]))
    {
        $nope_cs = $param["nope_cs"];
        $db->where("cs_key = UNHEX(?)", [md5($nope_cs) ]);
        $cs = $db->getone("cs");
        if ($cs == null)
        {
            die();
        }
        assign_child_db($cs["project_id"]);
    }
    else
    {
        assign_child_db($param["project_id"]);
    }
    $db2 = $app->db2;
    $m = new meta();

    

    ///////////////////
    $is_new_kontak = false;

    $new_kontak["phone"] = $param["phone"];
    $new_kontak["phone_hash"] = $db2->func("UNHEX(?)", [md5($param["phone"]) ]);
    $new_kontak["created"] = date("Y-m-d H:i:s");

    if ($db2->setQueryOption('IGNORE')->insert("new_kontak", $new_kontak))
    {
        $is_new_kontak = true;
    }

   
  //     $is_new_kontak = true;

    $trigger_kontak = false;
    $trigger_mql = false;
    $trigger_prospek = false;
    $trigger_purchase = false;
    $visitor_id = NULL;
    //////////////////////////////////////////// data /////////////////////////////////////////
    $tmp = json_encode($param);
    $pesan = $tmp;
    $phone = NULL;if (isset($param["phone"])){if ($param["phone"] != ""){$phone = $param["phone"];}}
    $nope_cs = NULL;if (isset($param["nope_cs"])){if ($param["nope_cs"] != ""){$nope_cs = $param["nope_cs"];}}
    $msg_type = $param["type"];
    ///////////////////////////////////////////////////////////////////////////////////////////////
    $post_archive = [
        'act' => 'archive_add',
        'pesan' => $pesan,
        'nope_cs' =>  $nope_cs,
        'phone' => $phone,
        'msg_type' => $msg_type,
    ];
    $forward_to =array("http://10.104.0.56/api.html");
    
  
    ///////////////////////////////////// kontak masuk
    $format_id = $m->get_meta("format_id");

    if($format_id["code"] == 0){
        preg_match("/ID \[(.*?)\]/s", $tmp, $match);
        if (count($match) > 1)
        {
            $visitor_id = trim($match[1]);
            $trigger_kontak = true;
        }else{
            if($is_new_kontak){
                $trigger_kontak = true;
            }
        }
    }else{
        $format_id = preg_quote(trim($format_id["result"]["data"]));
        $format_id = "/(?<=".$format_id." )\S+\b/is";
        $format_id = preg_replace('/\s+/', ' ', $format_id);
        $tmp = preg_replace('/\s+/', ' ', $tmp);
        preg_match($format_id, $tmp, $match);
        if (count($match) > 0)
        {
            $visitor_id = trim($match[0]);
            $trigger_kontak = true;
        }else{

            preg_match("/ID \[(.*?)\]/s", $tmp, $match);
            if (count($match) > 1)
            {
                $visitor_id = $match[1];
                $trigger_kontak = true;
               
            }else{
                if($is_new_kontak){
                    $trigger_kontak = true;
                }
            }
        }
       
    }

   //var_dump($format_id);
  // var_dump($visitor_id);
  // die();
    
   
   

  
    ///////////////////////////////////// end kontak masuk /////////////////////////////////////
    
    
    if ($param["type"] == "message_in")
    {

        

        //////////////////////////////////// mql //////////////////////////////////
        $data["count"] = 1;
        $data["phone"] = $param["phone"];
        $data["created"] = date("Y-m-d H:i:s");
        $data["phone_hash"] = $db2->func("UNHEX(?)", [md5($param["phone"]) ]);
        $db2->setQueryOption(["IGNORE"])->insert("mql", $data);

       
        $res = $m->get_meta("mql");
        if ($res["code"] == 1)
        {
            
            $mql_limit = $res["result"]["data"] - 1;
        }

        $db2->where("phone_hash = UNHEX(?)", [md5($param["phone"]) ]);
        $mql_data = $db2->getone("mql");
        $inc = true;
        if ($mql_data != null)
        {
          
            if ($mql_data["count"] == $mql_limit)
            {
                $trigger_mql = true;
            }
            if ($mql_data["count"] > $mql_limit )
            {
                $inc = false;
            }
        }
        if ($inc)
        {
            $data = [];
            $data_insert = [];

            $data["phone_hash"] = $db2->func("UNHEX(?)", [md5($param["phone"])]);
            $data["count"] = 1;
            $data_insert["count"] = $db2->inc(1);
            $db2->onDuplicate($data_insert);
            $db2->insert("mql", $data);
        }
    }
    
    ///////////////////////////////////////// end mql ///////////////////////////////////////

    if ($param["type"] == "message_out")
    {
        ///////////////////////// prospek
        $res = $m->get_meta("format_prospek");

        if ($res["code"] == 1)
        {
            $rgxFormatCheckout = preg_quote($res["result"]["data"], '/');
            $strregex = str_replace('%ID%', '(\d+)', $rgxFormatCheckout);
            $rgx = '/' . $strregex . '/';
            $res = preg_match($rgx, $pesan, $matches);

            if ($res !== false && $res > 0)
            {

                $trigger_prospek = true;
            }
        }
        //////////////////////// end prospek

        ///////////////////////// purchase
        $value = 0;
        $meta_result = $m->get_meta("format_purchase");
        $meta_result2 = $m->get_meta("format_purchase_value");
        if ($meta_result["code"] == 1 && $meta_result2["code"] == 1)
        {

            $rgxFormatPurchase = preg_quote($meta_result["result"]["data"], '/');
            $strregex = str_replace('%ID%', '(\d+)', $rgxFormatPurchase);
            $rgx = '/' . $strregex . '/';
            $res = preg_match($rgx, $pesan, $matches);

            if ($res !== false && $res > 0)
            {
              //  @file_put_contents('hooksas_purchase.txt', '[' . date('Y-m-d H:i:s') . "]\n\n" . json_encode($param)."\n\n\n\n", FILE_APPEND);

                $rgxFormatValuePurchase = preg_quote($meta_result2["result"]["data"], '/');
                $strregex = str_replace('%VALUE%', '([0-9,.]*)', $rgxFormatValuePurchase);
                $rgx = '/' . $strregex . '/';
                $res2 = preg_match($rgx, $pesan, $matches);

                if ($res2 !== false && $res2 > 0)
                {
                    $value = preg_replace('/[.,]/', '', $matches[1]);
                    $trigger_purchase = true;
                }
            }
        }
        //////////////////////// end purchase

    }
   



    if($trigger_kontak || $trigger_mql || $trigger_prospek || $trigger_purchase)
    {
       
         $visitor = $t->get_visitor($visitor_id,$phone);
        
        
         if($visitor == false){
            if($phone != NULL){
                $visitor_id = $t->create_visitor($phone);

                $visitor_id = convBase($visitor_id, "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
                $visitor_id = str_split($visitor_id, 4);
                $visitor_id = implode(".", $visitor_id);

                $x = $t->fbwa_personal($param["phone"],$param);
               // @file_put_contents('log_ctwa.txt', '[' . date('Y-m-d H:i:s') . "]\n" . $visitor_id."\n\n", FILE_APPEND);

                $visitor = $t->get_visitor($visitor_id,$phone);
            }
            else
            {
                //return false;
            }
         }

        //////////////////////// cek ctwa ///////////////////////////////////////////
       
        if($x)
        {
            $data_tmp["is_new_kontak"] = $is_new_kontak;
            $data_tmp["visitor"] = $visitor;
            $data_tmp["nope_cs"] = $nope_cs;
            $data_tmp["phone"] = $phone;
            $data_tmp = serialize($data_tmp);
          //  @file_put_contents('log_ctwa.txt', '[' . date('Y-m-d H:i:s') . "]\n" . $data_tmp."\n\n", FILE_APPEND);
        }

         if($trigger_kontak){

            $t->lead($is_new_kontak,$visitor,$nope_cs,$phone);
         }

         if($trigger_mql){
            $t->mql($nope_cs,$visitor,$phone);
         }

         if($trigger_prospek){
            $t->prospek($nope_cs,$visitor,$phone);
         }

         if($trigger_purchase){
            $t->purchase($nope_cs,$visitor,$phone,$value);
         }
    }

    $x = forwarder($post_archive,$forward_to);

   // $data = [];
   // include("hooksas_kontak.php");
   // include("hooksas_mql.php");
   // include("hooksas_prospek.php");
  //  include("hooksas_purchase.php");
    
  
    
}

