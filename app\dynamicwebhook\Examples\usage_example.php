<?php

/**
 * Usage examples for the refactored DynamicWebhook system
 */

// Include the main system
require_once __DIR__ . '/../../DynamicWebhook.php';

// Example 1: Basic webhook processing
function basicWebhookProcessing() {
    echo "=== Basic Webhook Processing ===\n";
    
    // Mock app object (in real usage, this would be your application instance)
    $app = new stdClass();
    $app->db = null; // Your database connection
    $app->db2 = null; // Your secondary database connection
    
    // Webhook parameters
    $projectKey = 'your-project-key';
    $csPhone = '081234567890';
    $input = json_encode([
        'phone' => '081987654321',
        'message' => 'Hello, this is a test message',
        'type' => 'message_in'
    ]);
    $provider = 'default';
    
    try {
        // Create and process webhook
        $webhook = new DynamicWebhook($app, $projectKey, $csPhone, $input, $provider);
        $result = $webhook->process();
        
        // Display results
        echo "Status: " . $result['status'] . "\n";
        echo "Platform: " . $result['summary']['platform'] . "\n";
        echo "Phone: " . $result['summary']['phone'] . "\n";
        echo "Message Type: " . $result['summary']['message_type'] . "\n";
        echo "Is New Contact: " . ($result['summary']['is_new_contact'] ? 'Yes' : 'No') . "\n";
        echo "Triggers: " . json_encode($result['summary']['triggers']) . "\n";
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// Example 2: Using individual components
function individualComponentsExample() {
    echo "=== Individual Components Example ===\n";
    
    // Platform Detection
    $detector = new DynamicWebhook\PlatformDetector();
    
    $wabaData = [
        'entry' => [[
            'changes' => [[
                'value' => [
                    'messages' => [[
                        'from' => '081234567890',
                        'text' => ['body' => 'Hello World'],
                        'type' => 'text'
                    ]]
                ]
            ]]
        ]]
    ];
    
    $platform = $detector->detect($wabaData);
    echo "Detected Platform: $platform\n";
    
    // Message Normalization
    $normalizer = new DynamicWebhook\MessageNormalizer();
    
    $rawPhone = '+<EMAIL>';
    $cleanPhone = $normalizer->cleanPhoneNumber($rawPhone);
    echo "Clean Phone: $cleanPhone\n";
    
    $rawMessage = '  Hello   World  with   extra   spaces  ';
    $normalizedMessage = $normalizer->normalizeMessage($rawMessage);
    echo "Normalized Message: '$normalizedMessage'\n";
    
    // Nested value extraction
    $nestedData = [
        'user' => [
            'profile' => [
                'contact' => [
                    'phone' => '081234567890'
                ]
            ]
        ]
    ];
    
    $extractedPhone = $normalizer->extractNestedValue($nestedData, 'user.profile.contact.phone');
    echo "Extracted Phone: $extractedPhone\n";
    
    echo "\n";
}

// Example 3: CTWA Processing
function ctwaProcessingExample() {
    echo "=== CTWA Processing Example ===\n";
    
    $normalizer = new DynamicWebhook\MessageNormalizer();
    $ctwaProcessor = new DynamicWebhook\CTWAProcessor($normalizer);
    
    // Qontak CTWA data
    $qontakData = [
        'room' => [
            'description' => 'ctwa_clid=abc123def456&source_id=campaign_001&campaign_id=meta_campaign_789'
        ],
        'text' => 'Hello from Qontak',
        'sender_type' => 'Models::Contact'
    ];
    
    $ctwaData = $ctwaProcessor->extractCTWAData($qontakData, 'qontak');
    echo "CTWA CLID: " . ($ctwaData['ctwa_clid'] ?? 'Not found') . "\n";
    echo "Source ID: " . ($ctwaData['source_id'] ?? 'Not found') . "\n";
    echo "Campaign Data: " . json_encode($ctwaData['campaign_data']) . "\n";
    
    // Validate and process
    $validatedData = $ctwaProcessor->validateAndProcess($ctwaData);
    echo "Validated CTWA CLID: " . ($validatedData['ctwa_clid'] ?? 'Not found') . "\n";
    
    // Check if has CTWA data
    $hasCTWA = $ctwaProcessor->hasCTWAData($qontakData, 'qontak');
    echo "Has CTWA Data: " . ($hasCTWA ? 'Yes' : 'No') . "\n";
    
    echo "\n";
}

// Example 4: Data Extraction
function dataExtractionExample() {
    echo "=== Data Extraction Example ===\n";
    
    $normalizer = new DynamicWebhook\MessageNormalizer();
    $configs = DynamicWebhook\Config\PlatformConfig::getAll();
    
    // Generic extractor
    $extractor = new DynamicWebhook\DataExtractor\GenericDataExtractor($normalizer, $configs);
    
    $testData = [
        'phone' => '081234567890',
        'message' => 'Test message content',
        'type' => 'message_in'
    ];
    
    $extracted = $extractor->extract($testData, 'default');
    echo "Extracted Phone: " . $extracted['phone'] . "\n";
    echo "Extracted Message: " . $extracted['message'] . "\n";
    echo "Message Type: " . $extracted['message_type'] . "\n";
    
    // Validation
    $isValid = $extractor->validate($extracted);
    echo "Data Valid: " . ($isValid ? 'Yes' : 'No') . "\n";
    
    echo "\n";
}

// Example 5: Platform Configuration
function platformConfigExample() {
    echo "=== Platform Configuration Example ===\n";
    
    // Get existing configuration
    $wabaConfig = DynamicWebhook\Config\PlatformConfig::get('waba');
    echo "WABA Phone Field: " . $wabaConfig['phone_field'] . "\n";
    
    // Check if platform exists
    $exists = DynamicWebhook\Config\PlatformConfig::exists('waba');
    echo "WABA Config Exists: " . ($exists ? 'Yes' : 'No') . "\n";
    
    // Get supported platforms
    $platforms = DynamicWebhook\Config\PlatformConfig::getSupportedPlatforms();
    echo "Supported Platforms: " . implode(', ', array_slice($platforms, 0, 5)) . "...\n";
    
    // Add custom platform configuration
    $customConfig = [
        'phone_field' => 'contact.mobile',
        'message_field' => 'content.text',
        'type_field' => 'direction',
        'incoming_condition' => 'received',
        'outgoing_condition' => 'sent'
    ];
    
    DynamicWebhook\Config\PlatformConfig::set('custom_platform', $customConfig);
    echo "Added custom platform configuration\n";
    
    // Validate configuration
    $errors = DynamicWebhook\Config\PlatformConfig::validate($customConfig);
    echo "Configuration Valid: " . (empty($errors) ? 'Yes' : 'No') . "\n";
    
    if (!empty($errors)) {
        echo "Validation Errors: " . implode(', ', $errors) . "\n";
    }
    
    echo "\n";
}

// Example 6: Error Handling
function errorHandlingExample() {
    echo "=== Error Handling Example ===\n";
    
    // Mock app with invalid setup
    $app = new stdClass();
    $app->db = null; // No database connection
    
    try {
        $webhook = new DynamicWebhook($app, 'invalid-key', '081234567890', '{}', null);
        $result = $webhook->process();
        
        if ($result['status'] === 'error') {
            echo "Error handled gracefully: " . $result['error'] . "\n";
        }
        
    } catch (Exception $e) {
        echo "Exception caught: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// Run all examples
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    echo "DynamicWebhook Refactored System - Usage Examples\n";
    echo "================================================\n\n";
    
    basicWebhookProcessing();
    individualComponentsExample();
    ctwaProcessingExample();
    dataExtractionExample();
    platformConfigExample();
    errorHandlingExample();
    
    echo "All examples completed!\n";
}
