<?php
/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * GENERATED CODE WARNING
 * Generated by gapic-generator-php from the file
 * https://github.com/googleapis/googleapis/blob/master/google/ads/googleads/v18/services/google_ads_service.proto
 * Updates to the above are reflected here through a refresh process.
 */

namespace Google\Ads\GoogleAds\V18\Services\Client;

use Google\Ads\GoogleAds\Lib\V18\GoogleAdsGapicClientTrait;
use Google\Ads\GoogleAds\V18\Services\MutateGoogleAdsRequest;
use Google\Ads\GoogleAds\V18\Services\MutateGoogleAdsResponse;
use Google\Ads\GoogleAds\V18\Services\SearchGoogleAdsRequest;
use Google\Ads\GoogleAds\V18\Services\SearchGoogleAdsStreamRequest;
use Google\ApiCore\ApiException;
use Google\ApiCore\CredentialsWrapper;
use Google\ApiCore\GapicClientTrait;
use Google\ApiCore\PagedListResponse;
use Google\ApiCore\ResourceHelperTrait;
use Google\ApiCore\RetrySettings;
use Google\ApiCore\ServerStream;
use Google\ApiCore\Transport\TransportInterface;
use Google\ApiCore\ValidationException;
use Google\Auth\FetchAuthTokenInterface;
use GuzzleHttp\Promise\PromiseInterface;

/**
 * Service Description: Service to fetch data and metrics across resources.
 *
 * This class provides the ability to make remote calls to the backing service through method
 * calls that map to API methods.
 *
 * Many parameters require resource names to be formatted in a particular way. To
 * assist with these names, this class includes a format method for each type of
 * name, and additionally a parseName method to extract the individual identifiers
 * contained within formatted names that are returned by the API.
 *
 * @method PromiseInterface mutateAsync(MutateGoogleAdsRequest $request, array $optionalArgs = [])
 * @method PromiseInterface searchAsync(SearchGoogleAdsRequest $request, array $optionalArgs = [])
 */
class GoogleAdsServiceClient
{
    use GapicClientTrait, GoogleAdsGapicClientTrait {
        GoogleAdsGapicClientTrait::modifyClientOptions insteadof GapicClientTrait;
        GoogleAdsGapicClientTrait::modifyUnaryCallable insteadof GapicClientTrait;
        GoogleAdsGapicClientTrait::modifyStreamingCallable insteadof GapicClientTrait;
    }
    use ResourceHelperTrait;

    /** The name of the service. */
    private const SERVICE_NAME = 'google.ads.googleads.v18.services.GoogleAdsService';

    /**
     * The default address of the service.
     *
     * @deprecated SERVICE_ADDRESS_TEMPLATE should be used instead.
     */
    private const SERVICE_ADDRESS = 'googleads.googleapis.com';

    /** The address template of the service. */
    private const SERVICE_ADDRESS_TEMPLATE = 'googleads.UNIVERSE_DOMAIN';

    /** The default port of the service. */
    private const DEFAULT_SERVICE_PORT = 443;

    /** The name of the code generator, to be included in the agent header. */
    private const CODEGEN_NAME = 'gapic';

    /** The default scopes required by the service. */
    public static $serviceScopes = [
        'https://www.googleapis.com/auth/adwords',
    ];

    private static function getClientDefaults()
    {
        return [
            'serviceName' => self::SERVICE_NAME,
            'apiEndpoint' => self::SERVICE_ADDRESS . ':' . self::DEFAULT_SERVICE_PORT,
            'clientConfig' => __DIR__ . '/../resources/google_ads_service_client_config.json',
            'descriptorsConfigPath' => __DIR__ . '/../resources/google_ads_service_descriptor_config.php',
            'gcpApiConfigPath' => __DIR__ . '/../resources/google_ads_service_grpc_config.json',
            'credentialsConfig' => [
                'defaultScopes' => self::$serviceScopes,
            ],
            'transportConfig' => [
                'rest' => [
                    'restClientConfigPath' => __DIR__ . '/../resources/google_ads_service_rest_client_config.php',
                ],
            ],
        ];
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * accessible_bidding_strategy resource.
     *
     * @param string $customerId
     * @param string $biddingStrategyId
     *
     * @return string The formatted accessible_bidding_strategy resource.
     */
    public static function accessibleBiddingStrategyName(string $customerId, string $biddingStrategyId): string
    {
        return self::getPathTemplate('accessibleBiddingStrategy')->render([
            'customer_id' => $customerId,
            'bidding_strategy_id' => $biddingStrategyId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a ad resource.
     *
     * @param string $customerId
     * @param string $adId
     *
     * @return string The formatted ad resource.
     */
    public static function adName(string $customerId, string $adId): string
    {
        return self::getPathTemplate('ad')->render([
            'customer_id' => $customerId,
            'ad_id' => $adId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a ad_group
     * resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     *
     * @return string The formatted ad_group resource.
     */
    public static function adGroupName(string $customerId, string $adGroupId): string
    {
        return self::getPathTemplate('adGroup')->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a ad_group_ad
     * resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $adId
     *
     * @return string The formatted ad_group_ad resource.
     */
    public static function adGroupAdName(string $customerId, string $adGroupId, string $adId): string
    {
        return self::getPathTemplate('adGroupAd')->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'ad_id' => $adId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_ad_label resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $adId
     * @param string $labelId
     *
     * @return string The formatted ad_group_ad_label resource.
     */
    public static function adGroupAdLabelName(string $customerId, string $adGroupId, string $adId, string $labelId): string
    {
        return self::getPathTemplate('adGroupAdLabel')->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'ad_id' => $adId,
            'label_id' => $labelId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_asset resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $assetId
     * @param string $fieldType
     *
     * @return string The formatted ad_group_asset resource.
     */
    public static function adGroupAssetName(string $customerId, string $adGroupId, string $assetId, string $fieldType): string
    {
        return self::getPathTemplate('adGroupAsset')->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'asset_id' => $assetId,
            'field_type' => $fieldType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_bid_modifier resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $criterionId
     *
     * @return string The formatted ad_group_bid_modifier resource.
     */
    public static function adGroupBidModifierName(string $customerId, string $adGroupId, string $criterionId): string
    {
        return self::getPathTemplate('adGroupBidModifier')->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_criterion resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $criterionId
     *
     * @return string The formatted ad_group_criterion resource.
     */
    public static function adGroupCriterionName(string $customerId, string $adGroupId, string $criterionId): string
    {
        return self::getPathTemplate('adGroupCriterion')->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_criterion_customizer resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $criterionId
     * @param string $customizerAttributeId
     *
     * @return string The formatted ad_group_criterion_customizer resource.
     */
    public static function adGroupCriterionCustomizerName(string $customerId, string $adGroupId, string $criterionId, string $customizerAttributeId): string
    {
        return self::getPathTemplate('adGroupCriterionCustomizer')->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'criterion_id' => $criterionId,
            'customizer_attribute_id' => $customizerAttributeId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_criterion_label resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $criterionId
     * @param string $labelId
     *
     * @return string The formatted ad_group_criterion_label resource.
     */
    public static function adGroupCriterionLabelName(string $customerId, string $adGroupId, string $criterionId, string $labelId): string
    {
        return self::getPathTemplate('adGroupCriterionLabel')->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'criterion_id' => $criterionId,
            'label_id' => $labelId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_customizer resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $customizerAttributeId
     *
     * @return string The formatted ad_group_customizer resource.
     */
    public static function adGroupCustomizerName(string $customerId, string $adGroupId, string $customizerAttributeId): string
    {
        return self::getPathTemplate('adGroupCustomizer')->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'customizer_attribute_id' => $customizerAttributeId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_extension_setting resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $extensionType
     *
     * @return string The formatted ad_group_extension_setting resource.
     */
    public static function adGroupExtensionSettingName(string $customerId, string $adGroupId, string $extensionType): string
    {
        return self::getPathTemplate('adGroupExtensionSetting')->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'extension_type' => $extensionType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_feed resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $feedId
     *
     * @return string The formatted ad_group_feed resource.
     */
    public static function adGroupFeedName(string $customerId, string $adGroupId, string $feedId): string
    {
        return self::getPathTemplate('adGroupFeed')->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'feed_id' => $feedId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * ad_group_label resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $labelId
     *
     * @return string The formatted ad_group_label resource.
     */
    public static function adGroupLabelName(string $customerId, string $adGroupId, string $labelId): string
    {
        return self::getPathTemplate('adGroupLabel')->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'label_id' => $labelId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a ad_parameter
     * resource.
     *
     * @param string $customerId
     * @param string $adGroupId
     * @param string $criterionId
     * @param string $parameterIndex
     *
     * @return string The formatted ad_parameter resource.
     */
    public static function adParameterName(string $customerId, string $adGroupId, string $criterionId, string $parameterIndex): string
    {
        return self::getPathTemplate('adParameter')->render([
            'customer_id' => $customerId,
            'ad_group_id' => $adGroupId,
            'criterion_id' => $criterionId,
            'parameter_index' => $parameterIndex,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a asset
     * resource.
     *
     * @param string $customerId
     * @param string $assetId
     *
     * @return string The formatted asset resource.
     */
    public static function assetName(string $customerId, string $assetId): string
    {
        return self::getPathTemplate('asset')->render([
            'customer_id' => $customerId,
            'asset_id' => $assetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a asset_group
     * resource.
     *
     * @param string $customerId
     * @param string $assetGroupId
     *
     * @return string The formatted asset_group resource.
     */
    public static function assetGroupName(string $customerId, string $assetGroupId): string
    {
        return self::getPathTemplate('assetGroup')->render([
            'customer_id' => $customerId,
            'asset_group_id' => $assetGroupId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * asset_group_asset resource.
     *
     * @param string $customerId
     * @param string $assetGroupId
     * @param string $assetId
     * @param string $fieldType
     *
     * @return string The formatted asset_group_asset resource.
     */
    public static function assetGroupAssetName(string $customerId, string $assetGroupId, string $assetId, string $fieldType): string
    {
        return self::getPathTemplate('assetGroupAsset')->render([
            'customer_id' => $customerId,
            'asset_group_id' => $assetGroupId,
            'asset_id' => $assetId,
            'field_type' => $fieldType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * asset_group_listing_group_filter resource.
     *
     * @param string $customerId
     * @param string $assetGroupId
     * @param string $listingGroupFilterId
     *
     * @return string The formatted asset_group_listing_group_filter resource.
     */
    public static function assetGroupListingGroupFilterName(string $customerId, string $assetGroupId, string $listingGroupFilterId): string
    {
        return self::getPathTemplate('assetGroupListingGroupFilter')->render([
            'customer_id' => $customerId,
            'asset_group_id' => $assetGroupId,
            'listing_group_filter_id' => $listingGroupFilterId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * asset_group_signal resource.
     *
     * @param string $customerId
     * @param string $assetGroupId
     * @param string $criterionId
     *
     * @return string The formatted asset_group_signal resource.
     */
    public static function assetGroupSignalName(string $customerId, string $assetGroupId, string $criterionId): string
    {
        return self::getPathTemplate('assetGroupSignal')->render([
            'customer_id' => $customerId,
            'asset_group_id' => $assetGroupId,
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a asset_set
     * resource.
     *
     * @param string $customerId
     * @param string $assetSetId
     *
     * @return string The formatted asset_set resource.
     */
    public static function assetSetName(string $customerId, string $assetSetId): string
    {
        return self::getPathTemplate('assetSet')->render([
            'customer_id' => $customerId,
            'asset_set_id' => $assetSetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * asset_set_asset resource.
     *
     * @param string $customerId
     * @param string $assetSetId
     * @param string $assetId
     *
     * @return string The formatted asset_set_asset resource.
     */
    public static function assetSetAssetName(string $customerId, string $assetSetId, string $assetId): string
    {
        return self::getPathTemplate('assetSetAsset')->render([
            'customer_id' => $customerId,
            'asset_set_id' => $assetSetId,
            'asset_id' => $assetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a audience
     * resource.
     *
     * @param string $customerId
     * @param string $audienceId
     *
     * @return string The formatted audience resource.
     */
    public static function audienceName(string $customerId, string $audienceId): string
    {
        return self::getPathTemplate('audience')->render([
            'customer_id' => $customerId,
            'audience_id' => $audienceId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * bidding_data_exclusion resource.
     *
     * @param string $customerId
     * @param string $seasonalityEventId
     *
     * @return string The formatted bidding_data_exclusion resource.
     */
    public static function biddingDataExclusionName(string $customerId, string $seasonalityEventId): string
    {
        return self::getPathTemplate('biddingDataExclusion')->render([
            'customer_id' => $customerId,
            'seasonality_event_id' => $seasonalityEventId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * bidding_seasonality_adjustment resource.
     *
     * @param string $customerId
     * @param string $seasonalityEventId
     *
     * @return string The formatted bidding_seasonality_adjustment resource.
     */
    public static function biddingSeasonalityAdjustmentName(string $customerId, string $seasonalityEventId): string
    {
        return self::getPathTemplate('biddingSeasonalityAdjustment')->render([
            'customer_id' => $customerId,
            'seasonality_event_id' => $seasonalityEventId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * bidding_strategy resource.
     *
     * @param string $customerId
     * @param string $biddingStrategyId
     *
     * @return string The formatted bidding_strategy resource.
     */
    public static function biddingStrategyName(string $customerId, string $biddingStrategyId): string
    {
        return self::getPathTemplate('biddingStrategy')->render([
            'customer_id' => $customerId,
            'bidding_strategy_id' => $biddingStrategyId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a campaign
     * resource.
     *
     * @param string $customerId
     * @param string $campaignId
     *
     * @return string The formatted campaign resource.
     */
    public static function campaignName(string $customerId, string $campaignId): string
    {
        return self::getPathTemplate('campaign')->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_asset resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $assetId
     * @param string $fieldType
     *
     * @return string The formatted campaign_asset resource.
     */
    public static function campaignAssetName(string $customerId, string $campaignId, string $assetId, string $fieldType): string
    {
        return self::getPathTemplate('campaignAsset')->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'asset_id' => $assetId,
            'field_type' => $fieldType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_asset_set resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $assetSetId
     *
     * @return string The formatted campaign_asset_set resource.
     */
    public static function campaignAssetSetName(string $customerId, string $campaignId, string $assetSetId): string
    {
        return self::getPathTemplate('campaignAssetSet')->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'asset_set_id' => $assetSetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_bid_modifier resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $criterionId
     *
     * @return string The formatted campaign_bid_modifier resource.
     */
    public static function campaignBidModifierName(string $customerId, string $campaignId, string $criterionId): string
    {
        return self::getPathTemplate('campaignBidModifier')->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_budget resource.
     *
     * @param string $customerId
     * @param string $campaignBudgetId
     *
     * @return string The formatted campaign_budget resource.
     */
    public static function campaignBudgetName(string $customerId, string $campaignBudgetId): string
    {
        return self::getPathTemplate('campaignBudget')->render([
            'customer_id' => $customerId,
            'campaign_budget_id' => $campaignBudgetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_conversion_goal resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $category
     * @param string $source
     *
     * @return string The formatted campaign_conversion_goal resource.
     */
    public static function campaignConversionGoalName(string $customerId, string $campaignId, string $category, string $source): string
    {
        return self::getPathTemplate('campaignConversionGoal')->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'category' => $category,
            'source' => $source,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_criterion resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $criterionId
     *
     * @return string The formatted campaign_criterion resource.
     */
    public static function campaignCriterionName(string $customerId, string $campaignId, string $criterionId): string
    {
        return self::getPathTemplate('campaignCriterion')->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_customizer resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $customizerAttributeId
     *
     * @return string The formatted campaign_customizer resource.
     */
    public static function campaignCustomizerName(string $customerId, string $campaignId, string $customizerAttributeId): string
    {
        return self::getPathTemplate('campaignCustomizer')->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'customizer_attribute_id' => $customizerAttributeId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_draft resource.
     *
     * @param string $customerId
     * @param string $baseCampaignId
     * @param string $draftId
     *
     * @return string The formatted campaign_draft resource.
     */
    public static function campaignDraftName(string $customerId, string $baseCampaignId, string $draftId): string
    {
        return self::getPathTemplate('campaignDraft')->render([
            'customer_id' => $customerId,
            'base_campaign_id' => $baseCampaignId,
            'draft_id' => $draftId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_extension_setting resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $extensionType
     *
     * @return string The formatted campaign_extension_setting resource.
     */
    public static function campaignExtensionSettingName(string $customerId, string $campaignId, string $extensionType): string
    {
        return self::getPathTemplate('campaignExtensionSetting')->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'extension_type' => $extensionType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_feed resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $feedId
     *
     * @return string The formatted campaign_feed resource.
     */
    public static function campaignFeedName(string $customerId, string $campaignId, string $feedId): string
    {
        return self::getPathTemplate('campaignFeed')->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'feed_id' => $feedId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_group resource.
     *
     * @param string $customerId
     * @param string $campaignGroupId
     *
     * @return string The formatted campaign_group resource.
     */
    public static function campaignGroupName(string $customerId, string $campaignGroupId): string
    {
        return self::getPathTemplate('campaignGroup')->render([
            'customer_id' => $customerId,
            'campaign_group_id' => $campaignGroupId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_label resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $labelId
     *
     * @return string The formatted campaign_label resource.
     */
    public static function campaignLabelName(string $customerId, string $campaignId, string $labelId): string
    {
        return self::getPathTemplate('campaignLabel')->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'label_id' => $labelId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * campaign_shared_set resource.
     *
     * @param string $customerId
     * @param string $campaignId
     * @param string $sharedSetId
     *
     * @return string The formatted campaign_shared_set resource.
     */
    public static function campaignSharedSetName(string $customerId, string $campaignId, string $sharedSetId): string
    {
        return self::getPathTemplate('campaignSharedSet')->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
            'shared_set_id' => $sharedSetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * carrier_constant resource.
     *
     * @param string $criterionId
     *
     * @return string The formatted carrier_constant resource.
     */
    public static function carrierConstantName(string $criterionId): string
    {
        return self::getPathTemplate('carrierConstant')->render([
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * combined_audience resource.
     *
     * @param string $customerId
     * @param string $combinedAudienceId
     *
     * @return string The formatted combined_audience resource.
     */
    public static function combinedAudienceName(string $customerId, string $combinedAudienceId): string
    {
        return self::getPathTemplate('combinedAudience')->render([
            'customer_id' => $customerId,
            'combined_audience_id' => $combinedAudienceId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * conversion_action resource.
     *
     * @param string $customerId
     * @param string $conversionActionId
     *
     * @return string The formatted conversion_action resource.
     */
    public static function conversionActionName(string $customerId, string $conversionActionId): string
    {
        return self::getPathTemplate('conversionAction')->render([
            'customer_id' => $customerId,
            'conversion_action_id' => $conversionActionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * conversion_custom_variable resource.
     *
     * @param string $customerId
     * @param string $conversionCustomVariableId
     *
     * @return string The formatted conversion_custom_variable resource.
     */
    public static function conversionCustomVariableName(string $customerId, string $conversionCustomVariableId): string
    {
        return self::getPathTemplate('conversionCustomVariable')->render([
            'customer_id' => $customerId,
            'conversion_custom_variable_id' => $conversionCustomVariableId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * conversion_goal_campaign_config resource.
     *
     * @param string $customerId
     * @param string $campaignId
     *
     * @return string The formatted conversion_goal_campaign_config resource.
     */
    public static function conversionGoalCampaignConfigName(string $customerId, string $campaignId): string
    {
        return self::getPathTemplate('conversionGoalCampaignConfig')->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * conversion_value_rule resource.
     *
     * @param string $customerId
     * @param string $conversionValueRuleId
     *
     * @return string The formatted conversion_value_rule resource.
     */
    public static function conversionValueRuleName(string $customerId, string $conversionValueRuleId): string
    {
        return self::getPathTemplate('conversionValueRule')->render([
            'customer_id' => $customerId,
            'conversion_value_rule_id' => $conversionValueRuleId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * conversion_value_rule_set resource.
     *
     * @param string $customerId
     * @param string $conversionValueRuleSetId
     *
     * @return string The formatted conversion_value_rule_set resource.
     */
    public static function conversionValueRuleSetName(string $customerId, string $conversionValueRuleSetId): string
    {
        return self::getPathTemplate('conversionValueRuleSet')->render([
            'customer_id' => $customerId,
            'conversion_value_rule_set_id' => $conversionValueRuleSetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * custom_conversion_goal resource.
     *
     * @param string $customerId
     * @param string $goalId
     *
     * @return string The formatted custom_conversion_goal resource.
     */
    public static function customConversionGoalName(string $customerId, string $goalId): string
    {
        return self::getPathTemplate('customConversionGoal')->render([
            'customer_id' => $customerId,
            'goal_id' => $goalId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a customer
     * resource.
     *
     * @param string $customerId
     *
     * @return string The formatted customer resource.
     */
    public static function customerName(string $customerId): string
    {
        return self::getPathTemplate('customer')->render([
            'customer_id' => $customerId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customer_asset resource.
     *
     * @param string $customerId
     * @param string $assetId
     * @param string $fieldType
     *
     * @return string The formatted customer_asset resource.
     */
    public static function customerAssetName(string $customerId, string $assetId, string $fieldType): string
    {
        return self::getPathTemplate('customerAsset')->render([
            'customer_id' => $customerId,
            'asset_id' => $assetId,
            'field_type' => $fieldType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customer_conversion_goal resource.
     *
     * @param string $customerId
     * @param string $category
     * @param string $source
     *
     * @return string The formatted customer_conversion_goal resource.
     */
    public static function customerConversionGoalName(string $customerId, string $category, string $source): string
    {
        return self::getPathTemplate('customerConversionGoal')->render([
            'customer_id' => $customerId,
            'category' => $category,
            'source' => $source,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customer_customizer resource.
     *
     * @param string $customerId
     * @param string $customizerAttributeId
     *
     * @return string The formatted customer_customizer resource.
     */
    public static function customerCustomizerName(string $customerId, string $customizerAttributeId): string
    {
        return self::getPathTemplate('customerCustomizer')->render([
            'customer_id' => $customerId,
            'customizer_attribute_id' => $customizerAttributeId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customer_extension_setting resource.
     *
     * @param string $customerId
     * @param string $extensionType
     *
     * @return string The formatted customer_extension_setting resource.
     */
    public static function customerExtensionSettingName(string $customerId, string $extensionType): string
    {
        return self::getPathTemplate('customerExtensionSetting')->render([
            'customer_id' => $customerId,
            'extension_type' => $extensionType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customer_feed resource.
     *
     * @param string $customerId
     * @param string $feedId
     *
     * @return string The formatted customer_feed resource.
     */
    public static function customerFeedName(string $customerId, string $feedId): string
    {
        return self::getPathTemplate('customerFeed')->render([
            'customer_id' => $customerId,
            'feed_id' => $feedId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customer_label resource.
     *
     * @param string $customerId
     * @param string $labelId
     *
     * @return string The formatted customer_label resource.
     */
    public static function customerLabelName(string $customerId, string $labelId): string
    {
        return self::getPathTemplate('customerLabel')->render([
            'customer_id' => $customerId,
            'label_id' => $labelId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customer_negative_criterion resource.
     *
     * @param string $customerId
     * @param string $criterionId
     *
     * @return string The formatted customer_negative_criterion resource.
     */
    public static function customerNegativeCriterionName(string $customerId, string $criterionId): string
    {
        return self::getPathTemplate('customerNegativeCriterion')->render([
            'customer_id' => $customerId,
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * customizer_attribute resource.
     *
     * @param string $customerId
     * @param string $customizerAttributeId
     *
     * @return string The formatted customizer_attribute resource.
     */
    public static function customizerAttributeName(string $customerId, string $customizerAttributeId): string
    {
        return self::getPathTemplate('customizerAttribute')->render([
            'customer_id' => $customerId,
            'customizer_attribute_id' => $customizerAttributeId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * detailed_demographic resource.
     *
     * @param string $customerId
     * @param string $detailedDemographicId
     *
     * @return string The formatted detailed_demographic resource.
     */
    public static function detailedDemographicName(string $customerId, string $detailedDemographicId): string
    {
        return self::getPathTemplate('detailedDemographic')->render([
            'customer_id' => $customerId,
            'detailed_demographic_id' => $detailedDemographicId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a experiment
     * resource.
     *
     * @param string $customerId
     * @param string $trialId
     *
     * @return string The formatted experiment resource.
     */
    public static function experimentName(string $customerId, string $trialId): string
    {
        return self::getPathTemplate('experiment')->render([
            'customer_id' => $customerId,
            'trial_id' => $trialId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * experiment_arm resource.
     *
     * @param string $customerId
     * @param string $trialId
     * @param string $trialArmId
     *
     * @return string The formatted experiment_arm resource.
     */
    public static function experimentArmName(string $customerId, string $trialId, string $trialArmId): string
    {
        return self::getPathTemplate('experimentArm')->render([
            'customer_id' => $customerId,
            'trial_id' => $trialId,
            'trial_arm_id' => $trialArmId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * extension_feed_item resource.
     *
     * @param string $customerId
     * @param string $feedItemId
     *
     * @return string The formatted extension_feed_item resource.
     */
    public static function extensionFeedItemName(string $customerId, string $feedItemId): string
    {
        return self::getPathTemplate('extensionFeedItem')->render([
            'customer_id' => $customerId,
            'feed_item_id' => $feedItemId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a feed
     * resource.
     *
     * @param string $customerId
     * @param string $feedId
     *
     * @return string The formatted feed resource.
     */
    public static function feedName(string $customerId, string $feedId): string
    {
        return self::getPathTemplate('feed')->render([
            'customer_id' => $customerId,
            'feed_id' => $feedId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a feed_item
     * resource.
     *
     * @param string $customerId
     * @param string $feedId
     * @param string $feedItemId
     *
     * @return string The formatted feed_item resource.
     */
    public static function feedItemName(string $customerId, string $feedId, string $feedItemId): string
    {
        return self::getPathTemplate('feedItem')->render([
            'customer_id' => $customerId,
            'feed_id' => $feedId,
            'feed_item_id' => $feedItemId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * feed_item_set resource.
     *
     * @param string $customerId
     * @param string $feedId
     * @param string $feedItemSetId
     *
     * @return string The formatted feed_item_set resource.
     */
    public static function feedItemSetName(string $customerId, string $feedId, string $feedItemSetId): string
    {
        return self::getPathTemplate('feedItemSet')->render([
            'customer_id' => $customerId,
            'feed_id' => $feedId,
            'feed_item_set_id' => $feedItemSetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * feed_item_set_link resource.
     *
     * @param string $customerId
     * @param string $feedId
     * @param string $feedItemSetId
     * @param string $feedItemId
     *
     * @return string The formatted feed_item_set_link resource.
     */
    public static function feedItemSetLinkName(string $customerId, string $feedId, string $feedItemSetId, string $feedItemId): string
    {
        return self::getPathTemplate('feedItemSetLink')->render([
            'customer_id' => $customerId,
            'feed_id' => $feedId,
            'feed_item_set_id' => $feedItemSetId,
            'feed_item_id' => $feedItemId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * feed_item_target resource.
     *
     * @param string $customerId
     * @param string $feedId
     * @param string $feedItemId
     * @param string $feedItemTargetType
     * @param string $feedItemTargetId
     *
     * @return string The formatted feed_item_target resource.
     */
    public static function feedItemTargetName(string $customerId, string $feedId, string $feedItemId, string $feedItemTargetType, string $feedItemTargetId): string
    {
        return self::getPathTemplate('feedItemTarget')->render([
            'customer_id' => $customerId,
            'feed_id' => $feedId,
            'feed_item_id' => $feedItemId,
            'feed_item_target_type' => $feedItemTargetType,
            'feed_item_target_id' => $feedItemTargetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a feed_mapping
     * resource.
     *
     * @param string $customerId
     * @param string $feedId
     * @param string $feedMappingId
     *
     * @return string The formatted feed_mapping resource.
     */
    public static function feedMappingName(string $customerId, string $feedId, string $feedMappingId): string
    {
        return self::getPathTemplate('feedMapping')->render([
            'customer_id' => $customerId,
            'feed_id' => $feedId,
            'feed_mapping_id' => $feedMappingId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * geo_target_constant resource.
     *
     * @param string $criterionId
     *
     * @return string The formatted geo_target_constant resource.
     */
    public static function geoTargetConstantName(string $criterionId): string
    {
        return self::getPathTemplate('geoTargetConstant')->render([
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a keyword_plan
     * resource.
     *
     * @param string $customerId
     * @param string $keywordPlanId
     *
     * @return string The formatted keyword_plan resource.
     */
    public static function keywordPlanName(string $customerId, string $keywordPlanId): string
    {
        return self::getPathTemplate('keywordPlan')->render([
            'customer_id' => $customerId,
            'keyword_plan_id' => $keywordPlanId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * keyword_plan_ad_group resource.
     *
     * @param string $customerId
     * @param string $keywordPlanAdGroupId
     *
     * @return string The formatted keyword_plan_ad_group resource.
     */
    public static function keywordPlanAdGroupName(string $customerId, string $keywordPlanAdGroupId): string
    {
        return self::getPathTemplate('keywordPlanAdGroup')->render([
            'customer_id' => $customerId,
            'keyword_plan_ad_group_id' => $keywordPlanAdGroupId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * keyword_plan_ad_group_keyword resource.
     *
     * @param string $customerId
     * @param string $keywordPlanAdGroupKeywordId
     *
     * @return string The formatted keyword_plan_ad_group_keyword resource.
     */
    public static function keywordPlanAdGroupKeywordName(string $customerId, string $keywordPlanAdGroupKeywordId): string
    {
        return self::getPathTemplate('keywordPlanAdGroupKeyword')->render([
            'customer_id' => $customerId,
            'keyword_plan_ad_group_keyword_id' => $keywordPlanAdGroupKeywordId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * keyword_plan_campaign resource.
     *
     * @param string $customerId
     * @param string $keywordPlanCampaignId
     *
     * @return string The formatted keyword_plan_campaign resource.
     */
    public static function keywordPlanCampaignName(string $customerId, string $keywordPlanCampaignId): string
    {
        return self::getPathTemplate('keywordPlanCampaign')->render([
            'customer_id' => $customerId,
            'keyword_plan_campaign_id' => $keywordPlanCampaignId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * keyword_plan_campaign_keyword resource.
     *
     * @param string $customerId
     * @param string $keywordPlanCampaignKeywordId
     *
     * @return string The formatted keyword_plan_campaign_keyword resource.
     */
    public static function keywordPlanCampaignKeywordName(string $customerId, string $keywordPlanCampaignKeywordId): string
    {
        return self::getPathTemplate('keywordPlanCampaignKeyword')->render([
            'customer_id' => $customerId,
            'keyword_plan_campaign_keyword_id' => $keywordPlanCampaignKeywordId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * keyword_theme_constant resource.
     *
     * @param string $expressCategoryId
     * @param string $expressSubCategoryId
     *
     * @return string The formatted keyword_theme_constant resource.
     */
    public static function keywordThemeConstantName(string $expressCategoryId, string $expressSubCategoryId): string
    {
        return self::getPathTemplate('keywordThemeConstant')->render([
            'express_category_id' => $expressCategoryId,
            'express_sub_category_id' => $expressSubCategoryId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a label
     * resource.
     *
     * @param string $customerId
     * @param string $labelId
     *
     * @return string The formatted label resource.
     */
    public static function labelName(string $customerId, string $labelId): string
    {
        return self::getPathTemplate('label')->render([
            'customer_id' => $customerId,
            'label_id' => $labelId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * language_constant resource.
     *
     * @param string $criterionId
     *
     * @return string The formatted language_constant resource.
     */
    public static function languageConstantName(string $criterionId): string
    {
        return self::getPathTemplate('languageConstant')->render([
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a life_event
     * resource.
     *
     * @param string $customerId
     * @param string $lifeEventId
     *
     * @return string The formatted life_event resource.
     */
    public static function lifeEventName(string $customerId, string $lifeEventId): string
    {
        return self::getPathTemplate('lifeEvent')->render([
            'customer_id' => $customerId,
            'life_event_id' => $lifeEventId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * mobile_app_category_constant resource.
     *
     * @param string $mobileAppCategoryId
     *
     * @return string The formatted mobile_app_category_constant resource.
     */
    public static function mobileAppCategoryConstantName(string $mobileAppCategoryId): string
    {
        return self::getPathTemplate('mobileAppCategoryConstant')->render([
            'mobile_app_category_id' => $mobileAppCategoryId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * mobile_device_constant resource.
     *
     * @param string $criterionId
     *
     * @return string The formatted mobile_device_constant resource.
     */
    public static function mobileDeviceConstantName(string $criterionId): string
    {
        return self::getPathTemplate('mobileDeviceConstant')->render([
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * operating_system_version_constant resource.
     *
     * @param string $criterionId
     *
     * @return string The formatted operating_system_version_constant resource.
     */
    public static function operatingSystemVersionConstantName(string $criterionId): string
    {
        return self::getPathTemplate('operatingSystemVersionConstant')->render([
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * recommendation_subscription resource.
     *
     * @param string $customerId
     * @param string $recommendationType
     *
     * @return string The formatted recommendation_subscription resource.
     */
    public static function recommendationSubscriptionName(string $customerId, string $recommendationType): string
    {
        return self::getPathTemplate('recommendationSubscription')->render([
            'customer_id' => $customerId,
            'recommendation_type' => $recommendationType,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * remarketing_action resource.
     *
     * @param string $customerId
     * @param string $remarketingActionId
     *
     * @return string The formatted remarketing_action resource.
     */
    public static function remarketingActionName(string $customerId, string $remarketingActionId): string
    {
        return self::getPathTemplate('remarketingAction')->render([
            'customer_id' => $customerId,
            'remarketing_action_id' => $remarketingActionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * shared_criterion resource.
     *
     * @param string $customerId
     * @param string $sharedSetId
     * @param string $criterionId
     *
     * @return string The formatted shared_criterion resource.
     */
    public static function sharedCriterionName(string $customerId, string $sharedSetId, string $criterionId): string
    {
        return self::getPathTemplate('sharedCriterion')->render([
            'customer_id' => $customerId,
            'shared_set_id' => $sharedSetId,
            'criterion_id' => $criterionId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a shared_set
     * resource.
     *
     * @param string $customerId
     * @param string $sharedSetId
     *
     * @return string The formatted shared_set resource.
     */
    public static function sharedSetName(string $customerId, string $sharedSetId): string
    {
        return self::getPathTemplate('sharedSet')->render([
            'customer_id' => $customerId,
            'shared_set_id' => $sharedSetId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * smart_campaign_setting resource.
     *
     * @param string $customerId
     * @param string $campaignId
     *
     * @return string The formatted smart_campaign_setting resource.
     */
    public static function smartCampaignSettingName(string $customerId, string $campaignId): string
    {
        return self::getPathTemplate('smartCampaignSetting')->render([
            'customer_id' => $customerId,
            'campaign_id' => $campaignId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * topic_constant resource.
     *
     * @param string $topicId
     *
     * @return string The formatted topic_constant resource.
     */
    public static function topicConstantName(string $topicId): string
    {
        return self::getPathTemplate('topicConstant')->render([
            'topic_id' => $topicId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a
     * user_interest resource.
     *
     * @param string $customerId
     * @param string $userInterestId
     *
     * @return string The formatted user_interest resource.
     */
    public static function userInterestName(string $customerId, string $userInterestId): string
    {
        return self::getPathTemplate('userInterest')->render([
            'customer_id' => $customerId,
            'user_interest_id' => $userInterestId,
        ]);
    }

    /**
     * Formats a string containing the fully-qualified path to represent a user_list
     * resource.
     *
     * @param string $customerId
     * @param string $userListId
     *
     * @return string The formatted user_list resource.
     */
    public static function userListName(string $customerId, string $userListId): string
    {
        return self::getPathTemplate('userList')->render([
            'customer_id' => $customerId,
            'user_list_id' => $userListId,
        ]);
    }

    /**
     * Parses a formatted name string and returns an associative array of the components in the name.
     * The following name formats are supported:
     * Template: Pattern
     * - accessibleBiddingStrategy: customers/{customer_id}/accessibleBiddingStrategies/{bidding_strategy_id}
     * - ad: customers/{customer_id}/ads/{ad_id}
     * - adGroup: customers/{customer_id}/adGroups/{ad_group_id}
     * - adGroupAd: customers/{customer_id}/adGroupAds/{ad_group_id}~{ad_id}
     * - adGroupAdLabel: customers/{customer_id}/adGroupAdLabels/{ad_group_id}~{ad_id}~{label_id}
     * - adGroupAsset: customers/{customer_id}/adGroupAssets/{ad_group_id}~{asset_id}~{field_type}
     * - adGroupBidModifier: customers/{customer_id}/adGroupBidModifiers/{ad_group_id}~{criterion_id}
     * - adGroupCriterion: customers/{customer_id}/adGroupCriteria/{ad_group_id}~{criterion_id}
     * - adGroupCriterionCustomizer: customers/{customer_id}/adGroupCriterionCustomizers/{ad_group_id}~{criterion_id}~{customizer_attribute_id}
     * - adGroupCriterionLabel: customers/{customer_id}/adGroupCriterionLabels/{ad_group_id}~{criterion_id}~{label_id}
     * - adGroupCustomizer: customers/{customer_id}/adGroupCustomizers/{ad_group_id}~{customizer_attribute_id}
     * - adGroupExtensionSetting: customers/{customer_id}/adGroupExtensionSettings/{ad_group_id}~{extension_type}
     * - adGroupFeed: customers/{customer_id}/adGroupFeeds/{ad_group_id}~{feed_id}
     * - adGroupLabel: customers/{customer_id}/adGroupLabels/{ad_group_id}~{label_id}
     * - adParameter: customers/{customer_id}/adParameters/{ad_group_id}~{criterion_id}~{parameter_index}
     * - asset: customers/{customer_id}/assets/{asset_id}
     * - assetGroup: customers/{customer_id}/assetGroups/{asset_group_id}
     * - assetGroupAsset: customers/{customer_id}/assetGroupAssets/{asset_group_id}~{asset_id}~{field_type}
     * - assetGroupListingGroupFilter: customers/{customer_id}/assetGroupListingGroupFilters/{asset_group_id}~{listing_group_filter_id}
     * - assetGroupSignal: customers/{customer_id}/assetGroupSignals/{asset_group_id}~{criterion_id}
     * - assetSet: customers/{customer_id}/assetSets/{asset_set_id}
     * - assetSetAsset: customers/{customer_id}/assetSetAssets/{asset_set_id}~{asset_id}
     * - audience: customers/{customer_id}/audiences/{audience_id}
     * - biddingDataExclusion: customers/{customer_id}/biddingDataExclusions/{seasonality_event_id}
     * - biddingSeasonalityAdjustment: customers/{customer_id}/biddingSeasonalityAdjustments/{seasonality_event_id}
     * - biddingStrategy: customers/{customer_id}/biddingStrategies/{bidding_strategy_id}
     * - campaign: customers/{customer_id}/campaigns/{campaign_id}
     * - campaignAsset: customers/{customer_id}/campaignAssets/{campaign_id}~{asset_id}~{field_type}
     * - campaignAssetSet: customers/{customer_id}/campaignAssetSets/{campaign_id}~{asset_set_id}
     * - campaignBidModifier: customers/{customer_id}/campaignBidModifiers/{campaign_id}~{criterion_id}
     * - campaignBudget: customers/{customer_id}/campaignBudgets/{campaign_budget_id}
     * - campaignConversionGoal: customers/{customer_id}/campaignConversionGoals/{campaign_id}~{category}~{source}
     * - campaignCriterion: customers/{customer_id}/campaignCriteria/{campaign_id}~{criterion_id}
     * - campaignCustomizer: customers/{customer_id}/campaignCustomizers/{campaign_id}~{customizer_attribute_id}
     * - campaignDraft: customers/{customer_id}/campaignDrafts/{base_campaign_id}~{draft_id}
     * - campaignExtensionSetting: customers/{customer_id}/campaignExtensionSettings/{campaign_id}~{extension_type}
     * - campaignFeed: customers/{customer_id}/campaignFeeds/{campaign_id}~{feed_id}
     * - campaignGroup: customers/{customer_id}/campaignGroups/{campaign_group_id}
     * - campaignLabel: customers/{customer_id}/campaignLabels/{campaign_id}~{label_id}
     * - campaignSharedSet: customers/{customer_id}/campaignSharedSets/{campaign_id}~{shared_set_id}
     * - carrierConstant: carrierConstants/{criterion_id}
     * - combinedAudience: customers/{customer_id}/combinedAudiences/{combined_audience_id}
     * - conversionAction: customers/{customer_id}/conversionActions/{conversion_action_id}
     * - conversionCustomVariable: customers/{customer_id}/conversionCustomVariables/{conversion_custom_variable_id}
     * - conversionGoalCampaignConfig: customers/{customer_id}/conversionGoalCampaignConfigs/{campaign_id}
     * - conversionValueRule: customers/{customer_id}/conversionValueRules/{conversion_value_rule_id}
     * - conversionValueRuleSet: customers/{customer_id}/conversionValueRuleSets/{conversion_value_rule_set_id}
     * - customConversionGoal: customers/{customer_id}/customConversionGoals/{goal_id}
     * - customer: customers/{customer_id}
     * - customerAsset: customers/{customer_id}/customerAssets/{asset_id}~{field_type}
     * - customerConversionGoal: customers/{customer_id}/customerConversionGoals/{category}~{source}
     * - customerCustomizer: customers/{customer_id}/customerCustomizers/{customizer_attribute_id}
     * - customerExtensionSetting: customers/{customer_id}/customerExtensionSettings/{extension_type}
     * - customerFeed: customers/{customer_id}/customerFeeds/{feed_id}
     * - customerLabel: customers/{customer_id}/customerLabels/{label_id}
     * - customerNegativeCriterion: customers/{customer_id}/customerNegativeCriteria/{criterion_id}
     * - customizerAttribute: customers/{customer_id}/customizerAttributes/{customizer_attribute_id}
     * - detailedDemographic: customers/{customer_id}/detailedDemographics/{detailed_demographic_id}
     * - experiment: customers/{customer_id}/experiments/{trial_id}
     * - experimentArm: customers/{customer_id}/experimentArms/{trial_id}~{trial_arm_id}
     * - extensionFeedItem: customers/{customer_id}/extensionFeedItems/{feed_item_id}
     * - feed: customers/{customer_id}/feeds/{feed_id}
     * - feedItem: customers/{customer_id}/feedItems/{feed_id}~{feed_item_id}
     * - feedItemSet: customers/{customer_id}/feedItemSets/{feed_id}~{feed_item_set_id}
     * - feedItemSetLink: customers/{customer_id}/feedItemSetLinks/{feed_id}~{feed_item_set_id}~{feed_item_id}
     * - feedItemTarget: customers/{customer_id}/feedItemTargets/{feed_id}~{feed_item_id}~{feed_item_target_type}~{feed_item_target_id}
     * - feedMapping: customers/{customer_id}/feedMappings/{feed_id}~{feed_mapping_id}
     * - geoTargetConstant: geoTargetConstants/{criterion_id}
     * - keywordPlan: customers/{customer_id}/keywordPlans/{keyword_plan_id}
     * - keywordPlanAdGroup: customers/{customer_id}/keywordPlanAdGroups/{keyword_plan_ad_group_id}
     * - keywordPlanAdGroupKeyword: customers/{customer_id}/keywordPlanAdGroupKeywords/{keyword_plan_ad_group_keyword_id}
     * - keywordPlanCampaign: customers/{customer_id}/keywordPlanCampaigns/{keyword_plan_campaign_id}
     * - keywordPlanCampaignKeyword: customers/{customer_id}/keywordPlanCampaignKeywords/{keyword_plan_campaign_keyword_id}
     * - keywordThemeConstant: keywordThemeConstants/{express_category_id}~{express_sub_category_id}
     * - label: customers/{customer_id}/labels/{label_id}
     * - languageConstant: languageConstants/{criterion_id}
     * - lifeEvent: customers/{customer_id}/lifeEvents/{life_event_id}
     * - mobileAppCategoryConstant: mobileAppCategoryConstants/{mobile_app_category_id}
     * - mobileDeviceConstant: mobileDeviceConstants/{criterion_id}
     * - operatingSystemVersionConstant: operatingSystemVersionConstants/{criterion_id}
     * - recommendationSubscription: customers/{customer_id}/recommendationSubscriptions/{recommendation_type}
     * - remarketingAction: customers/{customer_id}/remarketingActions/{remarketing_action_id}
     * - sharedCriterion: customers/{customer_id}/sharedCriteria/{shared_set_id}~{criterion_id}
     * - sharedSet: customers/{customer_id}/sharedSets/{shared_set_id}
     * - smartCampaignSetting: customers/{customer_id}/smartCampaignSettings/{campaign_id}
     * - topicConstant: topicConstants/{topic_id}
     * - userInterest: customers/{customer_id}/userInterests/{user_interest_id}
     * - userList: customers/{customer_id}/userLists/{user_list_id}
     *
     * The optional $template argument can be supplied to specify a particular pattern,
     * and must match one of the templates listed above. If no $template argument is
     * provided, or if the $template argument does not match one of the templates
     * listed, then parseName will check each of the supported templates, and return
     * the first match.
     *
     * @param string $formattedName The formatted name string
     * @param string $template      Optional name of template to match
     *
     * @return array An associative array from name component IDs to component values.
     *
     * @throws ValidationException If $formattedName could not be matched.
     */
    public static function parseName(string $formattedName, string $template = null): array
    {
        return self::parseFormattedName($formattedName, $template);
    }

    /**
     * Constructor.
     *
     * @param array $options {
     *     Optional. Options for configuring the service API wrapper.
     *
     *     @type string $apiEndpoint
     *           The address of the API remote host. May optionally include the port, formatted
     *           as "<uri>:<port>". Default 'googleads.googleapis.com:443'.
     *     @type string|array|FetchAuthTokenInterface|CredentialsWrapper $credentials
     *           The credentials to be used by the client to authorize API calls. This option
     *           accepts either a path to a credentials file, or a decoded credentials file as a
     *           PHP array.
     *           *Advanced usage*: In addition, this option can also accept a pre-constructed
     *           {@see \Google\Auth\FetchAuthTokenInterface} object or
     *           {@see \Google\ApiCore\CredentialsWrapper} object. Note that when one of these
     *           objects are provided, any settings in $credentialsConfig will be ignored.
     *     @type array $credentialsConfig
     *           Options used to configure credentials, including auth token caching, for the
     *           client. For a full list of supporting configuration options, see
     *           {@see \Google\ApiCore\CredentialsWrapper::build()} .
     *     @type bool $disableRetries
     *           Determines whether or not retries defined by the client configuration should be
     *           disabled. Defaults to `false`.
     *     @type string|array $clientConfig
     *           Client method configuration, including retry settings. This option can be either
     *           a path to a JSON file, or a PHP array containing the decoded JSON data. By
     *           default this settings points to the default client config file, which is
     *           provided in the resources folder.
     *     @type string|TransportInterface $transport
     *           The transport used for executing network requests. May be either the string
     *           `rest` or `grpc`. Defaults to `grpc` if gRPC support is detected on the system.
     *           *Advanced usage*: Additionally, it is possible to pass in an already
     *           instantiated {@see \Google\ApiCore\Transport\TransportInterface} object. Note
     *           that when this object is provided, any settings in $transportConfig, and any
     *           $apiEndpoint setting, will be ignored.
     *     @type array $transportConfig
     *           Configuration options that will be used to construct the transport. Options for
     *           each supported transport type should be passed in a key for that transport. For
     *           example:
     *           $transportConfig = [
     *               'grpc' => [...],
     *               'rest' => [...],
     *           ];
     *           See the {@see \Google\ApiCore\Transport\GrpcTransport::build()} and
     *           {@see \Google\ApiCore\Transport\RestTransport::build()} methods for the
     *           supported options.
     *     @type callable $clientCertSource
     *           A callable which returns the client cert as a string. This can be used to
     *           provide a certificate and private key to the transport layer for mTLS.
     * }
     *
     * @throws ValidationException
     */
    public function __construct(array $options = [])
    {
        $clientOptions = $this->buildClientOptions($options);
        $this->setClientOptions($clientOptions);
    }

    /** Handles execution of the async variants for each documented method. */
    public function __call($method, $args)
    {
        if (substr($method, -5) !== 'Async') {
            trigger_error('Call to undefined method ' . __CLASS__ . "::$method()", E_USER_ERROR);
        }

        array_unshift($args, substr($method, 0, -5));
        return call_user_func_array([$this, 'startAsyncCall'], $args);
    }

    /**
     * Creates, updates, or removes resources. This method supports atomic
     * transactions with multiple types of resources. For example, you can
     * atomically create a campaign and a campaign budget, or perform up to
     * thousands of mutates atomically.
     *
     * This method is essentially a wrapper around a series of mutate methods. The
     * only features it offers over calling those methods directly are:
     *
     * - Atomic transactions
     * - Temp resource names (described below)
     * - Somewhat reduced latency over making a series of mutate calls
     *
     * Note: Only resources that support atomic transactions are included, so this
     * method can't replace all calls to individual services.
     *
     * ## Atomic Transaction Benefits
     *
     * Atomicity makes error handling much easier. If you're making a series of
     * changes and one fails, it can leave your account in an inconsistent state.
     * With atomicity, you either reach the chosen state directly, or the request
     * fails and you can retry.
     *
     * ## Temp Resource Names
     *
     * Temp resource names are a special type of resource name used to create a
     * resource and reference that resource in the same request. For example, if a
     * campaign budget is created with `resource_name` equal to
     * `customers/123/campaignBudgets/-1`, that resource name can be reused in
     * the `Campaign.budget` field in the same request. That way, the two
     * resources are created and linked atomically.
     *
     * To create a temp resource name, put a negative number in the part of the
     * name that the server would normally allocate.
     *
     * Note:
     *
     * - Resources must be created with a temp name before the name can be reused.
     * For example, the previous CampaignBudget+Campaign example would fail if
     * the mutate order was reversed.
     * - Temp names are not remembered across requests.
     * - There's no limit to the number of temp names in a request.
     * - Each temp name must use a unique negative number, even if the resource
     * types differ.
     *
     * ## Latency
     *
     * It's important to group mutates by resource type or the request may time
     * out and fail. Latency is roughly equal to a series of calls to individual
     * mutate methods, where each change in resource type is a new call. For
     * example, mutating 10 campaigns then 10 ad groups is like 2 calls, while
     * mutating 1 campaign, 1 ad group, 1 campaign, 1 ad group is like 4 calls.
     *
     * List of thrown errors:
     * [AdCustomizerError]()
     * [AdError]()
     * [AdGroupAdError]()
     * [AdGroupCriterionError]()
     * [AdGroupError]()
     * [AssetError]()
     * [AuthenticationError]()
     * [AuthorizationError]()
     * [BiddingError]()
     * [CampaignBudgetError]()
     * [CampaignCriterionError]()
     * [CampaignError]()
     * [CampaignExperimentError]()
     * [CampaignSharedSetError]()
     * [CollectionSizeError]()
     * [ContextError]()
     * [ConversionActionError]()
     * [CriterionError]()
     * [CustomerFeedError]()
     * [DatabaseError]()
     * [DateError]()
     * [DateRangeError]()
     * [DistinctError]()
     * [ExtensionFeedItemError]()
     * [ExtensionSettingError]()
     * [FeedAttributeReferenceError]()
     * [FeedError]()
     * [FeedItemError]()
     * [FeedItemSetError]()
     * [FieldError]()
     * [FieldMaskError]()
     * [FunctionParsingError]()
     * [HeaderError]()
     * [ImageError]()
     * [InternalError]()
     * [KeywordPlanAdGroupKeywordError]()
     * [KeywordPlanCampaignError]()
     * [KeywordPlanError]()
     * [LabelError]()
     * [ListOperationError]()
     * [MediaUploadError]()
     * [MutateError]()
     * [NewResourceCreationError]()
     * [NullError]()
     * [OperationAccessDeniedError]()
     * [PolicyFindingError]()
     * [PolicyViolationError]()
     * [QuotaError]()
     * [RangeError]()
     * [RequestError]()
     * [ResourceCountLimitExceededError]()
     * [SettingError]()
     * [SharedSetError]()
     * [SizeLimitError]()
     * [StringFormatError]()
     * [StringLengthError]()
     * [UrlFieldError]()
     * [UserListError]()
     * [YoutubeVideoRegistrationError]()
     *
     * The async variant is {@see GoogleAdsServiceClient::mutateAsync()} .
     *
     * @param MutateGoogleAdsRequest $request     A request to house fields associated with the call.
     * @param array                  $callOptions {
     *     Optional.
     *
     *     @type RetrySettings|array $retrySettings
     *           Retry settings to use for this call. Can be a {@see RetrySettings} object, or an
     *           associative array of retry settings parameters. See the documentation on
     *           {@see RetrySettings} for example usage.
     * }
     *
     * @return MutateGoogleAdsResponse
     *
     * @throws ApiException Thrown if the API call fails.
     */
    public function mutate(MutateGoogleAdsRequest $request, array $callOptions = []): MutateGoogleAdsResponse
    {
        return $this->startApiCall('Mutate', $request, $callOptions)->wait();
    }

    /**
     * Returns all rows that match the search query.
     *
     * List of thrown errors:
     * [AuthenticationError]()
     * [AuthorizationError]()
     * [ChangeEventError]()
     * [ChangeStatusError]()
     * [ClickViewError]()
     * [HeaderError]()
     * [InternalError]()
     * [QueryError]()
     * [QuotaError]()
     * [RequestError]()
     *
     * The async variant is {@see GoogleAdsServiceClient::searchAsync()} .
     *
     * @param SearchGoogleAdsRequest $request     A request to house fields associated with the call.
     * @param array                  $callOptions {
     *     Optional.
     *
     *     @type RetrySettings|array $retrySettings
     *           Retry settings to use for this call. Can be a {@see RetrySettings} object, or an
     *           associative array of retry settings parameters. See the documentation on
     *           {@see RetrySettings} for example usage.
     * }
     *
     * @return PagedListResponse
     *
     * @throws ApiException Thrown if the API call fails.
     */
    public function search(SearchGoogleAdsRequest $request, array $callOptions = []): PagedListResponse
    {
        return $this->startApiCall('Search', $request, $callOptions);
    }

    /**
     * Returns all rows that match the search stream query.
     *
     * List of thrown errors:
     * [AuthenticationError]()
     * [AuthorizationError]()
     * [ChangeEventError]()
     * [ChangeStatusError]()
     * [ClickViewError]()
     * [HeaderError]()
     * [InternalError]()
     * [QueryError]()
     * [QuotaError]()
     * [RequestError]()
     *
     * @param SearchGoogleAdsStreamRequest $request     A request to house fields associated with the call.
     * @param array                        $callOptions {
     *     Optional.
     *
     *     @type int $timeoutMillis
     *           Timeout to use for this call.
     * }
     *
     * @return ServerStream
     *
     * @throws ApiException Thrown if the API call fails.
     */
    public function searchStream(SearchGoogleAdsStreamRequest $request, array $callOptions = []): ServerStream
    {
        return $this->startApiCall('SearchStream', $request, $callOptions);
    }
}
