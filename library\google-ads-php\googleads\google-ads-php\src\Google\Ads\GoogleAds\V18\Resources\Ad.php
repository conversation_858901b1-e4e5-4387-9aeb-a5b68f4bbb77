<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/resources/ad.proto

namespace Google\Ads\GoogleAds\V18\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * An ad.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.resources.Ad</code>
 */
class Ad extends \Google\Protobuf\Internal\Message
{
    /**
     * Immutable. The resource name of the ad.
     * Ad resource names have the form:
     * `customers/{customer_id}/ads/{ad_id}`
     *
     * Generated from protobuf field <code>string resource_name = 37 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     */
    protected $resource_name = '';
    /**
     * Output only. The ID of the ad.
     *
     * Generated from protobuf field <code>optional int64 id = 40 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $id = null;
    /**
     * The list of possible final URLs after all cross-domain redirects for the
     * ad.
     *
     * Generated from protobuf field <code>repeated string final_urls = 41;</code>
     */
    private $final_urls;
    /**
     * A list of final app URLs that will be used on mobile if the user has the
     * specific app installed.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.FinalAppUrl final_app_urls = 35;</code>
     */
    private $final_app_urls;
    /**
     * The list of possible final mobile URLs after all cross-domain redirects
     * for the ad.
     *
     * Generated from protobuf field <code>repeated string final_mobile_urls = 42;</code>
     */
    private $final_mobile_urls;
    /**
     * The URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 43;</code>
     */
    protected $tracking_url_template = null;
    /**
     * The suffix to use when constructing a final URL.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 44;</code>
     */
    protected $final_url_suffix = null;
    /**
     * The list of mappings that can be used to substitute custom parameter tags
     * in a `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     * For mutates, use url custom parameter operations.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.CustomParameter url_custom_parameters = 10;</code>
     */
    private $url_custom_parameters;
    /**
     * The URL that appears in the ad description for some ad formats.
     *
     * Generated from protobuf field <code>optional string display_url = 45;</code>
     */
    protected $display_url = null;
    /**
     * Output only. The type of ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdTypeEnum.AdType type = 5 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $type = 0;
    /**
     * Output only. Indicates if this ad was automatically added by Google Ads and
     * not by a user. For example, this could happen when ads are automatically
     * created as suggestions for new ads based on knowledge of how existing ads
     * are performing.
     *
     * Generated from protobuf field <code>optional bool added_by_google_ads = 46 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $added_by_google_ads = null;
    /**
     * The device preference for the ad. You can only specify a preference for
     * mobile devices. When this preference is set the ad will be preferred over
     * other ads when being displayed on a mobile device. The ad can still be
     * displayed on other device types, for example, if no other ads are
     * available. If unspecified (no device preference), all devices are targeted.
     * This is only supported by some ad types.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.DeviceEnum.Device device_preference = 20;</code>
     */
    protected $device_preference = 0;
    /**
     * Additional URLs for the ad that are tagged with a unique identifier that
     * can be referenced from other fields in the ad.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.UrlCollection url_collections = 26;</code>
     */
    private $url_collections;
    /**
     * Immutable. The name of the ad. This is only used to be able to identify the
     * ad. It does not need to be unique and does not affect the served ad. The
     * name field is currently only supported for DisplayUploadAd, ImageAd,
     * ShoppingComparisonListingAd and VideoAd.
     *
     * Generated from protobuf field <code>optional string name = 47 [(.google.api.field_behavior) = IMMUTABLE];</code>
     */
    protected $name = null;
    /**
     * Output only. If this ad is system managed, then this field will indicate
     * the source. This field is read-only.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.SystemManagedResourceSourceEnum.SystemManagedResourceSource system_managed_resource_source = 27 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $system_managed_resource_source = 0;
    protected $ad_data;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $resource_name
     *           Immutable. The resource name of the ad.
     *           Ad resource names have the form:
     *           `customers/{customer_id}/ads/{ad_id}`
     *     @type int|string $id
     *           Output only. The ID of the ad.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $final_urls
     *           The list of possible final URLs after all cross-domain redirects for the
     *           ad.
     *     @type array<\Google\Ads\GoogleAds\V18\Common\FinalAppUrl>|\Google\Protobuf\Internal\RepeatedField $final_app_urls
     *           A list of final app URLs that will be used on mobile if the user has the
     *           specific app installed.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $final_mobile_urls
     *           The list of possible final mobile URLs after all cross-domain redirects
     *           for the ad.
     *     @type string $tracking_url_template
     *           The URL template for constructing a tracking URL.
     *     @type string $final_url_suffix
     *           The suffix to use when constructing a final URL.
     *     @type array<\Google\Ads\GoogleAds\V18\Common\CustomParameter>|\Google\Protobuf\Internal\RepeatedField $url_custom_parameters
     *           The list of mappings that can be used to substitute custom parameter tags
     *           in a `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     *           For mutates, use url custom parameter operations.
     *     @type string $display_url
     *           The URL that appears in the ad description for some ad formats.
     *     @type int $type
     *           Output only. The type of ad.
     *     @type bool $added_by_google_ads
     *           Output only. Indicates if this ad was automatically added by Google Ads and
     *           not by a user. For example, this could happen when ads are automatically
     *           created as suggestions for new ads based on knowledge of how existing ads
     *           are performing.
     *     @type int $device_preference
     *           The device preference for the ad. You can only specify a preference for
     *           mobile devices. When this preference is set the ad will be preferred over
     *           other ads when being displayed on a mobile device. The ad can still be
     *           displayed on other device types, for example, if no other ads are
     *           available. If unspecified (no device preference), all devices are targeted.
     *           This is only supported by some ad types.
     *     @type array<\Google\Ads\GoogleAds\V18\Common\UrlCollection>|\Google\Protobuf\Internal\RepeatedField $url_collections
     *           Additional URLs for the ad that are tagged with a unique identifier that
     *           can be referenced from other fields in the ad.
     *     @type string $name
     *           Immutable. The name of the ad. This is only used to be able to identify the
     *           ad. It does not need to be unique and does not affect the served ad. The
     *           name field is currently only supported for DisplayUploadAd, ImageAd,
     *           ShoppingComparisonListingAd and VideoAd.
     *     @type int $system_managed_resource_source
     *           Output only. If this ad is system managed, then this field will indicate
     *           the source. This field is read-only.
     *     @type \Google\Ads\GoogleAds\V18\Common\TextAdInfo $text_ad
     *           Immutable. Details pertaining to a text ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\ExpandedTextAdInfo $expanded_text_ad
     *           Details pertaining to an expanded text ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\CallAdInfo $call_ad
     *           Details pertaining to a call ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\ExpandedDynamicSearchAdInfo $expanded_dynamic_search_ad
     *           Immutable. Details pertaining to an Expanded Dynamic Search Ad.
     *           This type of ad has its headline, final URLs, and display URL
     *           auto-generated at serving time according to domain name specific
     *           information provided by `dynamic_search_ads_setting` linked at the
     *           campaign level.
     *     @type \Google\Ads\GoogleAds\V18\Common\HotelAdInfo $hotel_ad
     *           Details pertaining to a hotel ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\ShoppingSmartAdInfo $shopping_smart_ad
     *           Details pertaining to a Smart Shopping ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\ShoppingProductAdInfo $shopping_product_ad
     *           Details pertaining to a Shopping product ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\ImageAdInfo $image_ad
     *           Immutable. Details pertaining to an Image ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\VideoAdInfo $video_ad
     *           Details pertaining to a Video ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\VideoResponsiveAdInfo $video_responsive_ad
     *           Details pertaining to a Video responsive ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\ResponsiveSearchAdInfo $responsive_search_ad
     *           Details pertaining to a responsive search ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\LegacyResponsiveDisplayAdInfo $legacy_responsive_display_ad
     *           Details pertaining to a legacy responsive display ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\AppAdInfo $app_ad
     *           Details pertaining to an app ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\LegacyAppInstallAdInfo $legacy_app_install_ad
     *           Immutable. Details pertaining to a legacy app install ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\ResponsiveDisplayAdInfo $responsive_display_ad
     *           Details pertaining to a responsive display ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\LocalAdInfo $local_ad
     *           Details pertaining to a local ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\DisplayUploadAdInfo $display_upload_ad
     *           Details pertaining to a display upload ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\AppEngagementAdInfo $app_engagement_ad
     *           Details pertaining to an app engagement ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\ShoppingComparisonListingAdInfo $shopping_comparison_listing_ad
     *           Details pertaining to a Shopping Comparison Listing ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\SmartCampaignAdInfo $smart_campaign_ad
     *           Details pertaining to a Smart campaign ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\AppPreRegistrationAdInfo $app_pre_registration_ad
     *           Details pertaining to an app pre-registration ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\DemandGenMultiAssetAdInfo $demand_gen_multi_asset_ad
     *           Details pertaining to a Demand Gen multi asset ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\DemandGenCarouselAdInfo $demand_gen_carousel_ad
     *           Details pertaining to a Demand Gen carousel ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\DemandGenVideoResponsiveAdInfo $demand_gen_video_responsive_ad
     *           Details pertaining to a Demand Gen video responsive ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\DemandGenProductAdInfo $demand_gen_product_ad
     *           Details pertaining to a Demand Gen product ad.
     *     @type \Google\Ads\GoogleAds\V18\Common\TravelAdInfo $travel_ad
     *           Details pertaining to a travel ad.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Resources\Ad::initOnce();
        parent::__construct($data);
    }

    /**
     * Immutable. The resource name of the ad.
     * Ad resource names have the form:
     * `customers/{customer_id}/ads/{ad_id}`
     *
     * Generated from protobuf field <code>string resource_name = 37 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getResourceName()
    {
        return $this->resource_name;
    }

    /**
     * Immutable. The resource name of the ad.
     * Ad resource names have the form:
     * `customers/{customer_id}/ads/{ad_id}`
     *
     * Generated from protobuf field <code>string resource_name = 37 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setResourceName($var)
    {
        GPBUtil::checkString($var, True);
        $this->resource_name = $var;

        return $this;
    }

    /**
     * Output only. The ID of the ad.
     *
     * Generated from protobuf field <code>optional int64 id = 40 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int|string
     */
    public function getId()
    {
        return isset($this->id) ? $this->id : 0;
    }

    public function hasId()
    {
        return isset($this->id);
    }

    public function clearId()
    {
        unset($this->id);
    }

    /**
     * Output only. The ID of the ad.
     *
     * Generated from protobuf field <code>optional int64 id = 40 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int|string $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt64($var);
        $this->id = $var;

        return $this;
    }

    /**
     * The list of possible final URLs after all cross-domain redirects for the
     * ad.
     *
     * Generated from protobuf field <code>repeated string final_urls = 41;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getFinalUrls()
    {
        return $this->final_urls;
    }

    /**
     * The list of possible final URLs after all cross-domain redirects for the
     * ad.
     *
     * Generated from protobuf field <code>repeated string final_urls = 41;</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFinalUrls($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->final_urls = $arr;

        return $this;
    }

    /**
     * A list of final app URLs that will be used on mobile if the user has the
     * specific app installed.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.FinalAppUrl final_app_urls = 35;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getFinalAppUrls()
    {
        return $this->final_app_urls;
    }

    /**
     * A list of final app URLs that will be used on mobile if the user has the
     * specific app installed.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.FinalAppUrl final_app_urls = 35;</code>
     * @param array<\Google\Ads\GoogleAds\V18\Common\FinalAppUrl>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFinalAppUrls($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Ads\GoogleAds\V18\Common\FinalAppUrl::class);
        $this->final_app_urls = $arr;

        return $this;
    }

    /**
     * The list of possible final mobile URLs after all cross-domain redirects
     * for the ad.
     *
     * Generated from protobuf field <code>repeated string final_mobile_urls = 42;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getFinalMobileUrls()
    {
        return $this->final_mobile_urls;
    }

    /**
     * The list of possible final mobile URLs after all cross-domain redirects
     * for the ad.
     *
     * Generated from protobuf field <code>repeated string final_mobile_urls = 42;</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFinalMobileUrls($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->final_mobile_urls = $arr;

        return $this;
    }

    /**
     * The URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 43;</code>
     * @return string
     */
    public function getTrackingUrlTemplate()
    {
        return isset($this->tracking_url_template) ? $this->tracking_url_template : '';
    }

    public function hasTrackingUrlTemplate()
    {
        return isset($this->tracking_url_template);
    }

    public function clearTrackingUrlTemplate()
    {
        unset($this->tracking_url_template);
    }

    /**
     * The URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 43;</code>
     * @param string $var
     * @return $this
     */
    public function setTrackingUrlTemplate($var)
    {
        GPBUtil::checkString($var, True);
        $this->tracking_url_template = $var;

        return $this;
    }

    /**
     * The suffix to use when constructing a final URL.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 44;</code>
     * @return string
     */
    public function getFinalUrlSuffix()
    {
        return isset($this->final_url_suffix) ? $this->final_url_suffix : '';
    }

    public function hasFinalUrlSuffix()
    {
        return isset($this->final_url_suffix);
    }

    public function clearFinalUrlSuffix()
    {
        unset($this->final_url_suffix);
    }

    /**
     * The suffix to use when constructing a final URL.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 44;</code>
     * @param string $var
     * @return $this
     */
    public function setFinalUrlSuffix($var)
    {
        GPBUtil::checkString($var, True);
        $this->final_url_suffix = $var;

        return $this;
    }

    /**
     * The list of mappings that can be used to substitute custom parameter tags
     * in a `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     * For mutates, use url custom parameter operations.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.CustomParameter url_custom_parameters = 10;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getUrlCustomParameters()
    {
        return $this->url_custom_parameters;
    }

    /**
     * The list of mappings that can be used to substitute custom parameter tags
     * in a `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     * For mutates, use url custom parameter operations.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.CustomParameter url_custom_parameters = 10;</code>
     * @param array<\Google\Ads\GoogleAds\V18\Common\CustomParameter>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUrlCustomParameters($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Ads\GoogleAds\V18\Common\CustomParameter::class);
        $this->url_custom_parameters = $arr;

        return $this;
    }

    /**
     * The URL that appears in the ad description for some ad formats.
     *
     * Generated from protobuf field <code>optional string display_url = 45;</code>
     * @return string
     */
    public function getDisplayUrl()
    {
        return isset($this->display_url) ? $this->display_url : '';
    }

    public function hasDisplayUrl()
    {
        return isset($this->display_url);
    }

    public function clearDisplayUrl()
    {
        unset($this->display_url);
    }

    /**
     * The URL that appears in the ad description for some ad formats.
     *
     * Generated from protobuf field <code>optional string display_url = 45;</code>
     * @param string $var
     * @return $this
     */
    public function setDisplayUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->display_url = $var;

        return $this;
    }

    /**
     * Output only. The type of ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdTypeEnum.AdType type = 5 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Output only. The type of ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdTypeEnum.AdType type = 5 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\AdTypeEnum\AdType::class);
        $this->type = $var;

        return $this;
    }

    /**
     * Output only. Indicates if this ad was automatically added by Google Ads and
     * not by a user. For example, this could happen when ads are automatically
     * created as suggestions for new ads based on knowledge of how existing ads
     * are performing.
     *
     * Generated from protobuf field <code>optional bool added_by_google_ads = 46 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return bool
     */
    public function getAddedByGoogleAds()
    {
        return isset($this->added_by_google_ads) ? $this->added_by_google_ads : false;
    }

    public function hasAddedByGoogleAds()
    {
        return isset($this->added_by_google_ads);
    }

    public function clearAddedByGoogleAds()
    {
        unset($this->added_by_google_ads);
    }

    /**
     * Output only. Indicates if this ad was automatically added by Google Ads and
     * not by a user. For example, this could happen when ads are automatically
     * created as suggestions for new ads based on knowledge of how existing ads
     * are performing.
     *
     * Generated from protobuf field <code>optional bool added_by_google_ads = 46 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param bool $var
     * @return $this
     */
    public function setAddedByGoogleAds($var)
    {
        GPBUtil::checkBool($var);
        $this->added_by_google_ads = $var;

        return $this;
    }

    /**
     * The device preference for the ad. You can only specify a preference for
     * mobile devices. When this preference is set the ad will be preferred over
     * other ads when being displayed on a mobile device. The ad can still be
     * displayed on other device types, for example, if no other ads are
     * available. If unspecified (no device preference), all devices are targeted.
     * This is only supported by some ad types.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.DeviceEnum.Device device_preference = 20;</code>
     * @return int
     */
    public function getDevicePreference()
    {
        return $this->device_preference;
    }

    /**
     * The device preference for the ad. You can only specify a preference for
     * mobile devices. When this preference is set the ad will be preferred over
     * other ads when being displayed on a mobile device. The ad can still be
     * displayed on other device types, for example, if no other ads are
     * available. If unspecified (no device preference), all devices are targeted.
     * This is only supported by some ad types.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.DeviceEnum.Device device_preference = 20;</code>
     * @param int $var
     * @return $this
     */
    public function setDevicePreference($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\DeviceEnum\Device::class);
        $this->device_preference = $var;

        return $this;
    }

    /**
     * Additional URLs for the ad that are tagged with a unique identifier that
     * can be referenced from other fields in the ad.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.UrlCollection url_collections = 26;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getUrlCollections()
    {
        return $this->url_collections;
    }

    /**
     * Additional URLs for the ad that are tagged with a unique identifier that
     * can be referenced from other fields in the ad.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.UrlCollection url_collections = 26;</code>
     * @param array<\Google\Ads\GoogleAds\V18\Common\UrlCollection>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUrlCollections($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Ads\GoogleAds\V18\Common\UrlCollection::class);
        $this->url_collections = $arr;

        return $this;
    }

    /**
     * Immutable. The name of the ad. This is only used to be able to identify the
     * ad. It does not need to be unique and does not affect the served ad. The
     * name field is currently only supported for DisplayUploadAd, ImageAd,
     * ShoppingComparisonListingAd and VideoAd.
     *
     * Generated from protobuf field <code>optional string name = 47 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return string
     */
    public function getName()
    {
        return isset($this->name) ? $this->name : '';
    }

    public function hasName()
    {
        return isset($this->name);
    }

    public function clearName()
    {
        unset($this->name);
    }

    /**
     * Immutable. The name of the ad. This is only used to be able to identify the
     * ad. It does not need to be unique and does not affect the served ad. The
     * name field is currently only supported for DisplayUploadAd, ImageAd,
     * ShoppingComparisonListingAd and VideoAd.
     *
     * Generated from protobuf field <code>optional string name = 47 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * Output only. If this ad is system managed, then this field will indicate
     * the source. This field is read-only.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.SystemManagedResourceSourceEnum.SystemManagedResourceSource system_managed_resource_source = 27 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getSystemManagedResourceSource()
    {
        return $this->system_managed_resource_source;
    }

    /**
     * Output only. If this ad is system managed, then this field will indicate
     * the source. This field is read-only.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.SystemManagedResourceSourceEnum.SystemManagedResourceSource system_managed_resource_source = 27 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setSystemManagedResourceSource($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\SystemManagedResourceSourceEnum\SystemManagedResourceSource::class);
        $this->system_managed_resource_source = $var;

        return $this;
    }

    /**
     * Immutable. Details pertaining to a text ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.TextAdInfo text_ad = 6 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\TextAdInfo|null
     */
    public function getTextAd()
    {
        return $this->readOneof(6);
    }

    public function hasTextAd()
    {
        return $this->hasOneof(6);
    }

    /**
     * Immutable. Details pertaining to a text ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.TextAdInfo text_ad = 6 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\TextAdInfo $var
     * @return $this
     */
    public function setTextAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\TextAdInfo::class);
        $this->writeOneof(6, $var);

        return $this;
    }

    /**
     * Details pertaining to an expanded text ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ExpandedTextAdInfo expanded_text_ad = 7;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ExpandedTextAdInfo|null
     */
    public function getExpandedTextAd()
    {
        return $this->readOneof(7);
    }

    public function hasExpandedTextAd()
    {
        return $this->hasOneof(7);
    }

    /**
     * Details pertaining to an expanded text ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ExpandedTextAdInfo expanded_text_ad = 7;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ExpandedTextAdInfo $var
     * @return $this
     */
    public function setExpandedTextAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ExpandedTextAdInfo::class);
        $this->writeOneof(7, $var);

        return $this;
    }

    /**
     * Details pertaining to a call ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CallAdInfo call_ad = 49;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\CallAdInfo|null
     */
    public function getCallAd()
    {
        return $this->readOneof(49);
    }

    public function hasCallAd()
    {
        return $this->hasOneof(49);
    }

    /**
     * Details pertaining to a call ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CallAdInfo call_ad = 49;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\CallAdInfo $var
     * @return $this
     */
    public function setCallAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\CallAdInfo::class);
        $this->writeOneof(49, $var);

        return $this;
    }

    /**
     * Immutable. Details pertaining to an Expanded Dynamic Search Ad.
     * This type of ad has its headline, final URLs, and display URL
     * auto-generated at serving time according to domain name specific
     * information provided by `dynamic_search_ads_setting` linked at the
     * campaign level.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ExpandedDynamicSearchAdInfo expanded_dynamic_search_ad = 14 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ExpandedDynamicSearchAdInfo|null
     */
    public function getExpandedDynamicSearchAd()
    {
        return $this->readOneof(14);
    }

    public function hasExpandedDynamicSearchAd()
    {
        return $this->hasOneof(14);
    }

    /**
     * Immutable. Details pertaining to an Expanded Dynamic Search Ad.
     * This type of ad has its headline, final URLs, and display URL
     * auto-generated at serving time according to domain name specific
     * information provided by `dynamic_search_ads_setting` linked at the
     * campaign level.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ExpandedDynamicSearchAdInfo expanded_dynamic_search_ad = 14 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ExpandedDynamicSearchAdInfo $var
     * @return $this
     */
    public function setExpandedDynamicSearchAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ExpandedDynamicSearchAdInfo::class);
        $this->writeOneof(14, $var);

        return $this;
    }

    /**
     * Details pertaining to a hotel ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.HotelAdInfo hotel_ad = 15;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\HotelAdInfo|null
     */
    public function getHotelAd()
    {
        return $this->readOneof(15);
    }

    public function hasHotelAd()
    {
        return $this->hasOneof(15);
    }

    /**
     * Details pertaining to a hotel ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.HotelAdInfo hotel_ad = 15;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\HotelAdInfo $var
     * @return $this
     */
    public function setHotelAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\HotelAdInfo::class);
        $this->writeOneof(15, $var);

        return $this;
    }

    /**
     * Details pertaining to a Smart Shopping ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ShoppingSmartAdInfo shopping_smart_ad = 17;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ShoppingSmartAdInfo|null
     */
    public function getShoppingSmartAd()
    {
        return $this->readOneof(17);
    }

    public function hasShoppingSmartAd()
    {
        return $this->hasOneof(17);
    }

    /**
     * Details pertaining to a Smart Shopping ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ShoppingSmartAdInfo shopping_smart_ad = 17;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ShoppingSmartAdInfo $var
     * @return $this
     */
    public function setShoppingSmartAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ShoppingSmartAdInfo::class);
        $this->writeOneof(17, $var);

        return $this;
    }

    /**
     * Details pertaining to a Shopping product ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ShoppingProductAdInfo shopping_product_ad = 18;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ShoppingProductAdInfo|null
     */
    public function getShoppingProductAd()
    {
        return $this->readOneof(18);
    }

    public function hasShoppingProductAd()
    {
        return $this->hasOneof(18);
    }

    /**
     * Details pertaining to a Shopping product ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ShoppingProductAdInfo shopping_product_ad = 18;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ShoppingProductAdInfo $var
     * @return $this
     */
    public function setShoppingProductAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ShoppingProductAdInfo::class);
        $this->writeOneof(18, $var);

        return $this;
    }

    /**
     * Immutable. Details pertaining to an Image ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ImageAdInfo image_ad = 22 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ImageAdInfo|null
     */
    public function getImageAd()
    {
        return $this->readOneof(22);
    }

    public function hasImageAd()
    {
        return $this->hasOneof(22);
    }

    /**
     * Immutable. Details pertaining to an Image ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ImageAdInfo image_ad = 22 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ImageAdInfo $var
     * @return $this
     */
    public function setImageAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ImageAdInfo::class);
        $this->writeOneof(22, $var);

        return $this;
    }

    /**
     * Details pertaining to a Video ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.VideoAdInfo video_ad = 24;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\VideoAdInfo|null
     */
    public function getVideoAd()
    {
        return $this->readOneof(24);
    }

    public function hasVideoAd()
    {
        return $this->hasOneof(24);
    }

    /**
     * Details pertaining to a Video ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.VideoAdInfo video_ad = 24;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\VideoAdInfo $var
     * @return $this
     */
    public function setVideoAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\VideoAdInfo::class);
        $this->writeOneof(24, $var);

        return $this;
    }

    /**
     * Details pertaining to a Video responsive ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.VideoResponsiveAdInfo video_responsive_ad = 39;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\VideoResponsiveAdInfo|null
     */
    public function getVideoResponsiveAd()
    {
        return $this->readOneof(39);
    }

    public function hasVideoResponsiveAd()
    {
        return $this->hasOneof(39);
    }

    /**
     * Details pertaining to a Video responsive ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.VideoResponsiveAdInfo video_responsive_ad = 39;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\VideoResponsiveAdInfo $var
     * @return $this
     */
    public function setVideoResponsiveAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\VideoResponsiveAdInfo::class);
        $this->writeOneof(39, $var);

        return $this;
    }

    /**
     * Details pertaining to a responsive search ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ResponsiveSearchAdInfo responsive_search_ad = 25;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ResponsiveSearchAdInfo|null
     */
    public function getResponsiveSearchAd()
    {
        return $this->readOneof(25);
    }

    public function hasResponsiveSearchAd()
    {
        return $this->hasOneof(25);
    }

    /**
     * Details pertaining to a responsive search ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ResponsiveSearchAdInfo responsive_search_ad = 25;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ResponsiveSearchAdInfo $var
     * @return $this
     */
    public function setResponsiveSearchAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ResponsiveSearchAdInfo::class);
        $this->writeOneof(25, $var);

        return $this;
    }

    /**
     * Details pertaining to a legacy responsive display ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LegacyResponsiveDisplayAdInfo legacy_responsive_display_ad = 28;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\LegacyResponsiveDisplayAdInfo|null
     */
    public function getLegacyResponsiveDisplayAd()
    {
        return $this->readOneof(28);
    }

    public function hasLegacyResponsiveDisplayAd()
    {
        return $this->hasOneof(28);
    }

    /**
     * Details pertaining to a legacy responsive display ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LegacyResponsiveDisplayAdInfo legacy_responsive_display_ad = 28;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\LegacyResponsiveDisplayAdInfo $var
     * @return $this
     */
    public function setLegacyResponsiveDisplayAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\LegacyResponsiveDisplayAdInfo::class);
        $this->writeOneof(28, $var);

        return $this;
    }

    /**
     * Details pertaining to an app ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AppAdInfo app_ad = 29;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\AppAdInfo|null
     */
    public function getAppAd()
    {
        return $this->readOneof(29);
    }

    public function hasAppAd()
    {
        return $this->hasOneof(29);
    }

    /**
     * Details pertaining to an app ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AppAdInfo app_ad = 29;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\AppAdInfo $var
     * @return $this
     */
    public function setAppAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\AppAdInfo::class);
        $this->writeOneof(29, $var);

        return $this;
    }

    /**
     * Immutable. Details pertaining to a legacy app install ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LegacyAppInstallAdInfo legacy_app_install_ad = 30 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\LegacyAppInstallAdInfo|null
     */
    public function getLegacyAppInstallAd()
    {
        return $this->readOneof(30);
    }

    public function hasLegacyAppInstallAd()
    {
        return $this->hasOneof(30);
    }

    /**
     * Immutable. Details pertaining to a legacy app install ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LegacyAppInstallAdInfo legacy_app_install_ad = 30 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\LegacyAppInstallAdInfo $var
     * @return $this
     */
    public function setLegacyAppInstallAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\LegacyAppInstallAdInfo::class);
        $this->writeOneof(30, $var);

        return $this;
    }

    /**
     * Details pertaining to a responsive display ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ResponsiveDisplayAdInfo responsive_display_ad = 31;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ResponsiveDisplayAdInfo|null
     */
    public function getResponsiveDisplayAd()
    {
        return $this->readOneof(31);
    }

    public function hasResponsiveDisplayAd()
    {
        return $this->hasOneof(31);
    }

    /**
     * Details pertaining to a responsive display ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ResponsiveDisplayAdInfo responsive_display_ad = 31;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ResponsiveDisplayAdInfo $var
     * @return $this
     */
    public function setResponsiveDisplayAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ResponsiveDisplayAdInfo::class);
        $this->writeOneof(31, $var);

        return $this;
    }

    /**
     * Details pertaining to a local ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LocalAdInfo local_ad = 32;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\LocalAdInfo|null
     */
    public function getLocalAd()
    {
        return $this->readOneof(32);
    }

    public function hasLocalAd()
    {
        return $this->hasOneof(32);
    }

    /**
     * Details pertaining to a local ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LocalAdInfo local_ad = 32;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\LocalAdInfo $var
     * @return $this
     */
    public function setLocalAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\LocalAdInfo::class);
        $this->writeOneof(32, $var);

        return $this;
    }

    /**
     * Details pertaining to a display upload ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DisplayUploadAdInfo display_upload_ad = 33;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DisplayUploadAdInfo|null
     */
    public function getDisplayUploadAd()
    {
        return $this->readOneof(33);
    }

    public function hasDisplayUploadAd()
    {
        return $this->hasOneof(33);
    }

    /**
     * Details pertaining to a display upload ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DisplayUploadAdInfo display_upload_ad = 33;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DisplayUploadAdInfo $var
     * @return $this
     */
    public function setDisplayUploadAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DisplayUploadAdInfo::class);
        $this->writeOneof(33, $var);

        return $this;
    }

    /**
     * Details pertaining to an app engagement ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AppEngagementAdInfo app_engagement_ad = 34;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\AppEngagementAdInfo|null
     */
    public function getAppEngagementAd()
    {
        return $this->readOneof(34);
    }

    public function hasAppEngagementAd()
    {
        return $this->hasOneof(34);
    }

    /**
     * Details pertaining to an app engagement ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AppEngagementAdInfo app_engagement_ad = 34;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\AppEngagementAdInfo $var
     * @return $this
     */
    public function setAppEngagementAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\AppEngagementAdInfo::class);
        $this->writeOneof(34, $var);

        return $this;
    }

    /**
     * Details pertaining to a Shopping Comparison Listing ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ShoppingComparisonListingAdInfo shopping_comparison_listing_ad = 36;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ShoppingComparisonListingAdInfo|null
     */
    public function getShoppingComparisonListingAd()
    {
        return $this->readOneof(36);
    }

    public function hasShoppingComparisonListingAd()
    {
        return $this->hasOneof(36);
    }

    /**
     * Details pertaining to a Shopping Comparison Listing ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ShoppingComparisonListingAdInfo shopping_comparison_listing_ad = 36;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ShoppingComparisonListingAdInfo $var
     * @return $this
     */
    public function setShoppingComparisonListingAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ShoppingComparisonListingAdInfo::class);
        $this->writeOneof(36, $var);

        return $this;
    }

    /**
     * Details pertaining to a Smart campaign ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.SmartCampaignAdInfo smart_campaign_ad = 48;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\SmartCampaignAdInfo|null
     */
    public function getSmartCampaignAd()
    {
        return $this->readOneof(48);
    }

    public function hasSmartCampaignAd()
    {
        return $this->hasOneof(48);
    }

    /**
     * Details pertaining to a Smart campaign ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.SmartCampaignAdInfo smart_campaign_ad = 48;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\SmartCampaignAdInfo $var
     * @return $this
     */
    public function setSmartCampaignAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\SmartCampaignAdInfo::class);
        $this->writeOneof(48, $var);

        return $this;
    }

    /**
     * Details pertaining to an app pre-registration ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AppPreRegistrationAdInfo app_pre_registration_ad = 50;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\AppPreRegistrationAdInfo|null
     */
    public function getAppPreRegistrationAd()
    {
        return $this->readOneof(50);
    }

    public function hasAppPreRegistrationAd()
    {
        return $this->hasOneof(50);
    }

    /**
     * Details pertaining to an app pre-registration ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AppPreRegistrationAdInfo app_pre_registration_ad = 50;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\AppPreRegistrationAdInfo $var
     * @return $this
     */
    public function setAppPreRegistrationAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\AppPreRegistrationAdInfo::class);
        $this->writeOneof(50, $var);

        return $this;
    }

    /**
     * Details pertaining to a Demand Gen multi asset ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DemandGenMultiAssetAdInfo demand_gen_multi_asset_ad = 62;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DemandGenMultiAssetAdInfo|null
     */
    public function getDemandGenMultiAssetAd()
    {
        return $this->readOneof(62);
    }

    public function hasDemandGenMultiAssetAd()
    {
        return $this->hasOneof(62);
    }

    /**
     * Details pertaining to a Demand Gen multi asset ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DemandGenMultiAssetAdInfo demand_gen_multi_asset_ad = 62;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DemandGenMultiAssetAdInfo $var
     * @return $this
     */
    public function setDemandGenMultiAssetAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DemandGenMultiAssetAdInfo::class);
        $this->writeOneof(62, $var);

        return $this;
    }

    /**
     * Details pertaining to a Demand Gen carousel ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DemandGenCarouselAdInfo demand_gen_carousel_ad = 63;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DemandGenCarouselAdInfo|null
     */
    public function getDemandGenCarouselAd()
    {
        return $this->readOneof(63);
    }

    public function hasDemandGenCarouselAd()
    {
        return $this->hasOneof(63);
    }

    /**
     * Details pertaining to a Demand Gen carousel ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DemandGenCarouselAdInfo demand_gen_carousel_ad = 63;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DemandGenCarouselAdInfo $var
     * @return $this
     */
    public function setDemandGenCarouselAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DemandGenCarouselAdInfo::class);
        $this->writeOneof(63, $var);

        return $this;
    }

    /**
     * Details pertaining to a Demand Gen video responsive ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DemandGenVideoResponsiveAdInfo demand_gen_video_responsive_ad = 64;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DemandGenVideoResponsiveAdInfo|null
     */
    public function getDemandGenVideoResponsiveAd()
    {
        return $this->readOneof(64);
    }

    public function hasDemandGenVideoResponsiveAd()
    {
        return $this->hasOneof(64);
    }

    /**
     * Details pertaining to a Demand Gen video responsive ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DemandGenVideoResponsiveAdInfo demand_gen_video_responsive_ad = 64;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DemandGenVideoResponsiveAdInfo $var
     * @return $this
     */
    public function setDemandGenVideoResponsiveAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DemandGenVideoResponsiveAdInfo::class);
        $this->writeOneof(64, $var);

        return $this;
    }

    /**
     * Details pertaining to a Demand Gen product ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DemandGenProductAdInfo demand_gen_product_ad = 61;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DemandGenProductAdInfo|null
     */
    public function getDemandGenProductAd()
    {
        return $this->readOneof(61);
    }

    public function hasDemandGenProductAd()
    {
        return $this->hasOneof(61);
    }

    /**
     * Details pertaining to a Demand Gen product ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DemandGenProductAdInfo demand_gen_product_ad = 61;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DemandGenProductAdInfo $var
     * @return $this
     */
    public function setDemandGenProductAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DemandGenProductAdInfo::class);
        $this->writeOneof(61, $var);

        return $this;
    }

    /**
     * Details pertaining to a travel ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.TravelAdInfo travel_ad = 54;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\TravelAdInfo|null
     */
    public function getTravelAd()
    {
        return $this->readOneof(54);
    }

    public function hasTravelAd()
    {
        return $this->hasOneof(54);
    }

    /**
     * Details pertaining to a travel ad.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.TravelAdInfo travel_ad = 54;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\TravelAdInfo $var
     * @return $this
     */
    public function setTravelAd($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\TravelAdInfo::class);
        $this->writeOneof(54, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getAdData()
    {
        return $this->whichOneof("ad_data");
    }

}

