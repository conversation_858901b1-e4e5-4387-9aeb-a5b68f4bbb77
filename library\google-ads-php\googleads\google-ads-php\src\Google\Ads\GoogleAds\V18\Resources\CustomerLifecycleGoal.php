<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/resources/customer_lifecycle_goal.proto

namespace Google\Ads\GoogleAds\V18\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Account level customer lifecycle goal settings.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.resources.CustomerLifecycleGoal</code>
 */
class CustomerLifecycleGoal extends \Google\Protobuf\Internal\Message
{
    /**
     * Immutable. The resource name of the customer lifecycle goal.
     * Customer lifecycle resource names have the form:
     * `customers/{customer_id}/customerLifecycleGoal`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     */
    protected $resource_name = '';
    /**
     * Output only. Customer acquisition goal customer level value settings.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LifecycleGoalValueSettings customer_acquisition_goal_value_settings = 3 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $customer_acquisition_goal_value_settings = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $resource_name
     *           Immutable. The resource name of the customer lifecycle goal.
     *           Customer lifecycle resource names have the form:
     *           `customers/{customer_id}/customerLifecycleGoal`
     *     @type \Google\Ads\GoogleAds\V18\Common\LifecycleGoalValueSettings $customer_acquisition_goal_value_settings
     *           Output only. Customer acquisition goal customer level value settings.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Resources\CustomerLifecycleGoal::initOnce();
        parent::__construct($data);
    }

    /**
     * Immutable. The resource name of the customer lifecycle goal.
     * Customer lifecycle resource names have the form:
     * `customers/{customer_id}/customerLifecycleGoal`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getResourceName()
    {
        return $this->resource_name;
    }

    /**
     * Immutable. The resource name of the customer lifecycle goal.
     * Customer lifecycle resource names have the form:
     * `customers/{customer_id}/customerLifecycleGoal`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setResourceName($var)
    {
        GPBUtil::checkString($var, True);
        $this->resource_name = $var;

        return $this;
    }

    /**
     * Output only. Customer acquisition goal customer level value settings.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LifecycleGoalValueSettings customer_acquisition_goal_value_settings = 3 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\LifecycleGoalValueSettings|null
     */
    public function getCustomerAcquisitionGoalValueSettings()
    {
        return $this->customer_acquisition_goal_value_settings;
    }

    public function hasCustomerAcquisitionGoalValueSettings()
    {
        return isset($this->customer_acquisition_goal_value_settings);
    }

    public function clearCustomerAcquisitionGoalValueSettings()
    {
        unset($this->customer_acquisition_goal_value_settings);
    }

    /**
     * Output only. Customer acquisition goal customer level value settings.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LifecycleGoalValueSettings customer_acquisition_goal_value_settings = 3 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\LifecycleGoalValueSettings $var
     * @return $this
     */
    public function setCustomerAcquisitionGoalValueSettings($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\LifecycleGoalValueSettings::class);
        $this->customer_acquisition_goal_value_settings = $var;

        return $this;
    }

}

