<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/services/recommendation_service.proto

namespace Google\Ads\GoogleAds\V18\Services\GenerateRecommendationsRequest;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Current AssetGroup information of the campaign.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.services.GenerateRecommendationsRequest.AssetGroupInfo</code>
 */
class AssetGroupInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * Required. Current url of the asset group.
     * This field is necessary for the following recommendation_types if
     * asset_group_info is set:
     * CAMPAIGN_BUDGET
     *
     * Generated from protobuf field <code>optional string final_url = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    protected $final_url = null;
    /**
     * Optional. Current headlines of the asset group.
     * This field is optional for the following recommendation_types if
     * asset_group_info is set:
     * CAMPAIGN_BUDGET
     *
     * Generated from protobuf field <code>repeated string headline = 2 [(.google.api.field_behavior) = OPTIONAL];</code>
     */
    private $headline;
    /**
     * Optional. Current descriptions of the asset group.
     * This field is optional for the following recommendation_types if
     * asset_group_info is set:
     * CAMPAIGN_BUDGET
     *
     * Generated from protobuf field <code>repeated string description = 3 [(.google.api.field_behavior) = OPTIONAL];</code>
     */
    private $description;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $final_url
     *           Required. Current url of the asset group.
     *           This field is necessary for the following recommendation_types if
     *           asset_group_info is set:
     *           CAMPAIGN_BUDGET
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $headline
     *           Optional. Current headlines of the asset group.
     *           This field is optional for the following recommendation_types if
     *           asset_group_info is set:
     *           CAMPAIGN_BUDGET
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $description
     *           Optional. Current descriptions of the asset group.
     *           This field is optional for the following recommendation_types if
     *           asset_group_info is set:
     *           CAMPAIGN_BUDGET
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Services\RecommendationService::initOnce();
        parent::__construct($data);
    }

    /**
     * Required. Current url of the asset group.
     * This field is necessary for the following recommendation_types if
     * asset_group_info is set:
     * CAMPAIGN_BUDGET
     *
     * Generated from protobuf field <code>optional string final_url = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return string
     */
    public function getFinalUrl()
    {
        return isset($this->final_url) ? $this->final_url : '';
    }

    public function hasFinalUrl()
    {
        return isset($this->final_url);
    }

    public function clearFinalUrl()
    {
        unset($this->final_url);
    }

    /**
     * Required. Current url of the asset group.
     * This field is necessary for the following recommendation_types if
     * asset_group_info is set:
     * CAMPAIGN_BUDGET
     *
     * Generated from protobuf field <code>optional string final_url = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param string $var
     * @return $this
     */
    public function setFinalUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->final_url = $var;

        return $this;
    }

    /**
     * Optional. Current headlines of the asset group.
     * This field is optional for the following recommendation_types if
     * asset_group_info is set:
     * CAMPAIGN_BUDGET
     *
     * Generated from protobuf field <code>repeated string headline = 2 [(.google.api.field_behavior) = OPTIONAL];</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getHeadline()
    {
        return $this->headline;
    }

    /**
     * Optional. Current headlines of the asset group.
     * This field is optional for the following recommendation_types if
     * asset_group_info is set:
     * CAMPAIGN_BUDGET
     *
     * Generated from protobuf field <code>repeated string headline = 2 [(.google.api.field_behavior) = OPTIONAL];</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setHeadline($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->headline = $arr;

        return $this;
    }

    /**
     * Optional. Current descriptions of the asset group.
     * This field is optional for the following recommendation_types if
     * asset_group_info is set:
     * CAMPAIGN_BUDGET
     *
     * Generated from protobuf field <code>repeated string description = 3 [(.google.api.field_behavior) = OPTIONAL];</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * Optional. Current descriptions of the asset group.
     * This field is optional for the following recommendation_types if
     * asset_group_info is set:
     * CAMPAIGN_BUDGET
     *
     * Generated from protobuf field <code>repeated string description = 3 [(.google.api.field_behavior) = OPTIONAL];</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setDescription($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->description = $arr;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AssetGroupInfo::class, \Google\Ads\GoogleAds\V18\Services\GenerateRecommendationsRequest_AssetGroupInfo::class);

