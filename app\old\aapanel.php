<?php

class bt_api {
	private $BT_KEY = "f5hQTfd60CHA2AGz9ddAw0Pa6LIwJWaK";
  	private $BT_PANEL = "https://panel.gass.co.id:7800";
	
	public function __construct($bt_panel = null,$bt_key = null){
		if($bt_panel) $this->BT_PANEL = $bt_panel;
		if($bt_key) $this->BT_KEY = $bt_key;
	}

	public function add_site($domain){
		$url = $this->BT_PANEL.'/site?action=AddSite';
		$p_data = $this->GetKeyData();
		$p_data['webname'] = '{"domain":"'.$domain.'","domainlist":[],"count":0}';
		$p_data['port'] = 80;
		$p_data['type'] = 'PHP';
        $p_data['ps'] = str_replace('.', '_', $domain);
        $p_data['path'] = '/www/wwwroot/'.$domain;
        $p_data['type_id'] = 0;
        $p_data['version'] = 74;
        $p_data['ftp'] = false;
        $p_data['sql'] = false;
        $p_data['codeing'] = 'utf8';
        $p_data['set_ssl'] = 1;
        $p_data['force_ssl'] = 1;
		$result = $this->HttpPostCookie($url,$p_data);
		$data = json_decode($result,true);
      	return $data;
        // Array ( [siteStatus] => 1 [siteId] => 11 [ftpStatus] => [databaseStatus] => [ssl] => 1 [redirect] => 1 ) 
	}

    public function add_site_proxy($domain){
		$url = $this->BT_PANEL.'/site?action=CreateProxy';
		$p_data = $this->GetKeyData();
		$p_data['subfilter'] = '[{"sub1":"","sub2":""},{"sub1":"","sub2":""},{"sub1":"","sub2":""}]';
		$p_data['type'] = 1;
        $p_data['proxyname'] = str_replace('.', '_', $domain);
        $p_data['cachetime'] = 1;
        $p_data['proxydir'] = '/';
        $p_data['proxysite'] = 'http://cta.gass.co.id/';
        $p_data['todomain'] = 'cta.gass.co.id';
        $p_data['cache'] = 0;
        $p_data['advanced'] = 0;
        $p_data['sitename'] = $domain;
		$result = $this->HttpPostCookie($url,$p_data);
		$data = json_decode($result,true);
      	return $data;
        //Array ( [status] => 1 [msg] => Setup successfully! ) 
	}
    
    public function delete_site($id, $domain){
		$url = $this->BT_PANEL.'/site?action=DeleteSite';
		$p_data = $this->GetKeyData();
		$p_data['id'] = $id;
        $p_data['webname'] = $domain;
		$result = $this->HttpPostCookie($url,$p_data);
		$data = json_decode($result,true);
      	return $data;
        //{"status": true, "msg": "Successfully deleted site!"}
	}
    
    public function get_site($domain){
		$url = $this->BT_PANEL.'/data?action=getData';
		$p_data = $this->GetKeyData();
		$p_data['table'] = 'sites';
        $p_data['limit'] = '';
        $p_data['search'] = '';
        $p_data['p'] = 1;
        $p_data['type'] = -1;
		$result = $this->HttpPostCookie($url,$p_data);
		$data = json_decode($result,true);
      	return $data;
        //Array ( [status] => 1 [msg] => Setup successfully! ) 
	}
    
    public function GetLogs(){
		$url = $this->BT_PANEL.'/data?action=getData';
		$p_data = $this->GetKeyData();
		$p_data['table'] = 'logs';
		$p_data['limit'] = 10;
		$p_data['tojs'] = 'test';
		$result = $this->HttpPostCookie($url,$p_data);
		$data = json_decode($result,true);
      	return $data;
	}
    
  	private function GetKeyData(){
  		$now_time = time();
    	$p_data = array(
			'request_token'	=>	md5($now_time.''.md5($this->BT_KEY)),
			'request_time'	=>	$now_time
		);
    	return $p_data;    
    }

    private function HttpPostCookie($url, $data,$timeout = 60){
        $cookie_file='./'.md5($this->BT_PANEL).'.cookie';
        if(!file_exists($cookie_file)){
            $fp = fopen($cookie_file,'w+');
            fclose($fp);
        }
		
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookie_file);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookie_file);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }
}
