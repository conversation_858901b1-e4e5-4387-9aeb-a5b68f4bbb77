<?php

class OrderProcessor {
    private $basePath = 'tmp_order';
    
    /**
     * Constructor to initialize the OrderProcessor
     * 
     * @param string $basePath Base directory for temporary order storage (optional)
     */
    public function __construct($basePath = null) {
        if ($basePath !== null) {
            $this->basePath = $basePath;
        }
        
        // Ensure base directory exists
        if (!is_dir($this->basePath)) {
            mkdir($this->basePath, 0755, true);
        }
    }
    
    /**
     * Add or update an order temporarily
     * 
     * @param string $project_id Project identifier
     * @param string $nope_client Client phone number
     * @param array $orderData Order data to store
     * @return bool Success status
     */
    public function addOrder($project_id, $nope_client, $orderData) {
        // Validate input parameters
        if (empty($project_id) || empty($nope_client) || !is_array($orderData)) {
            throw new InvalidArgumentException('Project ID, client phone number, and order data are required');
        }
        
        // Create directory structure: tmp_order/$project_id/
        $projectDir = $this->basePath . '/' . $project_id;
        if (!is_dir($projectDir)) {
            mkdir($projectDir, 0755, true);
        }
        
        // File path: tmp_order/$project_id/$nope_client.order
        $filePath = $projectDir . '/' . $nope_client . '.order';
        
        // Add timestamp to track when the order was last modified
        $orderData['last_modified'] = time();
        $orderData['created_at'] = $orderData['created_at'] ?? time();
        
        // Save order data as JSON
        $jsonContent = json_encode($orderData, JSON_PRETTY_PRINT);
        
        return file_put_contents($filePath, $jsonContent) !== false;
    }
    
    /**
     * Get order data if it hasn't been modified in the last specified time
     * 
     * @param string $project_id Project identifier
     * @param string $nope_client Client phone number
     * @param int $timeLimit Time limit in seconds (default: 6 hours = 21600 seconds)
     * @param bool $cleanupOldOrders Whether to clean up old orders in the same project (default: true)
     * @return array|null Order data or null if not available or recently modified
     */
    public function getOrder($project_id, $nope_client, $timeLimit = 21600, $cleanupOldOrders = true) {
        // Validate input parameters
        if (empty($project_id) || empty($nope_client)) {
            throw new InvalidArgumentException('Project ID and client phone number are required');
        }
        
        // File path: tmp_order/$project_id/$nope_client.order
        $filePath = $this->basePath . '/' . $project_id . '/' . $nope_client . '.order';
        
        // Check if file exists
        if (!file_exists($filePath)) {
            return null;
        }
        
        // Read file content
        $jsonContent = file_get_contents($filePath);
        if ($jsonContent === false) {
            return null;
        }
        
        // Decode JSON content
        $orderData = json_decode($jsonContent, true);
        if ($orderData === null) {
            return null;
        }
        
        // Check if order was modified within the time limit
        $lastModified = $orderData['last_modified'] ?? $orderData['created_at'] ?? time();
        $timeLimitAgo = time() - $timeLimit;
        
        // Clean up old orders in the same project if requested
        if ($cleanupOldOrders) {
            $this->cleanOldOrders();
        }
        
        // Return order data only if it hasn't been modified within the time limit
        if ($lastModified <= $timeLimitAgo) {
            return $orderData;
        }
        
        // Order was modified recently, return null
        return null;
    }
    
    /**
     * Get the file path for an order
     * 
     * @param string $project_id Project identifier
     * @param string $nope_client Client phone number
     * @return string File path
     */
    public function getOrderFilePath($project_id, $nope_client) {
        return $this->basePath . '/' . $project_id . '/' . $nope_client . '.order';
    }
    
    /**
     * Check if an order exists
     * 
     * @param string $project_id Project identifier
     * @param string $nope_client Client phone number
     * @return bool Whether the order exists
     */
    public function orderExists($project_id, $nope_client) {
        $filePath = $this->getOrderFilePath($project_id, $nope_client);
        return file_exists($filePath);
    }
    
    /**
     * Delete an order file
     * 
     * @param string $project_id Project identifier
     * @param string $nope_client Client phone number
     * @return bool Success status
     */
    public function deleteOrder($project_id, $nope_client) {
        $filePath = $this->getOrderFilePath($project_id, $nope_client);
        
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        
        return true; // File doesn't exist, consider it deleted
    }
    
    /**
     * Get all orders for a project
     * 
     * @param string $project_id Project identifier
     * @return array List of order files
     */
    public function getProjectOrders($project_id) {
        $projectDir = $this->basePath . '/' . $project_id;
        
        if (!is_dir($projectDir)) {
            return [];
        }
        
        $orders = [];
        $files = glob($projectDir . '/*.order');
        
        foreach ($files as $file) {
            $filename = basename($file, '.order');
            $orders[] = $filename;
        }
        
        return $orders;
    }
    
    /**
     * Clean up orders older than specified time (default: 2 days)
     * 
     * @param int $timeLimit Time limit in seconds (default: 2 days = 172800 seconds)
     * @return int Number of orders deleted
     */
    public function cleanOldOrders($timeLimit = 172800) {
        $deletedCount = 0;
        
        // Check if base directory exists
        if (!is_dir($this->basePath)) {
            return $deletedCount;
        }
        
        // Get all project directories
        $projects = glob($this->basePath . '/*', GLOB_ONLYDIR);
        
        foreach ($projects as $projectDir) {
            // Get all order files in this project directory
            $orderFiles = glob($projectDir . '/*.order');
            
            foreach ($orderFiles as $filePath) {
                // Check if file exists and get its modification time
                if (file_exists($filePath)) {
                    $fileModifiedTime = filemtime($filePath);
                    $timeLimitAgo = time() - $timeLimit;
                    
                    // If file is older than time limit, delete it
                    if ($fileModifiedTime <= $timeLimitAgo) {
                        if (unlink($filePath)) {
                            $deletedCount++;
                        }
                    }
                }
            }
        }
        
        return $deletedCount;
    }
    
    /**
     * Clean up old orders in a specific project
     * 
     * @param string $project_id Project identifier
     * @param int $timeLimit Time limit in seconds (default: 2 days = 172800 seconds)
     * @return int Number of orders deleted
     */
    public function cleanOldOrdersInProject($project_id, $timeLimit = 172800) {
        $deletedCount = 0;
        $projectDir = $this->basePath . '/' . $project_id;
        
        // Check if project directory exists
        if (!is_dir($projectDir)) {
            return $deletedCount;
        }
        
        // Get all order files in this project directory
        $orderFiles = glob($projectDir . '/*.order');
        
        foreach ($orderFiles as $filePath) {
            // Check if file exists and get its modification time
            if (file_exists($filePath)) {
                $fileModifiedTime = filemtime($filePath);
                $timeLimitAgo = time() - $timeLimit;
                
                // If file is older than time limit, delete it
                if ($fileModifiedTime <= $timeLimitAgo) {
                    if (unlink($filePath)) {
                        $deletedCount++;
                    }
                }
            }
        }
        
        return $deletedCount;
    }
}
