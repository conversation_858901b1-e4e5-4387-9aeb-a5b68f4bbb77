<?php

class ChatHistory 
{
    private $baseDir;
    private $projectId;
    private $encryptionKey;
    private $lastError = '';
    
    // Static properties untuk konstanta
    private static $fileExtension = '.log';
    private static $pathSeparator = '/';
    private static $timestampFormat = 'Y-m-d\TH:i:s';
    private static $encryptionMethod = 'AES-256-CBC';
    private static $maxFileSize = 10485760; // 10MB
    private static $maxMessageLength = 10000; // 10KB per message
    
    public function __construct($project_id, $baseDir = 'log/history') 
    {
        // Validate inputs
        if (empty($project_id)) {
            throw new InvalidArgumentException('Project ID must be a non-empty string');
        }
        
        if (!is_string($baseDir) || !preg_match('/^[a-zA-Z0-9_\-\/]+$/', $baseDir)) {
            throw new InvalidArgumentException('Invalid base directory');
        }
        
        $this->projectId = $this->sanitizeString($project_id);
        $this->baseDir = rtrim($baseDir, '/\\');
        
        // Generate encryption key dari project_id dengan salt yang lebih kuat
        $salt = 'gass_secure_salt_2025_' . PHP_VERSION;
        $this->encryptionKey = hash('sha256', $project_id . $salt, true);
    }
    
    /**
     * Get last error message
     * 
     * @return string
     */
    public function getLastError()
    {
        return $this->lastError;
    }
    
    /**
     * Set error message
     * 
     * @param string $error
     */
    private function setError($error)
    {
        $this->lastError = $error;
        // Log error jika diperlukan
        error_log("ChatHistory Error: " . $error);
    }
    
    /**
     * Sanitize string untuk mencegah path traversal
     * 
     * @param string $input
     * @return string
     */
    private function sanitizeString($input)
    {
        // Remove dangerous characters
        $sanitized = preg_replace('/[^a-zA-Z0-9_\-]/', '', $input);
        
        // Limit length
        return substr($sanitized, 0, 100);
    }
    
    /**
     * Generate file path untuk client dengan validasi
     * 
     * @param string $nope_client
     * @return string
     */
    private function getFilePath($nope_client) 
    {
        $sanitizedClient = $this->sanitizeString($nope_client);
        if (empty($sanitizedClient)) {
            throw new InvalidArgumentException('Invalid client identifier');
        }
        
        return $this->baseDir . self::$pathSeparator . $this->projectId . self::$pathSeparator . $sanitizedClient . self::$fileExtension;
    }
    
    /**
     * Validate file size
     * 
     * @param string $filePath
     * @return bool
     */
    private function isFileSizeValid($filePath)
    {
        if (!file_exists($filePath)) {
            return true;
        }
        
        return filesize($filePath) < self::$maxFileSize;
    }
    
    /**
     * Encrypt data dengan security yang lebih baik
     * 
     * @param string $data
     * @return string|false
     */
    private function encrypt($data) 
    {
        try {
            // Use secure random bytes
            $iv = random_bytes(openssl_cipher_iv_length(self::$encryptionMethod));
            $encrypted = openssl_encrypt($data, self::$encryptionMethod, $this->encryptionKey, OPENSSL_RAW_DATA, $iv);
            
            if ($encrypted === false) {
                $this->setError('Encryption failed');
                return false;
            }
            
            // Add integrity check dengan HMAC
            $hmac = hash_hmac('sha256', $encrypted, $this->encryptionKey, true);
            
            return base64_encode($iv . $hmac . $encrypted);
            
        } catch (Exception $e) {
            $this->setError('Encryption error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Decrypt data dengan integrity check
     * 
     * @param string $data
     * @return string|false
     */
    private function decrypt($data) 
    {
        try {
            $data = base64_decode($data, true);
            if ($data === false) {
                return false;
            }
            
            $ivLength = openssl_cipher_iv_length(self::$encryptionMethod);
            $hmacLength = 32; // SHA256 hash length
            
            if (strlen($data) < $ivLength + $hmacLength) {
                return false;
            }
            
            $iv = substr($data, 0, $ivLength);
            $hmac = substr($data, $ivLength, $hmacLength);
            $encrypted = substr($data, $ivLength + $hmacLength);
            
            // Verify HMAC
            $expectedHmac = hash_hmac('sha256', $encrypted, $this->encryptionKey, true);
            if (!hash_equals($hmac, $expectedHmac)) {
                $this->setError('Data integrity check failed');
                return false;
            }
            
            $decrypted = openssl_decrypt($encrypted, self::$encryptionMethod, $this->encryptionKey, OPENSSL_RAW_DATA, $iv);
            
            if ($decrypted === false) {
                $this->setError('Decryption failed');
                return false;
            }
            
            return $decrypted;
            
        } catch (Exception $e) {
            $this->setError('Decryption error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Read file header (block count) dengan error handling
     * 
     * @param string $filePath
     * @return int
     */
    private function readBlockCount($filePath) 
    {
        if (!file_exists($filePath)) {
            return 0;
        }
        
        try {
            $handle = fopen($filePath, 'r');
            if (!$handle) {
                $this->setError('Cannot open file for reading: ' . $filePath);
                return 0;
            }
            
            // Lock file untuk reading
            if (!flock($handle, LOCK_SH)) {
                fclose($handle);
                $this->setError('Cannot lock file for reading');
                return 0;
            }
            
            $header = fgets($handle);
            flock($handle, LOCK_UN);
            fclose($handle);
            
            if ($header && strpos($header, 'BLOCKS:') === 0) {
                return max(0, (int) substr($header, 7));
            }
            
            return 0;
            
        } catch (Exception $e) {
            $this->setError('Error reading block count: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Update block count in header dengan atomic operation
     * 
     * @param string $filePath
     * @param int $count
     * @return bool
     */
    private function updateBlockCount($filePath, $count) 
    {
        try {
            $tempFile = $filePath . '.tmp.' . uniqid();
            $newHeader = "BLOCKS:" . $count . "\n";
            
            // Read current content (skip old header)
            $content = '';
            if (file_exists($filePath)) {
                $currentContent = file_get_contents($filePath);
                if ($currentContent !== false) {
                    if (strpos($currentContent, 'BLOCKS:') === 0) {
                        $firstNewline = strpos($currentContent, "\n");
                        if ($firstNewline !== false) {
                            $content = substr($currentContent, $firstNewline + 1);
                        }
                    } else {
                        $content = $currentContent;
                    }
                }
            }
            
            // Write to temp file first (atomic operation)
            $result = file_put_contents($tempFile, $newHeader . $content, LOCK_EX);
            
            if ($result !== false) {
                // Atomic rename
                if (rename($tempFile, $filePath)) {
                    return true;
                } else {
                    unlink($tempFile);
                    $this->setError('Failed to rename temp file');
                    return false;
                }
            } else {
                $this->setError('Failed to write to temp file');
                return false;
            }
            
        } catch (Exception $e) {
            $this->setError('Error updating block count: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Simpan chat history ke file dengan validasi dan security
     * 
     * @param string $nope_client
     * @param string $type (in/out) - in = user, out = agent
     * @param string $message
     * @return bool
     */
    public function save($nope_client, $type, $message) 
    {
        try {
            // Validate inputs
            if (empty($nope_client)) {
                $this->setError('Invalid client identifier');
                return false;
            }
            
            if (!in_array($type, ['message_in', 'message_out'], true)) {
                $this->setError('Invalid message type. Must be "in" or "out"');
                return false;
            }
            
            if (!is_string($message)) {
                $this->setError('Message must be a string');
                return false;
            }
            if($message == '' || $message == null){
                return false;
            }
            // Limit message length
            if (strlen($message) > self::$maxMessageLength) {
                $message = substr($message, 0, self::$maxMessageLength);
            }
            
            // Buat direktori jika belum ada dengan secure permissions
            $dir = $this->baseDir . self::$pathSeparator . $this->projectId;
            if (!is_dir($dir)) {
                if (!mkdir($dir, 0750, true)) {
                    $this->setError('Cannot create directory: ' . $dir);
                    return false;
                }
            }
            
            $filePath = $this->getFilePath($nope_client);
            
            // Check file size limit
            if (!$this->isFileSizeValid($filePath)) {
                $this->setError('File size limit exceeded');
                return false;
            }
            
            $timestamp = date(self::$timestampFormat);
            
            // Konversi type ke role
            $role = ($type === 'message_in') ? 'user' : 'agent';
            
            // Remove emoticon dari message dan escape properly
            $cleanMessage = $this->removeEmoticons($message);
            
            // Format data sebagai proper JSON
            $data = [
                'role' => $role,
                'message' => $cleanMessage,
                'timestamp' => $timestamp
            ];
            
            $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            if ($jsonData === false) {
                $this->setError('JSON encoding failed');
                return false;
            }
            
            // Encrypt data sebagai block
            $encryptedBlock = $this->encrypt($jsonData);
            if ($encryptedBlock === false) {
                return false;
            }
            
            // Get current block count
            $currentCount = $this->readBlockCount($filePath);
            
            // Atomic append dengan proper locking
            $handle = fopen($filePath, 'a');
            if (!$handle) {
                $this->setError('Cannot open file for writing');
                return false;
            }
            
            if (!flock($handle, LOCK_EX)) {
                fclose($handle);
                $this->setError('Cannot lock file for writing');
                return false;
            }
            
            $result = fwrite($handle, $encryptedBlock . "\n");
            flock($handle, LOCK_UN);
            fclose($handle);
            
            if ($result === false) {
                $this->setError('Failed to write to file');
                return false;
            }
            
            // Update block count di header
            return $this->updateBlockCount($filePath, $currentCount + 1);
            
        } catch (Exception $e) {
            $this->setError('Save error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Menghilangkan emoticon dari text - optimized version
     * 
     * @param string $text
     * @return string
     */
    private function removeEmoticons($text) 
    {
        if (empty($text) || !is_string($text)) {
            return '';
        }
        
        // Efficient emoji removal in one pass
        $patterns = [
            // Unicode emoji ranges (comprehensive)
            '/[\x{1F600}-\x{1F64F}]/u', // Emoticons
            '/[\x{1F300}-\x{1F5FF}]/u', // Misc Symbols and Pictographs
            '/[\x{1F680}-\x{1F6FF}]/u', // Transport and Map
            '/[\x{1F1E0}-\x{1F1FF}]/u', // Regional indicator
            '/[\x{2600}-\x{26FF}]/u',   // Misc symbols
            '/[\x{2700}-\x{27BF}]/u',   // Dingbats
            '/[\x{1F900}-\x{1F9FF}]/u', // Supplemental Symbols and Pictographs
            '/[\x{1FA70}-\x{1FAFF}]/u', // Symbols and Pictographs Extended-A
            // Text emoticons (optimized pattern) - fixed regex
            '/[:;=8][D\)\(\[\]oOpP\|\/\\\\]{1,2}|<\/?3/',
        ];
        
        $text = preg_replace($patterns, '', $text);
        
        // Clean up extra whitespace - handle null case
        if ($text === null) {
            $text = '';
        }
        return preg_replace('/\s+/', ' ', trim($text));
    }
    
    /**
     * Read file dengan memory-efficient streaming
     * 
     * @param string $filePath
     * @return array
     */
    private function readFileLines($filePath)
    {
        if (!file_exists($filePath)) {
            return [];
        }
        
        $lines = [];
        $handle = fopen($filePath, 'r');
        if (!$handle) {
            return [];
        }
        
        if (!flock($handle, LOCK_SH)) {
            fclose($handle);
            return [];
        }
        
        while (($line = fgets($handle)) !== false) {
            $line = trim($line);
            if (!empty($line)) {
                $lines[] = $line;
            }
        }
        
        flock($handle, LOCK_UN);
        fclose($handle);
        
        return $lines;
    }
    
    /**
     * Ambil chat history dari file dengan optimized parsing
     * 
     * @param string $nope_client
     * @return array
     */
    public function get($nope_client) 
    {
        try {
            if (empty($nope_client)) {
                $this->setError('Invalid client identifier');
                return [];
            }
            
            $filePath = $this->getFilePath($nope_client);
            
            if (!file_exists($filePath)) {
                return [];
            }
            
            // Check file size
            if (!$this->isFileSizeValid($filePath)) {
                $this->setError('File size too large');
                return [];
            }
            
            // Use memory-efficient line reading
            $lines = $this->readFileLines($filePath);
            
            // Skip header jika ada (BLOCKS:n)
            $dataLines = [];
            foreach ($lines as $line) {
                if (strpos($line, 'BLOCKS:') === 0) {
                    continue;
                }
                $dataLines[] = $line;
            }
            
            $result = [];
            
            // Decrypt dan parse setiap block
            foreach ($dataLines as $line) {
                $decryptedData = $this->decrypt($line);
                if ($decryptedData === false) {
                    continue; // Skip corrupted blocks
                }
                
                // Parse JSON (better than regex)
                $data = json_decode($decryptedData, true);
                if ($data && isset($data['role'], $data['message'], $data['timestamp'])) {
                    $result[] = [
                        'role' => $data['role'],
                        'message' => $data['message'],
                        'timestamp' => $data['timestamp']
                    ];
                }
            }
            
            return $result;
            
        } catch (Exception $e) {
            $this->setError('Get error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Ambil chat history dengan limit - optimized
     * 
     * @param string $nope_client
     * @param int $limit
     * @return array
     */
    public function getLimit($nope_client, $limit = 50) 
    {
        if ($limit <= 0) {
            return [];
        }
        
        $chats = $this->get($nope_client);
        
        if (empty($chats)) {
            return [];
        }
        
        if (count($chats) > $limit) {
            return array_slice($chats, -$limit);
        }
        
        return $chats;
    }
    
    /**
     * Hapus chat history dengan proper cleanup
     * 
     * @param string $nope_client
     * @return bool
     */
    public function delete($nope_client) 
    {
        try {
            if (empty($nope_client)) {
                $this->setError('Invalid client identifier');
                return false;
            }
            
            $filePath = $this->getFilePath($nope_client);
            
            if (!file_exists($filePath)) {
                return true; // Already deleted
            }
            
            // Secure delete
            if (unlink($filePath)) {
                return true;
            } else {
                $this->setError('Cannot delete file');
                return false;
            }
            
        } catch (Exception $e) {
            $this->setError('Delete error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Cek apakah chat history ada
     * 
     * @param string $nope_client
     * @return bool
     */
    public function exists($nope_client) 
    {
        if (empty($nope_client)) {
            return false;
        }
        
        try {
            $filePath = $this->getFilePath($nope_client);
            return file_exists($filePath);
        } catch (Exception $e) {
            $this->setError('Exists check error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Hitung jumlah chat dengan optimized counting dari header
     * 
     * @param string $nope_client
     * @return int
     */
    public function count($nope_client) 
    {
        try {
            if (empty($nope_client)) {
                return 0;
            }
            
            $filePath = $this->getFilePath($nope_client);
            
            if (!file_exists($filePath)) {
                return 0;
            }
            
            // Use block count from header for efficiency
            $blockCount = $this->readBlockCount($filePath);
            
            // Always trust the header count if it exists and is valid
            return $blockCount;
            
        } catch (Exception $e) {
            $this->setError('Count error: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Validate file integrity
     * 
     * @param string $nope_client
     * @return array
     */
    public function validateIntegrity($nope_client)
    {
        try {
            $filePath = $this->getFilePath($nope_client);
            
            if (!file_exists($filePath)) {
                return ['valid' => true, 'message' => 'File does not exist'];
            }
            
            $headerCount = $this->readBlockCount($filePath);
            $lines = $this->readFileLines($filePath);
            
            // Count actual data lines (exclude header)
            $actualCount = 0;
            $corruptedBlocks = 0;
            
            foreach ($lines as $line) {
                if (strpos($line, 'BLOCKS:') === 0) {
                    continue;
                }
                
                $actualCount++;
                
                // Test decryption
                if ($this->decrypt($line) === false) {
                    $corruptedBlocks++;
                }
            }
            
            return [
                'valid' => ($headerCount === $actualCount && $corruptedBlocks === 0),
                'header_count' => $headerCount,
                'actual_count' => $actualCount,
                'corrupted_blocks' => $corruptedBlocks,
                'message' => $headerCount === $actualCount ? 'File is valid' : 'Block count mismatch'
            ];
            
        } catch (Exception $e) {
            return [
                'valid' => false,
                'message' => 'Validation error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Archive old messages (optional utility method)
     * 
     * @param string $nope_client
     * @param int $keepLast
     * @return bool
     */
    public function archive($nope_client, $keepLast = 100)
    {
        try {
            $chats = $this->get($nope_client);
            
            if (count($chats) <= $keepLast) {
                return true; // Nothing to archive
            }
            
            // Keep only last N messages
            $keptChats = array_slice($chats, -$keepLast);
            
            // Delete current file
            if (!$this->delete($nope_client)) {
                return false;
            }
            
            // Save kept messages
            foreach ($keptChats as $chat) {
                $type = ($chat['role'] === 'user') ? 'message_in' : 'message_out';
                if (!$this->save($nope_client, $type, $chat['message'])) {
                    return false;
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            $this->setError('Archive error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Reset folder by project_id - delete all chat history files for this project
     * 
     * @return bool
     */
    public function resetProjectFolder()
    {
        try {
            $projectDir = $this->baseDir . self::$pathSeparator . $this->projectId;
            
            if (!is_dir($projectDir)) {
                return true; // Directory doesn't exist, nothing to reset
            }
            
            // Get all files in the project directory
            $files = glob($projectDir . self::$pathSeparator . '*' . self::$fileExtension);
            
            if (empty($files)) {
                return true; // No files to delete
            }
            
            $deletedCount = 0;
            $errorCount = 0;
            
            foreach ($files as $file) {
                if (is_file($file)) {
                    if (unlink($file)) {
                        $deletedCount++;
                    } else {
                        $errorCount++;
                        $this->setError('Failed to delete file: ' . $file);
                    }
                }
            }
            
            // Try to remove the project directory if it's empty
            if (is_dir($projectDir) && count(scandir($projectDir)) <= 2) { // Only . and .. remain
                rmdir($projectDir);
            }
            
            if ($errorCount > 0) {
                $this->setError("Reset completed with errors. Deleted: $deletedCount, Failed: $errorCount");
                return false;
            }
            
            return true;
            
        } catch (Exception $e) {
            $this->setError('Reset project folder error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get project statistics
     * 
     * @return array
     */
    public function getProjectStats()
    {
        try {
            $projectDir = $this->baseDir . self::$pathSeparator . $this->projectId;
            
            if (!is_dir($projectDir)) {
                return [
                    'total_files' => 0,
                    'total_size' => 0,
                    'total_messages' => 0,
                    'directory_exists' => false
                ];
            }
            
            $files = glob($projectDir . self::$pathSeparator . '*' . self::$fileExtension);
            $totalSize = 0;
            $totalMessages = 0;
            
            foreach ($files as $file) {
                if (is_file($file)) {
                    $totalSize += filesize($file);
                    $totalMessages += $this->readBlockCount($file);
                }
            }
            
            return [
                'total_files' => count($files),
                'total_size' => $totalSize,
                'total_size_mb' => round($totalSize / 1024 / 1024, 2),
                'total_messages' => $totalMessages,
                'directory_exists' => true
            ];
            
        } catch (Exception $e) {
            $this->setError('Get project stats error: ' . $e->getMessage());
            return [
                'total_files' => 0,
                'total_size' => 0,
                'total_messages' => 0,
                'directory_exists' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Delete files older than specified days (cross-platform compatible)
     * 
     * @param int $days Number of days (default: 30)
     * @param bool $dryRun If true, only return files that would be deleted without actually deleting
     * @return array Result with deleted files count and details
     */
    public function cleanupOldFiles($days = 30, $dryRun = false)
    {
        try {
            $result = [
                'success' => false,
                'deleted_count' => 0,
                'deleted_files' => [],
                'errors' => [],
                'total_size_freed' => 0
            ];
            
            if ($days <= 0) {
                $this->setError('Days must be greater than 0');
                return $result;
            }
            
            $cutoffTime = time() - ($days * 24 * 60 * 60);
            $projectDir = $this->baseDir . self::$pathSeparator . $this->projectId;
            
            if (!is_dir($projectDir)) {
                $result['success'] = true;
                return $result; // No directory means no files to clean
            }
            
            // Use DirectoryIterator for better performance and cross-platform compatibility
            $iterator = new DirectoryIterator($projectDir);
            
            foreach ($iterator as $fileInfo) {
                if ($fileInfo->isDot() || !$fileInfo->isFile()) {
                    continue;
                }
                
                // Only process .log files for safety
                if ($fileInfo->getExtension() !== 'log') {
                    continue;
                }
                
                $filePath = $fileInfo->getPathname();
                $lastModified = $fileInfo->getMTime();
                
                if ($lastModified < $cutoffTime) {
                    $fileSize = $fileInfo->getSize();
                    $fileName = $fileInfo->getFilename();
                    
                    if ($dryRun) {
                        // Dry run - just add to list
                        $result['deleted_files'][] = [
                            'file' => $fileName,
                            'path' => $filePath,
                            'size' => $fileSize,
                            'last_modified' => date('Y-m-d H:i:s', $lastModified),
                            'age_days' => round((time() - $lastModified) / (24 * 60 * 60), 1)
                        ];
                        $result['deleted_count']++;
                        $result['total_size_freed'] += $fileSize;
                    } else {
                        // Actually delete the file
                        if (@unlink($filePath)) {
                            $result['deleted_files'][] = [
                                'file' => $fileName,
                                'path' => $filePath,
                                'size' => $fileSize,
                                'last_modified' => date('Y-m-d H:i:s', $lastModified),
                                'age_days' => round((time() - $lastModified) / (24 * 60 * 60), 1)
                            ];
                            $result['deleted_count']++;
                            $result['total_size_freed'] += $fileSize;
                        } else {
                            $result['errors'][] = "Failed to delete: " . $fileName;
                        }
                    }
                }
            }
            
            $result['success'] = true;
            return $result;
            
        } catch (Exception $e) {
            $this->setError('Cleanup error: ' . $e->getMessage());
            return [
                'success' => false,
                'deleted_count' => 0,
                'deleted_files' => [],
                'errors' => [$e->getMessage()],
                'total_size_freed' => 0
            ];
        }
    }
    
    /**
     * Delete specific client file if older than specified days
     * 
     * @param string $nope_client
     * @param int $days
     * @param bool $dryRun
     * @return array
     */
    public function cleanupClientFile($nope_client, $days = 30, $dryRun = false)
    {
        try {
            $result = [
                'success' => false,
                'deleted' => false,
                'file_info' => null,
                'error' => null
            ];
            
            if (empty($nope_client)) {
                $result['error'] = 'Invalid client identifier';
                return $result;
            }
            
            $filePath = $this->getFilePath($nope_client);
            
            if (!file_exists($filePath)) {
                $result['success'] = true;
                $result['error'] = 'File does not exist';
                return $result;
            }
            
            $lastModified = filemtime($filePath);
            $cutoffTime = time() - ($days * 24 * 60 * 60);
            $fileSize = filesize($filePath);
            
            $result['file_info'] = [
                'path' => $filePath,
                'size' => $fileSize,
                'last_modified' => date('Y-m-d H:i:s', $lastModified),
                'age_days' => round((time() - $lastModified) / (24 * 60 * 60), 1),
                'is_old' => $lastModified < $cutoffTime
            ];
            
            if ($lastModified < $cutoffTime) {
                if ($dryRun) {
                    $result['deleted'] = false; // Would be deleted
                } else {
                    if (@unlink($filePath)) {
                        $result['deleted'] = true;
                    } else {
                        $result['error'] = 'Failed to delete file';
                        return $result;
                    }
                }
            }
            
            $result['success'] = true;
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'deleted' => false,
                'file_info' => null,
                'error' => 'Cleanup error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get statistics about files in the project directory
     * 
     * @return array
     */
    public function getDirectoryStats()
    {
        try {
            $stats = [
                'total_files' => 0,
                'total_size' => 0,
                'oldest_file' => null,
                'newest_file' => null,
                'files_by_age' => [
                    '0-7_days' => 0,
                    '8-30_days' => 0,
                    '31-90_days' => 0,
                    'over_90_days' => 0
                ]
            ];
            
            $projectDir = $this->baseDir . self::$pathSeparator . $this->projectId;
            
            if (!is_dir($projectDir)) {
                return $stats;
            }
            
            $iterator = new DirectoryIterator($projectDir);
            $now = time();
            
            foreach ($iterator as $fileInfo) {
                if ($fileInfo->isDot() || !$fileInfo->isFile()) {
                    continue;
                }
                
                if ($fileInfo->getExtension() !== 'log') {
                    continue;
                }
                
                $stats['total_files']++;
                $stats['total_size'] += $fileInfo->getSize();
                
                $lastModified = $fileInfo->getMTime();
                $ageDays = ($now - $lastModified) / (24 * 60 * 60);
                
                // Track oldest and newest
                if ($stats['oldest_file'] === null || $lastModified < $stats['oldest_file']['timestamp']) {
                    $stats['oldest_file'] = [
                        'name' => $fileInfo->getFilename(),
                        'timestamp' => $lastModified,
                        'date' => date('Y-m-d H:i:s', $lastModified),
                        'age_days' => round($ageDays, 1)
                    ];
                }
                
                if ($stats['newest_file'] === null || $lastModified > $stats['newest_file']['timestamp']) {
                    $stats['newest_file'] = [
                        'name' => $fileInfo->getFilename(),
                        'timestamp' => $lastModified,
                        'date' => date('Y-m-d H:i:s', $lastModified),
                        'age_days' => round($ageDays, 1)
                    ];
                }
                
                // Categorize by age
                if ($ageDays <= 7) {
                    $stats['files_by_age']['0-7_days']++;
                } elseif ($ageDays <= 30) {
                    $stats['files_by_age']['8-30_days']++;
                } elseif ($ageDays <= 90) {
                    $stats['files_by_age']['31-90_days']++;
                } else {
                    $stats['files_by_age']['over_90_days']++;
                }
            }
            
            // Format total size
            $stats['total_size_formatted'] = $this->formatBytes($stats['total_size']);
            
            return $stats;
            
        } catch (Exception $e) {
            $this->setError('Stats error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Format bytes to human readable format
     * 
     * @param int $bytes
     * @return string
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $factor = floor((strlen($bytes) - 1) / 3);
        
        return sprintf("%.2f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }
    
}

?>