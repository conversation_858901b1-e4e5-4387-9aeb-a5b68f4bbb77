<?php

class trigger_fb
{

    private $table_visitor;
    private $table_log;
    private $table_hash;

    public function __construct()
    {
        $this->table_visitor = "visitor";
        $this->table_log = "log_connector";
        $this->table_hash = "log_connector_hash";
    }

    public static function hash_user_data($data)
    {
        return hash('sha256', $data);
    }

    public function sent_pixel($connector_key, $value, $pixel_id, $access_token, $vid, $type, $custom = null)
    {
        global $app;
        $db2 = $app->db2;

        // Create hash for tracking
        $data_hash = $this->createDataHash($pixel_id, $vid, $type);
        
        // Get visitor data
        $visitor = $this->getVisitorData($db2, $vid);
        if (!$visitor) {
            return ['result' => 'Visitor not found'];
        }

        // Process visitor data and setup tracking parameters
        $tracking_data = $this->processVisitorData($visitor);
        
        // Handle regular Facebook pixel event
        return $this->handleRegularPixelEvent(
            $db2, $pixel_id, $access_token, $connector_key, $tracking_data, 
            $visitor, $value, $type, $vid, $data_hash
        );
    }

    /**
     * Create data hash for tracking purposes
     */
    private function createDataHash($pixel_id, $vid, $type)
    {
        $hash_data = [
            "pixel_id" => $pixel_id,
            "source" => "facebook",
            "vid" => $vid,
            "type" => $type
        ];
        return json_encode($hash_data);
    }

    /**
     * Get visitor data from database
     */
    private function getVisitorData($db2, $vid)
    {
        $db2->where("visitor_id", $vid);
        return $db2->getone($this->table_visitor);
    }

    /**
     * Process visitor data and extract relevant information
     */
    private function processVisitorData($visitor)
    {
        $waktu_epoc = $this->getVisitorCreatedTime($visitor);
        $visitor_data = $visitor["data"] ? unserialize($visitor["data"]) : [];
        
        // Clean IP address
        $this->cleanIPAddress($visitor_data);
        
        // Setup Facebook tracking parameters
        $this->setupFacebookTracking($visitor_data, $waktu_epoc);
        
        return [
            'visitor_data' => $visitor_data,
            'waktu_epoc' => $waktu_epoc
        ];
    }

    /**
     * Get visitor created time or current time as fallback
     */
    private function getVisitorCreatedTime($visitor)
    {
        if (isset($visitor["created_unix"]) && $visitor["created_unix"] != 0) {
            return $visitor["created_unix"];
        }
        return time();
    }

    /**
     * Clean IP address (take first IP if multiple)
     */
    private function cleanIPAddress(&$visitor_data)
    {
        if (isset($visitor_data['ip'])) {
            $tmp = explode(",", $visitor_data['ip']);
            if (count($tmp) > 1) {
                $visitor_data["ip"] = $tmp[0];
            }
        }
    }

    /**
     * Setup Facebook tracking parameters (fbc, fbp)
     */
    private function setupFacebookTracking(&$visitor_data, $waktu_epoc)
    {
        // Setup FBC (Facebook Click ID)
        if (empty($visitor_data["fb"]["fbc"])) {
            if (isset($visitor_data["fb"]["fbclid"]) && $visitor_data["fb"]["fbclid"] != "") {
                $visitor_data["fb"]["fbc"] = "fb.1." . $waktu_epoc . "." . $visitor_data["fb"]["fbclid"];
            }
        }
        
        // Setup FBP (Facebook Browser ID)
        if (empty($visitor_data["fb"]["fbp"])) {
            $visitor_data["fb"]["fbp"] = "fb.1." . $waktu_epoc . "." . crc32($waktu_epoc);
        }
    }



    /**
     * Build hashed user data for Facebook API
     */
    private function buildHashedUserData($visitor_data, $visitor, $vid, $ctwa_clid = null)
    {
        return array_filter([
            'em' => isset($visitor_data['email']) ? self::hash_user_data($visitor_data['email']) : null,
            'ph' => isset($visitor['phone']) ? self::hash_user_data($visitor['phone']) : null,
            'external_id' => isset($vid) ? self::hash_user_data($vid) : null,
            'client_ip_address' => $visitor_data['ip'] ?? null,
            'client_user_agent' => $visitor_data['browser_agent'] ?? null,
            'fbc' => $visitor_data['fb']['fbc'] ?? null,
            'fbp' => $visitor_data['fb']['fbp'] ?? null,
            'ctwa_clid' => $ctwa_clid,
        ]);
    }

    /**
     * Build custom data including currency and value
     */
    private function buildCustomData($value)
    {
        if (!isset($value) || $value == 0) {
            return null;
        }

        $m = new meta();
        $cur = $m->get_meta("currency");
        
        return [
            'value' => $value,
            'currency' => $cur["result"]["data"] ?? "IDR"
        ];
    }

    /**
     * Generate event ID for deduplication
     */
    private function generateEventId($type, $vid)
    {
        $char_vid = convBase($vid, "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        $char_vid = str_split($char_vid, 4);
        $char_vid = implode(".", $char_vid);
        
        return $type . "-" . $char_vid;
    }



    /**
     * Handle regular Facebook pixel event
     */
    private function handleRegularPixelEvent($db2, $pixel_id, $access_token, $connector_key, $tracking_data, $visitor, $value, $type, $vid, $data_hash)
    {
        $hashed_user_data = $this->buildHashedUserData($tracking_data['visitor_data'], $visitor, $vid);
        $custom_data = $this->buildCustomData($value);
        $event_id = $this->generateEventId($type, $vid);

        $event_data = [
            'event_name' => $type,
            'event_time' => time(),
            'event_id' => $event_id,
        ];

        // Add user data and custom data if available
        if ($hashed_user_data) {
            $event_data["user_data"] = $hashed_user_data;
        }
        if ($custom_data) {
            $event_data["custom_data"] = $custom_data;
        }

        // Set action source and event source URL
        $this->setActionSourceAndUrl($event_data, $visitor, $type);

        $payload = [
            'data' => [$event_data],
            'access_token' => $access_token,
        ];

        $response = $this->sendCurlRequest(
            "https://graph.facebook.com/v22.0/$pixel_id/events",
            $payload
        );

        $log_data = ["msg" => $response['response']];
        if ($value && $value != 0) {
            $log_data["value"] = $value;
        }

        if (!$response['success']) {
            $log_data["input"] = $data_hash;
            $log_data["output"] = $log_data;
        }

        $this->logEvent($db2, $connector_key, $vid, $type, $log_data, $response['success'] ? 0 : 1);

        return ['result' => $log_data["msg"]];
    }

    /**
     * Set action source and event source URL
     */
    private function setActionSourceAndUrl(&$event_data, $visitor, $type)
    {
        if (!empty($visitor["page_url"])) {
            $event_data["event_source_url"] = $visitor["page_url"];
            $event_data["action_source"] = ($type == "ViewContent") ? "website" : "chat";
        } else {
            $event_data["action_source"] = "chat";
        }
    }

    /**
     * Send cURL request to Facebook API
     */
    private function sendCurlRequest($url, $payload)
    {
       
        // Convert data array to JSON string for POST field
        $post_data = [
            'data' => json_encode($payload['data'])
        ];
        
       
        
        
     
        
        
        
        
        
        // Add access_token if exists in payload
        if (isset($payload['access_token'])) {
            $post_data['access_token'] = $payload['access_token'];
        }
        
        // Add partner_agent if exists in payload
        if (isset($payload['partner_agent'])) {
            $post_data['partner_agent'] = $payload['partner_agent'];
        }
        
        // Add partner_agent if exists in payload
        if (isset($payload['test_event_code'])) {
            $post_data['test_event_code'] = $payload['test_event_code'];
        }

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
        
       

        $response = curl_exec($ch);

        if ($response === false) {
            $error = 'Curl error: ' . curl_error($ch);
            curl_close($ch);
            return ['success' => false, 'response' => $error];
        }

        $response_data = json_decode($response, true);
        curl_close($ch);

        if (isset($response_data['error'])) {
            return ['success' => false, 'response' => 'Error sending event: ' . $response_data['error']['message']];
        }

        return ['success' => true, 'response' => $response];
    }

    /**
     * Log event to database
     */
    private function logEvent($db2, $connector_key, $vid, $type, $log_data, $error_status)
    {
        $data_insert = [
            "error" => $error_status,
            "connector_key" => $db2->func("UNHEX(?)", [$connector_key]),
            "vid" => $vid,
            "event" => $type,
            "result" => json_encode($log_data),
            "waktu" => date("Y-m-d H:i:s")
        ];

        $db2->insert($this->table_log, $data_insert);
    }

    /**
     * Send CTWA pixel event (simplified version called from connector.php)
     */
    public function sent_ctwa_pixel($connector_key, $value, $pixel_id, $access_token, $vid, $type, $custom = null)
    {
        global $app;
        $db2 = $app->db2;

        // Create hash for tracking
        $data_hash = $this->createDataHash($pixel_id, $vid, $type);
        
        // Get visitor data
        $visitor = $this->getVisitorData($db2, $vid);
        if (!$visitor) {
            return ['result' => 'Visitor not found'];
        }

        // Process visitor data and setup tracking parameters
        $tracking_data = $this->processVisitorData($visitor);
        $visitor_data = $tracking_data['visitor_data'];

        // Extract CTWA data
        $ctwa_clid = $visitor_data["ctwaClid"] ?? $visitor_data["last_campaign"]["data"]["ctwaClid"] ?? null;
        $waba_id = $visitor_data["last_campaign"]["data"]["waba_id"] ?? null;

        if (!$waba_id || !$ctwa_clid) {
            return ['result' => 'CTWA data not found'];
        }

        // Build custom data for the event
        $custom_data = null;
        if (isset($value) && $value != 0) {
            $m = new meta();
            $cur = $m->get_meta("currency");
            $custom_data = [
                'value' => (float)$value,
                'currency' => $cur["result"]["data"] ?? "IDR"
            ];
        }

        // Build CTWA event data
        $ctwa_event_data = [
            'action_source' => 'business_messaging',
            'event_name' => $type,
            'event_time' => time(),
            'messaging_channel' => 'whatsapp',
            'user_data' => [
                'ctwa_clid' => $ctwa_clid,
                'whatsapp_business_account_id' => $waba_id
            ]
        ];

        // Add custom data if available
        if ($custom_data) {
            $ctwa_event_data["custom_data"] = $custom_data;
        }

        $payload = [
            'data' => [$ctwa_event_data],
            'partner_agent' => 'Gass.co.id'
        ];
        
       

        // Send to Facebook
        $response = $this->sendCurlRequest(
            "https://graph.facebook.com/v23.0/{$pixel_id}/events?access_token={$access_token}",
            $payload
        );
        
     

        // Prepare log data
        $log_data = [
            "msg" => $response['response'],
            "ctwa_clid" => $ctwa_clid,
            "waba_id" => $waba_id,
            "use_ctwa" => true,
            "event_type" => $type
        ];

        if ($value && $value != 0) {
            $log_data["value"] = $value;
            if ($custom_data) {
                $log_data["currency"] = $custom_data['currency'];
            }
        }

        if (!$response['success']) {
            $log_data["input"] = $data_hash;
            $log_data["output"] = $log_data;
        }

        // Log the event
        $this->logEvent($db2, $connector_key, $vid, $type, $log_data, $response['success'] ? 0 : 1);

        return ['result' => $log_data["msg"]];
    }



}
