<?php
class report_purchase
{
    public $platform;
    public $convertion_type;

    function __construct($platform,$convertion_type = NULL) {
        $this->platform =  strtolower($platform);
       
        if($convertion_type == NULL)
        {
            $this->convertion_type =  "realtime";
        }else{
            
            $ct =  
           $this->convertion_type = $convertion_type;
        }
    }

    function get_graph_history($option,$start,$end){
        $res = array();
        $res["summary"] = 0;
        $res["history"] = array();

        if($start == NULL && $end == NULL)
        {

            return false;
        }elseif ($start != NULL) {
            $date_range["start"] = $start;
            $date_range["end"] = date("Y-m-d");
        }elseif ($start == NULL && $end != NULL) {
            return  false;
        }else{
            $date_range["start"] = $start;
            $date_range["end"] = $end;
        }

        if(strtolower($option) == "page view"){
           $res["history"] =  $this->get_history("lp_view",$date_range);
           foreach ($res["history"] as $key => $value) {
               $res["summary"] += $value;
           }
           $res["type"] = "number";
        }
        elseif(strtolower($option) == "cta")
        {
            $res["history"] =  $this->get_history("cta",$date_range);
             foreach ($res["history"] as $key => $value) {
               $res["summary"] += $value;
           }
           $res["type"] = "number";
        }
        elseif(strtolower($option) == "cta rate")
        {
            $lp_view = $this->get_history("lp_view",$date_range);
            $wa =  $this->get_history("cta",$date_range);
            $ret = array();
            $total_wa = 0;
            $total_lp_view = 0;
            foreach ($lp_view as $key => $value) {
                if(isset($wa[$key])){
                    $total_wa += $wa[$key];
                    $total_lp_view += $lp_view[$key];

                    $ret[$key] = number_format($wa[$key] / $lp_view[$key] * 100,2,'.','');
                }else{
                    $ret[$key] =  0;
                }
                
            }
            if($total_wa != 0 and $total_lp_view != 0){
                  $res["summary"] += number_format($total_wa / $total_lp_view * 100,2,'.','.');
            }
          

            $res["history"] =  $ret;
            $res["type"] = "percentage";
        }


        elseif(strtolower($option) == "wa")
        {
            $res["history"] =  $this->get_history("wa",$date_range);
             foreach ($res["history"] as $key => $value) {
               $res["summary"] += $value;
           }
           $res["type"] = "number";
        }
        elseif(strtolower($option) == "wa rate")
        {
            $lp_view = $this->get_history("lp_view",$date_range);
            $wa =  $this->get_history("wa",$date_range);
            $ret = array();
            $total_wa = 0;
            $total_lp_view = 0;
            foreach ($lp_view as $key => $value) {
                if(isset($wa[$key])){
                    $total_wa += $wa[$key];
                    $total_lp_view += $lp_view[$key];

                    $ret[$key] = number_format($wa[$key] / $lp_view[$key] * 100,2,'.','');
                }else{
                    $ret[$key] =  0;
                }
                
            }
            if($total_wa != 0 and $total_lp_view != 0){
                  $res["summary"] += number_format($total_wa / $total_lp_view * 100,2,'.','.');
            }
          

            $res["history"] =  $ret;
            $res["type"] = "percentage";
        }
        elseif(strtolower($option) == "cost/wa")
        {
            $wa = $this->get_history("wa",$date_range);
            $spend =  $this->get_history("spend",$date_range);
            $ret = array();

            $total_wa = 0;
            $total_spend = 0;

            foreach ($wa as $key => $value) {
                if(isset($spend[$key])){

                    $total_wa += $wa[$key];
                    $total_spend += $spend[$key];

                    $ret[$key] = number_format($spend[$key] / $wa[$key],0,'.','');
                }else{
                    $ret[$key] =  0;
                }
                
            }

            if($total_wa != 0 and $total_spend != 0){

                  $res["summary"] += number_format($total_spend / $total_wa,2,'.','');
            }

            $res["history"] =  $ret;
            $res["type"] = "currency";

        }elseif(strtolower($option) == "mql")
        {
            $res["history"] =  $this->get_history("mql",$date_range);
            foreach ($res["history"] as $key => $value) {$res["summary"] += $value;}
            $res["type"] = "number";
        }
        elseif(strtolower($option) == "mql rate")
        {
            $total_wa = 0;
            $total_mql = 0;

            $wa = $this->get_history("wa",$date_range);
            $mql =  $this->get_history("mql",$date_range);
            $ret = array();
            foreach ($wa as $key => $value) {
                if(isset($mql[$key])){

                    $total_wa += $wa[$key];
                    $total_mql += $mql[$key];

                    $ret[$key] = number_format($mql[$key] / $wa[$key] * 100,2,'.','');
                }else{
                    $ret[$key] =  0;
                }
                
            }
            if($total_wa != 0 and $total_mql != 0){
                  $res["summary"] += number_format($total_mql / $total_wa * 100,2,'.','');
            }
            $res["history"] =  $ret;
            $res["type"] = "percentage";
        }
        elseif(strtolower($option) == "cost/mql")
        {
            $total_mql = 0;
            $total_spend = 0;

            $mql = $this->get_history("mql",$date_range);
            $spend =  $this->get_history("spend",$date_range);
            $ret = array();
            foreach ($mql as $key => $value) {
                if(isset($spend[$key])){

                    $total_spend += $spend[$key];
                    $total_mql += $mql[$key];

                    $ret[$key] = number_format($spend[$key] / $mql[$key],0,'.','');
                }else{
                    $ret[$key] =  0;
                }
                
            }
            if($total_mql != 0 and $total_spend != 0){
                  $res["summary"] += number_format($total_spend / $total_mql ,2,'.','');
            }
            $res["history"] =  $ret;
            $res["type"] = "currency";
        }
        elseif(strtolower($option) == "prospek")
        {
            $res["history"] =  $this->get_history("prospek",$date_range);
            foreach ($res["history"] as $key => $value) {$res["summary"] += $value;}
            $res["type"] = "number";
        }
        elseif(strtolower($option) == "prospek rate")
        {
            $total_wa = 0;
            $total_prospek = 0;

            $wa = $this->get_history("wa",$date_range);
            $prospek =  $this->get_history("prospek",$date_range);
            $ret = array();
            foreach ($wa as $key => $value) {
                if(isset($prospek[$key])){

                    $total_wa += $wa[$key];
                    $total_prospek += $prospek[$key];

                    $ret[$key] = number_format($prospek[$key] / $wa[$key] * 100,2,'.','');
                }else{
                    $ret[$key] =  0;
                }
                
            }
            if($total_wa != 0 and $total_prospek != 0){
                  $res["summary"] += number_format($total_prospek / $total_wa * 100,2,'.','');
            }
            $res["history"] =  $ret;
            $res["type"] = "percentage";
        }
        elseif(strtolower($option) == "cost/prospek")
        {
            $total_spend = 0;
            $total_prospek = 0;

            $prospek = $this->get_history("prospek",$date_range);
            $spend =  $this->get_history("spend",$date_range);
            $ret = array();
            foreach ($prospek as $key => $value) {
                if(isset($spend[$key])){

                    $total_spend += $spend[$key];
                    $total_prospek += $prospek[$key];

                    $ret[$key] = number_format($spend[$key] / $prospek[$key],0,'.','');
                }else{
                    $ret[$key] =  0;
                }
                
            }
            if($total_prospek != 0 and $total_spend != 0){
                  $res["summary"] += number_format($total_spend / $total_prospek ,2,'.','');
            }
            $res["history"] =  $ret;
            $res["type"] = "currency";
        }
        elseif(strtolower($option) == "purchase")
        {
            $res["history"] =  $this->get_history("purchase",$date_range);
            foreach ($res["history"] as $key => $value) {$res["summary"] += $value;}
            $res["type"] = "number";
        }
        elseif(strtolower($option) == "purchase value")
        {
            $res["history"] =  $this->get_history("purchase_value",$date_range);
            foreach ($res["history"] as $key => $value) {$res["summary"] += $value;}
            $res["type"] = "currency";
        }
        elseif(strtolower($option) == "unique purchase")
        {
        
            $res["history"] = $this->get_history("unik_purchase",$date_range);
            foreach ($res["history"] as $key => $value) {$res["summary"] += $value;}
            $res["type"] = "number";
        }
        elseif(strtolower($option) == "unique purchase rate")
        {
            $total_wa = 0;
            $total_unik_purchase = 0;

            $wa = $this->get_history("wa",$date_range);
            $unik_purchase =  $this->get_history("unik_purchase",$date_range);
            $ret = array();
            foreach ($wa as $key => $value) {
                if(isset($unik_purchase[$key])){

                    $total_wa += $wa[$key];
                    $total_unik_purchase += $unik_purchase[$key];

                    $ret[$key] = number_format($unik_purchase[$key] / $wa[$key] * 100,2,'.','');
                }else{
                    $ret[$key] =  0;
                }
                
            }
            if($total_wa != 0 and $total_unik_purchase != 0){
                  $res["summary"] += number_format($total_unik_purchase / $total_wa * 100,2,'.','');
            }
            $res["history"] = $ret;
            $res["type"] = "percentage";
        }
        elseif(strtolower($option) == "cost/unique purchase")
        {
            $total_spend = 0;
            $total_unik_purchase = 0;

            $unik_purchase = $this->get_history("unik_purchase",$date_range);
            $spend =  $this->get_history("spend",$date_range);
            $ret = array();
            foreach ($unik_purchase as $key => $value) {
                if(isset($spend[$key])){

                    $total_spend += $spend[$key];
                    $total_unik_purchase += $unik_purchase[$key];

                    $ret[$key] = number_format($spend[$key] / $unik_purchase[$key],0,'.','');
                }else{
                    $ret[$key] =  0;
                }
                
            }
            if($total_spend != 0 and $total_unik_purchase != 0){
                  $res["summary"] += number_format($total_spend / $total_unik_purchase * 100,2,'.','');
            }
            $res["history"] = $ret;
            $res["type"] = "currency";
        }
        elseif(strtolower($option) == "roas")
        {
            $total_spend = 0;
            $total_purchase_value = 0;

            $spend = $this->get_history("spend",$date_range);
            $purchase_value =  $this->get_history("purchase_value",$date_range);
            $ret = array();
            foreach ($spend as $key => $value) {
                if(isset($purchase_value[$key])){

                    $total_spend += $spend[$key];
                    $total_purchase_value += $purchase_value[$key];

                    $ret[$key] = number_format($purchase_value[$key] / $spend[$key] * 100,2,'.','');
                }else{
                    $ret[$key] =  0;
                }
                
            }
            if($total_spend != 0 and $total_purchase_value != 0){
                  $res["summary"] += number_format($total_purchase_value / $total_spend * 100,2,'.','');
            }
            $res["history"] = $ret;
           $res["type"] = "percentage";
        }


         return $res;
     
        
    }

    function get_recent_order($page)
    {
        global $app;
        $db2 = $app->db2;
        $start = 0;

        if($page > 1)
        {
            $start = $page * 10;
        }


        $db2->join("visitor","visitor.visitor_id = orders.visitor_id","LEFT");
         if($this->platform != NULL)
        {
            $db2->where("orders.source",$this->platform);
        }
        $db2->orderBy("orders.created","desc");
        $order = $db2->get("orders",[$start,10],"orders.visitor_id,orders.phone,orders.value,visitor.visit,visitor.waktu_contact,visitor.first_purchase,orders.source");

        foreach ($order as $key => $value) {
            $visitor_id = convBase($value["visitor_id"], "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
            $visitor_id = str_split($visitor_id, 4);
            $visitor_id = implode(".", $visitor_id);
            $order[$key]["visitor_id"] = $visitor_id;
        }
       

       return $order;


    }

    function get_available_platform()
    {
        global $app;
        $db2 = $app->db2;

        $res[0] = "All Platform";

        $db2->groupBy("source");
        $p = $db2->get("report",NULL,"report.source");
        $i = 1;
        foreach ($p as $key => $value) {
            $res[$i] = ucfirst($value["source"]);
            $i++;
        }

        return $res;
    }

    function get_buying_window($date_range = NULL)
    {
        global $app;
        $db2 = $app->db2;


        if($this->platform != NULL)
        {
            $db2->join("visitor_source","visitor_source.visitor_id = visitor.visitor_id");
            $db2->where("visitor_source.source",$this->platform);
        }

        if($date_range != NULL)
        {
          //  $db2->where("visitor.visit",[$date_range["start"],$date_range["end"]],"between");
        }

        $db2->where("visitor.purchase",1);
        $visitors = $db2->get("visitor",NULL,"visitor.first_purchase as waktu_purchase, visit");
        
        //$visitors = $db2->rawQuery($q);
        $hour_dif = array();
        foreach ($visitors as $key => $value) {
            if($value["visit"] != "0000-00-00" && $value["waktu_purchase"] != "0000-00-00")
            {
                $date1=date_create($value["waktu_purchase"]);
                $date2=date_create($value["visit"]);
                $diff=date_diff($date1,$date2);
                $hour_dif[] =  $diff->format("%h");
            }
            
        }

        sort($hour_dif);
        $count = sizeof($hour_dif);   
        $index = floor($count/2);  
        if (!$count) {
            return 0;
        } elseif ($count & 1) {    
            return $hour_dif[$index];
        } else {                  
            return ($hour_dif[$index-1] + $hour_dif[$index]) / 2;
        }
        
    }

    function get_history($key,$date_range){
        global $app;
        $db2 = $app->db2;

        if($this->platform != NULL)
        {
            $db2->join("report","report.report_id = report_data.report_id");
            $db2->where("report.source",$this->platform);
        }

        if($this->convertion_type == "visit"){
            if($key != "impression" && $key != "spend" && $key != "lp_view"){
                $key = "v-".$key;
            }
        }

        

        $db2->where("report_data.date",[$date_range["start"],$date_range["end"]],"between");
        $db2->where("report_data.report_key",$key);
        $db2->groupBy("report_data.date");
        $history = $db2->get("report_data",NULL,"sum(report_data.report_value) as report_value,date");
        $res = array();
        foreach ($history as $key => $value) {
            $res[$value["date"]] = $value["report_value"];
        }
        
        return $res;
    }


    function get_overview($start= NULL,$end = NULL)
    {
        if($start == NULL && $end == NULL)
        {
            return false;
        }elseif ($start != NULL) {
            $date_range["start"] = $start;
            $date_range["end"] = date("Y-m-d");
        }elseif ($start == NULL && $end != NULL) {
            return false;
        }else{
            $date_range["start"] = $start;
            $date_range["end"] = $end;
        }

        $date1=date_create($date_range["start"]);
        $date2=date_create($date_range["end"]);
        $diff=date_diff($date1,$date2);
        $date_diff =  $diff->format("%a");


        $last_range["end"] =  date('Y-m-d',strtotime($date_range["start"] . "-1 days"));
        $last_range["start"] = date('Y-m-d',strtotime($date_range["start"] . "-".$date_diff." days"));



        $data["buying_window"] = $this->get_buying_window($date_range);
/////////////////////////////
       // $data["cac"]["current"] = $this->get_cac($date_range);
        //$data["cac"]["last"] = $this->get_cac($last_range);


        if($data["cac"]["current"] > 0 && $data["cac"]["last"] > 0)
        {
            $selisih_cac = $data["cac"]["current"] - $data["cac"]["last"];
            $selisih_persen = round(($data["cac"]["current"] - $data["cac"]["last"]) / $data["cac"]["last"] * 100,2);
            $data["cac"]["selisih"] = $selisih_cac;
            $data["cac"]["selisih_persen"] = $selisih_persen;
        }
        else{
            $data["cac"]["selisih"] = 0;
            $data["cac"]["selisih_persen"] = 0;
        }
////////////////////////////////////////

        $data["omset"]["current"] = $this->get_report_value("purchase_value",$date_range);
        $data["omset"]["last"] = $this->get_report_value("purchase_value",$last_range);

        if($data["cac"]["current"] > 0 && $data["cac"]["last"] > 0)
        {
            $selisih_omset = $data["omset"]["current"] - $data["omset"]["last"];
            $selisih_persen = round(($data["omset"]["current"] - $data["omset"]["last"]) / $data["omset"]["last"] * 100,2);
            $data["omset"]["selisih"] = $selisih_omset;
            $data["omset"]["selisih_persen"] = $selisih_persen;
        }
        else{
            $data["omset"]["selisih"] = 0;
            $data["omset"]["selisih_persen"] = 0;
        }
      
      ///////////////////////////////////////////////

        $data["order"]["current"] = $this->get_report_value("unik_purchase",$date_range);
        $data["order"]["last"] = $this->get_report_value("unik_purchase",$last_range);

        if($data["cac"]["current"] > 0 && $data["cac"]["last"] > 0)
        {
            $selisih_order = $data["order"]["current"] - $data["order"]["last"];
            $selisih_persen = round(($data["order"]["current"] - $data["order"]["last"]) / $data["order"]["last"] * 100,2);
            $data["order"]["selisih"] = $selisih_order;
            $data["order"]["selisih_persen"] = $selisih_persen;
        }
        else{
            $data["order"]["selisih"] = 0;
            $data["order"]["selisih_persen"] = 0;
        }

        /////////////////////////////////////////////
        

       
        $data["ltv"] = $this->get_ltv();


        $data["purchase_hour"] = $this->purchase_hour($date_range);

        $data["closing_rate_hour"] = $this->closing_rate_hour($date_range);

        $data["visit_hour"] = $this->visit_hour($date_range);

        

        $data["purchase_day"] = $this->purchase_day($date_range);
        $data["closing_rate_day"] = $this->closing_rate_day($date_range);
         $data["visit_day"] = $this->visit_day($date_range);

        $data["history"]["omset"] = $this->get_history("purchase_value",$date_range);
        $data["history"]["order"] = $this->get_history("purchase",$date_range);
        $data["history"]["mql"] = $this->get_history("mql",$date_range);
        $data["history"]["wa"] = $this->get_history("wa",$date_range);

        return $data;
    }

    function get_report_value($key,$date_range = NULL)
    {
        global $app;
        $db2 = $app->db2;
       
        if($this->platform != NULL)
        {
            $db2->join("report","report.report_id = report_data.report_id");
            $db2->where("report.source",$this->platform);
        }

         if(isset($date_range)){
            $db2->where('report_data.date', Array ($date_range["start"], $date_range["end"]), 'BETWEEN');
        }

        if($this->convertion_type == "visit"){
            if($key != "impression" && $key != "spend" && $key != "lp_view"){
            $key = "v-".$key;
            }
        }


        $db2->where("report_key",$key);
        if( $x= $db2->getone("report_data","sum(report_data.report_value) as report_value")){
            
            $res = $x["report_value"];
        }
        else{
            $res = 0;
        }
        return $res;
    }

    function get_cac($date_range = NULL)
    {
        $spend = $this->get_report_value("spend",$date_range);
        $unik_purchase = $this->get_report_value("unik_purchase",$date_range);
        if($spend == 0){return 0;}
        if($unik_purchase == 0){return 0;}
        return (int)ceil($spend/$unik_purchase);
    }

    function get_ltv()
    {
        global $app;
        $db2 = $app->db2;
        $unik_purchase = $this->get_report_value("unik_purchase",NULL);
        $purchase_value = $this->get_report_value("purchase_value",NULL);

        if($purchase_value > 0 && $unik_purchase > 0)
        {
        return (int)ceil($purchase_value/$unik_purchase);
        }
        else{
            return 0;
        }
    }

    function purchase_hour($date_range)
    {
        global $app;
        $db2 = $app->db2;

        if($this->platform != NULL)
        {
            $db2->join("visitor_source","visitor_source.visitor_id = visitor.visitor_id");
            $db2->where("visitor_source.source",$this->platform);
        }

        if(isset($date_range)){
            $db2->where('visitor.visit', Array ($date_range["start"], $date_range["end"]), 'BETWEEN');
        }

        $db2->where("visitor.purchase","1");

        $db2->groupBy("hour");
       
        //if($data_purchaser = $db2->rawQuery($q))
        if($data_purchaser = $db2->get("visitor",NULL,"Hour(visitor.created) as hour, count(*) as count"))
        {

            $purchaser = array();
            foreach ($data_purchaser as $key => $value) {
               $purchaser[$value["hour"]] = $value["count"];
            }
             
            for ($i=0; $i < 24; $i++) { 
                if(!isset($purchaser[$i]))
                {
                    $purchaser[$i] = 0;
                }
            }

    
            return $purchaser;
        }
        else
        {
            return NULL;
        }
    }

    function visit_hour($date_range)
    {
        global $app;
        $db2 = $app->db2;

        if(isset($date_range)){
            $db2->where('visitor.visit', Array ($date_range["start"], $date_range["end"]), 'BETWEEN');
        }

        $db2->groupBy("HOUR(visitor.created)");
        $data_visitor = $db2->get("visitor",NULL,"Hour(visitor.created) as hour , count(*) as count");

        $visitors = array();
        foreach ($data_visitor as $key => $value) {
            $visitors[$value["hour"]] = $value["count"];
        }

         return $visitors;
    }

    function closing_rate_hour($date_range)
    {
        global $app;
        $db2 = $app->db2;

        $data_purchaser = $this->purchase_hour($date_range);
        
        if($this->platform != NULL)
        {
            $db2->join("visitor_source","visitor_source.visitor_id = visitor.visitor_id");
            $db2->where("visitor_source.source",$this->platform);
        }

        if(isset($date_range)){
            $db2->where('visitor.visit', Array ($date_range["start"], $date_range["end"]), 'BETWEEN');
        }

        $db2->groupBy("HOUR(visitor.created)");
        $data_visitor = $db2->get("visitor",NULL,"Hour(visitor.created) as hour , count(*) as count");

        $visitors = array();
        foreach ($data_visitor as $key => $value) {
            $visitors[$value["hour"]] = $value["count"];
        }
 
        $hour = array();
        for ($i=23; $i >= 0; $i--) { 
            if(isset($data_purchaser[$i]) && isset($visitors[$i])){
                $hour[(string)$i] = round($data_purchaser[$i] / $visitors[$i] * 100,2);
            }else
            {
                if(!isset($visitors[$i])){
                    $hour[(string)$i] = "no data";
                }
                elseif($visitors[$i] == 0){
                    $hour[(string)$i] = "no data";
                }elseif($visitors[$i] == ""){
                    $hour[(string)$i] = "no data";
                }
                else{
                    $hour[(string)$i] = 0;
                }
            }
            
        }

        return $hour;
    }

    function purchase_day($date_range)
    {
        global $app;
        $db2 = $app->db2;


        if($this->platform != NULL)
        {
            $db2->join("visitor_source","visitor_source.visitor_id = visitor.visitor_id");
            $db2->where("visitor_source.source",$this->platform);
        }

        if(isset($date_range)){
            $db2->where('visitor.visit', Array ($date_range["start"], $date_range["end"]), 'BETWEEN');
        }

        $db2->where("visitor.purchase","1");

        $db2->groupBy("day");

        
       // if($data_purchaser = $db2->rawQuery($q))
        if($data_purchaser = $db2->get("visitor",NULL,"DAYOFWEEK(visitor.created) as day , count(*) as count"))
        {
            $purchaser = array();
            foreach ($data_purchaser as $key => $value) {
               $purchaser[$value["day"]] = $value["count"];
            }

            return $purchaser;
        }
        else
        {
            return NULL;
        }
    }

    function visit_day($date_range)
    {
         global $app;
        $db2 = $app->db2;

        if(isset($date_range)){
            $db2->where('visitor.visit', Array ($date_range["start"], $date_range["end"]), 'BETWEEN');
        }

        $db2->groupBy("day");
        $data_visitor = $db2->get("visitor",NULL,"DAYOFWEEK(visitor.created) as day , count(*) as count");

         $visitors = array();
        foreach ($data_visitor as $key => $value) {
            $visitors[$value["day"]] = $value["count"];
        }

        return $visitors;
    }


    function closing_rate_day($date_range)
    {
        global $app;
        $db2 = $app->db2;

        $data_purchaser = $this->purchase_day($date_range);
        
        
        if($this->platform != NULL)
        {
            $db2->join("visitor_source","visitor_source.visitor_id = visitor.visitor_id");
            $db2->where("visitor_source.source",$this->platform);
        }

        if(isset($date_range)){
            $db2->where('visitor.visit', Array ($date_range["start"], $date_range["end"]), 'BETWEEN');
        }

        $db2->groupBy("day");
        $data_visitor = $db2->get("visitor",NULL,"DAYOFWEEK(visitor.created) as day , count(*) as count");

        $visitors = array();
        foreach ($data_visitor as $key => $value) {
            $visitors[$value["day"]] = $value["count"];
        }

        $day = array();
        for ($i=1; $i < 8; $i++) { 
            if(isset($data_purchaser[$i]) && isset($visitors[$i])){
                $day["$i"] = round($data_purchaser[$i] / $visitors[$i] * 100,2);
            }else
            {
                if(!isset($visitors[$i])){
                    $day["$i"] = "no data";
                }
                elseif($visitors[$i] == 0){
                    $day["$i"] = "no data";
                }elseif($visitors[$i] == ""){
                    $day["$i"] = "no data";
                }
                else{
                    $day["$i"] = 0;
                }
                
            }
            
        }



        return $day;
    }
}