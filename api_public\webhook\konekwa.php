<?php
$param = $_POST;

//
//die();

if (!isset($_GET["p"])) {echo "invalid link p";die();}

if (!isset($_GET["cs"])) {echo "invalid link c";die();}

file_put_contents('log/hooklog-konekwa-'.$_GET["cs"].'.txt', '[' . date('Y-m-d H:i:s') . "]\n", FILE_APPEND);
file_put_contents('log/hooklog-konekwa-'.$_GET["cs"].'.txt', json_encode($param), FILE_APPEND);
file_put_contents('log/hooklog-konekwa-'.$_GET["cs"].'.txt', "\n\n", FILE_APPEND);
 
$message_raw = array();
if (isset($param["raw"])) {
    $param["raw"] = urldecode($param["raw"]);
    $message_raw = json_decode($param["raw"], true);

}

if (isset($param["phone"])) {
    if (!is_numeric($param["phone"])) {
        return false;
        die();
    }
} else {
    return false;
    die();
}
if(strlen($param["phone"]) >= 16){
    return false;
    die();
}
$project_key = $_GET["p"];
$nope_cs = $_GET["cs"];

if($nope_cs == "628113608550"){
    $ch = curl_init("https://bot.gass.co.id/hook.php");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
    $response = curl_exec($ch);
    curl_close($ch);
}

global $app, $project;
$db = $app->db;

$db->where("project_key = UNHEX(?)", [$project_key]);
$project = $db->getone("project");

if ($project == null) {echo "invalid link pp";die();}

assign_child_db($project["project_id"]);

$trigger_kontak = false;
$trigger_mql = false;
$trigger_prospek = false;
$trigger_purchase = false;
$visitor_id = null;
$is_new_kontak = false;

$db2 = $app->db2;
$t = new track();
$m = new meta();
///////////////////

$new_kontak["phone"] = $param["phone"];
$new_kontak["phone_hash"] = $db2->func("UNHEX(?)", [md5($param["phone"])]);
$new_kontak["created"] = date("Y-m-d H:i:s");

if ($db2->setQueryOption('IGNORE')->insert("new_kontak", $new_kontak)) {
    $is_new_kontak = true;
    if ($param["type"] == "message_out") {
        $is_new_kontak = false;
    }
    $db2->where("phone", $param["phone"]);
    $db2->orderby("created", "desc");
    $tmp = $db2->getone("form");
    if ($tmp != null) {
        $visitor_id = $tmp["visitor_id"];
        $trigger_kontak = true;

        $update_form["cs_key"] = $db2->func("UNHEX(?)", [md5($nope_cs)]);
        $update_form["waktu_contact"] = date("Y-m-d H:i:s");

        $db2->where("visitor_id", $visitor_id);
        $db2->update("form", $update_form);

    }
}

//////////////////////////////////////////// data /////////////////////////////////////////
$tmp = json_encode($param);
$pesan = $tmp;
$phone = null;if (isset($param["phone"])) {if ($param["phone"] != "") {$phone = $param["phone"];}}
//$nope_cs = NULL;if (isset($param["nope_cs"])){if ($param["nope_cs"] != ""){$nope_cs = $param["nope_cs"];}}
$msg_type = $param["type"];
///////////////////////////////////////////////////////////////////////////////////////////////
$post_archive = [
    'act' => 'archive_add',
    'pesan' => $pesan,
    'nope_cs' => $nope_cs,
    'phone' => $phone,
    'msg_type' => $msg_type,
];
$forward_to = array("http://10.104.0.56/api.html");

$pesan = $param["message"];

///////////////////////////////////// kontak masuk
$format_id = $m->get_meta("format_id");

// Cek apakah format_id memiliki kode 0
if ($format_id["code"] == 0) {
    // Mencari ID dalam string $tmp
    preg_match("/ID \[(.*?)\]/s", $tmp, $match);
    // Jika ditemukan lebih dari satu match
    if (count($match) > 1) {
        $visitor_id = $match[1]; // Ambil visitor_id dari hasil match
        $trigger_kontak = true; // Set trigger_kontak menjadi true
    } else {
        // Jika ini adalah kontak baru, set trigger_kontak menjadi true
        if ($is_new_kontak) {
            $trigger_kontak = true;
        }
    }
} else {
    // Jika format_id tidak memiliki kode 0, bersihkan dan siapkan regex
    $format_id = clean_string($format_id["result"]["data"]);
    $format_id = preg_quote(trim($format_id));

    // Siapkan regex untuk mencocokkan visitor_id
    $format_id = "/(?<=" . $format_id . " )\S+\b/is";
    $format_id = preg_replace('/\s+/', ' ', $format_id);
    $tmp = preg_replace('/\s+/', ' ', $tmp);
    $tmp = str_replace('\n', " ", $tmp);
    
    // Mencari visitor_id menggunakan regex yang telah disiapkan
    preg_match($format_id, $tmp, $match);
    if (count($match) > 0) {
        $visitor_id = trim($match[0]); // Ambil visitor_id dari hasil match
        $trigger_kontak = true; // Set trigger_kontak menjadi true
    } else {
        // Jika tidak ditemukan, coba cari ID dalam string $tmp
        preg_match("/ID \[(.*?)\]/s", $tmp, $match);
        if (count($match) > 1) {
            $visitor_id = $match[1]; // Ambil visitor_id dari hasil match
            $trigger_kontak = true; // Set trigger_kontak menjadi true
        } else {
            // Jika ini adalah kontak baru, set trigger_kontak menjadi true
            if ($is_new_kontak) {
                $trigger_kontak = true;
            }
        }
    }
}

///////////////////////////////////// end kontak masuk /////////////////////////////////////
if ($param["type"] == "message_in") {
    //////////////////////////////////// mql //////////////////////////////////
    $data["count"] = 1;
    $data["phone"] = $param["phone"];
    $data["created"] = date("Y-m-d H:i:s");
    $data["phone_hash"] = $db2->func("UNHEX(?)", [md5($param["phone"])]);
    $db2->setQueryOption(["IGNORE"])->insert("mql", $data);
    $res = $m->get_meta("mql");
    if ($res["code"] == 1) {
        $mql_limit = $res["result"]["data"] - 1;
    }
    $db2->where("phone_hash = UNHEX(?)", [md5($param["phone"])]);
    $mql_data = $db2->getone("mql");
    $inc = true;
    if ($mql_data != null) {
        if ($mql_data["count"] == $mql_limit) {
            $trigger_mql = true;
        }
        if ($mql_data["count"] > $mql_limit) {
            $inc = false;
        }
    }
    if ($inc) {
        $data = [];
        $data_insert = [];
        $data["phone_hash"] = $db2->func("UNHEX(?)", [md5($param["phone"])]);
        $data["count"] = 1;
        $data_insert["count"] = $db2->inc(1);
        $db2->onDuplicate($data_insert);
        $db2->insert("mql", $data);
    }
}

///////////////////////////////////////// end mql ///////////////////////////////////////
if ($param["type"] == "message_out") {
    ///////////////////////// prospek
    $res = $m->get_meta("format_prospek");
    if ($res["code"] == 1) {

        $rgxFormatCheckout = clean_string($res["result"]["data"]);
        $rgxFormatCheckout = preg_quote($rgxFormatCheckout, '/');

        $strregex = str_replace('%ID%', '(\d+)', $rgxFormatCheckout);
        $rgx = '/' . $strregex . '/';
        $res = preg_match($rgx, $pesan, $matches);
        if ($res !== false && $res > 0) {
            $trigger_prospek = true;
        }
    }
    //////////////////////// end prospek

    ///////////////////////// purchase
    $value = 0;
    $meta_result = $m->get_meta("format_purchase");
    $meta_result2 = $m->get_meta("format_purchase_value");
    if ($meta_result["code"] == 1 && $meta_result2["code"] == 1) {

        $rgxFormatPurchase = clean_string($meta_result["result"]["data"]);
        $rgxFormatPurchase = preg_quote($rgxFormatPurchase, '/');
        $strregex = str_replace('%ID%', '(\d+)', $rgxFormatPurchase);
        $rgx = '/' . $strregex . '/';
        $res = preg_match($rgx, $pesan, $matches);
        if ($res !== false && $res > 0) {
            //  @file_put_contents('hooksas_purchase.txt', '[' . date('Y-m-d H:i:s') . "]\n\n" . json_encode($param)."\n\n\n\n", FILE_APPEND);
            $rgxFormatValuePurchase = clean_string($meta_result2["result"]["data"]);
            $rgxFormatValuePurchase = preg_quote($rgxFormatValuePurchase, '/');
            $strregex = str_replace('%VALUE%', '([0-9,.]*)', $rgxFormatValuePurchase);
            $rgx = '/' . $strregex . '/';
            $res2 = preg_match($rgx, $pesan, $matches);
            if ($res2 !== false && $res2 > 0) {
                $value = preg_replace('/[.,]/', '', $matches[1]);
                // Pastikan value selalu berupa integer yang valid
                $value = is_numeric($value) ? (int)$value : 0;
                $trigger_purchase = true;

                $data_order['value'] = $value;

                ////////////////////// get alamat dll

                $data_order['nama'] = null;
                $format_prov = $m->get_meta("format_purchase_nama")["result"]["data"] ?? "";
                if ($format_prov != "") {
                    $rgxFormat = preg_quote($format_prov, '/');
                    $strregex = str_replace('%NAMA%', '(.+)', $rgxFormat);
                    $rgx = '/' . $strregex . '/';
                    $res = preg_match($rgx, $pesan, $matches);

                    if ($res !== false && $res > 0) {
                        $data_order['nama'] = trim($matches[1]);
                    }
                }

                $data_order['kota'] = null;
                $format_kota = $m->get_meta("format_purchase_kota")["result"]["data"] ?? "";
                if ($format_kota != "") {
                    $rgxFormat = preg_quote($format_kota, '/');
                    $strregex = str_replace('%KOTA%', '(.+)', $rgxFormat);
                    $rgx = '/' . $strregex . '/';
                    $res = preg_match($rgx, $pesan, $matches);

                    if ($res !== false && $res > 0) {
                        $data_order['kota'] = trim($matches[1]);
                    }
                }

                $data_order['alamat'] = null;
                $format_alamat = $m->get_meta("format_purchase_alamat")["result"]["data"] ?? "";
                if ($format_alamat != "") {
                    $rgxFormat = preg_quote($format_alamat, '/');
                    $strregex = str_replace('%ALAMAT%', '(.+)', $rgxFormat);
                    $rgx = '/' . $strregex . '/';
                    $res = preg_match($rgx, $pesan, $matches);

                    if ($res !== false && $res > 0) {
                        $data_order['alamat'] = trim($matches[1]);
                    }
                }

                $data_order['provinsi'] = null;
                $format_prov = $m->get_meta("format_purchase_provinsi")["result"]["data"] ?? "";
                if ($format_prov != "") {
                    $rgxFormat = preg_quote($format_prov, '/');
                    $strregex = str_replace('%PROVINSI%', '(.+)', $rgxFormat);
                    $rgx = '/' . $strregex . '/';
                    $res = preg_match($rgx, $pesan, $matches);

                    if ($res !== false && $res > 0) {
                        $data_order['provinsi'] = trim($matches[1]);
                    }
                }

                $data_order['items'] = array();
                $pesan_line = explode("\n", $pesan);
                $format_qty = $m->get_meta("format_purchase_qty_sku")["result"]["data"] ?? "";
                if ($format_qty != "") {
                    $rgxFormat = preg_quote($format_qty, '/');
                    $strregex = str_replace('%QTY%', '(.+)', $rgxFormat);
                    $strregex = str_replace('%SKU%', '(.+)', $strregex);
                    $rgx = '/' . $strregex . '/';

                    foreach ($pesan_line as $line) {
                        $res = preg_match($rgx, $line, $matches);
                        if ($res !== false && $res > 0) {

                            $item['qty'] = trim($matches[1]);
                            $item['sku'] = trim($matches[2]);
                            array_push($data_order['items'], $item);
                        }
                    }
                }
            }
        }
    }
    //////////////////////// end purchase

}
$x = false;

$visitor = $t->get_visitor($visitor_id, $phone);
if ($visitor == false) {
    if ($phone != null) {

        if( $is_new_kontak){
            $trigger_kontak = true;
        }
        $visitor_id = $t->create_visitor($phone,$is_new_kontak);
        $tmp_visitor_id = $visitor_id;
      
        $visitor_id = convBase($visitor_id, "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        $visitor_id = str_split($visitor_id, 4);
        $visitor_id = implode(".", $visitor_id);
        $visitor = $t->get_visitor($visitor_id, $phone);

        //if($trigger_kontak == false){
        file_put_contents("log/insert-cs-".$nope_cs.".txt", '[' . date('Y-m-d H:i:s') . "]\n" . $phone."\n".$msg_type . "\n\n", FILE_APPEND);
            $t->insert_cs($tmp_visitor_id, $nope_cs, $msg_type);
        //}

        $x = $t->fbwa_personal($phone, $param);
        if($x){
            $db2->setQueryOption(array('IGNORE'))->insert("visitor_source", ["visitor_id" => $visitor_id, "source" => "meta"]);
            $visitor = $t->get_visitor($visitor_id, $phone);
        }        
    } 
}

//////////////////////// cek ctwa ///////////////////////////////////////////

if ($x) {
    $data_tmp["is_new_kontak"] = $is_new_kontak;
    $data_tmp["visitor"] = $visitor;
    $data_tmp["nope_cs"] = $nope_cs;
    $data_tmp["phone"] = $phone;
    $data_tmp = serialize($data_tmp);
    @file_put_contents('log/log_ctwa.txt', '[' . date('Y-m-d H:i:s') . "]\n" . $data_tmp."\n\n", FILE_APPEND);
    $trigger_kontak = true;

    $visitor_data = unserialize($visitor["data"]);
    
    // Upsert visitor_ctwa by phone: update adcopy_id, ctwa_clid, data; insert if not exists
    $adcopy_id = null;
    $ctwa_clid = null;
    if (is_array($visitor_data)) {
        if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
            $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];
        }
        if (isset($visitor_data["last_campaign"]["data"]["ctwaClid"])) {
            $ctwa_clid = $visitor_data["last_campaign"]["data"]["ctwaClid"];
        } elseif (isset($visitor_data["last_campaign"]["data"]["ctwa_clid"])) {
            $ctwa_clid = $visitor_data["last_campaign"]["data"]["ctwa_clid"];
        }
    }
    $data_payload = isset($visitor["data"]) ? $visitor["data"] : (is_array($visitor_data) ? serialize($visitor_data) : null);
    if (!empty($phone)) {
        $db2->where("phone", $phone);
        $existing_ctwa = $db2->getOne("visitor_ctwa");
        $row_update = [
            "adcopy_id" => $adcopy_id,
            "ctwa_clid" => $ctwa_clid,
            "data" => $data_payload,
            "phone_cs" => $nope_cs,
            "project_key" => $project_key,
            "project_id" => $project["project_id"]
        ];
        if ($existing_ctwa) {
            $db2->where("phone", $phone);
            $db2->update("visitor_ctwa", $row_update);
        } else {
            $row_insert = $row_update;
            $row_insert["visitor_id"] = $visitor["visitor_id"] ?? null;
            $row_insert["phone"] = $phone;
            $db2->insert("visitor_ctwa", $row_insert);
        }
    }    
}

if ($trigger_kontak || $trigger_mql || $trigger_prospek || $trigger_purchase) {

    $message_id = '';
    if ($param["type"] == "message_out") {
        if (isset($message_raw['mei']['messages'][0]['messageTimestamp'])) {
            $message_id = $phone . $message_raw['mei']['messages'][0]['messageTimestamp'];
        }
    } else {
        if (isset($message_raw['id'])) {
            $message_id = $message_raw['id'];
        }
    }
    if ($trigger_kontak) {
        if ($param["type"] == "message_out") {
            $msg_type = "message_out";
        } else {
            $msg_type = "message_in";
        }

        $t->lead($is_new_kontak, $visitor, $nope_cs, $phone, $message_id, $msg_type);
    }

    if ($trigger_mql) {
        $t->mql($nope_cs, $visitor, $phone, $message_id);
    }

    if ($trigger_prospek) {
        $t->prospek($nope_cs, $visitor, $phone, $message_id);
    }

    if ($trigger_purchase) {
        $t->purchase($nope_cs, $visitor, $phone, $value, $message_id, $data_order, $pesan);
    }
}


$save_history = 1;
if(file_exists('log/history/'.$project["project_id"].'/setting.text')){
    $save_history = file_get_contents('log/history/'.$project["project_id"].'/setting.text');
}
if($save_history == 1){ 
    $chatHistory = new ChatHistory($project["project_id"]);   
    if (!empty($phone) && !empty($pesan) && in_array($msg_type, ['message_in', 'message_out'])) {
        $chatHistory->save($phone, $msg_type, $pesan);
    }
}

//$x = forwarder($post_archive, $forward_to);

// $data = [];
// include("hooksas_kontak.php");
// include("hooksas_mql.php");
// include("hooksas_prospek.php");
//  include("hooksas_purchase.php");
