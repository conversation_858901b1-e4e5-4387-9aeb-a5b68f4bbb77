<?php 

class report2{

	/*
		get semua kolom + custom kolom yang ada

	*/

	function get_kolom()
	{

	}

	/*
	example
		kolom_name = CTR
		rumus = impression / lp_view (data impression dan lp_view diambil dari table report_data.report_key)
	*/
	function set_custom_kolom($kolom_name,$rumus)
	{

	}

	/////////////// get report where parent == 0 and date between and kolom apa aja

	/*	result
	-----------------------------------
		$ret["source"] = $source;
		$ret["report_id"]
		$ret["have_child"] =  true / false;
		$ret["kolom1"] = value;
		$ret["kolom2"] = value;
		$ret["kolom3"] = value;
		*/
	function get_report_overview($date_start,$date_end,$kolom,$sort_by,$sort_type)
	{
		

		
	}

	/*
		$type = "adset/campaign/adcopy/dst2 nya"
	*/
	/*	result
	-----------------------------------
		$ret["source"] = $source;
		$ret["have_child"] =  true / false;
		$ret["kolom1"] = value;
		$ret["kolom2"] = value;
		$ret["kolom3"] = value;
	*/
	function get_report_child($report_id,$date_start,$date_end,$kolom,$type,$sort_by,$sort_type)
	{

	}
}