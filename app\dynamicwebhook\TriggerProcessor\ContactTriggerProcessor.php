<?php

namespace DynamicWebhook\TriggerProcessor;

use DynamicWebhook\Abstracts\AbstractTriggerProcessor;

/**
 * Contact trigger processor
 */
class ContactTriggerProcessor extends AbstractTriggerProcessor
{
    /**
     * Get trigger type name
     */
    public function getTriggerType(): string
    {
        return 'contact';
    }

    /**
     * Check if trigger should be processed for given message type
     */
    public function shouldProcess(string $messageType): bool
    {
        // Contact trigger can be processed for both incoming and outgoing messages
        return true;
    }

    /**
     * Process specific trigger logic
     */
    protected function processSpecificTrigger(array $data, array $context): bool
    {
        $formatId = $this->getMeta("format_id");
        $input = $context['input'] ?? '';
        $isNewContact = $context['is_new_contact'] ?? false;
        
        if ($formatId["code"] == 0) {
            // Default format: look for ID [xxx] pattern
            $visitorId = $this->extractVisitorIdFromMessage($input);
            if ($visitorId) {
                $context['visitor_id'] = $visitorId;
                return true;
            } else {
                return $isNewContact;
            }
        } else {
            // Custom format
            $formatIdValue = $this->cleanString($formatId["result"]["data"]);
            $visitorId = $this->extractVisitorIdWithCustomFormat($input, $formatIdValue);
            
            if ($visitorId) {
                $context['visitor_id'] = $visitorId;
                return true;
            } else {
                // Fallback to default pattern
                $visitorId = $this->extractVisitorIdFromMessage($input);
                if ($visitorId) {
                    $context['visitor_id'] = $visitorId;
                    return true;
                } else {
                    return $isNewContact;
                }
            }
        }
    }

    /**
     * Extract visitor ID from message using default pattern
     */
    private function extractVisitorIdFromMessage(string $input): ?string
    {
        preg_match("/ID \[(.*?)\]/s", $input, $match);
        return isset($match[1]) ? $match[1] : null;
    }

    /**
     * Extract visitor ID using custom format
     */
    private function extractVisitorIdWithCustomFormat(string $input, string $formatId): ?string
    {
        $formatId = preg_quote(trim($formatId));
        $formatId = "/(?<=" . $formatId . " )\S+\b/is";
        $formatId = preg_replace('/\s+/', ' ', $formatId);
        $msg = preg_replace('/\s+/', ' ', $input);
        $msg = str_replace('\n', " ", $msg);
        
        preg_match($formatId, $msg, $match);
        return isset($match[0]) ? trim($match[0]) : null;
    }
}
