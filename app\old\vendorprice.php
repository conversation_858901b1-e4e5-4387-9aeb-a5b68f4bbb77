<?php
class vendorprice
{
	
	public function add($vendor, $negara, $type, $berat, $price, $service){
		global $keya, $c_time, $app;
		$data = null  ;
		$data["vendor_id"] = $vendor;
		$data["negara_id"] = $negara;
		$data["type"] = $type;
		$data["berat"] = $berat;
		$data["price"] = $price;
		//$data["service"] = $service;
		$id = $app->db->insert("v_vendor_price", $data);
		if ($id) {
			$res["code"] = 1;
			$res["id"] = $id;
		}else{
			$res["code"] = 0;
			$res["msg"] = "error";
		}
		return $res;  
	}
	  
	public function update($id, $vendor, $negara, $type, $berat, $price, $service){
		global $keya, $c_time, $app;
		$app->db->where('id = ?', array($id));
        $data["vendor_id"] = $vendor;
		$data["negara_id"] = $negara;
		$data["type"] = $type;
		$data["berat"] = $berat;
		$data["price"] = $price;
		//$data["service"] = $service;
        if ($app->db->update('v_vendor_price', $data)) {
            $res["code"] = 1;
			$res["msg"] = "success update";
        } else {
            $res["code"] = 0;
			$res["msg"] = "error update";
        }
		return $res;
	}
    
	public function delete($id){
    	if($id == NULL or $id == "")
    	{
    		return false;
    	}
    	global $keya, $c_time, $app;
    	$app->db->where("id = ?",array($id));
    	$res = $app->db->delete("v_vendor_price");
    	return $res;
    }
	  
	public function get(){
    	global $keya, $c_time, $app;
		$app->db->join("c_country r", "g.negara_id=r.id", "LEFT");
		$app->db->join("v_vendor p", "g.vendor_id=p.id", "LEFT");
		$app->db->orderBy("g.berat", "asc");
		$res = $app->db->get ("v_vendor_price g", array(0, 10), "g.*, p.nama as vendor, p.service, r.name as negara");
		$pos = array();$i = 0;
		foreach($res as $r){
			$pos[$i]["id"] = $r['id'];
			$pos[$i]["vendor"] = $r['vendor'];
			$pos[$i]["negara"] = $r['negara'];
			$pos[$i]["vendor_id"] = $r['vendor_id'];
			$pos[$i]["negara_id"] = $r['negara_id'];
			$pos[$i]["type"] = $r['type'];
			$pos[$i]["berat"] = $r['berat'];
			$pos[$i]["price"] = $r['price'];
			$pos[$i]["service"] = $r['service'];
			$i++;
		}
    	return $pos;
    }
}