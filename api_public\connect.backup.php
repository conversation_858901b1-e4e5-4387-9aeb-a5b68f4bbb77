<?php

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
use Google\Auth\CredentialsLoader;
use Google\Auth\OAuth2;
use Psr\Http\Message\ServerRequestInterface;
use React\EventLoop\Loop;
use React\Http\HttpServer;
use React\Http\Message\Response;
use React\Socket\SocketServer;

$clientId = '578522907736-o86tuu7pt0cgvplme2chrus2t6qrstvf.apps.googleusercontent.com';
$clientSecret = '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';
$developerToken = '-vapQDeqr2dKG0IZ5RCg7Q';
if(isset($_GET['code'])){
    $post['code'] = $_GET['code'];
    $post['client_secret'] = $clientSecret;
    $post['client_id'] = $clientId;
    $post['redirect_uri'] = 'https://new.gass.co.id/connect.html';
    $post['grant_type'] = 'authorization_code';
    $res = post_x_contents($post, 'https://oauth2.googleapis.com/token');  
    print_r($res);
    if(isset($res['access_token'])){
        // Conversion data
        $customer_id = 'YOUR_CUSTOMER_ID';
        $conversionActionId = 'YOUR_CONVERSION_ACTION_ID'; // Replace with your specific conversion action ID
        $conversionValue = 10.0; // The conversion value you want to track

        // API endpoint
        $apiEndpoint = 'https://www.googleapis.com/googleads/v8/customers/'.$customer_id.'/conversions';

        // API request data
        $data = [
            'conversions' => [
                [
                    'conversion_action' => $conversionActionId,
                    'value' => $conversionValue,
                ]
            ],
        ];

        // Set cURL options
        $ch = curl_init($apiEndpoint);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $res['access_token'],
        ]);

        // Execute the cURL request
        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            echo 'Error: ' . curl_error($ch);
        }

        // Close the cURL session
        curl_close($ch);

        // Process the response
        if ($response) {
            $responseData = json_decode($response, true);
            // Handle the response data as needed
            var_dump($responseData);
        }

    }
}else{
    header("Location: https://accounts.google.com/o/oauth2/auth?client_id=".$clientId."&redirect_uri=https://c.gass.co.id/connect2.html&scope=https://www.googleapis.com/auth/adwords&response_type=code");
}


die();