<?php 
class qontak {

public function add_cs($integration_id, $nope) {
    global $app;
    $db2                     = $app->db2;
    $integration_id_key                = sha1($integration_id);
    $nope                    = preg_replace("/[^0-9]/", "", $nope);
    $data["integration_id"]   = $integration_id;
    $data["integration_id_key"]  = $db2->func("UNHEX(?)", [$integration_id_key]);
    $data["cs_nope"] = $nope;

    $db2->setQueryOption(Array('LOW_PRIORITY', 'IGNORE'))->insert("qontak_waba", $data);
}

}

?>