<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/services/recommendation_service.proto

namespace Google\Ads\GoogleAds\V18\Services\ApplyRecommendationOperation;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Parameters to use when applying keyword recommendation.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.services.ApplyRecommendationOperation.KeywordParameters</code>
 */
class KeywordParameters extends \Google\Protobuf\Internal\Message
{
    /**
     * The ad group resource to add keyword to. This is a required field.
     *
     * Generated from protobuf field <code>optional string ad_group = 4;</code>
     */
    protected $ad_group = null;
    /**
     * The match type of the keyword. This is a required field.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.KeywordMatchTypeEnum.KeywordMatchType match_type = 2;</code>
     */
    protected $match_type = 0;
    /**
     * Optional, CPC bid to set for the keyword. If not set, keyword will use
     * bid based on bidding strategy used by target ad group.
     *
     * Generated from protobuf field <code>optional int64 cpc_bid_micros = 5;</code>
     */
    protected $cpc_bid_micros = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $ad_group
     *           The ad group resource to add keyword to. This is a required field.
     *     @type int $match_type
     *           The match type of the keyword. This is a required field.
     *     @type int|string $cpc_bid_micros
     *           Optional, CPC bid to set for the keyword. If not set, keyword will use
     *           bid based on bidding strategy used by target ad group.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Services\RecommendationService::initOnce();
        parent::__construct($data);
    }

    /**
     * The ad group resource to add keyword to. This is a required field.
     *
     * Generated from protobuf field <code>optional string ad_group = 4;</code>
     * @return string
     */
    public function getAdGroup()
    {
        return isset($this->ad_group) ? $this->ad_group : '';
    }

    public function hasAdGroup()
    {
        return isset($this->ad_group);
    }

    public function clearAdGroup()
    {
        unset($this->ad_group);
    }

    /**
     * The ad group resource to add keyword to. This is a required field.
     *
     * Generated from protobuf field <code>optional string ad_group = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setAdGroup($var)
    {
        GPBUtil::checkString($var, True);
        $this->ad_group = $var;

        return $this;
    }

    /**
     * The match type of the keyword. This is a required field.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.KeywordMatchTypeEnum.KeywordMatchType match_type = 2;</code>
     * @return int
     */
    public function getMatchType()
    {
        return $this->match_type;
    }

    /**
     * The match type of the keyword. This is a required field.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.KeywordMatchTypeEnum.KeywordMatchType match_type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setMatchType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\KeywordMatchTypeEnum\KeywordMatchType::class);
        $this->match_type = $var;

        return $this;
    }

    /**
     * Optional, CPC bid to set for the keyword. If not set, keyword will use
     * bid based on bidding strategy used by target ad group.
     *
     * Generated from protobuf field <code>optional int64 cpc_bid_micros = 5;</code>
     * @return int|string
     */
    public function getCpcBidMicros()
    {
        return isset($this->cpc_bid_micros) ? $this->cpc_bid_micros : 0;
    }

    public function hasCpcBidMicros()
    {
        return isset($this->cpc_bid_micros);
    }

    public function clearCpcBidMicros()
    {
        unset($this->cpc_bid_micros);
    }

    /**
     * Optional, CPC bid to set for the keyword. If not set, keyword will use
     * bid based on bidding strategy used by target ad group.
     *
     * Generated from protobuf field <code>optional int64 cpc_bid_micros = 5;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCpcBidMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->cpc_bid_micros = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(KeywordParameters::class, \Google\Ads\GoogleAds\V18\Services\ApplyRecommendationOperation_KeywordParameters::class);

