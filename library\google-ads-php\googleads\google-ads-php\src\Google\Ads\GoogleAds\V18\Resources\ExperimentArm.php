<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/resources/experiment_arm.proto

namespace Google\Ads\GoogleAds\V18\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A Google ads experiment for users to experiment changes on multiple
 * campaigns, compare the performance, and apply the effective changes.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.resources.ExperimentArm</code>
 */
class ExperimentArm extends \Google\Protobuf\Internal\Message
{
    /**
     * Immutable. The resource name of the experiment arm.
     * Experiment arm resource names have the form:
     * `customers/{customer_id}/experimentArms/{TrialArm.trial_id}~{TrialArm.trial_arm_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     */
    protected $resource_name = '';
    /**
     * Immutable. The experiment to which the ExperimentArm belongs.
     *
     * Generated from protobuf field <code>string experiment = 8 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     */
    protected $experiment = '';
    /**
     * Required. The name of the experiment arm. It must have a minimum length of
     * 1 and maximum length of 1024. It must be unique under an experiment.
     *
     * Generated from protobuf field <code>string name = 3 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    protected $name = '';
    /**
     * Whether this arm is a control arm. A control arm is the arm against
     * which the other arms are compared.
     *
     * Generated from protobuf field <code>bool control = 4;</code>
     */
    protected $control = false;
    /**
     * Traffic split of the trial arm. The value should be between 1 and 100
     * and must total 100 between the two trial arms.
     *
     * Generated from protobuf field <code>int64 traffic_split = 5;</code>
     */
    protected $traffic_split = 0;
    /**
     * List of campaigns in the trial arm. The max length is one.
     *
     * Generated from protobuf field <code>repeated string campaigns = 6 [(.google.api.resource_reference) = {</code>
     */
    private $campaigns;
    /**
     * Output only. The in design campaigns in the treatment experiment arm.
     *
     * Generated from protobuf field <code>repeated string in_design_campaigns = 7 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     */
    private $in_design_campaigns;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $resource_name
     *           Immutable. The resource name of the experiment arm.
     *           Experiment arm resource names have the form:
     *           `customers/{customer_id}/experimentArms/{TrialArm.trial_id}~{TrialArm.trial_arm_id}`
     *     @type string $experiment
     *           Immutable. The experiment to which the ExperimentArm belongs.
     *     @type string $name
     *           Required. The name of the experiment arm. It must have a minimum length of
     *           1 and maximum length of 1024. It must be unique under an experiment.
     *     @type bool $control
     *           Whether this arm is a control arm. A control arm is the arm against
     *           which the other arms are compared.
     *     @type int|string $traffic_split
     *           Traffic split of the trial arm. The value should be between 1 and 100
     *           and must total 100 between the two trial arms.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $campaigns
     *           List of campaigns in the trial arm. The max length is one.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $in_design_campaigns
     *           Output only. The in design campaigns in the treatment experiment arm.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Resources\ExperimentArm::initOnce();
        parent::__construct($data);
    }

    /**
     * Immutable. The resource name of the experiment arm.
     * Experiment arm resource names have the form:
     * `customers/{customer_id}/experimentArms/{TrialArm.trial_id}~{TrialArm.trial_arm_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getResourceName()
    {
        return $this->resource_name;
    }

    /**
     * Immutable. The resource name of the experiment arm.
     * Experiment arm resource names have the form:
     * `customers/{customer_id}/experimentArms/{TrialArm.trial_id}~{TrialArm.trial_arm_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setResourceName($var)
    {
        GPBUtil::checkString($var, True);
        $this->resource_name = $var;

        return $this;
    }

    /**
     * Immutable. The experiment to which the ExperimentArm belongs.
     *
     * Generated from protobuf field <code>string experiment = 8 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getExperiment()
    {
        return $this->experiment;
    }

    /**
     * Immutable. The experiment to which the ExperimentArm belongs.
     *
     * Generated from protobuf field <code>string experiment = 8 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setExperiment($var)
    {
        GPBUtil::checkString($var, True);
        $this->experiment = $var;

        return $this;
    }

    /**
     * Required. The name of the experiment arm. It must have a minimum length of
     * 1 and maximum length of 1024. It must be unique under an experiment.
     *
     * Generated from protobuf field <code>string name = 3 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Required. The name of the experiment arm. It must have a minimum length of
     * 1 and maximum length of 1024. It must be unique under an experiment.
     *
     * Generated from protobuf field <code>string name = 3 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * Whether this arm is a control arm. A control arm is the arm against
     * which the other arms are compared.
     *
     * Generated from protobuf field <code>bool control = 4;</code>
     * @return bool
     */
    public function getControl()
    {
        return $this->control;
    }

    /**
     * Whether this arm is a control arm. A control arm is the arm against
     * which the other arms are compared.
     *
     * Generated from protobuf field <code>bool control = 4;</code>
     * @param bool $var
     * @return $this
     */
    public function setControl($var)
    {
        GPBUtil::checkBool($var);
        $this->control = $var;

        return $this;
    }

    /**
     * Traffic split of the trial arm. The value should be between 1 and 100
     * and must total 100 between the two trial arms.
     *
     * Generated from protobuf field <code>int64 traffic_split = 5;</code>
     * @return int|string
     */
    public function getTrafficSplit()
    {
        return $this->traffic_split;
    }

    /**
     * Traffic split of the trial arm. The value should be between 1 and 100
     * and must total 100 between the two trial arms.
     *
     * Generated from protobuf field <code>int64 traffic_split = 5;</code>
     * @param int|string $var
     * @return $this
     */
    public function setTrafficSplit($var)
    {
        GPBUtil::checkInt64($var);
        $this->traffic_split = $var;

        return $this;
    }

    /**
     * List of campaigns in the trial arm. The max length is one.
     *
     * Generated from protobuf field <code>repeated string campaigns = 6 [(.google.api.resource_reference) = {</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getCampaigns()
    {
        return $this->campaigns;
    }

    /**
     * List of campaigns in the trial arm. The max length is one.
     *
     * Generated from protobuf field <code>repeated string campaigns = 6 [(.google.api.resource_reference) = {</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setCampaigns($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->campaigns = $arr;

        return $this;
    }

    /**
     * Output only. The in design campaigns in the treatment experiment arm.
     *
     * Generated from protobuf field <code>repeated string in_design_campaigns = 7 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getInDesignCampaigns()
    {
        return $this->in_design_campaigns;
    }

    /**
     * Output only. The in design campaigns in the treatment experiment arm.
     *
     * Generated from protobuf field <code>repeated string in_design_campaigns = 7 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setInDesignCampaigns($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->in_design_campaigns = $arr;

        return $this;
    }

}

