<?php
 
function update($param){
    
    //file_put_contents("log/spend.txt",json_encode($param));die();
    
    
    //$param = file_get_contents("log/spend.txt");
    
   //$param = json_decode($param,true);
    $param["data"] = json_decode($param["data"],true);
    extract($param);
    if(isset($project_id)){
        assign_child_db($project_id);
        
        $r = new report();
        //$source = "meta";
        if($source == "google"){
            file_put_contents("log/spend-google.txt", json_encode($param));
        }
        if($source == "meta"){
            file_put_contents("log/spend-meta.txt", json_encode($param));
        }
        $data_campaign = $data;
        
        foreach ($data_campaign as $key => $value){
                $cid = $value["campaign_id"];
                $adset_id = $value["adset_id"];
                $ad_id = $value["ad_id"];
                $campaigns[$cid]["name"] = $value["campaign_name"];
                $campaigns[$cid]["adset"][$adset_id]["name"] = $value["adset_name"];
                $campaigns[$cid]["adset"][$adset_id]["adcopy"][$ad_id]["name"] = $value["ad_name"];
            }
            foreach ($campaigns as $key => $campaign){
                $campaign_id = $r->add_report_kolom($source, $campaign["name"], "campaign", $key, null, true) ["data"];
                if (count($campaign["adset"]) > 0){
                    if ($campaign_id != NULL){
                        foreach ($campaign["adset"] as $key2 => $adset){
                            $adset_id = $r->add_report_kolom($source, $adset["name"], "adset", $key2, $campaign_id, true) ["data"];
                            if ($adset_id != NULL)
                            {

                                foreach ($adset["adcopy"] as $key3 => $adcopy)
                                {
                                    $adcopy_id = $r->add_report_kolom($source, $adcopy["name"], "adcopy", $key3, $adset_id, true) ["data"];
                                }
                            }
                        }
                    }
                }
            }
            foreach ($data_campaign as $key => $data_campaign_child){
                $tmp = [];
                $date = $data_campaign_child["date_start"];
                foreach ($data_campaign_child as $key => $value){
                    if ($key == "spend" || $key == "impressions") {
                        $tmp[$key] = $value;
                    }elseif ($key == "outbound_clicks"){
                        $tmp[$key] = (int)$value[0]["value"];
                    }
                }

                foreach ($tmp as $key2 => $value2){
                    $report_id = $r->get_report_id($data_campaign_child["ad_id"]);
                    if ($report_id){
                        $r->update_report_data($report_id, $key2, $value2, $date);
                    }
                }
            }
    }
    return 'update report spend success project_id : '.$project_id;
}


function update_new_old($param) {
    $param["data"] = json_decode($param["data"], true);
    extract($param);
    
    if(isset($project_id)) {
        assign_child_db($project_id);
        $r = new report();
        //$source = "meta";
        if($source == "google"){
            file_put_contents("log/spend-google.txt", json_encode($param));
        }
        if($source == "meta"){
            file_put_contents("log/spend-meta.txt", json_encode($param));
        }
        // Process campaign structure
        $campaigns = [];
        foreach ($data as $value) {
            $campaigns[$value["campaign_id"]]["name"] = $value["campaign_name"];
            $campaigns[$value["campaign_id"]]["adset"][$value["adset_id"]]["name"] = $value["adset_name"];
            $campaigns[$value["campaign_id"]]["adset"][$value["adset_id"]]["adcopy"][$value["ad_id"]]["name"] = $value["ad_name"];
        }

        // Save structure and update metrics
        foreach ($campaigns as $cid => $campaign) {
            $campaign_id = $r->add_report_kolom($source, $campaign["name"], "campaign", $cid, null, true)["data"];
            if ($campaign_id) {
                foreach ($campaign["adset"] as $aid => $adset) {
                    $adset_id = $r->add_report_kolom($source, $adset["name"], "adset", $aid, $campaign_id, true)["data"];
                    if ($adset_id) {
                        foreach ($adset["adcopy"] as $adid => $adcopy) {
                            $r->add_report_kolom($source, $adcopy["name"], "adcopy", $adid, $adset_id, true);
                        }
                    }
                }
            }
        }

        // Update metrics
        foreach ($data as $item) {
            $report_id = $r->get_report_id($item["ad_id"]);
            if ($report_id) {
                $r->update_report_data($report_id, "spend", $item["spend"], $item["date_start"]);
                $r->update_report_data($report_id, "impressions", $item["impressions"], $item["date_start"]);
                $r->update_report_data($report_id, "outbound_clicks", $item["outbound_clicks"][0]["value"], $item["date_start"]);
            }
        }
    }
    
    return 'update report spend success project_id : ' . $project_id;
}

function update_new($param) {
    $msg = [];
    try {
        if (empty($param["data"]) || $param["data"] === null  || $param["data"] == "null" || $param["data"] == "undefined") {
            $msg = [
                'status' => 'Error',
                'message' => "Data parameter is required and cannot be null ".$param["data"]
            ];
            //throw new Exception("Data parameter is required and cannot be null ".$param["data"]);
        }

        if (is_string($param["data"])) {
            $dd = $param["data"];
            $param["data"] = json_decode($param["data"], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $msg = [
                    'status' => 'Error',
                    'message' => "Invalid JSON data format".$dd
                ];
                //throw new Exception("Invalid JSON data format");
            }
        }
        
        extract($param);
        
        if(!isset($project_id)) {
            $msg = [
                'status' => 'Error',
                'message' => "Project ID is required"
            ];
            //throw new Exception("Project ID is required");
        }

        if(empty($data)) {
            $msg = [
                'status' => 'Error',
                'message' => "No campaign data available to process"
            ];
            //throw new Exception("No campaign data available to process");
        }

        assign_child_db($project_id);
        $r = new report();

        if($source == "google"){
            file_put_contents("log/spend-google-".$project_id.".txt", json_encode($param));
        }
        if($source == "meta"){
            file_put_contents("log/spend-meta".$project_id.".txt", json_encode($param));
        }

        if(!isset($msg['status'])){
            // Process campaign structure
            $campaigns = [];
            foreach ($data as $value) {
                $campaigns[$value["campaign_id"]]["name"] = $value["campaign_name"];
                $campaigns[$value["campaign_id"]]["adset"][$value["adset_id"]]["name"] = $value["adset_name"];
                $campaigns[$value["campaign_id"]]["adset"][$value["adset_id"]]["adcopy"][$value["ad_id"]]["name"] = $value["ad_name"];
            }

            // Save structure and update metrics
            foreach ($campaigns as $cid => $campaign) {
                $campaign_id = $r->add_report_kolom($source, $campaign["name"], "campaign", $cid, null, true)["data"];
                if ($campaign_id) {
                    foreach ($campaign["adset"] as $aid => $adset) {
                        $adset_id = $r->add_report_kolom($source, $adset["name"], "adset", $aid, $campaign_id, true)["data"];
                        if ($adset_id) {
                            foreach ($adset["adcopy"] as $adid => $adcopy) {
                                $r->add_report_kolom($source, $adcopy["name"], "adcopy", $adid, $adset_id, true);
                            }
                        }
                    }
                }
            }

            // Update metrics
            foreach ($data as $item) {
                $report_id = $r->get_report_id($item["ad_id"]);
                if ($report_id) {
                    $r->update_report_data($report_id, "spend", $item["spend"], $item["date_start"]);
                    $r->update_report_data($report_id, "impressions", $item["impressions"], $item["date_start"]);
                    $r->update_report_data($report_id, "outbound_clicks", $item["outbound_clicks"][0]["value"], $item["date_start"]);
                }
            }
        }
        

            // grab stat data
        // $pos["act"] = 'bill_get_project_user';
        // $pos["project_id"] = $project_id;
        // $user = json_decode(post_x_contents($pos, 'http://10.104.0.27/api.html'), true);
        // if(isset($user['setting_bill'])){
        //     $waktu = $user['data']['project_stat_grab_last'];
        //     $inputDate = new DateTime($waktu);
        //     $yesterday = new DateTime('yesterday');
        //     if($inputDate->format('Y-m-d') < $yesterday->format('Y-m-d')){
        //         $start = $inputDate->format('Y-m-d');
        //         $inputDate->modify('+1 day');
        //         $end = $inputDate->format('Y-m-d');
        //         $res = get_usage_range_new($project_id, $start, $end, $user);
                
        //         $res["act"] = 'internal_update_grab_stat';
        //         $res["project_id"] = $project_id;
        //         $res["start"] = $start;
        //         $res["end"] = $end;
        //         $rex = post_x_contents($res, 'http://10.104.0.27/api.html');
        //         print_r($rex);
        //     }else if($inputDate->format('Y-m-d') == $yesterday->format('Y-m-d')){

        //     }
        // }

        $msg = [
            'status' => 'success',
            'message' => 'Update report spend success project_id : ' . $project_id,
            //'data' => $data
        ];

    } catch (Exception $e) {
        file_put_contents("log/spend-error.txt", json_encode($param));
        $msg = [
            'status' => 'error',
            'message' => $e->getMessage(),
            'project_id' => isset($param["project_id"]) ? $param["project_id"] : null,
            'trace' => $e->getTraceAsString()
        ];
    }
    return $msg;
}


function get_usage_range_new($project_id, $start, $end, $user){
    global $app;
    assign_child_db($project_id);
    $db = $app->db2;
    $last_bill =  $start;  
    $yesterday =  $end;
    $db->where("DATE(created) BETWEEN ? AND ?", array($last_bill, $yesterday));  
    $site = $db->get("visitor", NULL, "site, DATE(created) as waktu");
    $sites = []; 
    foreach ($site as $item) {
        $sit = $item["site"];
        $sit = str_replace("www.","", $sit);
        if($sit!='' && $sit != 'b-cdn.net'){   ///// disable ctwa
            $waktu = $item["waktu"];
            if($sit==''){
                $sit = 'ctwa';
            }
            if (!isset($sites[$sit]['date'][$waktu])) {
                $sites[$sit]['date'][$waktu] = 0;
                $sites[$sit]['total']++;
            }
            $sites[$sit]['date'][$waktu]++;        
        }      
    }
    $total_day_site = 0;$total_day_ctwa = 0;
    foreach ($sites as $k => $v) {
        if($k != 'ctwa'){
            $total_day_site = $v['total'] + $total_day_site;
        }else{
            $total_day_ctwa = $v['total'] + $total_day_ctwa;
        }            
    }
    $total_bill_site = $user['setting_bill']['site'] * $total_day_site;
    $total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_day_ctwa;
    // hitung bill cs
    $db->where("DATE(tanggal) BETWEEN ? AND ?", array($last_bill, $yesterday));  
    $cs = $db->get("cs_log", NULL, "phone, DATE(tanggal) as waktu, impression, contact, mql, prospek, purchase, value");
    $nocs = []; 
    foreach ($cs as $item) {
        $c = $item["phone"];
        $waktu = $item["waktu"];
        if (!isset($nocs[$c]['date'][$waktu])) {
            $nocs[$c]['date'][$waktu] = 0;
            $nocs[$c]['total']++;
        }
        $nocs[$c]['date'][$waktu]++;        
    }
    $total_day_cs = 0;
    foreach ($nocs as $k => $v) {
        $total_day_cs = $v['total'] + $total_day_cs;
    }
    $total_bill_cs = $user['setting_bill']['cs'] * $total_day_cs;
    // hitung cta
    $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'unik_cta'", array($last_bill, $yesterday));  
    $db->orderBy("date","Asc");
    $cta = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
    $tcta = []; $total_click_cta = 0;$tcta_date = [];
    $last_cta = ''; $last_cta_value = 0;
    foreach ($cta as $item) {
        $c = $item["report_id"];
        $waktu = $item["waktu"];
        $last_cta = $waktu;
        if (!isset($tcta[$c]['date'][$waktu])) {
            $tcta[$c]['date'][$waktu] = 0;
            $tcta[$c]['total']++;
        }
        if (!isset($tcta_date[$waktu])) {
            $tcta_date[$waktu] = 0;
        }
        $tcta_date[$waktu] += $item["report_value"]; 
        $tcta[$c]['date'][$waktu] += $item["report_value"];    
        $total_click_cta += $item["report_value"];  
        $last_cta_value = $tcta[$c]['date'][$waktu];
    }
    $total_day_cta = 0;
    foreach ($tcta as $k => $v) {
        $total_day_cta = $v['total'] + $total_day_cta;
    }

    // hitung lp_view
    $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'lp_view'", array($last_bill, $yesterday));  
    $lp_view = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
    $tlp_view = []; $total_lp_view = 0;$tlp_view_date = [];
    foreach ($lp_view as $item) {
        $c = $item["report_id"];
        $waktu = $item["waktu"];
        if (!isset($tlp_view[$c]['date'][$waktu])) {
            $tlp_view[$c]['date'][$waktu] = 0;
            $tlp_view[$c]['total']++;
        }
        if (!isset($tlp_view_date[$waktu])) {
            $tlp_view_date[$waktu] = 0;
        }
        $tlp_view_date[$waktu] += $item["report_value"];  
        $tlp_view[$c]['date'][$waktu] += $item["report_value"];  
        $total_lp_view += $item["report_value"];  
    }
    $total_day_lp_view = 0;
    foreach ($tlp_view as $k => $v) {
        $total_day_lp_view= $v['total'] + $total_day_lp_view;
    }

    $total_bill_cta = $user['setting_bill']['lead'] * $total_click_cta;
    $total['total_bill_site'] = $total_bill_site;
    $total['total_bill_ctwa'] = $total_bill_ctwa;
    $total['total_bill_cs'] = $total_bill_cs;
    $total['total_bill_cta'] = $total_bill_cta;
    $total['total_day_site'] = $total_day_site;
    $total['total_day_ctwa'] = $total_day_ctwa;
    $total['total_day_cs'] = $total_day_cs;
    $total['total_day_cta'] = $total_day_cta;
    $total['total_click_cta'] = $total_click_cta;
    $total['total'] = $total_bill_ctwa + $total_bill_site + $total_bill_cs + $total_bill_cta;
    $alert_nominal = $user['data']['billing_alert'] - 5000;
    $create_bill = false;
    // cek minimal billing
    $msg['code']=1;        
    //$msg['data']['user'] = $user;

    $msg['data']['tcta_date'] = $tcta_date;
    $msg['data']['tlp_view_date'] = $tlp_view_date;

    $msg['data']['total'] = $total;
    $msg['last_cta'] = $last_cta;
    $msg['last_cta_value'] = $last_cta_value;

    return $msg;
    
}
