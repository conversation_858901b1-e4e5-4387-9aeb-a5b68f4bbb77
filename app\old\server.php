<?php

class server{
    public function get_available(){
        global $app;
        $db = $app->db;
        $db->where("jumlah < max and status = ?", array(1));
        $db->orderBy('jumlah',"Desc");
        $serv = $db->getone("x_server");
        if($db->count > 0){
            return $serv['address'];
        }else{
            return false;
        }
    }
    
    public function update_jml($server, $status = 1){
        global $app;
        $db = $app->db;
        if($status==1){
            $db->rawQuery("UPDATE x_server SET jumlah = jumlah + 1 WHERE address = ?", array($server));
        }else{
            $db->rawQuery("UPDATE x_server SET jumlah = jumlah - 1 WHERE address = ?", array($server));
        }        
        return true;
    }
}
