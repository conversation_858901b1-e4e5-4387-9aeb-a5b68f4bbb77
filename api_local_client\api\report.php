<?php





///////////////// for custom


function get_detail($param)
{
    $rules = [
        "project_key" => "required",
        "id" => "required",
    ];

    validate_param($rules, $param);

    extract($param);

    global $app;
    $db = $app->db;
   

    $db->where("project_key = UNHEX(?)",[$project_key]);
    $project = $db->getone("project");

    if($project == NULL){
        $msg["code"] = 0;
        $msg["message"] = "project key not found";
        return $msg;
    }
    assign_child_db($project['project_id']);
    $db2 = $app->db2;

    $db2->where("report_group_id",$id);
    $res = $db2->getone("report_group","report_group_name,logo_url");

    if($res == NULL)
    {
        $msg['code'] = 0;
        $msg["result"]['msg'] = 'Error report not found';
    }
    else{
        $msg['code'] = 1;
        $msg["result"]['msg'] = 'Sukses';
        $msg["result"]['data'] = $res;
    }

    return $msg;
}

function get_custom($param)
{
    $rules = [
        "project_key" => "required",
        "id" => "required",
        'column'      => 'required',
        'start'      => 'required',
        'end'      => 'required',
        'type' => 'required',
    ];
    validate_param($rules, $param);

    extract($param);

    global $app;
    $db = $app->db;

    $db->where("project_key = UNHEX(?)",[$project_key]);
    $project = $db->getone("project");

    if($project == NULL){
        $msg["code"] = 0;
        $msg["message"] = "project key not found";
        return $msg;
    }
    assign_child_db($project['project_id']);

    $column = explode(",",trim($column));
    $column = array_filter($column);


    $r = new report();
    if(!isset($parent_id))
    {
        $parent_id = 0;
    }
    if(!isset($source))
    {
        $source = "";
    }
    if(isset($platform))
    {
        $source = $platform;
    }

    if(isset($convertion_type))
    {
        $param_convertion_type = $convertion_type;
    }else{
        $param_convertion_type = "realtime";
    }

    if($type != "group" || $type != "custom")
    {
        $parent_id = $id;
        $id = "";
    }
   
    $sort_by =NULL;
    $sort_type= NULL;
    $platform = NULL;
    if(isset($param['sort_type'])){$sort_type = $param['sort_type'];}
    if(isset($param['sort_by'])){$sort_by = $param['sort_by'];}

    $data = $r->get_overview_custom($id,$param_convertion_type,$column,$start,$end,$type,$source,$parent_id,$sort_by,$sort_type);

    if($data)
    {
        $msg['code'] = 1;
        $msg["result"]['msg'] = 'Sukses';
        $msg["result"]['data'] = $data;
    }
    else{
        $msg['code'] = 0;
        $msg["result"]['msg'] = 'Error';
    }
    
    return $msg;
}





///////////// end for custom













function get_graph_history($param){
    global $app;
    $rules = array(
        'option'      => 'required',
        'start'      => 'required',
        'end'      => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $platform = NULL;
    if(isset($param["platform"])){$platform = $param["platform"];}

    if(isset($convertion_type))
    {
        $param_convertion_type = $convertion_type;
    }else{
        $param_convertion_type = "realtime";
    }

    $r = new report_purchase($platform,$convertion_type);
    $data = $r->get_graph_history($option,$start,$end);

    if($data)
    {
        $msg['code'] = 1;
        $msg["result"]['msg'] = 'Sukses';
        $msg["result"]['data'] = $data;
    }
    else{
        $msg['code'] = 0;
        $msg["result"]['msg'] = 'Error';
    }
    
    return $msg;
}

function get_graph_option()
{
    
    $ret =  [    
    "Page View",
    "CTA",
    "CTA Rate",
    "Wa",
    "WA Rate",
    "Cost/WA",
    "MQL",
    "MQL Rate",
    "Cost/MQL",
    "Prospek",
    "Prospek Rate",
    "Cost/Prospek",
    "Purchase",
    "Purchase Value",
    "Unique Purchase",
    "Unique Purchase Rate",
    "Cost/Unique Purchase",
    "ROAS"
    ];
    
    $msg['code'] = 1;
    $msg["result"]['msg'] = 'Sukses';
    $msg["result"]['data'] = $ret;

    return $msg;
    
}

function get_buying_window($param)
{
     extract($param);
    assign_child_db($project_id);
    
    $platform = NULL;
    if(isset($param["platform"])){$platform = $param["platform"];}

    $r = new report_purchase($platform);
    

    $msg['code'] = 1;
    $msg["result"]['msg'] = 'Sukses';
    $msg["result"]['data'] = $r->get_buying_window();
    return $msg;
}

function get_ltv($param)
{
     extract($param);
    assign_child_db($project_id);
    
    $platform = NULL;
    if(isset($param["platform"])){$platform = $param["platform"];}

    $r = new report_purchase($platform);

    $msg['code'] = 1;
    $msg["result"]['msg'] = 'Sukses';
    $msg["result"]['data'] = $r->get_ltv();
    return $msg;
}

function get_recent_order($param)
{
     extract($param);
    assign_child_db($project_id);

    $platform = NULL;
    if(isset($param["platform"])){$platform = $param["platform"];}

    $page = NULL;
    if(isset($param["page"])){$page = $param["page"];}

    $r = new report_purchase($platform);
   
    $msg['code'] = 1;
    $msg["result"]['msg'] = 'Sukses';
    $msg["result"]['data'] = $r->get_recent_order($page);
    return $msg;
}

function get_available_platform($param)
{

    extract($param);
    assign_child_db($project_id);

    $rp = new report_purchase(NULL);
    $msg = $rp->get_available_platform();

    return $msg;
}

function get_purchase_overview($param){
    global $app;
    $rules = array(
        'token'      => 'required', 
        'start'      => 'required',
        'end'      => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $platform = NULL;
    if(isset($param["platform"])){$platform = $param["platform"];}

    if(isset($convertion_type))
    {
        $param_convertion_type = $convertion_type;
    }else{
        $param_convertion_type = "realtime";
    }

    $r = new report_purchase($platform,$param_convertion_type);
    $data = $r->get_overview($start,$end);

    if($data)
    {
        $msg['code'] = 1;
        $msg["result"]['msg'] = 'Sukses';
        $msg["result"]['data'] = $data;
    }
    else{
        $msg['code'] = 0;
        $msg["result"]['msg'] = 'Error';
    }
    
    return $msg;
}

function get_overview($param){
    global $app;
    $rules = array(
        'token'      => 'required', 
        'column'      => 'required',
        'start'      => 'required',
        'end'      => 'required',
        'type' => 'required',
        
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $column = explode(",",trim($column));
    $column = array_filter($column);

    $r = new report();
    if(!isset($parent_id))
    {
        $parent_id = 0;
    }
    if(!isset($source))
    {
        $source = "";
    }
    if(isset($platform))
    {
        $source = $platform;
    }

    if(isset($convertion_type))
    {
        $param_convertion_type = $convertion_type;
    }else{
        $param_convertion_type = "realtime";
    }


    $sort_by =NULL;
    $sort_type= NULL;
    $platform = NULL;
    if(isset($param['sort_type'])){$sort_type = $param['sort_type'];}
    if(isset($param['sort_by'])){$sort_by = $param['sort_by'];}




        $data = $r->get_overview($param_convertion_type,$column,$start,$end,$type,$source,$parent_id,$sort_by,$sort_type);    
 
    

    if($data)
    {
        $msg['code'] = 1;
        $msg["result"]['msg'] = 'Sukses';
        $msg["result"]['data'] = $data;
    }
    else{
        $msg['code'] = 0;
        $msg["result"]['msg'] = 'Error';
    }
    
    return $msg;
}

function set_custom_metric($param)
{
    global $app;
    $rules = array(
        'project_id'      => 'required',
        'name'      => 'required',
        'formula'      => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $r = new report();
    $res = $r->add_metric($name, $formula);

    return $res;
}

function get_all_metric($param){
    global $app;
    $rules = array(
        'token'      => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $r = new report();
    $data = $r->get_all_metric();

    if($data)
    {
        $msg['code'] = 1;
        $msg["result"]['msg'] = 'Sukses';
        $msg["result"]['data'] = $data;
    }
    else{
        $msg['code'] = 0;
        $msg["result"]['msg'] = 'Error';
    }
    
    return $msg;
}

function cron_quee($param){
    global $app;
    $db = $app->db;
    $db->where("cron_report = ? AND project_id > 6", array(1));
    $db->orderBy("last_cron", "ASC"); 
    $projects = $db->get("project", 3);
    if($db->count > 0){
        foreach($projects as $key => $project){
            $webhook = 'http://localhost/api.html?act=report_cron&project_id='.$project['project_id'];
            $res = file_get_contents($webhook);
            $msg['code'] = 1;
            $msg["result"]['msg'] = 'Sukses cron project '.$project['project_id'];
            $msg["result"]['data'] = $res;
            $db->where("project_id = ?", array($project['project_id']));
            $db->update("project", array('cron_report' => 0));
        }
    }else{
        $msg['code'] = 0;
        $msg["result"]['msg'] = 'Error cron project '.$project['project_id'];
    }
    return $msg;
}

function cron($param){
    ini_set('max_execution_time', 0);
    set_time_limit(0);
    global $app;
	$rules = [
        "project_id" => "required"
    ];
    $msg = array();
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $db = $app->db;
    $db2 = $app->db2;
    $acc = new account($project_id);
    // $db2->where("project_id = ?", array($project_id));
    // $integration = $db2->get("integration");
    // if($db2->count >0){
    //     foreach($integration as $k=>$v){
    //         $v['data'] = unserialize($v['data']);
    //         if($v["platform"] == "facebook" || $v["type"] == "ctwa"){
    //             $msg = $acc->grab_fb($v);
    //             print_r($msg);
    //         }
    //     }
    // }  
    //if(isset($msg['code']) && $msg['code'] == 1){
        $db->where("project_id = ?", array($project_id));
        $db->update("project", array('cron_report' => 0));
        //echo 'delete cron '.$project_id;
        //$bt      = new bt_api();
        //$bt->delete_cron_name('report_'.$project_id);
    //}
    /// cron bill
    $dt = new DateTime("now", new DateTimeZone('Asia/Jakarta'));
    $timestamp = $dt->format('Y-m-d H:i:s');
    $db->where("waktu < ?", array($timestamp));
    $db->orderby('waktu', 'Asc');
    $res = $db->getone("cron");
    $msg = '';
    if ($db->count > 0) {
        $dt->modify('+1 day');
        $newtimestamp = $dt->format('Y-m-d H:i:s');
        // $rex['res'] = [];
        // $rex['type'] = 'cron';
        // $rex['webhook'] = $res['url'] ;
        // $pos['msg'] = json_encode($rex);
        // $res = post_x_contents($pos, 'https://que-fb1.gass.co.id/add-task');

        //$msg = file_get_contents($res['url']);
        echo $msg;
        $db->where('id', $res["id"]);
        $db->update("cron", array('waktu' => $newtimestamp));
        $parsedUrl = parse_url($res['url']);
        $ip = $parsedUrl['host'];
    }else{
        $res = $db->getone("cron");
        if ($db->count > 0) {
            $parsedUrl = parse_url($res['url']);
            $ip = $parsedUrl['host'];
        }else{
            $ip = $_SERVER['SERVER_ADDR'];
        }
    }

    // send rabbit mq fb grab
    // $acc = new account($project_id);
    // $db2->where("project_id = ?", array($project_id));
    // $integration = $db2->get("integration");
    // if($db2->count >0){
    //     foreach($integration as $k=>$v){
    //         //$v['webhook'] = 'http://'.$_SERVER['SERVER_ADDR'].'/api.html?act=spend_update&project_id='.$project_id;
    //         $v['data'] = unserialize($v['data']);
    //         unset($v['account_hash']);
    //         if($v['platform']=='google'){
    //             $v['data']['google_clientId'] = $app->config->google_clientId;
    //             $v['data']['google_clientSecret'] = $app->config->google_clientSecret;
    //             $v['data']['google_developerToken'] = $app->config->google_developerToken;
    //             $rex['webhook'] = 'http://'.$ip.'/api.html?act=spend_update_new&project_id='.$project_id.'&source=google';
    //         }elseif($v['platform']=='facebook'){
    //             $v['data']['fb_app_id'] = $app->config->fb_app_id;
    //             $v['data']['fb_app_secret'] = $app->config->fb_app_secret;
    //             $rex['webhook'] = 'http://'.$ip.'/api.html?act=spend_update_new&project_id='.$project_id.'&source=meta';
    //         }                
    //         $rex['res'] = $v;
    //         $pos['msg'] = json_encode($rex);
    //         $res = post_x_contents($pos, 'https://que-fb1.gass.co.id/add-task');
    //         print_r($res);
    //     }
    // }
    
    $clean = new clean();
    $clean->move_old();
    return $msg;
    // Ambil daftar tabel
    $tables = $db2->rawQuery("SHOW TABLES");

    if ($tables) {
        $msg ='';
        foreach ($tables as $tableRow) {
            $table = array_values($tableRow)[0]; // Mengambil nama tabel dari hasil array
            $check = $db2->rawQuery("CHECK TABLE $table");
    
            foreach ($check as $checkRow) {
                $msg .= "Table: " . $checkRow['Table'] . " - Status: " . $checkRow['Msg_text'] . "<br>";
                if (strpos($checkRow['Msg_text'], 'OK') === false) {
                    $msg .= "⚠ Tabel $table mungkin korup!<br>";
                }
            }
        }
        echo $msg;
        $pos["act"] = 'broadcast_add';
        $pos["project_id"] = $project_id;
        $pos["phone"] = '6282139817939';
        $pos["name"] = 'pile';
        $pos["pesan"] = $msg.' Project ID : '.$project_id;
        post_x_contents($pos, 'http://10.104.0.27/api.html');
    } else {
        echo "Gagal mendapatkan daftar tabel.";
    }
}