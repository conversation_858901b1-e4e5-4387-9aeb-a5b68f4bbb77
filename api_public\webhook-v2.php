<?php
/**
 * Enhanced Webhook Handler with Dynamic Processing
 * Supports all webhook platforms through unified DynamicWebhookProcessor
 */

global $app;  
$db = $app->db;
header('Access-Control-Allow-Origin: *'); 
header('Access-Control-Allow-Methods: GET, POST');
set_time_limit(0);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

if (count($_GET) > 0) {
    $_POST = array_merge($_POST,$_GET);
}

// Normalize provider parameter
if(isset($_GET["type"])) {
    $_GET["provider"] = $_GET["type"];
}

// Get input data
$input = file_get_contents('php://input');
$provider = $_GET["provider"];

// Handle form-encoded data
if (empty($input) && !empty($_POST)) {
    $input = json_encode($_POST);
} elseif (!empty($input) && strpos($input, '=') !== false && strpos($input, '&') !== false) {
    // Handle raw form-encoded input
    parse_str($input, $parsed_data);
    $input = json_encode($parsed_data);
}

// Ensure input is not empty
if (empty($input)) {
    $input = '{}'; // Fallback to empty JSON object
}

// Validate required parameters
if (!isset($_GET["p"])) {
    http_response_code(400);
    echo "invalid link: missing project key";
    die();
}

if (!isset($_GET["cs"])) {
    http_response_code(400);  
    echo "invalid link: missing cs";
    die();
}

$project_key = $_GET["p"];
$nope_cs = $_GET["cs"];
$provider = $_GET["provider"] ?? $_GET["pv"] ?? null;

try {
    // Use DynamicWebhookProcessor for supported platforms
    $supportedPlatforms = [
        'konekwa', 'waba', 'halosis', 'sleekflow', 'barantum', 
        'chatdaddy', 'fonnte', 'freshchat', 'smartchat', 
        'botsailor', 'kommo', 'otoklix', 'pancake', 
        'qiscus-omnichannel', 'rasayel', 'respondio', 
        'wabahalosis', 'hs', "qontak"
    ];
    
    if (in_array($provider, $supportedPlatforms) || !$provider) {
        // Process dengan DynamicWebhookProcessor
        $processor = new DynamicWebhookProcessor($app, $project_key, $nope_cs, $input, $provider);
        $result = $processor->process();
        
        if ($result && isset($result['status'])) {
            if ($result['status'] === 'success') {
                // Return detailed step information
                $response = [
                    'status' => 'success',
                    'message' => 'Webhook processed successfully',
                    'summary' => $result['summary'],
                    'steps' => $result['steps'],
                    'performance' => $processor->getStepPerformance()
                ];
                
                header('Content-Type: application/json');
                echo json_encode($response, JSON_PRETTY_PRINT);
            } else {
                //http_response_code(400);
                $response = [
                    'status' => 'failed',
                    'message' => $result['error'] ?? 'Processing failed',
                    'steps' => $result['steps'] ?? [],
                    'summary' => $result['summary'] ?? []
                ];
                
                header('Content-Type: application/json');
                echo json_encode($response, JSON_PRETTY_PRINT);
            }
        } else {
            //http_response_code(400); 
            $response = [
                'status' => 'failed',
                'message' => 'Processing failed - no result returned',
                'steps' => [],
                'summary' => []
            ];
            
            header('Content-Type: application/json');
            echo json_encode($response, JSON_PRETTY_PRINT);
        }
        //file_put_contents('log/hooklog-v2-'.$nope_cs.'.txt', '[' . date('Y-m-d H:i:s') . "]\n" . json_encode($result)."\n\n", FILE_APPEND);
    } else {
        // Fallback ke file individual untuk platform yang belum di-migrate
        $file = "api_public/webhook/".$provider.".php";
        if (file_exists($file)) {
            include "webhook/".$provider.".php";
        } else {
            http_response_code(404);
            echo "Provider not found: " . $provider;
        }
    }
    
} catch (Exception $e) {
    //error_log("Webhook Error: " . $e->getMessage());
    http_response_code(500);
    echo "Internal error";
}