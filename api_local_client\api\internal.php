<?php

function get_imp_cta($param)
{

    $rules = [
        "project_id" => "required",
        "token" => "required",
    ];
    validate_param($rules, $param);
    //extract($param);
    $token = $param["token"];

    $challenge = "ZIsYnpnGNZQXFM1";
    $timeSlot = floor(time() / 5);
    $challenge = sha1(crc32($challenge . $timeSlot));

    if ($token != $challenge) {
        $res["code"] = 0;
        $res["msg"] = "invalid token";
        return $res;
    }

    assign_child_db($param['project_id']);
    global $app;
    $db2 = $app->db2;
    //$db2->where("tanggal >= DATE_SUB(CURDATE(), INTERVAL 27 DAY);");
    $db2->where("report_key = 'lp_view'");
    $db2->orwhere("report_key = 'unik_cta'");
    $tmp = $db2->get("report_data", null, "date as tanggal,report_key,report_value");

    $i = 0;
    foreach ($tmp as $key => $value) {
        $data[$i]["imp"] = 0;
        $data[$i]["cta"] = 0;

        if ($value["report_key"] == "lp_view") {
            $data[$i]["imp"] = $value["report_value"];
        } else {
            $data[$i]["cta"] = $value["report_value"];
        }
        $data[$i]["tanggal"] = $value["tanggal"];
        $i++;
    }

    $weeksData = sumDataEverySevenDays($data);

    return $weeksData;

}

function getLastDateWithImpression($data)
{
    $lastDateWithImpression = null;

    // Iterate through the array
    foreach (array_reverse($data) as $date => $values) {
        // Check if the current date has impressions
        if (isset($values['imp']) && $values['imp'] > 0) {
            $lastDateWithImpression = $date;
            break;
        }
    }

    return $lastDateWithImpression;
}

function getLastDateWithNonZeroCTA($data)
{
    $lastDateWithCTA = null;

    // Iterate through the array
    foreach (array_reverse($data) as $date => $values) {
        // Check if the current date has a non-zero CTA
        if (isset($values['cta']) && $values['cta'] > 0) {
            $lastDateWithCTA = $date;
            break;
        }
    }

    return $lastDateWithCTA;
}
function sumDataEverySevenDays($data)
{
    $dates = array();
    for ($i = 27; $i >= 0; $i--) {
        $tgl = date('Y-m-d', strtotime("-$i days"));
        $dates[$tgl]["imp"] = 0;
        $dates[$tgl]["cta"] = 0;
    }

    foreach ($data as $key => $value) {

        $d_tgl = $value["tanggal"];
        if (isset($dates[$d_tgl])) {
            $dates[$d_tgl]["cta"] = $dates[$d_tgl]["cta"] + $value["cta"];
            $dates[$d_tgl]["imp"] = $dates[$d_tgl]["imp"] + $value["imp"];
        }
    }

    $res["last_cta"] = getLastDateWithNonZeroCTA($dates);
    $res["last_imp"] = getLastDateWithImpression($dates);

    // Initialize variables to store grouped data
    $groupedData = array();
    $intervalStart = null;
    $intervalEnd = null;
    $totalImpression = 0;
    $totalCta = 0;
    $counter = 1;

    // Iterate through the data
    foreach ($dates as $date => $values) {
        // Convert the date to a Unix timestamp
        $timestamp = strtotime($date);

        // If it's the first iteration, set the interval start
        if ($intervalStart === null) {
            $intervalStart = $timestamp;
        }

        // If the current date exceeds the end of the current interval
        if ($timestamp > $intervalStart + (6 * 24 * 60 * 60)) {
            // Store the grouped data and reset variables
            $groupedData[$counter] = array(
                "imp" => $totalImpression,
                "cta" => $totalCta,
            );
            $intervalStart = $timestamp;
            $totalImpression = 0;
            $totalCta = 0;

            $counter++;
        }

        // Accumulate values for the current interval
        $totalImpression += $values['imp'];
        $totalCta += $values['cta'];

        // Set the end of the current interval
        $intervalEnd = $timestamp;
    }

    // Store the last grouped data
    $groupedData[$counter] = array(
        "imp" => $totalImpression,
        "cta" => $totalCta,
    );

    $res["data"] = $groupedData;

    return $res;

}

function get_performance()
{
    $bt = new bt_api();

    $res = $bt->get_performance();

    return $res;
}
function get_stat($param)
{

    $rules = [
        "project_id" => "required",
        "tanggal" => "required",
        "key" => "required",
    ];
    extract($param);

    assign_child_db($project_id);

    global $app;
    $db2 = $app->db2;

    $db2->where("date", $tanggal);
    $db2->where("report_key", $key);
    $result = $db2->getone("report_data", "sum(report_value) as value");

    if ($result["value"] == null) {
        $res["code"] = 1;
        $res["msg"] = 0;

    } else {
        $res["code"] = 1;
        $res["msg"] = $result["value"];
    }

    return $res;
}
function delete_project($param)
{
    $rules = [
        "project_id" => "required",
        "token" => "required",
    ];
    validate_param($rules, $param);
    extract($param);

    $challenge = "ZIsYnpnGNZQXFM1";
    $timeSlot = floor(time() / 5);
    $challenge = sha1(crc32($challenge . $timeSlot));

    if ($token != $challenge) {
        $res["code"] = 0;
        $res["msg"] = "invalid token";
        return $res;
    }

    $error = false;
    $error_msg = array();

    try {

        assign_child_db($project_id);
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
    } catch (\Throwable $th) {}

    if ($db2 != null) {

        try {
            $db_name = "client_" . $project_id;
            $db2->rawQuery("DROP DATABASE {$db_name}");

        } catch (\Throwable $th) {
            array_push($error_msg, "Drop db {$db_name} error");
        }

        try {
            $db_name = "client_" . $project_id . "_old";
            $db2->rawQuery("DROP DATABASE {$db_name}");
        } catch (\Throwable $th) {
            array_push($error_msg, "Drop db {$db_name} error \n" . $db2->getLastQuery());
        }

        try {
            $db->where("project_id", $project_id);
            if ($db->delete("cs")) {} else {
                $error = true;
                array_push($error_msg, "Error delete cs from master db server");
            }
        } catch (\Throwable $th) {}

        try {
            $db->where("project_id", $project_id);
            if ($db->delete("cs_jadwal")) {} else {
                $error = true;
                array_push($error_msg, "Error delete cs from master db server");
            }
        } catch (\Throwable $th) {}

        try {
            $db->where("project_id", $project_id);
            if ($db->delete("project")) {} else {
                $error = true;
                array_push($error_msg, "Error delete cs from master db server");
            }
        } catch (\Throwable $th) {}

        try {
            $db->where("project_id", $project_id);
            if ($db->delete("shortlink")) {} else {
                $error = true;
                array_push($error_msg, "Error delete cs from master db server");
            }
        } catch (\Throwable $th) {}

    } else {
        $res["code"] = 0;
        $res["msg"] = "gagal";
        $res["data"] = "Project already deleted on remote server";
        return $res;
    }

    if (count($error_msg) < 1) {
        $res["code"] = 1;
        $res["msg"] = "sukses";
    } else {
        $res["code"] = 0;
        $res["msg"] = "gagal";
        $res["data"] = $error_msg;
    }
    return $res;
}
function get_table()
{
    $project_id = 1;
    assign_child_db($project_id);

    global $app;
    $db3 = $app->db3;
    $db2 = $app->db2;
    $s = new sync_db();

    $res["main"] = $s->get_table($db2, $project_id);

    $res["old"] = $s->get_table($db3, $project_id,true);

    $res = serialize($res);
    $res = rtrim(strtr(base64_encode(gzdeflate($res, 9)), '+/', '-_'), '=');
   
    return $res;
}

function update_table($param)
{

    $rules = [
        "project_id" => "required",
        "data_table" => "required",
    ];
    validate_param($rules, $param);
    extract($param);

    assign_child_db($project_id);

    echo "Trying to update table for project id $project_id"."<br/><br/>";
    global $app;
    $db3 = $app->db3;
    $db2 = $app->db2;

    $data_table = gzinflate(base64_decode(strtr($data_table, '-_', '+/')));

    $data_table = unserialize($data_table);

    $main = $data_table["main"];

    foreach ($main["create"] as $m) {
        try {
            $db2->rawQuery($m);
            $db3->rawQuery($m);
        } catch (\Throwable $th) {
             //echo 'Error: ' . $th->getMessage();
            // Optionally, display more details such as the file and line number
            //echo ' in ' . $th->getFile() . ' on line ' . $th->getLine();

        }
    }

    try {
        $sync_db = new sync_db();
        $sync_db->run($data_table["main"]["alter"], $db2, $project_id);
        $sync_db->run_engine($data_table["main"]["engine"], $db2, $project_id);
        $sync_db->run_collation($data_table["main"]["collation"], $db2, $project_id);

        $sync_db->run_index($data_table["main"]["index"], $db2, $project_id);
    } catch (\Throwable $th) {

        echo 'Error: ' . $th->getMessage();
        // Optionally, display more details such as the file and line number
        echo ' in ' . $th->getFile() . ' on line ' . $th->getLine();

    }

    try {
        $sync_db = new sync_db();
        $sync_db->run($data_table["old"]["alter"], $db3, $project_id);
        $sync_db->run_engine($data_table["old"]["engine"], $db3, $project_id);
        $sync_db->run_collation($data_table["old"]["collation"], $db3, $project_id);

        $sync_db->run_index($data_table["old"]["index"], $db3, $project_id);

    } catch (\Throwable $th) {

        echo 'Error: ' . $th->getMessage();
        // Optionally, display more details such as the file and line number
        echo ' in ' . $th->getFile() . ' on line ' . $th->getLine();
    }

    return $res;
}


function update_table_master($param)
{

    $rules = [
        "query" => "required",
    ];
    validate_param($rules, $param);
    extract($param);

    global $app;
    $db = $app->db;
    $res = $db->rawQuery($query);
    return $res;
}

