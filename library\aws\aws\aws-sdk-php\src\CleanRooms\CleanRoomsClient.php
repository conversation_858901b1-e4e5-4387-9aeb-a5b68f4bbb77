<?php
namespace Aws\CleanRooms;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Clean Rooms Service** service.
 * @method \Aws\Result batchGetSchema(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchGetSchemaAsync(array $args = [])
 * @method \Aws\Result createCollaboration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCollaborationAsync(array $args = [])
 * @method \Aws\Result createConfiguredTable(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createConfiguredTableAsync(array $args = [])
 * @method \Aws\Result createConfiguredTableAnalysisRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createConfiguredTableAnalysisRuleAsync(array $args = [])
 * @method \Aws\Result createConfiguredTableAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createConfiguredTableAssociationAsync(array $args = [])
 * @method \Aws\Result createMembership(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMembershipAsync(array $args = [])
 * @method \Aws\Result deleteCollaboration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCollaborationAsync(array $args = [])
 * @method \Aws\Result deleteConfiguredTable(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteConfiguredTableAsync(array $args = [])
 * @method \Aws\Result deleteConfiguredTableAnalysisRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteConfiguredTableAnalysisRuleAsync(array $args = [])
 * @method \Aws\Result deleteConfiguredTableAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteConfiguredTableAssociationAsync(array $args = [])
 * @method \Aws\Result deleteMember(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMemberAsync(array $args = [])
 * @method \Aws\Result deleteMembership(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMembershipAsync(array $args = [])
 * @method \Aws\Result getCollaboration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCollaborationAsync(array $args = [])
 * @method \Aws\Result getConfiguredTable(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getConfiguredTableAsync(array $args = [])
 * @method \Aws\Result getConfiguredTableAnalysisRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getConfiguredTableAnalysisRuleAsync(array $args = [])
 * @method \Aws\Result getConfiguredTableAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getConfiguredTableAssociationAsync(array $args = [])
 * @method \Aws\Result getMembership(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMembershipAsync(array $args = [])
 * @method \Aws\Result getProtectedQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getProtectedQueryAsync(array $args = [])
 * @method \Aws\Result getSchema(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSchemaAsync(array $args = [])
 * @method \Aws\Result getSchemaAnalysisRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSchemaAnalysisRuleAsync(array $args = [])
 * @method \Aws\Result listCollaborations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCollaborationsAsync(array $args = [])
 * @method \Aws\Result listConfiguredTableAssociations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listConfiguredTableAssociationsAsync(array $args = [])
 * @method \Aws\Result listConfiguredTables(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listConfiguredTablesAsync(array $args = [])
 * @method \Aws\Result listMembers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMembersAsync(array $args = [])
 * @method \Aws\Result listMemberships(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMembershipsAsync(array $args = [])
 * @method \Aws\Result listProtectedQueries(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listProtectedQueriesAsync(array $args = [])
 * @method \Aws\Result listSchemas(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSchemasAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result startProtectedQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startProtectedQueryAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateCollaboration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCollaborationAsync(array $args = [])
 * @method \Aws\Result updateConfiguredTable(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateConfiguredTableAsync(array $args = [])
 * @method \Aws\Result updateConfiguredTableAnalysisRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateConfiguredTableAnalysisRuleAsync(array $args = [])
 * @method \Aws\Result updateConfiguredTableAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateConfiguredTableAssociationAsync(array $args = [])
 * @method \Aws\Result updateMembership(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateMembershipAsync(array $args = [])
 * @method \Aws\Result updateProtectedQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateProtectedQueryAsync(array $args = [])
 */
class CleanRoomsClient extends AwsClient {}
