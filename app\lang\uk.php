<?php
return array(
    'validate_required' => 'Поле {field} є обов’язковим',
    'validate_valid_email' => 'Поле {field} має бути Email адресою',
    'validate_max_len' => 'У полі {field} має бути максимум {param} символів',
    'validate_min_len' => 'У полі {field} має бути мінімум {param} символів',
    'validate_exact_len' => 'Поле {field} має містити рівно {param} символів',
    'validate_alpha' => 'Поле {field} може містити тільки літери',
    'validate_alpha_numeric' => 'Поле {field} може містити тільки букви і цифри',
    'validate_alpha_numeric_space' => 'Поле {field} може містити тільки букви, цифри та пробіли',
    'validate_alpha_dash' => 'Поле {field} може містити тільки букви та дефіс',
    'validate_alpha_space' => 'Поле {field} може містити тільки букви та пробіл',
    'validate_numeric' => 'Поле {field} має бути числом',
    'validate_integer' => 'Поле {field} має бути цілим числом',
    'validate_boolean' => 'У поля {field} повинен бути логічний (булевий) тип даних true або false',
    'validate_float' => 'Поле {field} має бути не цілим числом',
    'validate_valid_url' => 'Поле {field} має бути гіперпосиланням',
    'validate_url_exists' => 'Гіперпосилання {field} недоступне',
    'validate_valid_ip' => 'Поле {field} має бути IP адресою',
    'validate_valid_ipv4' => 'Поле {field} має бути IPv4 адресою',
    'validate_valid_ipv6' => 'Поле {field} має бути IPv6 адресою',
    'validate_guidv4' => 'Поле {field} має бути GUIDом',
    'validate_valid_cc' => 'Номер картки {field} не є валідним',
    'validate_valid_name' => 'Поле {field} має містити повне ім’я',
    'validate_contains' => 'Поле {field} може містити наступні значення: {param}',
    'validate_contains_list' => 'Значення {field} не може бути використано як відповідь',
    'validate_doesnt_contain_list' => 'Значення {field} не може бути використано',
    'validate_street_address' => 'Поле {field} має бути адресою',
    'validate_date' => 'Поле {field} має бути датою',
    'validate_min_numeric' => 'Поле {field} має бути числом не менше ніж {param}',
    'validate_max_numeric' => 'Поле {field} має бути числом не більше ніж {param}',
    'validate_min_age' => 'Вік повинен бути більшим ніж {param}',
    'validate_invalid' => 'Поле {field} не пройшло перевірку',
    'validate_starts' => 'Поле {field} має починатися з {param}',
    'validate_extension' => 'Файл {field} повинен бути наступного типу: {param}',
    'validate_required_file' => 'Файл {field} обов’язковий до завантаження',
    'validate_equalsfield' => 'Поле {field} має бути ідентичним {param}',
    'validate_iban' => 'Поле {field} має бути валідним IBAN',
    'validate_phone_number' => 'Поле {field} має бути правильним номером телефону',
    'validate_regex' => 'Поле {field} повинно містити правильне значення',
    'validate_valid_json_string' => 'Поле {field} має бути валідним JSON рядком',
    'validate_valid_array_size_greater' => 'Поле {field} має містити мінімум {param} значень',
    'validate_valid_array_size_lesser' => 'Поле {field} повинно містити максимум {param} значень',
    'validate_valid_array_size_equal' => 'Поле {field} має містити рівно {param} значень',
);
