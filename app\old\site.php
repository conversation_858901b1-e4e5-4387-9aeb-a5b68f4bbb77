<?php
class site
{
    function add_site($param)
    {
        /*
            user_id
            param list
            fb_pixel
            fb_token
            adw_tag
            adw_conv_id
            google_contact
            google_purchase
            gtm
            tiktok_pixel
            tiktok_token
        */
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
        $param["domain"] = str_replace("http://", "", $param["domain"]);
        $param["domain"] = str_replace("https://", "", $param["domain"]);
        $param["domain"] = str_replace("/", "", $param["domain"]);
        $param["domain"] = str_replace(" ", "", $param["domain"]);
        $param["domain"] = strtolower($param["domain"]);
        $dom_hex = md5(trim(strtoupper($param["domain"])));
        $param["domain_hex"] = $db->func("UNHEX(?)", array($dom_hex));
        if ($id = $db->insert("x_site", $param)) {
            $this->create_table($id);
            return true;
        } else {
            return false;
        }
    }
    function create_table($id)
    {
        global $app;
        $db = $app->db;
        $db2 = $app->db2;

        $sql = "CREATE TABLE `" . $id . "_log` (
            `id` bigint(20) NOT NULL,
            `source` varchar(20) DEFAULT NULL,
            `type` varchar(100) DEFAULT NULL,
            `error` tinyint(1) NOT NULL DEFAULT '0',
            `waktu` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `param` text,
            `result` text
          ) ENGINE=MyISAM DEFAULT CHARSET=utf8;";
        $db2->rawQuery($sql);

        $sql = "ALTER TABLE `" . $id . "_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `source` (`source`),
  ADD KEY `error` (`error`);";
        $db2->rawQuery($sql);

        $sql = "ALTER TABLE `" . $id . "_log`
        MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;";
        $db2->rawQuery($sql);


        $sql = "CREATE TABLE IF NOT EXISTS `" . $id . "_campaign` (
                        `campaign_id` int(10) UNSIGNED NOT NULL,
                        `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                        `sapaan` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                        `data_range` smallint(5) UNSIGNED NOT NULL DEFAULT 0 DEFAULT 30,
                        `pembagian_chat` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                        `cta` varchar(250) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                        `imp_test` smallint(5) UNSIGNED NOT NULL DEFAULT 0,
                        `cta_test` smallint(5) UNSIGNED NOT NULL DEFAULT 0,
                        `traffic_test` smallint(5) UNSIGNED NOT NULL DEFAULT 0,
                        `focus_cta` smallint(5) UNSIGNED NOT NULL DEFAULT 0,
                        `focus_contact` smallint(5) UNSIGNED NOT NULL DEFAULT 0,
                        `focus_purchase` smallint(5) UNSIGNED NOT NULL DEFAULT 0,
                        `impression` int(10) UNSIGNED NOT NULL DEFAULT 0,
                        `lead` int(10) UNSIGNED NOT NULL DEFAULT 0,
                        `contact` int(11) UNSIGNED NOT NULL DEFAULT 0,
                        `purchase` int(10) UNSIGNED NOT NULL DEFAULT 0,
                        `value` int(10) UNSIGNED NOT NULL DEFAULT 0,
                        `direct_link` tinyint(1) NOT NULL DEFAULT 0,
                        `fb_pixel` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                        `fb_token` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                        `adw_tag` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                        `adw_conv_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                        `google_contact` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                        `google_purchase` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                        `gtm` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                        `tiktok_pixel` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                        `tiktok_token` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                        PRIMARY KEY (campaign_id),
                        KEY `contact` (contact)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; ";
        $db2->rawQuery($sql);
        $sql = " CREATE TABLE IF NOT EXISTS `" . $id . "_campaign_cs` (
                            `id` int(10) UNSIGNED NOT NULL,
                            `campaign_id` int(10) UNSIGNED NOT NULL,
                            `cs_id` int(10) UNSIGNED NOT NULL,
                            PRIMARY KEY (id),
                            KEY `campaign_id` (campaign_id)
                        ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        $db2->rawQuery($sql);
        $sql = " CREATE TABLE IF NOT EXISTS `" . $id . "_campaign_detail` (
                            `campaign_id` int(10) UNSIGNED NOT NULL,
                            `tanggal` date NOT NULL,
                            `hash` binary(16) NOT NULL,
                            `impression` int(10) UNSIGNED NOT NULL DEFAULT 0,
                            `lead` int(10) UNSIGNED NOT NULL DEFAULT 0,
                            `contact` int(10) UNSIGNED NOT NULL DEFAULT 0,
                            `purchase` int(10) UNSIGNED NOT NULL DEFAULT 0,
                            `value` int(10) UNSIGNED NOT NULL DEFAULT 0,
                            `pay` int(2) UNSIGNED NOT NULL DEFAULT 0,
                            PRIMARY KEY (hash),
                            KEY `tanggal` (tanggal),
                            KEY `campaign_id` (campaign_id)
                        ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        $db2->rawQuery($sql);
        $sql = "CREATE TABLE IF NOT EXISTS `" . $id . "_landing_page` (
            `id` int(10) UNSIGNED NOT NULL,
            `campaign_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `name` varchar(250) COLLATE utf8mb4_unicode_ci NOT NULL,
            `url` varchar(250) COLLATE utf8mb4_unicode_ci NOT NULL,
            `impression` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `lead` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `contact` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `purchase` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `value` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `status` tinyint(1) NOT NULL DEFAULT 0,
            PRIMARY KEY (id),
            KEY `campaign_id` (campaign_id),
            KEY `status` (status)
            ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        $db2->rawQuery($sql);
        $sql = "CREATE TABLE IF NOT EXISTS `" . $id . "_landing_page_detail` (
            `lp_id` int(10) UNSIGNED NOT NULL,
            `tanggal` date NOT NULL,
            `hash` binary(16) NOT NULL,
            `impression` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `lead` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `contact` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `purchase` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `value` int(10) UNSIGNED NOT NULL DEFAULT 0,
            PRIMARY KEY (hash),
            KEY `tanggal` (tanggal),
            KEY `lp_id` (lp_id)
            ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        $db2->rawQuery($sql);
        $sql = "CREATE TABLE IF NOT EXISTS `" . $id . "_cs` (
            `id` smallint(5) UNSIGNED NOT NULL,
            `name` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL,
            `nope` varchar(50) COLLATE utf8mb4_unicode_520_ci NOT NULL,
            `last_contact` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `status` tinyint(1) NOT NULL DEFAULT 0,
            PRIMARY KEY (id)
            ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;";
        $db2->rawQuery($sql);
        $sql = "CREATE TABLE IF NOT EXISTS `" . $id . "_cs_log` (
            `id` bigint(20) NOT NULL,
            `cs_id` int(10) UNSIGNED NOT NULL,
            `tanggal` date NOT NULL,
            `contact` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `purchase` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `value` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `hash` binary(16) NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY `hash` (`hash`)
            ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;";
        $db2->rawQuery($sql);
        $sql = "CREATE TABLE IF NOT EXISTS `" . $id . "_visitor` (
            `vid` bigint(20) NOT NULL,
            `cs_id` smallint(6) NOT NULL DEFAULT 0,
            `nama` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
            `phone` varchar(15) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
            `created` timestamp NOT NULL DEFAULT current_timestamp(),
            `waktu_contact` datetime DEFAULT NULL,
            `waktu_purchase` datetime DEFAULT NULL,
            `fbp` varchar(250) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
            `fbc` varchar(250) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
            `fbclid` varchar(250) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
            `gclid` varchar(250) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
            `ttclid` text COLLATE utf8mb4_unicode_ci DEFAULT NULL ,
            `ttclid2` text COLLATE utf8mb4_unicode_ci DEFAULT NULL ,
            `ip` varchar(250) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
            `useragent` text COLLATE utf8mb4_unicode_ci NOT NULL ,
            `lp` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `campaign_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `cart` tinyint(1) NOT NULL DEFAULT 0,
            `contact` tinyint(1) NOT NULL DEFAULT 0,
            `purchase` tinyint(1) NOT NULL DEFAULT 0,
            `value` int(10) UNSIGNED NOT NULL DEFAULT 0,
            PRIMARY KEY (vid)
            ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        $db2->rawQuery($sql);
        $sql = "CREATE TABLE IF NOT EXISTS `" . $id . "_hash` (
            `hash` binary(16) NOT NULL ,
            `created` timestamp NOT NULL DEFAULT current_timestamp(),
            PRIMARY KEY (hash)
            ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        $db2->rawQuery($sql);
        $sql = "CREATE TABLE IF NOT EXISTS `" . $id . "_event` (
            `tanggal` date NOT NULL,
            `campaign_id` smallint(5) UNSIGNED NOT NULL DEFAULT 0,
            `lp` smallint(5) UNSIGNED NOT NULL DEFAULT 0,
            `contact` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `purchase` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `value` int(10) UNSIGNED NOT NULL DEFAULT 0,
            `hash` binary(16) NOT NULL  ,
            PRIMARY KEY (hash)
            ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        $db2->rawQuery($sql);
        $ez_query = "
            ALTER TABLE `" . $id . "_campaign`
            MODIFY `campaign_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;";
        $db2->rawQuery($ez_query);
        $ez_query = "
            ALTER TABLE `" . $id . "_campaign_cs`
            MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;";
        $db2->rawQuery($ez_query);
        $ez_query = "
            ALTER TABLE `" . $id . "_campaign`
            MODIFY `direct_link` tinyint(1) NOT NULL DEFAULT '0';";
        $db2->rawQuery($ez_query);
        $ez_query = "
            ALTER TABLE `" . $id . "_landing_page`
            MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;";
        $db2->rawQuery($ez_query);
        $ez_query = "
            ALTER TABLE `" . $id . "_cs`
            MODIFY `id` smallint(5) UNSIGNED NOT NULL AUTO_INCREMENT;";
        $db2->rawQuery($ez_query);
        $ez_query = "ALTER TABLE `" . $id . "_cs` ADD INDEX( `nope`)";
        $db2->rawQuery($ez_query);
        $ez_query = "ALTER TABLE `" . $id . "_cs` ADD INDEX( `last_contact`)";
        $db2->rawQuery($ez_query);
        $ez_query = "
            ALTER TABLE `" . $id . "_cs_log`
            MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;";
        $db2->rawQuery($ez_query);
        $ez_query = "
            ALTER TABLE `" . $id . "_visitor`
            MODIFY `vid` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;";
        $db2->rawQuery($ez_query);
    }
}
