<?php
class cs{
    public function login($phone, $kode){
        global $keya, $c_time, $app;
		$db= $app->db;
        $db->where("nope = ? and status = 1", array($phone));
        $users = $db->getone("x_cs");
        if ($app->db->count > 0) {
            $code = md5(strtolower($phone).time());
            $data["token"]   = $db->func("UNHEX(?)", array($code));
            $db->where("nope = ? and status = 1", array($phone));
            if ($app->db->update('x_cs', $data)) {
                $ret["token"] = $code;
                $ret["code"] = 1;
                $ret["msg"]  = "Sukses login.";
            }else {
                $ret["code"] = 0;
                $ret["msg"]  = "gagal login.";
            }
        } else {
            $ret["code"] = 0;
            $ret["msg"]  = "gagal login, CS tidak ditemukan.";
        }       
        return $ret;
    }
    
    public function generate_code($phone){
        global $keya, $c_time, $app;
        $wa = new wa();
		$db= $app->db;
        $db->where("nope = ? and status = 1", array($phone));
        $users = $db->getone("x_cs");
        if ($app->db->count > 0) {
            $data["verif"]   = $verf = rand(111111,999999);
            $db->where("nope = ? and status = 1", array($phone));
            if ($app->db->update('x_cs', $data)) {
                echo 'xxx';
                $msg = "( Ini merupakan pesan otomatis ) \n";
				$msg .= "Kode OTP gass.co.id anda ".$data["verif"] ;
				$rex = $wa->send('text', $app->config->phone_whatsapp, $phone, $msg);
                $ret["code"] = 1;
                $ret["msg"]  = "Sukses generate.";
            }else {
                $ret["code"] = 0;
                $ret["msg"]  = "gagal generate.";
            }
        } else {
            $msg = "( Ini merupakan pesan otomatis ) \n";
            $msg .= "Nomer whatsapp kamu tidak ditemukan ";
            $wa->send('text', $app->config->phone_whatsapp, $phone, $msg);
            $ret["code"] = 0;
            $ret["msg"]  = "gagal generate, CS tidak ditemukan.";
        }       
        return $ret;
    }
    
    public function convert_token($token){
        global $keya, $c_time, $app;
		$db= $app->db;
        $db->where("token = UNHEX(?)", array($token));
        $users = $db->getone("x_cs");
        if ($app->db->count > 0) {
            unset($users['token']);
            $ret["data"] = $users;
            $ret["code"] = 1;
            $ret["msg"]  = "Sukses generate.";
        } else {
            $ret["code"] = 0;
            $ret["msg"]  = "gagal convert token, CS tidak ditemukan.";
        }       
        return $ret;
    }
    
    public function add_cs($user_id,$site_id,$nama,$nope,$autodc, $formatVisit, $formatCheckout, $valueCheckout, $formatPurchase, $valuePurchase, $without_scan=0){
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
        $nope = hp($nope);
        $db->where("nope = ? and status = 1", array($nope));
        $tmp = $db->getone("x_cs");
        if($tmp != NULL){
            $ret["code"] = 0;
            $ret["msg"] = "Already exist, plz contact support";
            return $ret;
        }
        $t_cs = $site_id."_cs";
        $db2->where("nope = ? ", array($nope));
        $tmp = $db2->getone($t_cs);
        if($tmp != NULL){
            $ret["code"] = 0;
            $ret["msg"] = "Already exist, plz contact support";
            return $ret;
        }
        $s = new server();
        $ip = $s->get_available();
        if($ip == false){
            $ret["code"] = 0;
            $ret["msg"] = "Server full, plz contact support";
            return $ret; 
        }
        $db->where("id", $site_id);
        $x = $db->getone("x_site");
        $table = "";
        if($x != NULL){
            $site_id = $x["id"];
            $table_cs = $site_id."_cs";
            $domain = $x["domain"];
            $domain = strtoupper($domain);
            $domain = trim($domain);
            $domain_hex = md5($domain);
            $webhook  = "https://gass.co.id/apisas.html?site_id=".$site_id;
            $data = [
                'phone' => $nope,
                'webhook' => $webhook,
                'license' => $domain,
                'formatVisit' => $formatVisit,
                'formatCheckout' => $formatCheckout,
                'valueCheckout' => $valueCheckout,
                'formatPurchase' => $formatPurchase,
                'valuePurchase' => $valuePurchase,
            ];
            if($without_scan==0){
                $res = json_decode(post_gateway($data, 'http://'.$ip.':8080/add-whatsapp'), true);
                if($res["code"] == 1){
                    $param["name"] = $nama;
                    $param["nope"] = hp($nope);
                    $param["status"] = 1;
                    $cs_id = $db2->insert($table_cs, $param);
                    $param["site_id"] = $site_id;
                    $param["user_id"] = $user_id;
                    $param["cs_id"] = $cs_id;
                    $param["server"] = $ip;
                    $param["webhook"] = $webhook;
                    $param["license"] = $domain;
                    $param["autodc"] = $autodc;
                    $param["formatVisit"] = $formatVisit;
                    $param["formatCheckout"] = $formatCheckout;
                    $param["valueCheckout"] = $valueCheckout;
                    $param["formatPurchase"] = $formatPurchase;
                    $param["valuePurchase"] = $valuePurchase;
                    $param["without_scan"] = $without_scan;
                    $id = $db->insert("x_cs", $param);
                    if($id){
                        $s->update_jml($ip);
                    }
                    $ret["code"] = 1;
                    $ret["msg"] = "sukses add phone";
                }else{
                    $ret["code"] = 0;
                    $ret["msg"] = $res["msg"];
    
                }
            }else{
                $param["name"] = $nama;
                $param["nope"] = hp($nope);
                $param["status"] = 1;
                $cs_id = $db2->insert($table_cs, $param);
                $param["site_id"] = $site_id;
                $param["user_id"] = $user_id;
                $param["cs_id"] = $cs_id;
                $param["server"] = $ip;
                $param["webhook"] = $webhook;
                $param["license"] = $domain;
                $param["autodc"] = $autodc;
                $param["formatVisit"] = $formatVisit;
                $param["formatCheckout"] = $formatCheckout;
                $param["valueCheckout"] = $valueCheckout;
                $param["formatPurchase"] = $formatPurchase;
                $param["valuePurchase"] = $valuePurchase;
                $param["without_scan"] = $without_scan;
                $id = $db->insert("x_cs", $param);
                if($id){
                    $ret["code"] = 1;
                    $ret["msg"] = "sukses add phone";
                }else{
                    $ret["code"] = 0;
                    $ret["msg"] = "Error add phone";
                } 
            }
            
            
        }else{
            $ret["code"] = 0;
            $ret["msg"] = "domain not found";
        }
        return $ret;
    }

    public function edit_cs($user_id, $cs_id,$nama,$autodc, $formatVisit, $formatCheckout, $valueCheckout, $formatPurchase, $valuePurchase, $without_scan=0){
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
        $db->where("id = ? and status = 1", array($cs_id));
        $cs = $db->getone("x_cs");
        if($db->count == 0){
            $ret["code"] = 0;
            $ret["msg"] = "CS not found.";
            return $ret;
        }
        $data = [
            'phone' => $cs['nope'],
            'formatVisit' => $formatVisit,
            'formatCheckout' => $formatCheckout,
            'valueCheckout' => $valueCheckout,
            'formatPurchase' => $formatPurchase,
            'valuePurchase' => $valuePurchase,
        ];
        $res = json_decode(post_gateway($data, 'http://'.$cs['server'].':8080/edit-format'), true);
        if($res["code"] == 1){
            
            $param["name"] = $nama;
            $param["autodc"] = $autodc;
            $param["formatVisit"] = $formatVisit;
            $param["formatCheckout"] = $formatCheckout;
            $param["valueCheckout"] = $valueCheckout;
            $param["formatPurchase"] = $formatPurchase;
            $param["valuePurchase"] = $valuePurchase;
            $param["without_scan"] = $without_scan;
            $db->where("id = ? and status = 1", array($cs_id));
            $id = $db->update("x_cs", $param);
            $table_cs = $cs['site_id']."_cs";
            $db2->where("id", $cs["cs_id"]);
            $db2->update($table_cs, array('name' => $nama));
            $ret["code"] = 1;
            $ret["msg"] = "sukses edit CS";
        }else{
            $ret["code"] = 0;
            $ret["msg"] = $res["msg"];

        }
        return $ret;
    }
}
