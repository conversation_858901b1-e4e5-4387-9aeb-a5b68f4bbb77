<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/services/audience_insights_service.proto

namespace Google\Ads\GoogleAds\V18\Services\DynamicLineupAttributeMetadata;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A YouTube channel returned as an example of the content in a lineup.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.services.DynamicLineupAttributeMetadata.SampleChannel</code>
 */
class SampleChannel extends \Google\Protobuf\Internal\Message
{
    /**
     * A YouTube channel.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.YouTubeChannelInfo youtube_channel = 1;</code>
     */
    protected $youtube_channel = null;
    /**
     * The name of the sample channel.
     *
     * Generated from protobuf field <code>string display_name = 2;</code>
     */
    protected $display_name = '';
    /**
     * Metadata for the sample channel.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.services.YouTubeChannelAttributeMetadata youtube_channel_metadata = 3;</code>
     */
    protected $youtube_channel_metadata = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Ads\GoogleAds\V18\Common\YouTubeChannelInfo $youtube_channel
     *           A YouTube channel.
     *     @type string $display_name
     *           The name of the sample channel.
     *     @type \Google\Ads\GoogleAds\V18\Services\YouTubeChannelAttributeMetadata $youtube_channel_metadata
     *           Metadata for the sample channel.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Services\AudienceInsightsService::initOnce();
        parent::__construct($data);
    }

    /**
     * A YouTube channel.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.YouTubeChannelInfo youtube_channel = 1;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\YouTubeChannelInfo|null
     */
    public function getYoutubeChannel()
    {
        return $this->youtube_channel;
    }

    public function hasYoutubeChannel()
    {
        return isset($this->youtube_channel);
    }

    public function clearYoutubeChannel()
    {
        unset($this->youtube_channel);
    }

    /**
     * A YouTube channel.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.YouTubeChannelInfo youtube_channel = 1;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\YouTubeChannelInfo $var
     * @return $this
     */
    public function setYoutubeChannel($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\YouTubeChannelInfo::class);
        $this->youtube_channel = $var;

        return $this;
    }

    /**
     * The name of the sample channel.
     *
     * Generated from protobuf field <code>string display_name = 2;</code>
     * @return string
     */
    public function getDisplayName()
    {
        return $this->display_name;
    }

    /**
     * The name of the sample channel.
     *
     * Generated from protobuf field <code>string display_name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setDisplayName($var)
    {
        GPBUtil::checkString($var, True);
        $this->display_name = $var;

        return $this;
    }

    /**
     * Metadata for the sample channel.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.services.YouTubeChannelAttributeMetadata youtube_channel_metadata = 3;</code>
     * @return \Google\Ads\GoogleAds\V18\Services\YouTubeChannelAttributeMetadata|null
     */
    public function getYoutubeChannelMetadata()
    {
        return $this->youtube_channel_metadata;
    }

    public function hasYoutubeChannelMetadata()
    {
        return isset($this->youtube_channel_metadata);
    }

    public function clearYoutubeChannelMetadata()
    {
        unset($this->youtube_channel_metadata);
    }

    /**
     * Metadata for the sample channel.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.services.YouTubeChannelAttributeMetadata youtube_channel_metadata = 3;</code>
     * @param \Google\Ads\GoogleAds\V18\Services\YouTubeChannelAttributeMetadata $var
     * @return $this
     */
    public function setYoutubeChannelMetadata($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Services\YouTubeChannelAttributeMetadata::class);
        $this->youtube_channel_metadata = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(SampleChannel::class, \Google\Ads\GoogleAds\V18\Services\DynamicLineupAttributeMetadata_SampleChannel::class);

