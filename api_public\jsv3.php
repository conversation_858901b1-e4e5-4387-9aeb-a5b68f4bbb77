<?php header("Content-Type: application/javascript");header("Cache-Control: max-age=604800, public");?>  
!function(e){var t={};function n(i){if(t[i])return t[i].exports;var o=t[i]={i:i,l:!1,exports:{}};return e[i].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(i,o,function(t){return e[t]}.bind(null,o));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){e.exports=n(1)},function(e,t){function n(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var i=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.options={overlayBackgroundColor:"#666666",overlayOpacity:.6,spinnerIcon:"ball-circus",spinnerColor:"#000",spinnerSize:"3x",overlayIDName:"overlay",spinnerIDName:"spinner",offsetY:0,offsetX:0,lockScroll:!1,containerID:null},this.stylesheetBaseURL="https://cdn.jsdelivr.net/npm/load-awesome@1.1.0/css/",this.spinner=null,this.spinnerStylesheetURL=null,this.numberOfEmptyDivForSpinner={"ball-8bits":16,"ball-atom":4,"ball-beat":3,"ball-circus":5,"ball-climbing-dot":1,"ball-clip-rotate":1,"ball-clip-rotate-multiple":2,"ball-clip-rotate-pulse":2,"ball-elastic-dots":5,"ball-fall":3,"ball-fussion":4,"ball-grid-beat":9,"ball-grid-pulse":9,"ball-newton-cradle":4,"ball-pulse":3,"ball-pulse-rise":5,"ball-pulse-sync":3,"ball-rotate":1,"ball-running-dots":5,"ball-scale":1,"ball-scale-multiple":3,"ball-scale-pulse":2,"ball-scale-ripple":1,"ball-scale-ripple-multiple":3,"ball-spin":8,"ball-spin-clockwise":8,"ball-spin-clockwise-fade":8,"ball-spin-clockwise-fade-rotating":8,"ball-spin-fade":8,"ball-spin-fade-rotating":8,"ball-spin-rotate":2,"ball-square-clockwise-spin":8,"ball-square-spin":8,"ball-triangle-path":3,"ball-zig-zag":2,"ball-zig-zag-deflect":2,cog:1,"cube-transition":2,fire:3,"line-scale":5,"line-scale-party":5,"line-scale-pulse-out":5,"line-scale-pulse-out-rapid":5,"line-spin-clockwise-fade":8,"line-spin-clockwise-fade-rotating":8,"line-spin-fade":8,"line-spin-fade-rotating":8,pacman:6,"square-jelly-box":2,"square-loader":1,"square-spin":1,timer:1,"triangle-skew-spin":1},this.originalBodyPosition="",this.originalBodyTop="",this.originalBodywidth=""}var t,i,o;return t=e,(i=[{key:"show",value:function(e){this.setOptions(e),this.addSpinnerStylesheet(),this.generateSpinnerElement(),this.options.lockScroll&&(document.body.style.overflow="hidden",document.documentElement.style.overflow="hidden"),this.generateAndAddOverlayElement()}},{key:"hide",value:function(){this.options.lockScroll&&(document.body.style.overflow="",document.documentElement.style.overflow="");var e=document.getElementById("loading-overlay-stylesheet");e&&(e.disabled=!0,e.parentNode.removeChild(e),document.getElementById(this.options.overlayIDName).remove(),document.getElementById(this.options.spinnerIDName).remove())}},{key:"setOptions",value:function(e){if(void 0!==e)for(var t in e)this.options[t]=e[t]}},{key:"generateAndAddOverlayElement",value:function(){var e="50%";0!==this.options.offsetX&&(e="calc(50% + "+this.options.offsetX+")");var t="50%";if(0!==this.options.offsetY&&(t="calc(50% + "+this.options.offsetY+")"),this.options.containerID&&document.body.contains(document.getElementById(this.options.containerID))){var n='<div id="'.concat(this.options.overlayIDName,'" style="display: block !important; position: absolute; top: 0; left: 0; overflow: auto; opacity: ').concat(this.options.overlayOpacity,"; background: ").concat(this.options.overlayBackgroundColor,'; z-index: 50; width: 100%; height: 100%;"></div><div id="').concat(this.options.spinnerIDName,'" style="display: block !important; position: absolute; top: ').concat(t,"; left: ").concat(e,'; -webkit-transform: translate(-50%); -ms-transform: translate(-50%); transform: translate(-50%); z-index: 9999;">').concat(this.spinner,"</div>"),i=document.getElementById(this.options.containerID);return i.style.position="relative",void i.insertAdjacentHTML("beforeend",n)}var o='<div id="'.concat(this.options.overlayIDName,'" style="display: block !important; position: fixed; top: 0; left: 0; overflow: auto; opacity: ').concat(this.options.overlayOpacity,"; background: ").concat(this.options.overlayBackgroundColor,'; z-index: 50; width: 100%; height: 100%;"></div><div id="').concat(this.options.spinnerIDName,'" style="display: block !important; position: fixed; top: ').concat(t,"; left: ").concat(e,'; -webkit-transform: translate(-50%); -ms-transform: translate(-50%); transform: translate(-50%); z-index: 9999;">').concat(this.spinner,"</div>");document.body.insertAdjacentHTML("beforeend",o)}},{key:"generateSpinnerElement",value:function(){var e=this,t=Object.keys(this.numberOfEmptyDivForSpinner).find((function(t){return t===e.options.spinnerIcon})),n=this.generateEmptyDivElement(this.numberOfEmptyDivForSpinner[t]);this.spinner='<div style="color: '.concat(this.options.spinnerColor,'" class="la-').concat(this.options.spinnerIcon," la-").concat(this.options.spinnerSize,'">').concat(n,"</div>")}},{key:"addSpinnerStylesheet",value:function(){this.setSpinnerStylesheetURL();var e=document.createElement("link");e.setAttribute("id","loading-overlay-stylesheet"),e.setAttribute("rel","stylesheet"),e.setAttribute("type","text/css"),e.setAttribute("href",this.spinnerStylesheetURL),document.getElementsByTagName("head")[0].appendChild(e)}},{key:"setSpinnerStylesheetURL",value:function(){this.spinnerStylesheetURL=this.stylesheetBaseURL+this.options.spinnerIcon+".min.css"}},{key:"generateEmptyDivElement",value:function(e){for(var t="",n=1;n<=e;n++)t+="<div></div>";return t}}])&&n(t.prototype,i),o&&n(t,o),e}();window.JsLoadingOverlay=new i,e.exports=JsLoadingOverlay}]);

!function(){const t=["linkedin","facebook","tiktok"],e=(document.currentScript||Array.from(document.getElementsByTagName("script")).pop()).getAttribute("src").split("?")[1]||"",i=new URLSearchParams(e).get("platform");if(!i)return;const n=i.split(",").map((t=>t.trim().toLowerCase())).filter((e=>{const i=t.includes(e);return i}));var o,s,a,r,d,c;n.includes("linkedin")&&function(t){window.lintrk||(window.lintrk=function(t,e){window.lintrk.q.push([t,e])},window.lintrk.q=[]);var e=document.getElementsByTagName("script")[0],i=document.createElement("script");i.type="text/javascript",i.async=!0,i.src="https://snap.licdn.com/li.lms-analytics/insight.min.js",e.parentNode.insertBefore(i,e)}(),n.includes("facebook")&&(o=window,s=document,a="script",o.fbq||(r=o.fbq=function(){r.callMethod?r.callMethod.apply(r,arguments):r.queue.push(arguments)},o._fbq||(o._fbq=r),r.push=r,r.loaded=!0,r.version="2.0",r.queue=[],(d=s.createElement(a)).async=!0,d.src="https://connect.facebook.net/en_US/fbevents.js",(c=s.getElementsByTagName(a)[0]).parentNode.insertBefore(d,c))),n.includes("tiktok")&&function(t,e,i){t.TiktokAnalyticsObject=i;var n=t[i]=t[i]||[];n.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"],n.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var o=0;o<n.methods.length;o++)n.setAndDefer(n,n.methods[o]);n.instance=function(t){for(var e=n._i[t]||[],i=0;i<n.methods.length;i++)n.setAndDefer(e,n.methods[i]);return e},n.load=function(t,e){var o="https://analytics.tiktok.com/i18n/pixel/events.js";n._i=n._i||{},n._i[t]=[],n._i[t]._u=o,n._t=n._t||{},n._t[t]=+new Date,n._o=n._o||{},n._o[t]=e||{},(e=document.createElement("script")).type="text/javascript",e.async=!0,e.src=o+"?sdkid="+t+"&lib="+i,(t=document.getElementsByTagName("script")[0]).parentNode.insertBefore(e,t)}}(window,document,"ttq")}(),"function"!=typeof Array.prototype.indexOf&&(Array.prototype.indexOf=function(t){for(var e=0;e<this.length;e++)if(this[e]===t)return e;return-1}),window.addEventListener("DOMContentLoaded",(function(){for(var t=document.querySelectorAll("a"),e=0;e<t.length;e++)t[e].href.indexOf("/cta")}),!0),window.gass=function(){function t(t){for(var e=0;e<t.length;e++)this[e]=t[e];this.length=t.length}t.prototype.forEach=function(t){return this.map(t),this},t.prototype.map=function(t){for(var e=[],i=0;i<this.length;i++)e.push(t.call(this,this[i],i));return e},t.prototype.mapOne=function(t){var e=this.map(t);return e.length>1?e:e[0]},t.prototype.text=function(t){return void 0!==t?this.forEach((function(e){e.innerText=t})):this.mapOne((function(t){return t.innerText}))},t.prototype.html=function(t){return void 0!==t?this.forEach((function(e){e.innerHTML=t})):this.mapOne((function(t){return t.innerHTML}))},t.prototype.addClass=function(t){var e="";if("string"!=typeof t)for(var i=0;i<t.length;i++)e+=" "+t[i];else e=" "+t;return this.forEach((function(t){t.className+=e}))},t.prototype.removeClass=function(t){return this.forEach((function(e){for(var i,n=e.className.split(" ");(i=n.indexOf(t))>-1;)n=n.slice(0,i).concat(n.slice(++i));e.className=n.join(" ")}))},t.prototype.attr=function(t,e){return void 0!==e?this.forEach((function(i){i.setAttribute(t,e)})):this.mapOne((function(e){return e.getAttribute(t)}))},t.prototype.append=function(t){return this.forEach((function(e,i){t.forEach((function(t){e.appendChild(i>0?t.cloneNode(!0):t)}))}))},t.prototype.prepend=function(t){return this.forEach((function(e,i){for(var n=t.length-1;n>-1;n--)e.insertBefore(i>0?t[n].cloneNode(!0):t[n],e.firstChild)}))},t.prototype.remove=function(){return this.forEach((function(t){return t.parentNode.removeChild(t)}))},t.prototype.on=document.addEventListener?function(t,e){return this.forEach((function(i){i.addEventListener(t,e,!1)}))}:document.attachEvent?function(t,e){return this.forEach((function(i){i.attachEvent("on"+t,e)}))}:function(t,e){return this.forEach((function(i){i["on"+t]=e}))},t.prototype.off=document.removeEventListener?function(t,e){return this.forEach((function(i){i.removeEventListener(t,e,!1)}))}:document.detachEvent?function(t,e){return this.forEach((function(i){i.detachEvent("on"+t,e)}))}:function(t,e){return this.forEach((function(e){e["on"+t]=null}))},t.prototype.updatelink=function(t){if(e.countpost<2)if(null==e.getCookie("visitor_id")&&0==e.countpost){try{e.post_visit(e.campaign_id,"v_visit",(function(){e.post_visit(e.campaign_id,"v_cron",(function(){}))}))}catch(t){(i=new FormData).append("message",t.message),i.append("stack",t.stack),i.append("url",window.location.href),i.append("project_key",e.pkey),e.request_post("https://"+e.subdomain+"/api.html?act=bug_js_error",i,(function(t){}))}null!=t&&t(e)}else{try{e.visitor_id=e.getCookie("visitor_id"),e.updateCta(),e.post_visit(e.campaign_id,"v_visit",(function(){e.post_visit(e.campaign_id,"v_cron",(function(){}))}))}catch(t){var i;(i=new FormData).append("message",t.message),i.append("stack",t.stack),i.append("url",window.location.href),i.append("project_key",e.pkey),e.request_post("https://"+e.subdomain+"/api.html?act=bug_js_error",i,(function(t){}))}null!=t&&t(e)}};var e={version:"0.0.1",interval:2e3,cta_hidden:0,use_form_ig:0,use_form_fb:0,use_form:0,send_cta_js:0,back_view:0,debug:!1,timer:null,timer1:null,timer2:null,timer3:null,param_get:{},connector:[],cctors:[],CTAeventName:null,pkey:null,page_url:null,ip:null,browser_agent:null,domain:null,subdomain:null,id:null,visitor_id:null,fbp:null,fbc:null,ref:null,fbclid:null,gclid:null,ttclid:null,adw_tag:null,_ttp:null,countpost:0,domload:0,divisi:null,form:[],lintrk:null,use_widget:0,widgetConfig:null,formConfig:null,formConfigJson:null,link_cta:[],colorMap:{blue:{bg:"#e3f0ff",border:"#2196f3",text:"#0d47a1",button:"#2196f3",buttonText:"#fff",close:"#0d47a1"},green:{bg:"#e8f5e9",border:"#43a047",text:"#1b5e20",button:"#43a047",buttonText:"#fff",close:"#1b5e20"},grey:{bg:"#f5f5f5",border:"#757575",text:"#212121",button:"#757575",buttonText:"#fff",close:"#212121"},red:{bg:"#ffebee",border:"#e53935",text:"#b71c1c",button:"#e53935",buttonText:"#fff",close:"#b71c1c"},orange:{bg:"#fff3e0",border:"#ff9800",text:"#e65100",button:"#ff9800",buttonText:"#fff",close:"#e65100"},purple:{bg:"#f3e5f5",border:"#9c27b0",text:"#4a148c",button:"#9c27b0",buttonText:"#fff",close:"#4a148c"},teal:{bg:"#e0f2f1",border:"#009688",text:"#004d40",button:"#009688",buttonText:"#fff",close:"#004d40"},yellow:{bg:"#fffde7",border:"#ffeb3b",text:"#f57f17",button:"#ffeb3b",buttonText:"#333",close:"#f57f17"},pink:{bg:"#fce4ec",border:"#e91e63",text:"#880e4f",button:"#e91e63",buttonText:"#fff",close:"#880e4f"},brown:{bg:"#efebe9",border:"#795548",text:"#3e2723",button:"#795548",buttonText:"#fff",close:"#3e2723"},indigo:{bg:"#e8eaf6",border:"#3f51b5",text:"#1a237e",button:"#3f51b5",buttonText:"#fff",close:"#1a237e"},cyan:{bg:"#e0f7fa",border:"#00bcd4",text:"#006064",button:"#00bcd4",buttonText:"#fff",close:"#006064"},lime:{bg:"#f9fbe7",border:"#cddc39",text:"#827717",button:"#cddc39",buttonText:"#333",close:"#827717"},amber:{bg:"#fff8e1",border:"#ffc107",text:"#ff6f00",button:"#ffc107",buttonText:"#333",close:"#ff6f00"},deeporange:{bg:"#fbe9e7",border:"#ff5722",text:"#bf360c",button:"#ff5722",buttonText:"#fff",close:"#bf360c"},lightblue:{bg:"#e1f5fe",border:"#03a9f4",text:"#01579b",button:"#03a9f4",buttonText:"#fff",close:"#01579b"},lightgreen:{bg:"#f1f8e9",border:"#8bc34a",text:"#33691e",button:"#8bc34a",buttonText:"#333",close:"#33691e"},deepPurple:{bg:"#ede7f6",border:"#673ab7",text:"#311b92",button:"#673ab7",buttonText:"#fff",close:"#311b92"},black:{bg:"#222",border:"#000",text:"#fff",button:"#000",buttonText:"#fff",close:"#fff"},white:{bg:"#fff",border:"#eee",text:"#333",button:"#eee",buttonText:"#333",close:"#333"},default:{bg:"#fff",border:"#c36",text:"#333",button:"#c36",buttonText:"#fff",close:"#333"}},getCookie:function(t){const e=`; ${document.cookie}`.split(`; ${t}=`);if(2===e.length)return e.pop().split(";").shift()},run:function(i,n){var o=this;if(i="object"==typeof i&&i||{},this.connector=i.connector||this.connector,this.pkey=i.pkey||this.pkey,this.interval=i.interval||this.interval,this.cta_hidden=i.cta_hidden||this.cta_hidden,this.use_form_ig=i.use_form_ig||this.use_form_ig,this.use_form_fb=i.use_form_fb||this.use_form_fb,this.use_form=i.use_form||this.use_form,this.send_cta_js=i.send_cta_js||this.send_cta_js,this.debug=i.debug||this.debug,this.formConfigJson=i.formConfigJson||this.formConfigJson,this.formConfig=i.formConfig||{title:"Contact",fields:{firstname:{placeholder:"First Name",required:!1},phone:{placeholder:"Phone",required:!0},message:{placeholder:"Message",required:!1}},submitButton:"Submit",closeButton:"×",colorTemplate:"default"},this.use_widget=i.use_widget||this.use_widget,null!=this.formConfigJson&&(this.formConfig=JSON.parse(atob(this.formConfigJson))),1==this.use_widget&&(this.widgetConfig=i.widgetConfig||{buttonText:"Chat WhatsApp",theme:"green",buttonPosition:"bottom-right",buttonSize:"small",showForm:!1,csList:[{name:"CS WA",number:"",divisi:"lead",message:"[_gid_] Halo, saya ingin bertanya",description:"Spesialisasi: Produk A & B"}],csHeaderTitle:"Customer Service",csHeaderDescription:"Pilih CS yang ingin Anda hubungi",csButtonText:"Chat WhatsApp"}),this.back_view=i.back_view||this.back_view,this.subdomain=i.subdomain,null!=i.adw_tag&&(this.adw_tag=i.adw_tag),1==o.cta_hidden)for(var s=document.querySelectorAll("a"),a=0;a<s.length;a++)-1!=s[a].href.indexOf("/cta")&&(s[a].style.display="none");window.addEventListener("load",(function(){o.domload=1}));var r=window.location.href;r=(r=r.replace("https://www.","")).replace("http://www.","");try{if(this.domain=r.replace("http://","").replace("https://","").split(/[/?#]/)[0],-1!==window.location.href.indexOf("&")){var d=window.location.href.split("?")[1].split("&"),c={};for(a=0;a<d.length;a++){var p=d[a].split("=");c[decodeURIComponent(p[0])]=decodeURIComponent(p[1])}this.param_get=c}void 0!==document.referrer&&(this.ref=document.referrer),window.clearInterval(this.timer),window.clearInterval(this.timer1),window.clearInterval(this.timer2);var h=new t(this.get("a"));o.request_get("https://ip.gass.co.id/",(function(t){if(1==t.code){o.ip=t.msg;var e=t.msg;let i=new Date;i.setTime(i.getTime()+864e5);const n="expires="+i.toUTCString();document.cookie="ip_gass="+e+"; "+n+"; path=/"}})),h.updatelink((function(t){if(void 0!==n){if(1==o.use_widget){e.createWhatsAppWidget(o.widgetConfig)}n(t)}})),o.timer=window.setInterval((function(){o.fbp=o.getCookie("_fbp"),void 0!==o.fbp&&(o.post_visit(o.campaign_id,"v_update",(function(){})),window.clearInterval(o.timer))}),2e3),o.timer1=window.setInterval((function(){o.fbc=o.getCookie("_fbc"),void 0!==o.fbc&&(o.post_visit(o.campaign_id,"v_update",(function(){})),window.clearInterval(o.timer1))}),2e3),o.timer2=window.setInterval((function(){void 0!==o.getCookie("client_id")&&(o.post_visit(o.campaign_id,"v_update",(function(){})),window.clearInterval(o.timer2))}),2e3),o.timer3=window.setInterval((function(){var t=o.getCookie("_ttp");void 0!==t&&(o._ttp=t,o.post_visit(o.campaign_id,"v_update",(function(){})),window.clearInterval(o.timer3))}),2e3),o.timer_link(o),setTimeout((function(){1!=o.use_form_ig&&1!=o.use_form_fb&&1!=o.use_form||o.enableForm()}),1e3)}catch(t){var l=new FormData;l.append("message",t.message),l.append("stack",t.stack),l.append("url",r),l.append("project_key",o.pkey),o.request_post("https://"+o.subdomain+"/api.html?act=bug_js_error",l,(function(t){}))}},timer_link:function(t){t.updateCta(),setInterval((function(){null!=t&&t.updateCta()}),2e3),setTimeout((function(){null!=t&&(t.debug,t.updateCta())}),5e3),1==t.back_view&&(history.pushState({page:1},"",""),window.addEventListener("popstate",(function(e){var i=t.get("a");t.getCookie("visitor_id");i.forEach((function(e){void 0===t.visitor_id||-1==e.href.indexOf("https://"+t.subdomain+"/cta")&&-1==e.href.indexOf("http://"+t.subdomain+"/cta")||t.post_click(e.href)}))})),setTimeout((function(){if(null!=t){var e=t.get("a");t.getCookie("visitor_id");e.forEach((function(e){void 0===t.visitor_id||-1==e.href.indexOf("https://"+t.subdomain+"/cta")&&-1==e.href.indexOf("http://"+t.subdomain+"/cta")||t.post_click(e.href)}))}}),1e4))},append_googletag:function(t,e,i){var n=t.getElementsByTagName(e)[0],o=t.createElement(e);o.async=!0,o.src="https://www.googletagmanager.com/gtag/js?id="+i,n.parentNode.insertBefore(o,n)},gtag:function(){window.dataLayer=window.dataLayer||[],dataLayer.push(arguments)},check_src:function(t){for(var e=document.getElementsByTagName("script"),i=0;i<e.length;i++)if(e[i].getAttribute("src")===t)return!0;return!1},post_visit:function(e,i,n){var o=this;o.countpost++,o.ip=o.getCookie("ip_gass"),o.fbp=o.getCookie("_fbp"),o.fbc=o.getCookie("_fbc");var s=new FormData;s=o.appendFormdata(s),o.request_post("https://"+o.subdomain+"/api.html?act="+i,s,(function(e){if(null!=e.visitor_id){o.visitor_id=e.visitor_id;var i=e.visitor_id;let n=new Date;n.setTime(n.getTime()+2592e6);const s="expires="+n.toUTCString();document.cookie="visitor_id="+i+"; "+s+"; path=/; SameSite=None; Secure",null!=e.connector&&(o.cctors=e.connector,Object.keys(e.connector).forEach((t=>{if("googleads"==e.connector[t].type){var i=e.connector[t].data.global_tag;null!=i&&0==o.check_src("https://www.googletagmanager.com/gtag/js?id="+i)&&(o.append_googletag(document,"script",i),o.gtag("js",new Date),o.gtag("config",i),o.gtag("event","page_view",{send_to:i,user_id:e.connector[t].data.account_id}))}else if("google analytic"==e.connector[t].type){var n=e.connector[t].data.measurement_id;0==o.check_src("https://www.googletagmanager.com/gtag/js?id="+n)&&(o.append_googletag(document,"script",n),o.gtag("js",new Date),o.gtag("config",n),o.gtag("get",n,"client_id",(function(t){o.param_get.clientId=t,document.cookie="client_id="+t+"; "+s+"; path=/"})))}else"facebook"==e.connector[t].type?(o.CTAeventName=e.connector[t].data.event&&e.connector[t].data.event.cta?e.connector[t].data.event.cta:"AddToCart",o.fbq=fbq,o.fbq("init",e.connector[t].data.pixel_id,{external_id:o.visitor_id,eventID:"ViewContent-"+o.visitor_id}),o.fbq("track","ViewContent")):"tiktok"==e.connector[t].type?(o.ttq=ttq,o.ttq.load(e.connector[t].data.pixel_id),o.ttq.page(),o.ttq.track("ViewContent",{user:[{external_id:e.connector[t].data.visitor_id_hash}],event_id:"ViewContent-"+o.visitor_id})):"linkedin"==e.connector[t].type&&(_linkedin_partner_id=e.connector[t].data.partner_id,window._linkedin_event_id="ViewContent-"+o.visitor_id,window._linkedin_data_partner_ids=window._linkedin_data_partner_ids||[],window._linkedin_data_partner_ids.push(_linkedin_partner_id))}))),o.updateCta();new t(o.get("a"))}null!=n&&n(o)}))},post_click:function(t){for(var e=this,i=t.substr(1).split("&"),n={},o=0;o<i.length;o++){var s=i[o].split("=");n[decodeURIComponent(s[0])]=decodeURIComponent(s[1])}e.countpost++,e.fbp=e.getCookie("_fbp"),e.fbc=e.getCookie("_fbc"),e.visitor_id=e.getCookie("visitor_id");var a=new FormData;if(void 0!==typeof e.visitor_id&&null!==e.visitor_id&&a.append("visitor_id",e.visitor_id),void 0!==n.p&&a.append("project_key",n.p),void 0!==n.divisi&&null!==n.divisi&&a.append("divisi",n.divisi),void 0!==n.d&&null!==n.d&&a.append("divisi",n.d),void 0!==n.msg){var r=n.msg;r=r.replace(/\+/g," "),a.append("msg",r)}e.isMobileDevice()?a.append("use_deeplink",1):a.append("use_deeplink",0),a=e.appendFormdata(a),e.request_post("https://"+e.subdomain+"/api.html?act=v_cta",a,(function(t){e.isInstagramBrowser()?1==e.use_form_ig||0==e.use_form&&(window.location=t.wa_url):e.isFacebookBrowser()?1==e.use_form_fb||0==e.use_form&&(window.location=t.wa_url):void 0!==n.form||0==e.use_form&&(window.location=t.wa_url)}))},isMobileDevice:function(t){return void 0!==window.orientation||-1!==navigator.userAgent.indexOf("IEMobile")},request_post:function(t,e,i){var n=new XMLHttpRequest;n.open("POST",t,!0),n.onreadystatechange=function(){if(4===n.readyState)if(n.status>=200&&n.status<400){var t=n.responseText;if(t)try{i(JSON.parse(t))}catch(e){i(t)}}else{i({code:0,msg:"Request failed"})}},n.onerror=function(){i({code:0,msg:"Request Error"})},n.send(e)},request_get:function(t,e){var i=new XMLHttpRequest;i.open("GET",t,!0),i.onreadystatechange=function(){if(i.readyState===XMLHttpRequest.DONE)if(i.status>=200&&i.status<400){var t=i.responseText;if(t)e({code:1,msg:t})}else{e({code:0,msg:"Request failed"})}},i.onerror=function(){e({code:0,msg:"Request Error"})},i.send()},appendFormdata:function(t){var e=this;e.ip=e.getCookie("ip_gass"),t.append("domain",e.domain),t.append("page_url",window.location),void 0!==e.ip&&t.append("ip",e.ip),void 0!==e.visitor_id&&null!==e.visitor_id?t.append("visitor_id",e.visitor_id):(e.visitor_id=e.getCookie("visitor_id"),void 0!==typeof e.visitor_id&&null!==e.visitor_id&&t.append("visitor_id",e.visitor_id)),void 0!==e.pkey&&null!==e.pkey&&t.append("project_key",e.pkey),void 0!==e.divisi&&null!==e.divisi&&t.append("divisi",e.divisi),void 0!==e.fbc&&void 0!==e.fbc&&null!==e.fbc&&t.append("fbc",e.fbc),void 0!==e.fbp&&void 0!==e.fbp&&null!==e.fbp&&t.append("fbp",e.fbp),void 0!==e._ttp&&void 0!==e._ttp&&null!==e._ttp&&t.append("_ttp",e._ttp),null!==e.ref&&t.append("ref",e.ref);var i=e.getCookie("client_id");void 0!==i&&t.append("clientId",i),e.connector.forEach((function(e){t.append("connector[]",e)}));const n=window.location.search;return new URLSearchParams(n).forEach(((i,n)=>{""!==n&&null!=i&&t.append(n,e.safeDecode(i))})),Object.keys(e.param_get).forEach((i=>{""!==i&&void 0!==e.param_get[i]&&null!==e.param_get[i]&&t.append(i,e.safeDecode(e.param_get[i]))})),t},safeDecode:function(t){try{return decodeURIComponent(t)}catch(e){return t}},enableForm:function(){var t=this,e=t.formConfig&&t.formConfig.colorTemplate?t.formConfig.colorTemplate:"default",i=t.colorMap&&t.colorMap[e]?t.colorMap[e]:t.colorMap.default;const n=document.createElement("div");n.id="popupForm",n.style.display="none",n.style.position="fixed",n.style.top="0",n.style.left="0",n.style.width="100%",n.style.height="100%",n.style.backgroundColor="rgba(0, 0, 0, 0.5)",n.style.justifyContent="center",n.style.alignItems="center",n.style.zIndex="99";const o=document.createElement("div");o.style.backgroundColor=i.bg,o.style.padding="20px",o.style.borderRadius="5px",o.style.width="300px",o.style.boxShadow="0 4px 8px rgba(0, 0, 0, 0.2)",o.style.textAlign="center",o.style.border="1px solid "+i.border;const s=document.createElement("span");s.innerHTML="&times;",s.style.float="right",s.style.fontSize="24px",s.style.cursor="pointer",s.style.color=i.close,s.onclick=function(){n.style.display="none"};const a=document.createElement("h2");a.innerText=t.formConfig.title||"Contact",a.style.color=i.text;const r=document.createElement("p");r.innerText=t.formConfig.notif||"";const d=document.createElement("form");let c,p,h;d.id="ContactForm",t.formConfig.fields.firstname&&(c=document.createElement("input"),c.type="text",c.id="firstname",c.name="firstname",c.placeholder=t.formConfig.fields.firstname.placeholder||"First Name",c.required=t.formConfig.fields.firstname.required||!1,d.appendChild(document.createElement("br")),d.appendChild(c)),t.formConfig.fields.phone&&(p=document.createElement("input"),p.type="tel",p.id="phone",p.name="phone",p.placeholder=t.formConfig.fields.phone.placeholder||"Phone",p.required=!1!==t.formConfig.fields.phone.required,d.appendChild(document.createElement("br")),d.appendChild(document.createElement("br")),d.appendChild(p)),t.formConfig.fields.message&&(h=document.createElement("textarea"),h.id="msg",h.name="msg",h.placeholder=t.formConfig.fields.message.placeholder||"Message",h.required=t.formConfig.fields.message.required||!1,d.appendChild(document.createElement("br")),d.appendChild(document.createElement("br")),d.appendChild(h));const l=document.createElement("button");l.type="submit",l.innerText=t.formConfig.submitButton||"Submit",l.style.borderStyle="none",l.style.padding="0",l.style.display="inline-block",l.style.fontWeight="400",l.style.color=i.buttonText,l.style.textAlign="center",l.style.whiteSpace="nowrap",l.style.userSelect="none",l.style.backgroundColor=i.button,l.style.border="1px solid "+i.border,l.style.padding=".5rem 1rem",l.style.fontSize="1rem",l.style.borderRadius="3px",l.style.transition="all .3s",l.style.cursor="pointer",d.appendChild(document.createElement("br")),d.appendChild(document.createElement("br")),d.appendChild(l),o.appendChild(s),o.appendChild(a),o.appendChild(r),o.appendChild(d),n.appendChild(o),t.form=d,document.body.appendChild(n),d.addEventListener("submit",(function(e){e.preventDefault();var i=new FormData(d);void 0!==typeof t.visitor_id&&null!==t.visitor_id&&i.append("visitor_id",t.visitor_id),i=t.appendFormdata(i),t.request_post("https://"+t.subdomain+"/api.html?act=v_form",i,(function(e){n.style.display="none",1==e.code?(t.showAlertForm(e.result.msg,0),setTimeout((function(){}),2e3)):t.showAlertForm(e.result.msg,1)}))}));d.querySelectorAll("input, textarea").forEach((t=>{t.style.width="90%",t.style.border="1px solid "+i.border,t.style.borderRadius="3px",t.style.padding=".5rem 1rem",t.style.transition="all .3s",t.style.overflow="visible",t.style.fontFamily="inherit",t.style.fontSize="1rem",t.style.lineHeight="1.5",t.style.margin="0",t.style.color=i.text,t.style.backgroundColor=i.bg})),window.onclick=function(t){t.target===n&&(n.style.display="none")}},showFormPopup:function(t){document.getElementById("popupForm")||this.enableForm();for(var e=t.substr(1).split("&"),i={},n=0;n<e.length;n++){var o=e[n].split("=");i[decodeURIComponent(o[0])]=decodeURIComponent(o[1])}void 0!==i.divisi&&(this.divisi=i.divisi);const s=document.getElementById("popupForm");s&&(s.style.display="flex");const a=document.getElementById("msg");if(a&&void 0!==i.msg){var r=i.msg;r=(r=r.replace(/\+/g," ")).replaceAll("%break%","\n"),a.value=r}},showAlertForm:function(t,e){var i=this,n=i.formConfig&&i.formConfig.colorTemplate?i.formConfig.colorTemplate:"default",o=i.colorMap&&i.colorMap[n]?i.colorMap[n]:i.colorMap.default;const s=document.createElement("div");s.style.position="fixed",s.style.top="0",s.style.left="0",s.style.width="100%",s.style.height="100%",s.style.backgroundColor="rgba(0, 0, 0, 0.5)",s.style.display="flex",s.style.alignItems="center",s.style.justifyContent="center",s.style.zIndex="99";const a=document.createElement("div");a.style.width="300px",a.style.padding="20px",a.style.backgroundColor=o.bg,a.style.borderRadius="5px",a.style.boxShadow="0 4px 8px rgba(0, 0, 0, 0.2)",a.style.textAlign="center",a.style.position="relative",a.style.border="1px solid "+o.border;const r=document.createElement("div");r.innerHTML=t,r.style.margin="0",r.style.fontSize="1rem",r.style.color=o.text,r.style.userSelect="text";const d=document.createElement("button");d.innerText="Close",d.style.marginTop="20px",d.style.padding="10px 20px",d.style.fontSize="1rem",d.style.color=o.buttonText,d.style.backgroundColor=o.button,d.style.border="none",d.style.borderRadius="3px",d.style.cursor="pointer",d.onclick=function(){if(document.body.removeChild(s),e){var t=document.getElementById("popupForm");t&&(t.style.display="flex")}},a.appendChild(r),a.appendChild(d),s.appendChild(a),document.body.appendChild(s)},isInstagramBrowser:function(){const t=navigator.userAgent||navigator.vendor||window.opera;return/Instagram/i.test(t)},isFacebookBrowser:function(){const t=navigator.userAgent||navigator.vendor||window.opera;return/FBAN|FBAV/i.test(t)},updateCta:function(){var t=this;if(t.debug,0==t.domload)return t.debug,void setTimeout((()=>{t.updateCta()}),1e3);if(t.visitor_id||(t.visitor_id=t.getCookie("visitor_id"),t.visitor_id)){var e=this.get("a");t.debug,e.forEach((function(e,i){try{var n=e.href;if(-1!==n.indexOf("/cta")||-1!==n.indexOf("cta?")){t.debug;var o={};try{var s=new URL(n);s.search&&s.searchParams.forEach(((t,e)=>{o[e]=t}))}catch(t){if(-1!==n.indexOf("?"))for(var a=n.split("?")[1].split("&"),r=0;r<a.length;r++){var d=a[r].split("=");2===d.length&&(o[decodeURIComponent(d[0])]=decodeURIComponent(d[1]))}}if(o.v||o.visitor_id)t.debug;else{t.debug;var c=e.cloneNode(!0),p=-1!==n.indexOf("?")?"&":"?",h=n+p+"cta=1&v="+t.visitor_id;-1===n.indexOf("p=")&&t.pkey&&(h+="&p="+t.pkey),c.href=h;var l={originalUrl:n,updatedUrl:h,visitor_id:t.visitor_id,timestamp:(new Date).toISOString(),element:c};-1===t.link_cta.findIndex((function(t){return t.originalUrl===n}))&&(t.link_cta.push(l),t.debug),c.addEventListener("click",(function(e){if(e.preventDefault(),t.debug,1==t.send_cta_js&&t.CTAeventName){try{t.fbq=fbq,t.fbq("track",t.CTAeventName,{},{eventID:t.CTAeventName+"-"+t.visitor_id}),t.debug}catch(e){}setTimeout((()=>{t.post_click(h)}),1e3)}else t.post_click(h);(!t.isInstagramBrowser()||1!=t.use_form_ig&&1!=t.use_form)&&(!t.isFacebookBrowser()||1!=t.use_form_fb&&1!=t.use_form)?(1==t.use_form_ig||1==t.use_form_fb||1==t.use_form||o.form)&&t.showFormPopup(h):t.showFormPopup(h)})),c.style.display="",e.replaceWith(c),t.get(c).attr("target","_self")}}}catch(t){}})),t.debug}else t.debug},getUpdatedCtaLinks:function(){return this.link_cta},getCtaLinksCount:function(){return this.link_cta.length},findCtaLink:function(t){return this.link_cta.find((function(e){return e.originalUrl===t}))||null},removeCtaLink:function(t){var e=this.link_cta.findIndex((function(e){return e.originalUrl===t}));return-1!==e&&(this.link_cta.splice(e,1),this.debug,!0)},clearCtaLinks:function(){this.link_cta=[],this.debug},get:function(e){return new t("string"==typeof e?document.querySelectorAll(e):e.length?e:[e])},create:function(e,i){var n=new t([document.createElement(e)]);if(i)for(var o in i.className&&(n.addClass(i.className),delete i.className),i.text&&(n.text(i.text),delete i.text),i)i.hasOwnProperty(o)&&n.attr(o,i[o]);return n},WhatsAppWidget:function(t){var i={buttonText:"Chat WhatsApp",buttonIcon:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NzguMTY1IDQ3OC4xNjUiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDQ3OC4xNjUgNDc4LjE2NSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiPjxwYXRoIGQ9Ik00NzguMTY1IDIzMi45NDZjMCAxMjguNTY3LTEwNS4wNTcgMjMyLjk2Ni0yMzQuNjc5IDIzMi45NjYtNDEuMTAyIDAtNzkuODE0LTEwLjU5OS0xMTMuNDQ1LTI4Ljk2OUwwIDQ3OC4xNjVsNDIuNDM3LTEyNS4wNGMtMjEuNDM4LTM1LjA2NS0zMy43Ny03Ni4yMDctMzMuNzctMTIwLjE1OUM4LjY2NyAxMDQuMzQgMTEzLjc2MyAwIDI0My40ODUgMGMxMjkuNjIzIDAgMjM0LjY4IDEwNC4zNCAyMzQuNjggMjMyLjk0NnpNMjQzLjQ4NSAzNy4wOThjLTEwOC44MDIgMC0xOTcuNDIyIDg3LjgwMy0xOTcuNDIyIDE5NS44NjggMCA0Mi45MTUgMTMuOTg2IDgyLjYwMyAzNy41NzYgMTE0Ljg3OWwtMjQuNTg2IDcyLjU0MiA3NS44NDktMjMuOTY4YzMxLjEyMSAyMC40ODEgNjguNDU3IDMyLjI5NiAxMDguNTgzIDMyLjI5NiAxMDguNzIzIDAgMTk3LjMyMy04Ny44NDMgMTk3LjMyMy0xOTUuOTA4IDAtMTA3Ljg4Ni04OC42LTE5NS43MDktMTk3LjMyMy0xOTUuNzA5ek0zNjEuOTMxIDI4Ni42MmMtMS4zOTUtMi4zMzEtNS4yMi0zLjc0Ni0xMC44OTgtNi44MTQtNS45MTctMi44NDktMzQuMDg5LTE2LjQ5Ny0zOS41MDgtMTguMzctNS4xNi0xLjkxMy04Ljk4Ni0yLjg0OS0xMi44MTEgMi44MjktNC4wMDUgNS42MzgtMTQuOTAzIDE4LjYyOS0xOC4yMyAyMi4zNTQtMy41NDYgMy43ODUtNi44NTQgNC4yNjQtMTIuNTUyIDEuNDM1LTUuNjE4LTIuODA5LTI0LjI2Ny04Ljg2Ni00Ni4yMDMtMjguMzkxLTE3LjA1NS0xNS4wNDItMjguNjctMzMuNzExLTMxLjk5Ny0zOS41MDgtMy40MjctNS43NTgtLjM5OC04LjgyNiAyLjQ3MS0xMS42MzUgMi42OS0yLjU5IDUuNzc4LTYuNzM0IDguNjI3LTEwLjA0MSAyLjk2OS0zLjI4NyAzLjkwNS01LjYzOCA1Ljc5OC05LjQyNCAxLjkxMy0zLjkwNS45MzYtNy4xOTItLjQ3OC0xMC4xNDEtMS40MTUtMi44NDktMTMuMDEtMzAuODgxLTE3Ljc1Mi00Mi4zMzctNC44NDEtMTEuNDE2LTkuNTQzLTkuNTIzLTEyLjg3MS05LjUyMy0zLjQ2NyAwLTcuMjEyLS40NzgtMTEuMTE3LS40NzgtMy43ODUgMC0xMC4wNDEgMS4zOTUtMTUuMzgxIDcuMTkyLTUuMiA1LjY1OC0yMC4xMjMgMTkuNDY1LTIwLjEyMyA0Ny41OTcgMCAyOC4wNTIgMjAuNjAxIDU1LjMwOCAyMy41NSA1OS4wNTMgMi44NjkgMy43ODUgMzkuNzQ3IDYzLjE5NyA5OC4zMDMgODYuMDcgNTguNDc2IDIyLjg3MiA1OC40NzYgMTUuMzIxIDY5LjExNSAxNC4zNjUgMTAuMzgtLjk1NiAzNC4wNjktMTMuODY3IDM4LjgxMS0yNy4wOTYgNC42Ni0xMy40NSA0LjY2LTI0Ljc2NiAzLjI0Ni0yNy4xMzd6IiBmaWxsPSIjRkZGIi8+PC9zdmc+",buttonPosition:"bottom-right",buttonSize:"medium",buttonColor:"#25D366",buttonTextColor:"#ffffff",showForm:!1,formTitle:"Hubungi Kami",formFields:{name:{placeholder:"Nama Lengkap",required:!0},phone:{placeholder:"Nomor WhatsApp",required:!0},message:{placeholder:"Pesan Anda...",required:!1}},submitButton:"Kirim Pesan",theme:"default",customColors:null,whatsappNumber:"",defaultMessage:"Halo, saya ingin bertanya tentang produk Anda.",divisi:"",csList:[],csTitle:"Pilih Customer Service",csButtonText:"Chat dengan CS",csHeaderTitle:"Customer Service",csHeaderDescription:"Pilih CS yang ingin Anda hubungi",autoOpen:!1,delay:0,showOnMobile:!0,showOnDesktop:!0,onFormSubmit:null,onButtonClick:null,onWidgetLoad:null,onCsSelect:null},n={default:{primary:"#25D366",secondary:"#128C7E",background:"#ffffff",text:"#333333",border:"#e0e0e0"},blue:{primary:"#2196F3",secondary:"#1976D2",background:"#ffffff",text:"#333333",border:"#e0e0e0"},green:{primary:"#4CAF50",secondary:"#388E3C",background:"#ffffff",text:"#333333",border:"#e0e0e0"},red:{primary:"#F44336",secondary:"#D32F2F",background:"#ffffff",text:"#333333",border:"#e0e0e0"},orange:{primary:"#FF9800",secondary:"#F57C00",background:"#ffffff",text:"#333333",border:"#e0e0e0"},purple:{primary:"#9C27B0",secondary:"#7B1FA2",background:"#ffffff",text:"#333333",border:"#e0e0e0"},dark:{primary:"#333333",secondary:"#000000",background:"#ffffff",text:"#333333",border:"#e0e0e0"}},o=this;this.mergeConfig=function(t){var e={};for(var n in i)e[n]=i[n];for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},this.getTheme=function(){return this.config&&this.config.customColors?this.config.customColors:n[this.config&&this.config.theme?this.config.theme:"default"]||n.default},this.shouldShowWidget=function(){var t=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);return!(t&&!this.config.showOnMobile)&&!(!t&&!this.config.showOnDesktop)},this.createWidget=function(){this.widget=document.createElement("div"),this.widget.id="whatsapp-widget",this.widget.className="whatsapp-widget",this.createFloatingButton(),this.config.showForm&&this.createFormPopup(),this.config.csList&&this.config.csList.length>0&&this.createFloatingCsList(),this.addStyles(),document.body.appendChild(this.widget)},this.init=function(){this.shouldShowWidget()&&(this.createWidget(),this.addEventListeners(),this.config.delay>0?setTimeout((function(){o.showWidget()}),this.config.delay):this.showWidget(),this.config.autoOpen&&setTimeout((function(){o.showForm()}),2e3),this.config.onWidgetLoad&&this.config.onWidgetLoad(this))},this.config=this.mergeConfig(t),this.theme=this.getTheme(),this.isFormVisible=!1,this.isCsListVisible=!1,this.isWidgetLoaded=!1,this.createFloatingButton=function(){this.floatingButton=document.createElement("div"),this.floatingButton.className="whatsapp-widget-button";var t=document.createElement("div");if(t.className="whatsapp-widget-icon",this.config.buttonIcon.startsWith("data:image/svg+xml")){var e=document.createElement("img");e.src=this.config.buttonIcon,e.alt="WhatsApp",e.style.width="30px",e.style.height="30px",t.appendChild(e)}else t.innerHTML=this.config.buttonIcon;var i=document.createElement("div");i.className="whatsapp-widget-text",i.textContent=this.config.buttonText,this.floatingButton.appendChild(t),this.floatingButton.appendChild(i),this.widget.appendChild(this.floatingButton)},this.createFormPopup=function(){this.formOverlay=document.createElement("div"),this.formOverlay.className="whatsapp-widget-overlay",this.formOverlay.style.display="none",this.formContainer=document.createElement("div"),this.formContainer.className="whatsapp-widget-form";var t=document.createElement("div");for(var e in t.className="whatsapp-widget-form-header",t.innerHTML="<h3>"+this.config.formTitle+'</h3><button class="whatsapp-widget-close">&times;</button>',this.form=document.createElement("form"),this.form.className="whatsapp-widget-form-content",this.config.formFields)if(this.config.formFields.hasOwnProperty(e)){var i=this.config.formFields[e],n=document.createElement("div");n.className="whatsapp-widget-field";var o,s=document.createElement("label");s.textContent=i.placeholder,s.htmlFor=e,"message"===e?(o=document.createElement("textarea")).rows=3:(o=document.createElement("input")).type="phone"===e?"tel":"text",o.id=e,o.name=e,o.placeholder=i.placeholder,i.required&&(o.required=!0),n.appendChild(s),n.appendChild(o),this.form.appendChild(n)}var a=document.createElement("button");a.type="submit",a.className="whatsapp-widget-submit",a.textContent=this.config.submitButton,this.form.appendChild(a),this.formContainer.appendChild(t),this.formContainer.appendChild(this.form),this.formOverlay.appendChild(this.formContainer),this.widget.appendChild(this.formOverlay)},this.createFloatingCsList=function(){this.csListContainer=document.createElement("div"),this.csListContainer.className="whatsapp-widget-cs-floating",this.csListContainer.style.display="none";var t=document.createElement("div");t.className="whatsapp-widget-cs-header",t.innerHTML='<div class="whatsapp-widget-cs-header-content"><div class="whatsapp-widget-cs-header-icon"><img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NzguMTY1IDQ3OC4xNjUiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDQ3OC4xNjUgNDc4LjE2NSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiPjxwYXRoIGQ9Ik00NzguMTY1IDIzMi45NDZjMCAxMjguNTY3LTEwNS4wNTcgMjMyLjk2Ni0yMzQuNjc5IDIzMi45NjYtNDEuMTAyIDAtNzkuODE0LTEwLjU5OS0xMTMuNDQ1LTI4Ljk2OUwwIDQ3OC4xNjVsNDIuNDM3LTEyNS4wNGMtMjEuNDM4LTM1LjA2NS0zMy43Ny03Ni4yMDctMzMuNzctMTIwLjE1OUM4LjY2NyAxMDQuMzQgMTEzLjc2MyAwIDI0My40ODUgMGMxMjkuNjIzIDAgMjM0LjY4IDEwNC4zNCAyMzQuNjggMjMyLjk0NnpNMjQzLjQ4NSAzNy4wOThjLTEwOC44MDIgMC0xOTcuNDIyIDg3LjgwMy0xOTcuNDIyIDE5NS44NjggMCA0Mi45MTUgMTMuOTg2IDgyLjYwMyAzNy41NzYgMTE0Ljg3OWwtMjQuNTg2IDcyLjU0MiA3NS44NDktMjMuOTY4YzMxLjEyMSAyMC40ODEgNjguNDU3IDMyLjI5NiAxMDguNTgzIDMyLjI5NiAxMDguNzIzIDAgMTk3LjMyMy04Ny44NDMgMTk3LjMyMy0xOTUuOTA4IDAtMTA3Ljg4Ni04OC42LTE5NS43MDktMTk3LjMyMy0xOTUuNzA5ek0zNjEuOTMxIDI4Ni42MmMtMS4zOTUtMi4zMzEtNS4yMi0zLjc0Ni0xMC44OTgtNi44MTQtNS45MTctMi44NDktMzQuMDg5LTE2LjQ5Ny0zOS41MDgtMTguMzctNS4xNi0xLjkxMy04Ljk4Ni0yLjg0OS0xMi44MTEgMi44MjktNC4wMDUgNS42MzgtMTQuOTAzIDE4LjYyOS0xOC4yMyAyMi4zNTQtMy41NDYgMy43ODUtNi44NTQgNC4yNjQtMTIuNTUyIDEuNDM1LTUuNjE4LTIuODA5LTI0LjI2Ny04Ljg2Ni00Ni4yMDMtMjguMzkxLTE3LjA1NS0xNS4wNDItMjguNjctMzMuNzExLTMxLjk5Ny0zOS41MDgtMy40MjctNS43NTgtLjM5OC04LjgyNiAyLjQ3MS0xMS42MzUgMi42OS0yLjU5IDUuNzc4LTYuNzM0IDguNjI3LTEwLjA0MSAyLjk2OS0zLjI4NyAzLjkwNS01LjYzOCA1Ljc5OC05LjQyNCAxLjkxMy0zLjkwNS45MzYtNy4xOTItLjQ3OC0xMC4xNDEtMS40MTUtMi44NDktMTMuMDEtMzAuODgxLTE3Ljc1Mi00Mi4zMzctNC44NDEtMTEuNDE2LTkuNTQzLTkuNTIzLTEyLjg3MS05LjUyMy0zLjQ2NyAwLTcuMjEyLS40NzgtMTEuMTE3LS40NzgtMy43ODUgMC0xMC4wNDEgMS4zOTUtMTUuMzgxIDcuMTkyLTUuMiA1LjY1OC0yMC4xMjMgMTkuNDY1LTIwLjEyMyA0Ny41OTcgMCAyOC4wNTIgMjAuNjAxIDU1LjMwOCAyMy41NSA1OS4wNTMgMi44NjkgMy43ODUgMzkuNzQ3IDYzLjE5NyA5OC4zMDMgODYuMDcgNTguNDc2IDIyLjg3MiA1OC40NzYgMTUuMzIxIDY5LjExNSAxNC4zNjUgMTAuMzgtLjk1NiAzNC4wNjktMTMuODY3IDM4LjgxMS0yNy4wOTYgNC42Ni0xMy40NSA0LjY2LTI0Ljc2NiAzLjI0Ni0yNy4xMzd6IiBmaWxsPSIjRkZGIi8+PC9zdmc+" alt="WhatsApp" /></div><div class="whatsapp-widget-cs-header-text"><div class="whatsapp-widget-cs-header-title">'+this.config.csHeaderTitle+"</div>"+(this.config.csHeaderDescription?'<div class="whatsapp-widget-cs-header-description">'+this.config.csHeaderDescription+"</div>":"")+"</div></div>",this.csContent=document.createElement("div"),this.csContent.className="whatsapp-widget-cs-content";for(var e=0;e<this.config.csList.length;e++){var i=this.config.csList[e],n=document.createElement("div");n.className="whatsapp-widget-cs-item",n.innerHTML='<div class="whatsapp-widget-cs-info"><div class="whatsapp-widget-cs-name">'+i.name+"</div>"+(i.description?'<div class="whatsapp-widget-cs-description">'+i.description+"</div>":"")+'</div><button class="whatsapp-widget-cs-button" data-cs-index="'+e+'">'+this.config.csButtonText+"</button>",this.csContent.appendChild(n)}this.csListContainer.appendChild(t),this.csListContainer.appendChild(this.csContent),this.widget.appendChild(this.csListContainer)},this.addStyles=function(){if(!document.getElementById("whatsapp-widget-styles")){var t=document.createElement("style");t.id="whatsapp-widget-styles",t.textContent=this.getStyles(),document.head.appendChild(t)}},this.getStyles=function(){var t=this.getButtonSize(),e=this.getButtonPosition();return'.whatsapp-widget{position:fixed;z-index:9999;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif}.whatsapp-widget-button{position:fixed;'+e+";display:flex;align-items:center;background:"+this.theme.primary+";color:"+this.config.buttonTextColor+";border:none;border-radius:50px;padding:12px 20px;cursor:pointer;box-shadow:0 4px 12px rgba(0,0,0,0.15);transition:all 0.3s ease;font-size:14px;font-weight:500;text-decoration:none;z-index:10000;"+t+"}.whatsapp-widget-button:hover{transform:translateY(-2px);box-shadow:0 6px 16px rgba(0,0,0,0.2);background:"+this.theme.secondary+"}.whatsapp-widget-icon{font-size:30px;margin-right:8px}.whatsapp-widget-text{white-space:nowrap}.whatsapp-widget-overlay{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:10001}.whatsapp-widget-form{background:"+this.theme.background+";border-radius:12px;padding:0;width:90%;max-width:400px;box-shadow:0 10px 30px rgba(0,0,0,0.3);animation:whatsapp-widget-slideIn 0.3s ease}@keyframes whatsapp-widget-slideIn{from{opacity:0;transform:translateY(-20px)}to{opacity:1;transform:translateY(0)}}.whatsapp-widget-form-header{display:flex;justify-content:space-between;align-items:center;padding:20px 20px 0 20px;border-bottom:1px solid "+this.theme.border+"}.whatsapp-widget-form-header h3{margin:0;color:"+this.theme.text+";font-size:18px;font-weight:600}.whatsapp-widget-close{background:none;border:none;font-size:24px;cursor:pointer;color:#999;padding:0;width:30px;height:30px;display:flex;align-items:center;justify-content:center;border-radius:50%;transition:background 0.2s ease}.whatsapp-widget-close:hover{background:#f0f0f0}.whatsapp-widget-form-content{padding:20px}.whatsapp-widget-field{margin-bottom:16px}.whatsapp-widget-field label{display:block;margin-bottom:6px;color:"+this.theme.text+";font-size:14px;font-weight:500}.whatsapp-widget-field input,.whatsapp-widget-field textarea{width:100%;padding:12px;border:1px solid "+this.theme.border+";border-radius:6px;font-size:14px;transition:border-color 0.2s ease;box-sizing:border-box}.whatsapp-widget-field input:focus,.whatsapp-widget-field textarea:focus{outline:none;border-color:"+this.theme.primary+"}.whatsapp-widget-submit{width:100%;padding:12px;background:"+this.theme.primary+";color:white;border:none;border-radius:6px;font-size:16px;font-weight:500;cursor:pointer;transition:background 0.2s ease}.whatsapp-widget-submit:hover{background:"+this.theme.secondary+"}.whatsapp-widget-submit:disabled{background:#ccc;cursor:not-allowed}.whatsapp-widget-cs-content{padding:20px}.whatsapp-widget-cs-item{display:flex;justify-content:space-between;align-items:center;padding:15px 0;border-bottom:1px solid "+this.theme.border+"}.whatsapp-widget-cs-item:last-child{border-bottom:none}.whatsapp-widget-cs-info{flex:1;margin-right:15px}.whatsapp-widget-cs-name{font-weight:600;color:"+this.theme.text+";font-size:16px;margin-bottom:4px}.whatsapp-widget-cs-description{color:#666;font-size:14px}.whatsapp-widget-cs-button{padding:8px 16px;background:"+this.theme.primary+";color:white;border:none;border-radius:6px;font-size:14px;font-weight:500;cursor:pointer;transition:background 0.2s ease;white-space:nowrap}.whatsapp-widget-cs-button:hover{background:"+this.theme.secondary+"}.whatsapp-widget-cs-floating{position:fixed;z-index:9998;background:"+this.theme.background+";border-radius:12px;box-shadow:0 8px 25px rgba(0,0,0,0.15);border:1px solid "+this.theme.border+";animation:whatsapp-widget-cs-slideIn 0.3s ease;max-width:350px;min-width:280px}.whatsapp-widget-cs-header{padding:20px 20px 15px 20px;border-bottom:1px solid "+this.theme.border+";background:linear-gradient(135deg,"+this.theme.primary+" 0%,"+this.theme.secondary+" 100%);border-radius:12px 12px 0 0}.whatsapp-widget-cs-header-content{display:flex;align-items:flex-start;gap:12px}.whatsapp-widget-cs-header-icon{flex-shrink:0;width:32px;height:32px;display:flex;align-items:center;justify-content:center}.whatsapp-widget-cs-header-icon img{width:24px;height:24px;filter:brightness(0) invert(1)}.whatsapp-widget-cs-header-text{flex:1}.whatsapp-widget-cs-header-title{font-size:18px;font-weight:600;color:#ffffff;margin-bottom:5px}.whatsapp-widget-cs-header-description{font-size:14px;color:rgba(255,255,255,0.9);line-height:1.4}@keyframes whatsapp-widget-cs-slideIn{from{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.whatsapp-widget-cs-floating{"+e.replace("bottom","top").replace("top","bottom")+";margin-bottom:70px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-content{padding:15px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-item{padding:12px 0}.whatsapp-widget-cs-floating .whatsapp-widget-cs-button{padding:6px 12px;font-size:13px}@media (max-width:480px){.whatsapp-widget-button{padding:10px 16px;font-size:13px}.whatsapp-widget-icon{font-size:18px;margin-right:6px}.whatsapp-widget-form{width:95%;margin:20px}.whatsapp-widget-cs-item{flex-direction:column;align-items:flex-start}.whatsapp-widget-cs-info{margin-right:0;margin-bottom:10px}.whatsapp-widget-cs-button{width:100%}.whatsapp-widget-cs-floating{max-width:95%;min-width:250px;margin-bottom:60px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-header{padding:15px 15px 10px 15px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-header-content{gap:10px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-header-icon{width:28px;height:28px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-header-icon img{width:20px;height:20px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-header-title{font-size:16px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-header-description{font-size:13px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-content{padding:12px}}"},this.getButtonSize=function(){var t={small:"padding:8px 16px;font-size:12px",medium:"padding:12px 20px;font-size:14px",large:"padding:16px 24px;font-size:16px"};return t[this.config.buttonSize]||t.medium},this.getButtonPosition=function(){var t={"bottom-right":"bottom:20px;right:20px","bottom-left":"bottom:20px;left:20px","top-right":"top:20px;right:20px","top-left":"top:20px;left:20px"};return t[this.config.buttonPosition]||t["bottom-right"]},this.addEventListeners=function(){var t=this;if(this.floatingButton.addEventListener("click",(function(e){e.preventDefault(),t.handleButtonClick()})),this.form&&this.form.addEventListener("submit",(function(e){e.preventDefault(),t.handleFormSubmit()})),this.formOverlay){var e=this.formOverlay.querySelector(".whatsapp-widget-close");e&&e.addEventListener("click",(function(){t.hideForm()})),this.formOverlay.addEventListener("click",(function(e){e.target===t.formOverlay&&t.hideForm()}))}if(this.csListContainer)for(var i=this.csListContainer.querySelectorAll(".whatsapp-widget-cs-button"),n=0;n<i.length;n++)i[n].addEventListener("click",(function(e){var i=parseInt(e.target.getAttribute("data-cs-index"));t.handleCsSelect(i)}));document.addEventListener("keydown",(function(e){"Escape"===e.key&&(t.isFormVisible||t.isCsListVisible)&&(t.isFormVisible?t.hideForm():t.isCsListVisible&&t.hideCsList())}))},this.handleButtonClick=function(){this.config.onButtonClick&&this.config.onButtonClick(this),this.config.showForm?this.showForm():this.config.csList&&this.config.csList.length>0?this.toggleCsList():this.openWhatsApp()},this.handleFormSubmit=function(){var t=new FormData(this.form),e={};for(var i of t.entries())e[i[0]]=i[1];this.config.onFormSubmit?this.config.onFormSubmit(e,this):this.submitForm(e)},this.submitForm=function(t){var e=this.buildWhatsAppMessage(t);this.openWhatsApp(e),this.hideForm()},this.buildWhatsAppMessage=function(t){var e=this.config.defaultMessage;return t.message&&(e=t.message),t.name&&(e="Halo, saya "+t.name+".\n\n"+e),encodeURIComponent(e)},this.openWhatsApp=function(t){var e="https://wa.me/"+this.config.whatsappNumber.replace(/\D/g,"")+"?text="+t;window.open(e,"_blank")},this.showWidget=function(){this.widget.style.display="block",this.isWidgetLoaded=!0},this.hideWidget=function(){this.widget.style.display="none"},this.showForm=function(){if(this.formOverlay){this.formOverlay.style.display="flex",this.isFormVisible=!0;var t=this.form.querySelector("input, textarea");t&&t.focus()}},this.hideForm=function(){this.formOverlay&&(this.formOverlay.style.display="none",this.isFormVisible=!1,this.form.reset())},this.showCsList=function(){this.csListContainer&&(this.csListContainer.style.display="block",this.isCsListVisible=!0,this.updateButtonIcon("close"))},this.hideCsList=function(){this.csListContainer&&(this.csListContainer.style.display="none",this.isCsListVisible=!1,this.updateButtonIcon("whatsapp"))},this.toggleCsList=function(){this.isCsListVisible?(this.hideCsList(),this.updateButtonIcon("whatsapp")):(this.showCsList(),this.updateButtonIcon("close"))},this.updateButtonIcon=function(t){if(this.floatingButton){var e=this.floatingButton.querySelector(".whatsapp-widget-icon");if(e)if(e.innerHTML="","whatsapp"===t)if(this.config.buttonIcon.startsWith("data:image/svg+xml")){var i=document.createElement("img");i.src=this.config.buttonIcon,i.alt="WhatsApp",i.style.width="30px",i.style.height="30px",e.appendChild(i)}else e.innerHTML=this.config.buttonIcon;else"close"===t&&(e.innerHTML="✕")}},this.resetButtonIcon=function(){this.updateButtonIcon("whatsapp")},this.handleCsSelect=function(t){var e=this.config.csList[t];e&&(this.config.onCsSelect?this.config.onCsSelect(e,t,this):this.openWhatsAppWithCs(e))},this.openWhatsAppWithCs=function(t){var e=t.number.replace(/\D/g,""),i=t.message||this.config.defaultMessage,n="https://wa.me/"+e+"?text="+encodeURIComponent(i);window.open(n,"_blank")},this.updateConfig=function(t){this.config=this.mergeConfig(t),this.theme=this.getTheme(),this.updateStyles()},this.updateStyles=function(){var t=document.getElementById("whatsapp-widget-styles");t&&(t.textContent=this.getStyles())},this.setWhatsAppNumber=function(t){this.config.whatsappNumber=t},this.setMessage=function(t){this.config.defaultMessage=t},this.show=function(){this.showWidget()},this.hide=function(){this.hideWidget()},this.toggle=function(){this.isFormVisible?this.hideForm():this.isCsListVisible?this.hideCsList():this.config.showForm?this.showForm():this.config.csList&&this.config.csList.length>0&&this.toggleCsList()},this.destroy=function(){this.widget&&this.widget.parentNode&&this.widget.parentNode.removeChild(this.widget);var t=document.getElementById("whatsapp-widget-styles");t&&t.remove()},this.init(),this.createFloatingCsList=function(){this.csListContainer=document.createElement("div"),this.csListContainer.className="whatsapp-widget-cs-floating",this.csListContainer.style.display="none";var t=document.createElement("div");t.className="whatsapp-widget-cs-header",t.innerHTML='<div class="whatsapp-widget-cs-header-content"><div class="whatsapp-widget-cs-header-icon"><img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NzguMTY1IDQ3OC4xNjUiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDQ3OC4xNjUgNDc4LjE2NSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiPjxwYXRoIGQ9Ik00NzguMTY1IDIzMi45NDZjMCAxMjguNTY3LTEwNS4wNTcgMjMyLjk2Ni0yMzQuNjc5IDIzMi45NjYtNDEuMTAyIDAtNzkuODE0LTEwLjU5OS0xMTMuNDQ1LTI4Ljk2OUwwIDQ3OC4xNjVsNDIuNDM3LTEyNS4wNGMtMjEuNDM4LTM1LjA2NS0zMy43Ny03Ni4yMDctMzMuNzctMTIwLjE1OUM4LjY2NyAxMDQuMzQgMTEzLjc2MyAwIDI0My40ODUgMGMxMjkuNjIzIDAgMjM0LjY4IDEwNC4zNCAyMzQuNjggMjMyLjk0NnpNMjQzLjQ4NSAzNy4wOThjLTEwOC44MDIgMC0xOTcuNDIyIDg3LjgwMy0xOTcuNDIyIDE5NS44NjggMCA0Mi45MTUgMTMuOTg2IDgyLjYwMyAzNy41NzYgMTE0Ljg3OWwtMjQuNTg2IDcyLjU0MiA3NS44NDktMjMuOTY4YzMxLjEyMSAyMC40ODEgNjguNDU3IDMyLjI5NiAxMDguNTgzIDMyLjI5NiAxMDguNzIzIDAgMTk3LjMyMy04Ny44NDMgMTk3LjMyMy0xOTUuOTA4IDAtMTA3Ljg4Ni04OC42LTE5NS43MDktMTk3LjMyMy0xOTUuNzA5ek0zNjEuOTMxIDI4Ni42MmMtMS4zOTUtMi4zMzEtNS4yMi0zLjc0Ni0xMC44OTgtNi44MTQtNS45MTctMi44NDktMzQuMDg5LTE2LjQ5Ny0zOS41MDgtMTguMzctNS4xNi0xLjkxMy04Ljk4Ni0yLjg0OS0xMi44MTEgMi44MjktNC4wMDUgNS42MzgtMTQuOTAzIDE4LjYyOS0xOC4yMyAyMi4zNTQtMy41NDYgMy43ODUtNi44NTQgNC4yNjQtMTIuNTUyIDEuNDM1LTUuNjE4LTIuODA5LTI0LjI2Ny04Ljg2Ni00Ni4yMDMtMjguMzkxLTE3LjA1NS0xNS4wNDItMjguNjctMzMuNzExLTMxLjk5Ny0zOS41MDgtMy40MjctNS43NTgtLjM5OC04LjgyNiAyLjQ3MS0xMS42MzUgMi42OS0yLjU5IDUuNzc4LTYuNzM0IDguNjI3LTEwLjA0MSAyLjk2OS0zLjI4NyAzLjkwNS01LjYzOCA1Ljc5OC05LjQyNCAxLjkxMy0zLjkwNS45MzYtNy4xOTItLjQ3OC0xMC4xNDEtMS40MTUtMi44NDktMTMuMDEtMzAuODgxLTE3Ljc1Mi00Mi4zMzctNC44NDEtMTEuNDE2LTkuNTQzLTkuNTIzLTEyLjg3MS05LjUyMy0zLjQ2NyAwLTcuMjEyLS40NzgtMTEuMTE3LS40NzgtMy43ODUgMC0xMC4wNDEgMS4zOTUtMTUuMzgxIDcuMTkyLTUuMiA1LjY1OC0yMC4xMjMgMTkuNDY1LTIwLjEyMyA0Ny41OTcgMCAyOC4wNTIgMjAuNjAxIDU1LjMwOCAyMy41NSA1OS4wNTMgMi44NjkgMy43ODUgMzkuNzQ3IDYzLjE5NyA5OC4zMDMgODYuMDcgNTguNDc2IDIyLjg3MiA1OC40NzYgMTUuMzIxIDY5LjExNSAxNC4zNjUgMTAuMzgtLjk1NiAzNC4wNjktMTMuODY3IDM4LjgxMS0yNy4wOTYgNC42Ni0xMy40NSA0LjY2LTI0Ljc2NiAzLjI0Ni0yNy4xMzd6IiBmaWxsPSIjRkZGIi8+PC9zdmc+" alt="WhatsApp" /></div><div class="whatsapp-widget-cs-header-text"><div class="whatsapp-widget-cs-header-title">'+this.config.csHeaderTitle+"</div>"+(this.config.csHeaderDescription?'<div class="whatsapp-widget-cs-header-description">'+this.config.csHeaderDescription+"</div>":"")+"</div></div>",this.csContent=document.createElement("div"),this.csContent.className="whatsapp-widget-cs-content";for(var e=0;e<this.config.csList.length;e++){var i=this.config.csList[e],n=document.createElement("div");n.className="whatsapp-widget-cs-item",n.innerHTML='<div class="whatsapp-widget-cs-info"><div class="whatsapp-widget-cs-name">'+i.name+"</div>"+(i.description?'<div class="whatsapp-widget-cs-description">'+i.description+"</div>":"")+'</div><button class="whatsapp-widget-cs-button" data-cs-index="'+e+'">'+this.config.csButtonText+"</button>",this.csContent.appendChild(n)}this.csListContainer.appendChild(t),this.csListContainer.appendChild(this.csContent),this.widget.appendChild(this.csListContainer)},this.addStyles=function(){if(!document.getElementById("whatsapp-widget-styles")){var t=document.createElement("style");t.id="whatsapp-widget-styles",t.textContent=this.getStyles(),document.head.appendChild(t)}},this.getStyles=function(){var t=this.getButtonSize(),e=this.getButtonPosition();return'.whatsapp-widget{position:fixed;z-index:9999;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif}.whatsapp-widget-button{position:fixed;'+e+";display:flex;align-items:center;background:"+this.theme.primary+";color:"+this.config.buttonTextColor+";border:none;border-radius:50px;padding:12px 20px;cursor:pointer;box-shadow:0 4px 12px rgba(0,0,0,0.15);transition:all 0.3s ease;font-size:14px;font-weight:500;text-decoration:none;z-index:10000;"+t+"}.whatsapp-widget-button:hover{transform:translateY(-2px);box-shadow:0 6px 16px rgba(0,0,0,0.2);background:"+this.theme.secondary+"}.whatsapp-widget-icon{font-size:30px;margin-right:8px}.whatsapp-widget-text{white-space:nowrap}.whatsapp-widget-overlay{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:10001}.whatsapp-widget-form{background:"+this.theme.background+";border-radius:12px;padding:0;width:90%;max-width:400px;box-shadow:0 10px 30px rgba(0,0,0,0.3);animation:whatsapp-widget-slideIn 0.3s ease}@keyframes whatsapp-widget-slideIn{from{opacity:0;transform:translateY(-20px)}to{opacity:1;transform:translateY(0)}}.whatsapp-widget-form-header{display:flex;justify-content:space-between;align-items:center;padding:20px 20px 0 20px;border-bottom:1px solid "+this.theme.border+"}.whatsapp-widget-form-header h3{margin:0;color:"+this.theme.text+";font-size:18px;font-weight:600}.whatsapp-widget-close{background:none;border:none;font-size:24px;cursor:pointer;color:#999;padding:0;width:30px;height:30px;display:flex;align-items:center;justify-content:center;border-radius:50%;transition:background 0.2s ease}.whatsapp-widget-close:hover{background:#f0f0f0}.whatsapp-widget-form-content{padding:20px}.whatsapp-widget-field{margin-bottom:16px}.whatsapp-widget-field label{display:block;margin-bottom:6px;color:"+this.theme.text+";font-size:14px;font-weight:500}.whatsapp-widget-field input,.whatsapp-widget-field textarea{width:100%;padding:12px;border:1px solid "+this.theme.border+";border-radius:6px;font-size:14px;transition:border-color 0.2s ease;box-sizing:border-box}.whatsapp-widget-field input:focus,.whatsapp-widget-field textarea:focus{outline:none;border-color:"+this.theme.primary+"}.whatsapp-widget-submit{width:100%;padding:12px;background:"+this.theme.primary+";color:white;border:none;border-radius:6px;font-size:16px;font-weight:500;cursor:pointer;transition:background 0.2s ease}.whatsapp-widget-submit:hover{background:"+this.theme.secondary+"}.whatsapp-widget-submit:disabled{background:#ccc;cursor:not-allowed}.whatsapp-widget-cs-content{padding:20px}.whatsapp-widget-cs-item{display:flex;justify-content:space-between;align-items:center;padding:15px 0;border-bottom:1px solid "+this.theme.border+"}.whatsapp-widget-cs-item:last-child{border-bottom:none}.whatsapp-widget-cs-info{flex:1;margin-right:15px}.whatsapp-widget-cs-name{font-weight:600;color:"+this.theme.text+";font-size:16px;margin-bottom:4px}.whatsapp-widget-cs-description{color:#666;font-size:14px}.whatsapp-widget-cs-button{padding:8px 16px;background:"+this.theme.primary+";color:white;border:none;border-radius:6px;font-size:14px;font-weight:500;cursor:pointer;transition:background 0.2s ease;white-space:nowrap}.whatsapp-widget-cs-button:hover{background:"+this.theme.secondary+"}.whatsapp-widget-cs-floating{position:fixed;z-index:9998;background:"+this.theme.background+";border-radius:12px;box-shadow:0 8px 25px rgba(0,0,0,0.15);border:1px solid "+this.theme.border+";animation:whatsapp-widget-cs-slideIn 0.3s ease;max-width:350px;min-width:280px}.whatsapp-widget-cs-header{padding:20px 20px 15px 20px;border-bottom:1px solid "+this.theme.border+";background:linear-gradient(135deg,"+this.theme.primary+" 0%,"+this.theme.secondary+" 100%);border-radius:12px 12px 0 0}.whatsapp-widget-cs-header-content{display:flex;align-items:flex-start;gap:12px}.whatsapp-widget-cs-header-icon{flex-shrink:0;width:32px;height:32px;display:flex;align-items:center;justify-content:center}.whatsapp-widget-cs-header-icon img{width:24px;height:24px;filter:brightness(0) invert(1)}.whatsapp-widget-cs-header-text{flex:1}.whatsapp-widget-cs-header-title{font-size:18px;font-weight:600;color:#ffffff;margin-bottom:5px}.whatsapp-widget-cs-header-description{font-size:14px;color:rgba(255,255,255,0.9);line-height:1.4}@keyframes whatsapp-widget-cs-slideIn{from{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.whatsapp-widget-cs-floating{"+e.replace("bottom","top").replace("top","bottom")+";margin-bottom:70px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-content{padding:15px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-item{padding:12px 0}.whatsapp-widget-cs-floating .whatsapp-widget-cs-button{padding:6px 12px;font-size:13px}@media (max-width:480px){.whatsapp-widget-button{padding:10px 16px;font-size:13px}.whatsapp-widget-icon{font-size:18px;margin-right:6px}.whatsapp-widget-form{width:95%;margin:20px}.whatsapp-widget-cs-item{flex-direction:column;align-items:flex-start}.whatsapp-widget-cs-info{margin-right:0;margin-bottom:10px}.whatsapp-widget-cs-button{width:100%}.whatsapp-widget-cs-floating{max-width:95%;min-width:250px;margin-bottom:60px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-header{padding:15px 15px 10px 15px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-header-content{gap:10px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-header-icon{width:28px;height:28px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-header-icon img{width:20px;height:20px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-header-title{font-size:16px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-header-description{font-size:13px}.whatsapp-widget-cs-floating .whatsapp-widget-cs-content{padding:12px}}"},this.getButtonSize=function(){var t={small:"padding:8px 16px;font-size:12px",medium:"padding:12px 20px;font-size:14px",large:"padding:16px 24px;font-size:16px"};return t[this.config.buttonSize]||t.medium},this.getButtonPosition=function(){var t={"bottom-right":"bottom:20px;right:20px","bottom-left":"bottom:20px;left:20px","top-right":"top:20px;right:20px","top-left":"top:20px;left:20px"};return t[this.config.buttonPosition]||t["bottom-right"]},this.addEventListeners=function(){var t=this;if(this.floatingButton.addEventListener("click",(function(e){e.preventDefault(),t.handleButtonClick()})),this.form&&this.form.addEventListener("submit",(function(e){e.preventDefault(),t.handleFormSubmit()})),this.formOverlay){var e=this.formOverlay.querySelector(".whatsapp-widget-close");e&&e.addEventListener("click",(function(){t.hideForm()})),this.formOverlay.addEventListener("click",(function(e){e.target===t.formOverlay&&t.hideForm()}))}if(this.csListContainer)for(var i=this.csListContainer.querySelectorAll(".whatsapp-widget-cs-button"),n=0;n<i.length;n++)i[n].addEventListener("click",(function(e){var i=parseInt(e.target.getAttribute("data-cs-index"));t.handleCsSelect(i)}));document.addEventListener("keydown",(function(e){"Escape"===e.key&&(t.isFormVisible||t.isCsListVisible)&&(t.isFormVisible?t.hideForm():t.isCsListVisible&&t.hideCsList())}))},this.handleButtonClick=function(){this.config.onButtonClick&&this.config.onButtonClick(this),this.config.showForm?this.showForm():this.config.csList&&this.config.csList.length>0?this.toggleCsList():this.openWhatsApp()},this.handleFormSubmit=function(){var t=new FormData(this.form),e={};for(var i of t.entries())e[i[0]]=i[1];this.config.onFormSubmit?this.config.onFormSubmit(e,this):this.submitForm(e)},this.submitForm=function(t){var e=this.buildWhatsAppMessage(t);this.openWhatsApp(e),this.hideForm()},this.buildWhatsAppMessage=function(t){var e=this.config.defaultMessage;return t.message&&(e=t.message),t.name&&(e="Halo, saya "+t.name+".\n\n"+e),encodeURIComponent(e)},this.openWhatsApp=function(t){var i=this.config.whatsappNumber.replace(/\D/g,""),n=this.config.divisi||e.divisi;if(""==i)var o="https://"+e.domain+"/cta?p="+e.project_key+"&divisi="+n+"&msg="+encodeURIComponent(t)+"&cta=1&v="+e.visitor_id;else{t=t.replace("_gid_",e.visitor_id);o="https://wa.me/"+i+"?text="+encodeURIComponent(t)}e.post_click(o)},this.showWidget=function(){this.widget.style.display="block",this.isWidgetLoaded=!0},this.hideWidget=function(){this.widget.style.display="none"},this.showForm=function(){if(this.formOverlay){this.formOverlay.style.display="flex",this.isFormVisible=!0;var t=this.form.querySelector("input, textarea");t&&t.focus()}},this.hideForm=function(){this.formOverlay&&(this.formOverlay.style.display="none",this.isFormVisible=!1,this.form.reset())},this.showCsList=function(){this.csListContainer&&(this.csListContainer.style.display="block",this.isCsListVisible=!0,this.updateButtonIcon("close"))},this.hideCsList=function(){this.csListContainer&&(this.csListContainer.style.display="none",this.isCsListVisible=!1,this.updateButtonIcon("whatsapp"))},this.toggleCsList=function(){this.isCsListVisible?(this.hideCsList(),this.updateButtonIcon("whatsapp")):(this.showCsList(),this.updateButtonIcon("close"))},this.updateButtonIcon=function(t){if(this.floatingButton){var e=this.floatingButton.querySelector(".whatsapp-widget-icon");if(e)if(e.innerHTML="","whatsapp"===t)if(this.config.buttonIcon.startsWith("data:image/svg+xml")){var i=document.createElement("img");i.src=this.config.buttonIcon,i.alt="WhatsApp",i.style.width="30px",i.style.height="30px",e.appendChild(i)}else e.innerHTML=this.config.buttonIcon;else"close"===t&&(e.innerHTML="✕")}},this.resetButtonIcon=function(){this.updateButtonIcon("whatsapp")},this.handleCsSelect=function(t){var e=this.config.csList[t];e&&(this.config.onCsSelect?this.config.onCsSelect(e,t,this):this.openWhatsAppWithCs(e))},this.openWhatsAppWithCs=function(t){var i="";t.number&&null!==t.number&&""!==t.number&&(i=t.number.replace(/\D/g,""));var n=t.message||this.config.defaultMessage,o=t.divisi||this.config.divisi;if(""==i)var s="https://"+e.domain+"/cta?p="+e.project_key+"&divisi="+o+"&msg="+encodeURIComponent(n)+"&cta=1&v="+e.visitor_id;else{n=n.replace("_gid_",e.visitor_id);s="https://wa.me/"+i+"?text="+encodeURIComponent(n)}e.post_click(s)},this.updateConfig=function(t){this.config=this.mergeConfig(t),this.theme=this.getTheme(),this.updateStyles()},this.updateStyles=function(){var t=document.getElementById("whatsapp-widget-styles");t&&(t.textContent=this.getStyles())},this.setWhatsAppNumber=function(t){this.config.whatsappNumber=t},this.setMessage=function(t){this.config.defaultMessage=t},this.show=function(){this.showWidget()},this.hide=function(){this.hideWidget()},this.toggle=function(){this.isFormVisible?this.hideForm():this.isCsListVisible?this.hideCsList():this.config.showForm?this.showForm():this.config.csList&&this.config.csList.length>0&&this.toggleCsList()},this.destroy=function(){this.widget&&this.widget.parentNode&&this.widget.parentNode.removeChild(this.widget);var t=document.getElementById("whatsapp-widget-styles");t&&t.remove()}},createWhatsAppWidget:function(t){return new this.WhatsAppWidget(t)}};return e}();