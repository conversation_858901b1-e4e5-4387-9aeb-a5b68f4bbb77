<?php

/**
 * PSR-4 Autoloader for DynamicWebhook namespace
 */

spl_autoload_register(function ($className) {
    // Base namespace
    $baseNamespace = 'DynamicWebhook\\';
    
    // Base directory for the namespace
    $baseDir = __DIR__ . '/';
    
    // Check if the class uses the namespace prefix
    $len = strlen($baseNamespace);
    if (strncmp($baseNamespace, $className, $len) !== 0) {
        // No, move to the next registered autoloader
        return;
    }
    
    // Get the relative class name
    $relativeClass = substr($className, $len);
    
    // Replace the namespace prefix with the base directory, replace namespace
    // separators with directory separators in the relative class name, append
    // with .php
    $file = $baseDir . str_replace('\\', '/', $relativeClass) . '.php';
    
    // If the file exists, require it
    if (file_exists($file)) {
        require $file;
    }
});

/**
 * Helper function to load all DynamicWebhook classes
 */
function loadDynamicWebhookClasses() {
    $baseDir = __DIR__ . '/';
    
    // Core classes
    $coreClasses = [
        'PlatformDetector.php',
        'MessageNormalizer.php',
        'CTWAProcessor.php',
        'VisitorManager.php'
    ];
    
    foreach ($coreClasses as $class) {
        $file = $baseDir . $class;
        if (file_exists($file)) {
            require_once $file;
        }
    }
    
    // Interface files
    $interfaceDir = $baseDir . 'Interfaces/';
    if (is_dir($interfaceDir)) {
        $interfaces = glob($interfaceDir . '*.php');
        foreach ($interfaces as $interface) {
            require_once $interface;
        }
    }
    
    // Abstract classes
    $abstractDir = $baseDir . 'Abstracts/';
    if (is_dir($abstractDir)) {
        $abstracts = glob($abstractDir . '*.php');
        foreach ($abstracts as $abstract) {
            require_once $abstract;
        }
    }
    
    // Data extractors
    $extractorDir = $baseDir . 'DataExtractor/';
    if (is_dir($extractorDir)) {
        $extractors = glob($extractorDir . '*.php');
        foreach ($extractors as $extractor) {
            require_once $extractor;
        }
    }
    
    // Trigger processors
    $triggerDir = $baseDir . 'TriggerProcessor/';
    if (is_dir($triggerDir)) {
        $triggers = glob($triggerDir . '*.php');
        foreach ($triggers as $trigger) {
            require_once $trigger;
        }
    }
}

// Auto-load all classes when this file is included
loadDynamicWebhookClasses();
