<?php 

class divisi{

	private $i;

    function __construct() {
       $this->i = 1;
    }

	function add_divisi($name)
	{
		global $app,$keya;
		$db2 = $app->db2;

		$db2->rawQuery("Delete FROM `divisi` WHERE divisi_key IS NULL;");

		$data["slug"]=$this->unik_divisi($name);

		
		
		$data["divisi_key"] = $db2->func("UNHEX(?)",array(md5($data["slug"])));
		$data["name"] = $name;

		$id = $db2->insert("divisi",$data);

		if($id)
		{	
			$return["code"] = 1;
			$return["result"]["data"]["id"] = $id;
			$return["result"]["data"]["key"] = md5($data["slug"]);
			$return["result"]["msg"] = "sukses";
		}
		else
		{
			$return["code"] = 0;
			$return["result"]["msg"] = "error, failed to insert";
		}

		return $return;	
		
	}

	public static function slugify($text, string $divider = '-')
        {
          // replace non letter or digits by divider
          $text = preg_replace('~[^\pL\d]+~u', $divider, $text);

          // transliterate
          $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);

          // remove unwanted characters
          $text = preg_replace('~[^-\w]+~', '', $text);

          // trim
          $text = trim($text, $divider);

          // remove duplicate divider
          $text = preg_replace('~-+~', $divider, $text);

          // lowercase
          $text = strtolower($text);

          if (empty($text)) {
            return 'n-a';
          }

          return $text;
        }

	function unik_divisi($name)
	{
		global $app,$keya;
		$db2 = $app->db2;

		$name = $this->slugify($name);
		
		if($this->i != 1){
			$newname = $name . "-".$this->i;
		}else
		{
			$newname = $name;
		}

		$name_key = md5($newname);

		$db2->where("divisi_key = UNHEX(?)",[$name_key]);
		$res = $db2->getone("divisi");

		if($res == NULL)
		{
			
			return  strtolower($newname);
		}
		else{
			$this->i++;
			return $this->unik_divisi(strtolower($name));
		}

	}

	function get_divisi()
	{
		global $app,$keya;
		$db2 = $app->db2;

		

		
		
		$divisi = $db2->get("divisi",NULL,"HEX(divisi_key) as divisi_key,name,slug");

		$return["code"] = 1;
		
		$return["result"]["data"] = $divisi;
		$return["result"]["msg"] = "sukses";
		


		return $return;	
		
	}

	function edit_divisi($name,$divisi_key)
	{
		global $app;
		$db2 = $app->db2;
		
		$data["name"] = $name;
		if($divisi_key == NULL || $divisi_key == 0 || $divisi_key == "")
		{
			$return["code"] = 0;
			$return["result"]["msg"] = "error";
			return $return;
		}
		$db2->where("divisi_key = UNHEX(?)",array($divisi_key));
		if($db2->update("divisi",$data))
		{
			$return["code"] = 1;
			$return["result"]["msg"] = "sukses";
		}
		else{
			$return["code"] = 0;
			$return["result"]["msg"] = "error";
		}
		return $return;
	}

	function delete_divisi($divisi_key)
	{
		global $app;
		$db2 = $app->db2;

		if($divisi_key == NULL || trim($divisi_key) == "" || $divisi_key == 0)
		{
			$return["code"] = 0;
			$return["result"]["msg"] = "wrong divisi id";
			return $return;
		}
		
	
		$db2->where("divisi_key = UNHEX(?)",array($divisi_key));
		$db2->delete("divisi");


		$db2->where("divisi_key = UNHEX(?)",array($divisi_key));
		$db2->delete("cs_divisi");

		$return["code"] = 1;
		$return["result"]["msg"] = "sukses";
		return $return;

	}

	function add_cs($phone,$divisi_key)
	{
		global $app;
		$db2 = $app->db2;

		$cs_key = md5($phone);

		$db2->where("divisi_key = UNHEX(?)",array($divisi_key));
		$divisi = $db2->getone("divisi");

		if($divisi == NULL)
		{
			$return["code"] = 0;
			$return["result"]["msg"] = "divisi not found";
			return $return;
		}
		
		$db2->where("cs_key = UNHEX(?)",[$cs_key]);
		$db2->where("divisi_id",$divisi["divisi_id"]);
		$db2->delete("cs_divisi");

		$data["cs_key"] = $db2->func("UNHEX(?)",[$cs_key]);
		$data["divisi_id"]= $divisi["divisi_id"];

		$db2->insert("cs_divisi",$data);

		$return["code"] = 1;
		$return["result"]["msg"] = "sukses";
		return $return;
	}

	function remove_cs($cs_key,$divisi_key = NULL)
	{
		global $app;
		$db2 = $app->db2;

		if($divisi_key != NULL)
		{
			$db2->where("divisi_key = UNHEX(?)",array($divisi_key));
		}

		$db2->where("cs_key = UNHEX(?)",[$cs_key]);
		$db2->delete("cs_divisi");


		$return["code"] = 1;
		$return["result"]["msg"] = "sukses";
		return $return;
	}

	function get_cs($divisi_key){
		global $app;
		$db2 = $app->db2;

		
		$db2->join("cs_divisi","cs_divisi.cs_key = cs.cs_key");
		$db2->join("divisi","divisi.divisi_id = cs_divisi.divisi_id");

		$db2->where("divisi.divisi_key = UNHEX(?)",array($divisi_key));

		$cs = $db2->get("cs",NULL,"cs.name,cs.phone,HEX(cs.cs_key) as cs_key");



		$return["code"] = 1;
		$return["result"]["data"] = $cs;
		$return["result"]["msg"] = "sukses";
		return $return;
	}

	function get_pembagian($divisi_key){
		global $app;
		$db2 = $app->db2;

		$db2->where("divisi.divisi_key = UNHEX(?)",array($divisi_key));
		$divisi = $db2->getone("divisi");

		if($divisi == NULL)
		{
			$return["code"] = 0;
			$return["result"]["msg"] = "Divisi Not Found";
			return $return;
		}
		
		$m = new meta();
		$x = $m->get_meta("mode_pembagian_divisi_".$divisi['divisi_id']);

		if(isset($x["result"]["data"])){
			$ret["mode_pembagian"] = $x["result"]["data"];
		}else{
			$ret["mode_pembagian"] = "auto";
		}

		$cs = $this->get_cs($divisi_key);
		$data_tmp = $m->get_meta("pembagian_divisi_".$divisi['divisi_id']);
		if(isset($data_tmp["result"]["data"]))
		{
			$data_tmp = $data_tmp["result"]["data"];
		}else{
			$data_tmp = NULL;
		}

		$data_pembagian = array();
        $data_pembagian_name = array();
		if(isset($cs["result"]["data"])){
			foreach ($cs["result"]["data"] as $key => $value) {
				if(isset($data_tmp[$value["phone"]])){
                    $data_pembagian[$value["phone"]] = $data_tmp[$value["phone"]];
					$data_pembagian_name[$value["phone"]] = array("name" => $value["name"], "pembagian"=> $data_tmp[$value["phone"]]);
				}else{
                    $data_pembagian[$value["phone"]] = 0;
					$data_pembagian_name[$value["phone"]] = array("name" => $value["name"], "pembagian"=> 0);
				}
			}	
		}else{
			$return["code"] = 0;
			$return["result"]["msg"] = "get cs error";
			return $return;
		}

		if(count($data_pembagian) > 0)
		{
			$ret["data_pembagian"] = $data_pembagian;
            $ret["data_pembagian_name"] = $data_pembagian_name;
		}

		$return["code"] = 1;
		$return["result"]["data"] = $ret;
		$return["result"]["msg"] = "sukses";
		return $return;
	}

	function get_pembagian2($divisi_key){
		global $app;
		$db2 = $app->db2;

		$db2->where("divisi.divisi_key = UNHEX(?)",array($divisi_key));
		$divisi = $db2->getone("divisi");

		if($divisi == NULL)
		{
			$return["code"] = 0;
			$return["result"]["msg"] = "Divisi Not Found";
			return $return;
		}
		
		$m = new meta();
		$x = $m->get_meta("mode_pembagian_divisi_".$divisi['divisi_id']);

		if(isset($x["result"]["data"])){
			$ret["mode_pembagian"] = $x["result"]["data"];
		}else{
			$ret["mode_pembagian"] = "auto";
		}

		$cs = $this->get_cs($divisi_key);
		$data_tmp = $m->get_meta("pembagian_divisi_".$divisi['divisi_id']);
		if(isset($data_tmp["result"]["data"]))
		{
			$data_tmp = $data_tmp["result"]["data"];
		}else{
			$data_tmp = NULL;
		}

		$data_pembagian = array();

		if(isset($cs["result"]["data"])){
			foreach ($cs["result"]["data"] as $key => $value) {
				if(isset($data_tmp[$value["phone"]])){
					$data_pembagian[$value["phone"]]["value"] = $data_tmp[$value["phone"]];
				}else{
					$data_pembagian[$value["phone"]]["value"] = 0;
				}
				$data_pembagian[$value["phone"]]["name"] = $value["name"];
			}	
		}else{
			$return["code"] = 0;
			$return["result"]["msg"] = "get cs error";
			return $return;
		}

		if(count($data_pembagian) > 0)
		{
			$ret["data_pembagian"] = $data_pembagian;
		}


		$return["code"] = 1;
		$return["result"]["data"] = $ret;
		$return["result"]["msg"] = "sukses";
		return $return;
	}

	function set_pembagian($project_id,$divisi_key,$mode_pembagian,$data_pembagian=NULL)
	{
		global $app;
		$db2 = $app->db2;

		$db2->where("divisi.divisi_key = UNHEX(?)",array($divisi_key));
		$divisi = $db2->getone("divisi");

		if($divisi == NULL)
		{
			$return["code"] = 0;
			$return["result"]["msg"] = "Divisi Not Found";
			return $return;
		}

		$m = new meta($project_id);
		$m->set_meta("mode_pembagian_divisi_".$divisi['divisi_id'],$mode_pembagian);

		if($mode_pembagian == "manual")
		{
			if(!is_array($data_pembagian))
			{
				$return["code"] = 0;
				$return["result"]["msg"] = "Data Error";
				return $return;
			}

			$xx = 0;
			$data_tmp = array();
			foreach ($data_pembagian as $key => $value) {
				$xx += $value["value"];
				$data_tmp[$value["phone"]] = $value["value"];
			}
			if($xx == 0)
			{
				$return["code"] = 0;
				$return["result"]["msg"] = "Persen Error";
				return $return;
			}

			$cs = $this->get_cs($divisi_key);

			$data_pembagian = array();

			if(isset($cs["result"]["data"])){
				foreach ($cs["result"]["data"] as $key => $value) {
					if(isset($data_tmp[$value["phone"]])){
						$data_pembagian[$value["phone"]] = $data_tmp[$value["phone"]];
					}else{
						$data_pembagian[$value["phone"]] = 0;
					}
					
				
				}	
			}else{
				$return["code"] = 0;
				$return["result"]["msg"] = "get cs error";
				return $return;
			}
			
			
			
			$m->set_meta("pembagian_divisi_".$divisi['divisi_id'],json_encode($data_pembagian));
		}
		
		$return["code"] = 1;
		$return["result"]["msg"] = "sukses";
		return $return;
	}

}
