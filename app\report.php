<?php
/**
 * Changes (2025-08):
 * - Tambah fallback saat report_id > mediumint(9):
 *   Ambil insights dari Facebook (mirip implementasi di app/track.php)
 *   untuk membuat struktur report (campaign/adset/adcopy), lalu gunakan
 *   parent_id (adset) sebagai report_id yang disimpan ke report_data.
 * - Fallback ini dipanggil dari add_report_data() & update_report_data().
 *
 * Changes (2025-09-04):
 * - Added ensure_unknown_structure(): pastikan entri campaign 'Unknown' (report_id=1)
 *   tersedia agar penulisan metrik untuk sumber unknown selalu aman.
 */
class report
{
    /**
     * Ensure Unknown campaign exists (no fixed report_id). Return its report_id.
     * - Uses external_id = 'unknown' and source/name/type = 'Unknown'/'campaign'
     */
    public function ensure_unknown_structure($phone=null)
    {
        global $app;
        $db2 = $app->db2;
        if($phone != null){
            $db2->where("phone", $phone);
            $row = $db2->getone("visitor");
            if(isset($row["data"]) && $row["data"] != null){
                $visitor_data = unserialize($row["data"]);
                if(isset($visitor_data["last_campaign"]["data"]["source_id"])){
                    $externalId = $visitor_data["last_campaign"]["data"]["source_id"];
                    $db2->where("external_key = UNHEX(?)", [sha1("unknown_" .$externalId)]);
                    $rox = $db2->getone("report");
                    if($rox != null){
                        $reportId = $rox['report_id'];
                        return $reportId;
                    }
                }                
            }
        }
        $externalId = 'unknown';
        $db2->where("external_key = UNHEX(?)", [sha1($externalId)]);
        $row = $db2->getone("report");

        if ($row == null) {
            // Create Unknown campaign via helper to respect unique external_key
            $ret = $this->add_report_kolom('Unknown', 'Unknown', 'campaign', $externalId, null, true);
            $reportId = isset($ret['data']) ? $ret['data'] : null;
        } else {
            $reportId = $row['report_id'];
            // keep attributes consistent
            $update = array();
            if ($row["name"] !== "Unknown") { $update["name"] = "Unknown"; }
            if ($row["type"] !== "campaign") { $update["type"] = "campaign"; }
            if ($row["source"] !== "Unknown") { $update["source"] = "Unknown"; }
            if (!empty($update)) {
                $db2->where("report_id", $reportId)->update("report", $update);
            }
        }   

        return $reportId;
    }
    public function get_all_metric()
    {
        global $app;
        $db2 = $app->db2;

        $db2->groupBy("report_key");
        $standart_metric = $db2->get("report_data", null, "report_key");

        $custom_metric = self::get_metric();

        $metric = array_merge($standart_metric, $custom_metric);

        return $metric;

    }

    public function get_report_data_child($convertion_type, $column, $start, $end, $source, $type, $data_level, $parent_id)
    {

        global $app;
        $db2 = $app->db2;
        
       

        if (!is_array($column)) {
            return false;
        }

        if ($data_level == 3) {
            $db2->join("report r3", "r3.report_id = report_data.report_id");
            $db2->join("report r2", "r2.report_id = r3.parent_id");
            $db2->join("report r1", "r1.report_id = r2.parent_id");
        } elseif ($data_level == 2) {
            $db2->join("report r2", "r2.report_id = report_data.report_id");
            $db2->join("report r1", "r1.report_id = r2.parent_id");
        } elseif ($data_level == 1) {
            $db2->join("report r1", "r1.report_id = report_data.report_id");
        }

        if ($convertion_type == "visit") {
            foreach ($column as $key => $value) {
                if ($column[$key] != "impression" && $column[$key] != "spend" && $column[$key] != "lp_view") {
                    $column[$key] = "v-" . $value;
                }
            }
        }

        $db2->where("report_data.date BETWEEN ? and ?", [$start, $end]);
        $db2->where("report_data.report_key", $column, "in");

        if ($type == "platform") {
            $db2->groupBy("r1.source,report_data.report_key");
            $report_data = $db2->get("report_data", null, "r1.source as report_id  ,report_data.report_key , sum(report_data.report_value) as report_value");
        } elseif ($type == "campaign") {
            $db2->where("r1.type", $type);
            $db2->where("r1.source", $source);
            $db2->groupBy("report_data.report_key,r1.report_id");
            $report_data = $db2->get("report_data", null, "r1.report_id as report_id,r1.name ,report_data.report_key , sum(report_data.report_value) as report_value");

        } elseif ($type == "adset") {
            if ($parent_id != 0) {
                $db2->where("r2.parent_id", $parent_id);
            }
            $db2->where("r2.type", $type);
            $db2->groupBy("report_data.report_key,r2.report_id");
            $report_data = $db2->get("report_data", null, "r2.report_id as report_id,r2.name ,report_data.report_key , sum(report_data.report_value) as report_value");

        } elseif ($type == "adcopy") {
            if ($parent_id != 0) {
                $db2->where("r3.parent_id", $parent_id);
            }
            $db2->where("r3.type", $type);
            $db2->groupBy("report_data.report_key,r3.report_id");
            $report_data = $db2->get("report_data", null, "r3.report_id as report_id,r3.name  ,report_data.report_key , sum(report_data.report_value) as report_value");
        } elseif ($type == "group") {
            $db2->join("report_group_rel rel", "rel.report_id = r1.report_id");
            $db2->where("rel.report_group_id ", $parent_id);
            $db2->groupBy("report_data.report_key,r1.report_id");
            $report_data = $db2->get("report_data", null, "r1.report_id as report_id,r1.name ,report_data.report_key , sum(report_data.report_value) as report_value");

        } elseif ($type == "custom") {

            $db2->join("report_group_rel rel", "rel.report_id = r1.report_id");
            $db2->join("report_group rg", "rg.report_group_id = rel.report_group_id");
            $db2->join("report_group rg_parent", "rg_parent.report_group_id = rg.report_group_id_parent");
            $db2->where("rg_parent.report_group_id ", $parent_id);
            // $db2->groupBy("rg.report_group_id");
            $db2->groupBy("report_data.report_key,rg.report_group_id");

            $report_data = $db2->get("report_data", null, "rg.report_group_id as report_id,rg.report_group_name as name ,report_data.report_key , sum(report_data.report_value) as report_value");

         
        } elseif ($type == "custom_campaign") {
            $db2->groupBy("report_data.report_key,r1.report_id");
            $report_data = $db2->get("report_data", null, "r1.report_id as report_id,r1.name ,report_data.report_key , sum(report_data.report_value) as report_value");
        }

/*
if ($level == 0)
{

$db2->join("report r3", "r3.report_id = report_data.report_id");
$db2->join("report r2", "r2.report_id = r3.parent_id");
$db2->join("report r1", "r1.report_id = r2.parent_id");
$db2->where("report_data.date BETWEEN ? and ?", [$start, $end]);
$db2->where("report_data.report_key", $column, "in");
//$db2->where("report_data.report_key in (?)",[$column]);
$db2->groupBy("report_data.report_key");
$report_data = $db2->get("report_data", NULL, "r1.source ,
report_data.report_key , sum(report_data.report_value) as report_value");
}
if ($level == 1)
{

$db2->join("report r2", "r2.report_id = report_data.report_id");
$db2->join("report r1", "r1.report_id = r2.parent_id");
$db2->where("report_data.date BETWEEN ? and ?", [$start, $end]);
$db2->where("report_data.report_key", $column, "in");
$db2->groupBy("report_data.report_key,r1.report_id");
//$db2->groupBy("r1.source");
$report_data = $db2->get("report_data", NULL, "r1.source, r1.report_id as campaign_id, r1.name as campaign_name ,r1.report_id as adset_id, r2.name as adset_name ,report_data.report_key , sum(report_data.report_value) as report_value");

}
 */

        return $report_data;
    }

    public function get_overview_custom($report_group_id, $convertion_type, $column, $start, $end, $type, $source, $parent_id, $sort_by, $sort_type)
    {

        global $app;
        $db2 = $app->db2;
        $level = 0;
        $data_level = 2;
        if (!is_array($column)) {
            return false;
        }
        
       

        $source = strtolower($source);

        if ($source == "google") {
            $data_level = 1;
            if($type=='adset'){
                $data_level = 2;
            }elseif($type=='adcopy'){
                $data_level = 3;
            }
        } elseif ($source == "organic") {
            $data_level = 2;
        } elseif ($source == "shortlink") {
            $data_level = 2;
        }
        if ($type == "c") {
            $data_level = 1;
        }
        if ($parent_id == "" or $parent_id == null) {
            $parent_id = $report_group_id;
        }

        // $report_data = $this->get_report_data_child($convertion_type,$column, $start, $end,$source,$type,$data_level,$parent_id);
        // $data_level = 1;
        // if($type=="adset"){
        //     $data_level = 2;
        // }
        $data_level = 1;
        $report_data = $this->get_report_data_child($convertion_type, $column, $start, $end, $source, $type, $data_level, $parent_id);
        
        $data_level = 2;
        $report_data2 = $this->get_report_data_child($convertion_type, $column, $start, $end, $source, $type, $data_level, $parent_id);
        
        $data_level = 3;
        $report_data3 = $this->get_report_data_child($convertion_type, $column, $start, $end, $source, $type, $data_level, $parent_id);
        
        $report_data = mergeAndSumTwoArrays($report_data, $report_data2);
        $report_data = mergeAndSumTwoArrays($report_data, $report_data3);
       // var_dump($report_data3);die();
        //var_dump($report_data);die();

        if ($convertion_type == "visit") {
            foreach ($report_data as $key => $value) {
                if (!str_starts_with($report_data[$key]["report_key"], 'v-')) {
                    $report_data[$key]["report_key"] = "v-" . $report_data[$key]["report_key"];
                }
            }
        }

        $formulas = $this->get_metric_set($column);

        $available_columns = array_unique(array_column($report_data, 'report_key'));

        $tokens_var = [];
        foreach ($formulas as $name => $formula) {
            $tokens_var = array_merge($tokens_var, $this->formulaGetVars($formula));

        }
        $tokens_var = array_unique($tokens_var);

        if (!empty($tokens_var)) {
            foreach ($tokens_var as $key => $token) {
                if (substr($key, 0, 12) == 'report_data:') {
                    if ($convertion_type == "visit") {
                        //if($token != "spend"){
                        $token = "v-" . $token;
                        //}
                    }
                    $token_columns[] = $token;
                }
            }

            $additional_columns = array_diff($token_columns, $available_columns);

            sort($additional_columns);

            if (count($additional_columns) > 0) {
                if ($convertion_type == "visit") {
                    foreach ($additional_columns as $key => $value) {
                        if (str_starts_with($additional_columns[$key], 'v-')) {
                            $additional_columns[$key] = ltrim($additional_columns[$key], "v-");
                        }
                    }
                }

                $additional_rows = $this->get_report_data_child($convertion_type, $additional_columns, $start, $end, $source, $type, $data_level, $parent_id);

                if ($convertion_type == "visit") {
                    foreach ($additional_rows as $key => $value) {
                        if (!str_starts_with($additional_rows[$key]["report_key"], 'v-')) {
                            $additional_rows[$key]["report_key"] = "v-" . $additional_rows[$key]["report_key"];
                        }
                    }
                }

                $report_data = array_merge($report_data, $additional_rows);
            }

            $grouped_report_data = [];
            $reports_info = [];
        }

        foreach ($report_data as $row) {
            // create grouped report_data array by report_id, date
            $grouped_report_data[$row['report_id']][] = $row;

            // create reports_info by report_id
            $report_info[$row['report_id']] = ['name' => $row['name'], 'source' => $row['source']];
        }

        $i = 0;
        foreach ($grouped_report_data as $report_id => &$report) {

            // find column that have no data record
            $non_existing_report_keys = array_diff($column, array_column($report, 'report_key'));

            if (!empty($non_existing_report_keys)) {

                foreach ($non_existing_report_keys as $report_key) {

                    // $formula_name = $report_key;
                    // unprocessing custom report data skeleton
                    $formula = !empty($formulas[$report_key]) ? $formulas[$report_key] : null;
                    $custom_report_item = [
                        'source' => $report_info[$report_id]['source'],
                        'name' => $report_info[$report_id]['name'],
                        'report_id' => $report_id,
                        'report_key' => $report_key,
                        'report_value' => null, // $API->get_metric_formula($report_key),
                        'type' => 'custom_report',

                        'formula' => $formula ? self::formulaPrettyPrint($formula) : null,
                        'formula_tokens' => $formula ? self::getTokens($formula) : null,
                    ];

                    // debug($custom_report_item);

                    // calculating custom report value if formula exists
                    if ($formula) {
                        try {
                            // data to calculate formula: token => value
                            $data = array_column($report, 'report_value', 'report_key');

                            if ($convertion_type == "visit") {
                                $tokens = self::getTokens($formula);
                                // $tokens_var = self::tokensGetVars($tokens);

                                foreach ($tokens as $key => $value) {

                                    if (substr($value, 0, 12) == 'report_data:') {
                                        $cc = substr($value, 12);

                                        //if( $cc != "impression" &&  $cc != "spend" &&  $cc != "lp_view"){
                                        $tokens[$key] = str_replace($cc, "v-" . $cc, $value);
                                        // }
                                    }

                                }

                                $formula = '["' . implode('","', $tokens) . '"]';
                            }

                            // debug($custom_report_item, $data);
                            $report_value = self::calculateMetric($formula, $data);

                            $custom_report_item['report_value'] = $report_value;
                            // calculating formula done, append custom report

                            $report[] = $custom_report_item;

                        } catch (Exception $e) {
                            //$app::jsonResponse([], $e->getMessage(), 500);

                        }
                    } else {
                        // no report or custom report with formula found
                    }
                }
                // debug($report);
            }
            // debug($report_by_date);

        }
        $ungrouped_report_data = array();
        foreach ($grouped_report_data as $report_id => $value3) {

            foreach ($value3 as $key => $value2) {
                if (isset($value2["name"])) {
                    $ungrouped_report_data[$report_id]["name"] = $value2["name"];
                }
                $ungrouped_report_data[$report_id][$value2["report_key"]] = $value2["report_value"];

            }

            //$ungrouped_report_data[] = $tmp;
        }

        // filter $ungrouped_report_data by requested columns
        $column_filtered_report_data = array_filter($ungrouped_report_data, function ($item) use ($columns) {
            return empty($columns) ? true : in_array($item['report_key'], $columns);
        });

        // reassign processed report_data to $report_data
        $report_data = $column_filtered_report_data;
        //$response['items'] = array_values($report_data);

        // var_dump($column_filtered_report_data);die();

        $result_data = array();

        foreach ($report_data as $key => $value) {

            if (isset($report_data[$key]["spend"]) && isset($report_data[$key]["impressions"])) {
                $report_data[$key]["cpm"] = ceil($report_data[$key]["spend"] / $report_data[$key]["impressions"] * 1000);
            }
            if (isset($report_data[$key]["outbound_clicks"]) && isset($report_data[$key]["impressions"])) {
                $report_data[$key]["outbound_clicks_ctr"] = round($report_data[$key]["outbound_clicks"] / $report_data[$key]["impressions"] * 100, 2);
            }

            foreach ($column as $key2 => $c) {
                if (!isset($value[$c])) {
                    $value[$c] = 0;
                }
            }

            $value["report_id"] = $key;

            $result_data[$key] = $value;
            if ($type == "platform") {
                if ($key == "Unknown") {
                    $result_data[$key]["have_child"] = 0;
                } else {
                    $result_data[$key]["have_child"] = 1;
                }
            } elseif ($type == "campaign") {
                if ($key == "Shortlink") {
                    $result_data[$key]["have_child"] = 0;
                } else {
                    $result_data[$key]["have_child"] = 1;
                }
            } elseif ($type == "adset") {

                if ($source == "organic") {
                    $result_data[$key]["have_child"] = 0;
                } else {
                    $result_data[$key]["have_child"] = 1;
                }
            } else {
                $result_data[$key]["have_child"] = 1;
            }
        }

        $keys = array();
        foreach ($result_data as $key => $value) {
            foreach ($value as $k => $v) {
                if (!isset($keys[$k])) {
                    $keys[$k] = 1;
                }
            }
        }

        foreach ($result_data as $key => $value) {
            foreach ($keys as $k => $v) {

                if (!isset($value[$k])) {

                    $result_data[$key][$k] = 0;
                }
            }

        }

        if (isset($sort_by) && isset($sort_type)) {
            if ($sort_type == "asc") {
                $sort = SORT_ASC;
            } else {
                $sort = SORT_DESC;
            }
            array_multisort(array_column($result_data, $sort_by), $sort, SORT_NUMERIC, $result_data);
        }

        if ($convertion_type == "visit") {

            foreach ($result_data as $key => $value) {
                foreach ($value as $key2 => $value2) {

                    if (!str_starts_with($key2, 'v-')) {

                        if ($key2 != "name" && $key2 != "have_child" && $key2 != "report_id") {
                            if ($value2 != 0 && $value2 != "") {
                                $result_data[$key]["v-" . $key2] = $value2;
                                unset($result_data[$key][$key2]);
                            }
                        }
                    }
                }

            }
        }

        foreach ($result_data as $key => $value) {
            foreach ($value as $key2 => $value2) {
                $new_key = str_replace(".", " ", $key2);
                $result_data[$key][$new_key] = $value2;
                // unset($result_data[$key][$key2]);

                // var_dump($new_key);
            }
        }
        // var_dump($result_data);

        return $result_data;
    }

    public function get_overview($convertion_type, $column, $start, $end, $type, $source, $parent_id, $sort_by, $sort_type)
    {

        global $app;
        $db2 = $app->db2;
        $level = 0;
        $data_level = 3;
        if (!is_array($column)) {
            return false;
        }

        $source = strtolower($source);

        if ($source == "google") {
            $data_level = 2;
//            if($type=='adset'){
//                $data_level = 2;
//            }elseif($type=='adcopy'){
//                $data_level = 3;
//            }
            if($type=='adcopy'){
                $data_level = 3;
            }
        } elseif ($source == "organic") {
            $data_level = 2;
        } elseif ($source == "shortlink") {
            $data_level = 2;
        }
        if ($type == "platform") {
            $data_level = 1;
        }

        $report_data = $this->get_report_data_child($convertion_type, $column, $start, $end, $source, $type, $data_level, $parent_id);
        if ($source == "google") {
            $data_level = 1;
            if($type=='adset'){
                //echo $db2->getLastQuery();
                $data_level = 2;
            }elseif($type=='adcopy'){
                $data_level = 3;
                //echo $db2->getLastQuery();
            }else{
                $report_data2 = $this->get_report_data_child($convertion_type, $column, $start, $end, $source, $type, $data_level, $parent_id);
                $report_data = mergeAndSumTwoArrays($report_data, $report_data2);
            }
        }

        if ($convertion_type == "visit") {
            foreach ($report_data as $key => $value) {
                if (!str_starts_with($report_data[$key]["report_key"], 'v-')) {
                    $report_data[$key]["report_key"] = "v-" . $report_data[$key]["report_key"];
                }
            }
        }

        $formulas = $this->get_metric_set($column);

        $available_columns = array_unique(array_column($report_data, 'report_key'));

        $tokens_var = [];
        foreach ($formulas as $name => $formula) {
            $tokens_var = array_merge($tokens_var, $this->formulaGetVars($formula));

        }
        $tokens_var = array_unique($tokens_var);

        if (!empty($tokens_var)) {
            foreach ($tokens_var as $key => $token) {
                if (substr($key, 0, 12) == 'report_data:') {
                    if ($convertion_type == "visit") {
                        //if($token != "spend"){
                        $token = "v-" . $token;
                        //}
                    }
                    $token_columns[] = $token;
                }
            }

            $additional_columns = array_diff($token_columns, $available_columns);

            sort($additional_columns);

            if (count($additional_columns) > 0) {
                if ($convertion_type == "visit") {
                    foreach ($additional_columns as $key => $value) {
                        if (str_starts_with($additional_columns[$key], 'v-')) {
                            $additional_columns[$key] = ltrim($additional_columns[$key], "v-");
                        }
                    }
                }

                $additional_rows = $this->get_report_data_child($convertion_type, $additional_columns, $start, $end, $source, $type, $data_level, $parent_id);

                if ($convertion_type == "visit") {
                    foreach ($additional_rows as $key => $value) {
                        if (!str_starts_with($additional_rows[$key]["report_key"], 'v-')) {
                            $additional_rows[$key]["report_key"] = "v-" . $additional_rows[$key]["report_key"];
                        }
                    }
                }

                $report_data = array_merge($report_data, $additional_rows);
            }

            $grouped_report_data = [];
            $reports_info = [];
        }

        foreach ($report_data as $row) {
            // create grouped report_data array by report_id, date
            $grouped_report_data[$row['report_id']][] = $row;

            // create reports_info by report_id
            $report_info[$row['report_id']] = ['name' => $row['name'], 'source' => $row['source']];
        }

        $i = 0;

        foreach ($grouped_report_data as $report_id => &$report) {

            // find column that have no data record
            $non_existing_report_keys = array_diff($column, array_column($report, 'report_key'));

            if (!empty($non_existing_report_keys)) {

                foreach ($non_existing_report_keys as $report_key) {

                    // $formula_name = $report_key;
                    // unprocessing custom report data skeleton
                    $formula = !empty($formulas[$report_key]) ? $formulas[$report_key] : null;
                    $custom_report_item = [
                        'source' => $report_info[$report_id]['source'],
                        'name' => $report_info[$report_id]['name'],
                        'report_id' => $report_id,
                        'report_key' => $report_key,
                        'report_value' => null, // $API->get_metric_formula($report_key),
                        'type' => 'custom_report',

                        'formula' => $formula ? self::formulaPrettyPrint($formula) : null,
                        'formula_tokens' => $formula ? self::getTokens($formula) : null,
                    ];

                    // debug($custom_report_item);

                    // calculating custom report value if formula exists
                    if ($formula) {
                        try {
                            // data to calculate formula: token => value
                            $data = array_column($report, 'report_value', 'report_key');

                            if ($convertion_type == "visit") {
                                $tokens = self::getTokens($formula);
                                // $tokens_var = self::tokensGetVars($tokens);

                                foreach ($tokens as $key => $value) {

                                    if (substr($value, 0, 12) == 'report_data:') {
                                        $cc = substr($value, 12);

                                        //if( $cc != "impression" &&  $cc != "spend" &&  $cc != "lp_view"){
                                        $tokens[$key] = str_replace($cc, "v-" . $cc, $value);
                                        // }
                                    }

                                }

                                $formula = '["' . implode('","', $tokens) . '"]';
                            }

                            // debug($custom_report_item, $data);
                            $report_value = self::calculateMetric($formula, $data);

                            $custom_report_item['report_value'] = $report_value;
                            // calculating formula done, append custom report

                            $report[] = $custom_report_item;

                        } catch (Exception $e) {
                            //$app::jsonResponse([], $e->getMessage(), 500);

                        }
                    } else {
                        // no report or custom report with formula found
                    }
                }
                // debug($report);
            }
            // debug($report_by_date);

        }

        //var_dump($grouped_report_data);

/*
$tmp = array();
// foreach ($grouped_report_data as $key => $value) {
// ungroup/flatten $grouped_report_data
$tmp2[] = $value;

$ungrouped_report_data = array_reduce($grouped_report_data, function($carry, $item) {
foreach ($item as $subItem) {
//var_dump($carry);
//var_dump($item);
//  var_dump($subItem);
$carry = array_merge($carry, $subItem);
//die();
}
return $carry;
}, []);

$tmp[] = $ungrouped_report_data;

// }
 */

        $ungrouped_report_data = array();
        foreach ($grouped_report_data as $report_id => $value3) {

            foreach ($value3 as $key => $value2) {
                if (isset($value2["name"])) {
                    $ungrouped_report_data[$report_id]["name"] = $value2["name"];
                }
                $ungrouped_report_data[$report_id][$value2["report_key"]] = $value2["report_value"];

            }

            //$ungrouped_report_data[] = $tmp;
        }

        // filter $ungrouped_report_data by requested columns
        $column_filtered_report_data = array_filter($ungrouped_report_data, function ($item) use ($columns) {
            return empty($columns) ? true : in_array($item['report_key'], $columns);
        });

        // reassign processed report_data to $report_data
        $report_data = $column_filtered_report_data;
        //$response['items'] = array_values($report_data);

        // var_dump($column_filtered_report_data);die();

        $result_data = array();

        foreach ($report_data as $key => $value) {

            if (isset($report_data[$key]["spend"]) && isset($report_data[$key]["purchase_value"])) {
                            $report_data[$key]["ROAS"] = ceil($report_data[$key]["purchase_value"] / $report_data[$key]["spend"] * 1000);
                        }

                        if (isset($report_data[$key]["spend"]) && isset($report_data[$key]["impressions"])) {
                            $report_data[$key]["cpm"] = ceil($report_data[$key]["spend"] / $report_data[$key]["impressions"] * 1000);
                        }
                        if (isset($report_data[$key]["outbound_clicks"]) && isset($report_data[$key]["impressions"])) {
                            $report_data[$key]["outbound_clicks_ctr"] = round($report_data[$key]["outbound_clicks"] / $report_data[$key]["impressions"] * 100, 2);
                        }
        }

        foreach ($report_data as $key => $value) {
           

            foreach ($column as $key2 => $c) {
                if (!isset($value[$c])) {
                    $value[$c] = 0;
                }
            }

            $value["report_id"] = $key;

            $result_data[$key] = $value;
            if ($type == "platform") {
                if ($key == "Unknown") {
                    $result_data[$key]["have_child"] = 0;
                } else {
                    $result_data[$key]["have_child"] = 1;
                }
            } elseif ($type == "campaign") {
                if ($key == "Shortlink") {
                    $result_data[$key]["have_child"] = 0;
                } else {
                    $result_data[$key]["have_child"] = 1;
                }
            } elseif ($type == "adset") {

                if ($source == "organic") {
                    $result_data[$key]["have_child"] = 0;
                } else {
                    $result_data[$key]["have_child"] = 1;
                }
            } else {
                $result_data[$key]["have_child"] = 1;
            }
        }

        $keys = array();
        foreach ($result_data as $key => $value) {
            foreach ($value as $k => $v) {
                if (!isset($keys[$k])) {
                    $keys[$k] = 1;
                }
            }
        }

        foreach ($result_data as $key => $value) {
            foreach ($keys as $k => $v) {

                if (!isset($value[$k])) {

                    $result_data[$key][$k] = 0;
                }
            }

        }

        if (isset($sort_by) && isset($sort_type)) {
            if ($sort_type == "asc") {
                $sort = SORT_ASC;
            } else {
                $sort = SORT_DESC;
            }
            array_multisort(array_column($result_data, $sort_by), $sort, SORT_NUMERIC, $result_data);
        }

        if ($convertion_type == "visit") {

            foreach ($result_data as $key => $value) {
                foreach ($value as $key2 => $value2) {

                    if (!str_starts_with($key2, 'v-')) {

                        if ($key2 != "name" && $key2 != "have_child" && $key2 != "report_id") {
                            if ($value2 != 0 && $value2 != "") {
                                $result_data[$key]["v-" . $key2] = $value2;
                                unset($result_data[$key][$key2]);
                            }
                        }
                    }
                }

            }
        }

        foreach ($result_data as $key => $value) {
            foreach ($value as $key2 => $value2) {
                $new_key = str_replace(".", " ", $key2);
                $result_data[$key][$new_key] = $value2;
                // unset($result_data[$key][$key2]);

                // var_dump($new_key);
            }
        }
        // var_dump($result_data);

        return $result_data;
    }

    public function add_hash($key)
    {
        global $app;
        $db2 = $app->db2;
        $hash = hash('sha256', $key);
        $report_hash_data['hash'] = $db2->func("UNHEX(?)", [$hash]);
        $report_hash_data["value"] = $key;
        $report_hash_data["created"] = date("Y-m-d H:i:s");

        if ($db2->setQueryOption(array(

            'IGNORE',
        ))
            ->insert("report_hash", $report_hash_data)) {
            return true;
        }
        return false;
    }

    public function get_hash($key)
    {
        global $app;
        $db2 = $app->db2;

        $hash = hash('sha256', $key);
        $db2->where("hash = UNHEX(?)", [$hash]);
        return $db2->getone("report_hash");
    }

    public function add_report($source, $type, $external_id, $data_campaign = null, $value = 0)
    {

        global $app;
        $db2 = $app->db2;

        if ($type == "lp_view") {
            $table_type = "lp_view";
        } elseif ($type == "cta") {
            $table_type = "cta";
        } elseif ($type == "unique_cta") {
            $table_type = "unique_cta";
        } elseif ($type == "contact") {
            $table_type = "contact";
        } elseif ($type == "mql") {
            $table_type = "mql";
        } elseif ($type == "prospek") {
            $table_type = "prospek";
        } elseif ($type == "purchase") {
            $table_type = "purchase";
        } else {
            return false;
        }

        $db2->where("external_key = UNHEX(?)", [sha1($external_id)]);
        $tmp = $db2->getone("report");

        if ($tmp == null) {
            if ($source == "meta") {
                extract($data_campaign);
                if (isset($gcn) && isset($gadsn) && isset($gadcn) && isset($gcid) && isset($gadsid) && isset($gadcid)) {
                    $cat_id = $this->add_report_kolom($source, $gcn, "campaign", $gcid, null)["data"];
                    $cat_id = $this->add_report_kolom($source, $gadsn, "adset", $gadsid, $cat_id)["data"];
                    $report_id = $this->add_report_kolom($source, $gadcn, "adcopy", $gadcid, $cat_id)["data"];

                } else {
                    extract($data_campaign);
                    if (isset($page_url)) {
                        $external_id = "page-" . $page_url;
                        $report_id = $this->add_report_kolom($source, $page_url, "campaign", $external_id)["data"];

                        $ref = str_replace("https://", "", $ref);
                        $ref = str_replace("http://", "", $ref);
                        $ref = rtrim($ref, '/');
                        $external_id = $page_url . "-ref-" . $ref;
                        $report_id = $this->add_report_kolom($source, $ref, "adset", $external_id, $report_id)["data"];

                    }
                }
            } elseif ($source == "tiktok") {
                extract($data_campaign);
                if (isset($gcn) && isset($gadsn) && isset($gadcn) && isset($gcid) && isset($gadsid) && isset($gadcid)) {
                    $cat_id = $this->add_report_kolom($source, $gcn, "campaign", $gcid, null)["data"];
                    $cat_id = $this->add_report_kolom($source, $gadsn, "adset", $gadsid, $cat_id)["data"];
                    $report_id = $this->add_report_kolom($source, $gadcn, "adcopy", $gadcid, $cat_id)["data"];

                } else {
                    extract($data_campaign);
                    if (isset($page_url)) {
                        $external_id = "page-" . $page_url;
                        $report_id = $this->add_report_kolom($source, $page_url, "campaign", $external_id)["data"];

                        $ref = str_replace("https://", "", $ref);
                        $ref = str_replace("http://", "", $ref);
                        $ref = rtrim($ref, '/');
                        $external_id = $page_url . "-ref-" . $ref;
                        $report_id = $this->add_report_kolom($source, $ref, "adset", $external_id, $report_id)["data"];

                    }
                }
            } elseif ($source == "shortlink") {

                extract($data_campaign);
                if (isset($page_url)) {
                    $external_id = "page-" . $page_url;
                    $report_id = $this->add_report_kolom($source, $page_url, "campaign", $external_id)["data"];

                    $ref = str_replace("https://", "", $ref);
                    $ref = str_replace("http://", "", $ref);
                    $ref = rtrim($ref, '/');
                    $external_id = $page_url . "-ref-" . $ref;
                    $report_id = $this->add_report_kolom($source, $ref, "adset", $external_id, $report_id)["data"];

                } else {
                    extract($data_campaign);
                    if (isset($page_url)) {
                        $external_id = "page-" . $page_url;
                        $report_id = $this->add_report_kolom($source, $page_url, "campaign", $external_id)["data"];

                        $ref = str_replace("https://", "", $ref);
                        $ref = str_replace("http://", "", $ref);
                        $ref = rtrim($ref, '/');
                        $external_id = $page_url . "-ref-" . $ref;
                        $report_id = $this->add_report_kolom($source, $ref, "adset", $external_id, $report_id)["data"];

                    }
                }
            } elseif ($source == "google") {

                extract($data_campaign);
                if (isset($gcn) && isset($gadsn) && isset($gadcn) && isset($gcid) && isset($gadsid) && isset($gadcid)) {
                    //$report_id = $this->add_report_kolom($source, $gcn, "campaign", $gcid, NULL) ["data"];
                    //  $cat_id = $this->add_report_kolom($source, $gadsn, "adset", $gadsid, $cat_id) ["data"];
                    //  $report_id = $this->add_report_kolom($source, $gadcn, "adcopy", $gadcid, $cat_id) ["data"];

                    $cat_id = $this->add_report_kolom($source, $gcn, "campaign", $gcid, null)["data"];
                    $cat_id = $this->add_report_kolom($source, $gadsn, "adset", $gadsid, $cat_id)["data"];
                    $report_id = $this->add_report_kolom($source, $gadsn, "adcopy", $gadsid, $cat_id)["data"];
                    //$report_id = $this->add_report_kolom($source, $gadcn, "adcopy", $gadcid, $cat_id) ["data"];
                } else {
                    extract($data_campaign);
                    if (isset($page_url)) {
                        $external_id = "page-" . $page_url;
                        $report_id = $this->add_report_kolom($source, $page_url, "campaign", $external_id)["data"];

                        $ref = str_replace("https://", "", $ref);
                        $ref = str_replace("http://", "", $ref);
                        $ref = rtrim($ref, '/');
                        $external_id = $page_url . "-ref-" . $ref;
                        $report_id = $this->add_report_kolom($source, $ref, "adset", $external_id, $report_id)["data"];

                    }
                }
            } elseif ($source == "snack") {
                extract($data_campaign);
                if (isset($gcn) && isset($gadsn) && isset($gadcn) && isset($gcid) && isset($gadsid) && isset($gadcid)) {
                    $cat_id = $this->add_report_kolom($source, $gcn, "campaign", $gcid, null)["data"];
                    $cat_id = $this->add_report_kolom($source, $gadsn, "adset", $gadsid, $cat_id)["data"];
                    $report_id = $this->add_report_kolom($source, $gadcn, "adcopy", $gadcid, $cat_id)["data"];

                } else {
                    extract($data_campaign);
                    if (isset($page_url)) {
                        $external_id = "page-" . $page_url;
                        $report_id = $this->add_report_kolom($source, $page_url, "campaign", $external_id)["data"];

                        $ref = str_replace("https://", "", $ref);
                        $ref = str_replace("http://", "", $ref);
                        $ref = rtrim($ref, '/');
                        $external_id = $page_url . "-ref-" . $ref;
                        $report_id = $this->add_report_kolom($source, $ref, "adset", $external_id, $report_id)["data"];

                    }
                }
                
            } elseif ($source == "mgid") {
                extract($data_campaign);
                if (isset($gcn) && isset($gadsn) && isset($gadcn) && isset($gcid) && isset($gadsid) && isset($gadcid)) {
                    $cat_id = $this->add_report_kolom($source, $gcn, "campaign", $gcid, null)["data"];
                    $cat_id = $this->add_report_kolom($source, $gadsn, "adset", $gadsid, $cat_id)["data"];
                    $report_id = $this->add_report_kolom($source, $gadcn, "adcopy", $gadcid, $cat_id)["data"];

                } else {
                    extract($data_campaign);
                    if (isset($page_url)) {
                        $external_id = "page-" . $page_url;
                        $report_id = $this->add_report_kolom($source, $page_url, "campaign", $external_id)["data"];

                        $ref = str_replace("https://", "", $ref);
                        $ref = str_replace("http://", "", $ref);
                        $ref = rtrim($ref, '/');
                        $external_id = $page_url . "-ref-" . $ref;
                        $report_id = $this->add_report_kolom($source, $ref, "adset", $external_id, $report_id)["data"];

                    }
                }
                
            }elseif ($source == "organic") {

                extract($data_campaign);
                if (isset($page_url)) {
                    $external_id = "page-" . $page_url;
                    $report_id = $this->add_report_kolom($source, $page_url, "campaign", $external_id)["data"];

                    $ref = str_replace("https://", "", $ref);
                    $ref = str_replace("http://", "", $ref);
                    $ref = rtrim($ref, '/');
                    $external_id = $page_url . "-ref-" . $ref;
                    $report_id = $this->add_report_kolom($source, $ref, "adset", $external_id, $report_id)["data"];

                }
            }

        } else {
            $report_id = $tmp["report_id"];
        }

        $this->add_report_data(null, $report_id, $type, $value);

        return $report_id;
    }

    public function add_report_kolom($source, $name, $type, $external_id, $parent = null, $update = false)
    {
        global $app;
        $db2 = $app->db2;
        $data_category = array();

        $name = substr($name, 0, 250); // Batasi $name agar muat ke varchar 250

        $data_category["source"] = $source;
        $data_category["name"] = $name;
        $data_category["type"] = $type;

        if ($parent != null) {
            $data_category["parent_id"] = $parent;
        }

        $data_category["external_id"] = $external_id;

        $data_category["external_key"] = $db2->func("UNHEX(?)", [sha1($external_id)]);

        if ($report_id = $db2->setQueryOption(array(
            'IGNORE',
        ))
            ->insert("report", $data_category))
        //if($report_id = $db2->insert("report",$data_category))

        {

            $ret["code"] = 1;
            $ret["msg"] = "sukses";
            $ret["data"] = $report_id;
        } else {
            $db2->where("external_key = UNHEX(?)", [sha1($external_id)]);
            $tmp = $db2->getone("report");

            $db2->where("external_key = UNHEX(?)", [sha1($external_id)]);
            $db2->update("report", ["name" => $name]);

            $ret["code"] = 0;
            $ret["msg"] = "already exist";
            $ret["data"] = $tmp["report_id"];
        }

        return $ret;
    }

    public function get_report_id($external_id)
    {
        global $app;
        $db2 = $app->db2;

        $db2->where("external_key = UNHEX(?)", [sha1($external_id)]);
        $report = $db2->getone("report");
        if ($report == null) {
            return false;
        } else {
            return $report["report_id"];
        }

    }

    

    public function add_report_data($visitor, $report_id, $type, $value = 0, $tanggal = null)
    {
        global $app;
        $db2 = $app->db2;
        $project_id = $app->project_id;

        $MEDIUMINT_MAX = 16777215;
        $numericId = (int) $report_id;
        if ($numericId >= $MEDIUMINT_MAX) {
            return false;
        }
/*
$m = new meta($project_id);
$convertion_record = $m->get_meta("convertion_record");
if ($convertion_record["code"] == 1)
{
$convertion_record = $convertion_record["result"]["data"];
}
else
{
$convertion_record = "realtime";
}
 */
        $data_insert["report_id"] = $report_id;

        /*
        if (is_null($tanggal) || $tanggal == "")
        {
        if ($convertion_record == "realtime")
        {
        $data_insert["date"] = date("Y-m-d");
        }
        elseif ($convertion_record == "visit")
        {
        if ($visitor == NULL)
        {
        $data_insert["date"] = date("Y-m-d");
        }
        else
        {
        if ($ ["visit"] == "0000-00-00 00:00:00" || $visitor["visit"] == NULL)
        {
        $data_insert["date"] = date("Y-m-d");
        }
        else
        {

        $data_insert["date"] = date("Y-m-d", strtotime($visitor["visit"]));
        }
        }
        }

        }
        else
        {
        $data_insert["date"] = $tanggal;
        }
         */

        if ($value == 0) {
            $data_update["report_value"] = $db2->inc(1);
            $data_insert["report_value"] = 1;
        } else {
            $data_update["report_value"] = $db2->inc($value);
            $data_insert["report_value"] = $value;
        }

        $db2->onDuplicate($data_update);

        $data_insert["date"] = date("Y-m-d");
        $data_insert["report_key"] = $type;

        $db2->insert("report_data", $data_insert);

        //////////////////////////// insert by visit
        if ($visitor == null) {
            $data_insert["date"] = date("Y-m-d");
        } else {
            if ($visitor["visit"] == "0000-00-00 00:00:00" || $visitor["visit"] == null) {
                $data_insert["date"] = date("Y-m-d");
            } else {
                $data_insert["date"] = date("Y-m-d", strtotime($visitor["visit"]));
            }
        }
        $data_insert["report_key"] = "v-" . $type;
        $db2->onDuplicate($data_update);
        $db2->insert("report_data", $data_insert);

/*
$log_data = "";
if($db2->insert("report_data", $data_insert))
{
// $log_data .= $db2->getLastQuery();
//  var_dump($db2->getLastQuery());
// @file_put_contents("log_report_data.txt", "[" . date("Y-m-d H:i:s") . "]\n" . $log_data . "\n\n\n\n", FILE_APPEND);
}
else{
//$log_data = json_encode($visitor) . "\n\n";
// $log_data .= $db2->getLastQuery();
//@file_put_contents("log_report_data.txt", "[" . date("Y-m-d H:i:s") . "]\n" . $log_data . "\n\n\n\n", FILE_APPEND);
}
 */

    }
    public function update_report_data($report_id, $type, $value = 0, $tanggal = null)
    {
        global $app;
        $db2 = $app->db2;
        $project_id = $app->project_id;

        $data_insert["report_id"] = $report_id;

        $data_insert["date"] = $tanggal;

        if ($value == 0) {
            $data_insert["report_value"] = 0;
            $data_update["report_value"] = 0;
        } else {
            $data_update["report_value"] = $value;
            $data_insert["report_value"] = $value;
        }
        $data_insert["report_key"] = $type;

        $db2->onDuplicate($data_update);

        $db2->insert("report_data", $data_insert);
    }

    public function add_metric($name, $formula)
    {
        global $app;
        $db2 = $app->db2;

        $formula = is_array($formula) ? json_encode($formula) : $formula;

        $db2->where("metric_name", $name);
        $c = $db2->getone("report_metric");

        if ($c != null) {
            $ret["code"] = 0;
            $ret["msg"] = "already exist";

            return $ret;
        }

        if ($db2->insert("report_metric", ["metric_name" => $name, "metric_formula" => $formula])) {
            $ret["code"] = 1;
            $ret["msg"] = "sukses";
        } else {
            $ret["code"] = 0;
            $ret["msg"] = "gagal";
        }

        return $ret;
    }
    public function get_metric($name = null)
    {
        global $app;
        $db2 = $app->db2;
        if (!is_null($name)) {
            $db2->where("metric_name", $name);
        }

        return $db2->get("report_metric");
    }

    public function get_metric_formula($name)
    {
        global $app;
        return $app
            ->db2
            ->row("SELECT metric_formula FROM report_metric WHERE metric_name=?", [$name]);
    }
    public function get_metric_set($names)
    {

        global $app;
        $app
            ->db2
            ->where("metric_name", $names, "in");
        $rows = $app
            ->db2
            ->get("report_metric");
        return array_column($rows, 'metric_formula', 'metric_name');
    }

    public static function commaSeparatedToArray($str)
    {
        return array_unique(array_filter(array_map('trim', explode(',', $str))));
    }
    public static function getTokens($formula)
    {
        return json_decode($formula, true);
        return array_filter(preg_split('/\s?[\(\)\*\/\+\-]\s?/', $formula), function ($var) {
            return $var && !is_numeric($var);
        });
    }
    public static function formulaPrettyPrint($formula)
    {
        return implode(' ', self::getTokens($formula));
    }
    public static function formulaGetVars($formula)
    {

        $tokens = self::getTokens($formula);
        $tokens_var = self::tokensGetVars($tokens);
        return $tokens_var;
    }
    public static function tokensGetVars(array $tokens)
    {
        $tokens_var = [];
        foreach ($tokens as $i => &$token) {
            if (is_array($token)) {
                continue;
            } elseif (in_array($token, ['*', '/', '+', '-', '(', ')', 'AND', 'OR'])) {
                continue;
            } elseif (strpos($token, ':') !== false) {
                list(, $val) = explode(':', $token);
                $tokens_var[$token] = $val;
            } elseif (substr($token, 0, 12) == 'report_data:') {
                $tokens_var[$token] = substr($token, 12);
            } elseif (substr($token, 0, 7) == 'report:') {
                $tokens_var[$token] = substr($token, 7);
            } elseif (is_numeric($token)) {
                continue;
            } elseif (is_string($token)) {
                $token = var_export($token, true);
            }
        }
        return $tokens_var;
    }
    public static function metricToEvalCode($formula, $data)
    {

        $tokens = self::getTokens($formula);
        $tokens_var = self::tokensGetVars($tokens);
        $data_key_diff = array_diff($tokens_var, array_keys($data));

        // debug($formula, $data, $tokens_var, $data_key_diff);
        if (!empty($data_key_diff)) {
            $missing = implode(', ', $data_key_diff);
            throw new \Exception("Missing data: {$missing}.");
        }
        foreach ($tokens_var as $key => &$token) {
            $token = $data[$token];
        }

        // debug($formula, $data, $tokens, $tokens_var);
        // Replace variables in the tokens with their corresponding values

        foreach ($tokens as &$token) {
            if (isset($tokens_var[$token])) {
                $token = $tokens_var[$token];
            }

        }

        $eval_code = implode('', $tokens);
        // debug($formula, $data, $tokens, $tokens_var, $eval_code);

        return $eval_code;
    }
    public static function calculateMetric($formula, $data)
    {

        $eval_code = self::metricToEvalCode($formula, $data);

        // debug($formula."\n", $data, $eval_code);
        // Use eval to calculate the result
        $result = null;
        // set_error_handler(function($errno, $errstr) { throw new \Exception('Invalid data.'); }, E_WARNING);
        //eval("\$result = $eval_code;");
        try {
            eval("\$result = $eval_code;");
        } catch (\Throwable $th) {

        }
        // restore_error_handler();
        if ($result === null) {
            throw new Exception('Invalid formula.');
        }

        return $result;
    }
    public static function calculateFormula($formula, $data)
    {
        // Create a whitelist of allowed operations
        $sanitizedFormula = preg_replace('/[^a-zA-Z0-9\(\)\+\-\*\/_\s]/', '', $formula);
        // Check if the sanitized formula is the same as the original formula
        if ($sanitizedFormula !== $formula) {
            throw new Exception('Invalid characters detected in the formula.');
        }

        $tokens = self::getTokens($formula);
        $diff = array_diff($tokens, array_keys($data));
        if (!empty($diff)) {
            $missing = implode(', ', $diff);
            throw new \Exception("Missing data: {$missing}.");
        }
        // Replace variables in the formula with their corresponding values
        foreach ($data as $var => $value) {
            $sanitizedFormula = preg_replace("/\b$var\b/", $value, $sanitizedFormula);
        }

        // Use eval to calculate the result
        $result = null;
        // set_error_handler(function($errno, $errstr) { throw new \Exception('Invalid data.'); }, E_WARNING);
        eval("\$result = $sanitizedFormula;");
        // restore_error_handler();
        if ($result === null) {
            throw new Exception('Invalid formula.');
        }

        return $result;
    }
    public static function isSequentialArray($array)
    {
        if (!is_array($array)) {
            return false; // Not an array

        }

        $expectedKey = 0;
        foreach ($array as $key => $value) {
            if ($key !== $expectedKey) {
                return false; // Keys are not consecutive integers

            }
            $expectedKey++;
        }

        return true; // Keys are consecutive integers

    }

}
