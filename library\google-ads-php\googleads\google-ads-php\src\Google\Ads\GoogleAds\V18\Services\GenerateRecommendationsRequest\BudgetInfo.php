<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/services/recommendation_service.proto

namespace Google\Ads\GoogleAds\V18\Services\GenerateRecommendationsRequest;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Current budget information of the campaign.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.services.GenerateRecommendationsRequest.BudgetInfo</code>
 */
class BudgetInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * Required. Current budget amount.
     * This field is necessary for the following recommendation_types if
     * budget_info is set:
     * CAMPAIGN_BUDGET
     *
     * Generated from protobuf field <code>optional int64 current_budget = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    protected $current_budget = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $current_budget
     *           Required. Current budget amount.
     *           This field is necessary for the following recommendation_types if
     *           budget_info is set:
     *           CAMPAIGN_BUDGET
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Services\RecommendationService::initOnce();
        parent::__construct($data);
    }

    /**
     * Required. Current budget amount.
     * This field is necessary for the following recommendation_types if
     * budget_info is set:
     * CAMPAIGN_BUDGET
     *
     * Generated from protobuf field <code>optional int64 current_budget = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return int|string
     */
    public function getCurrentBudget()
    {
        return isset($this->current_budget) ? $this->current_budget : 0;
    }

    public function hasCurrentBudget()
    {
        return isset($this->current_budget);
    }

    public function clearCurrentBudget()
    {
        unset($this->current_budget);
    }

    /**
     * Required. Current budget amount.
     * This field is necessary for the following recommendation_types if
     * budget_info is set:
     * CAMPAIGN_BUDGET
     *
     * Generated from protobuf field <code>optional int64 current_budget = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param int|string $var
     * @return $this
     */
    public function setCurrentBudget($var)
    {
        GPBUtil::checkInt64($var);
        $this->current_budget = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(BudgetInfo::class, \Google\Ads\GoogleAds\V18\Services\GenerateRecommendationsRequest_BudgetInfo::class);

