<?php header("Content-Type: application/javascript");header("Cache-Control: max-age=604800, public");?>
!function(t){var e={};function n(i){if(e[i])return e[i].exports;var o=e[i]={i:i,l:!1,exports:{}};return t[i].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(i,o,function(e){return t[e]}.bind(null,o));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=0)}([function(t,e,n){t.exports=n(1)},function(t,e){function n(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var i=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.options={overlayBackgroundColor:"#666666",overlayOpacity:.6,spinnerIcon:"ball-circus",spinnerColor:"#000",spinnerSize:"3x",overlayIDName:"overlay",spinnerIDName:"spinner",offsetY:0,offsetX:0,lockScroll:!1,containerID:null},this.stylesheetBaseURL="https://cdn.jsdelivr.net/npm/load-awesome@1.1.0/css/",this.spinner=null,this.spinnerStylesheetURL=null,this.numberOfEmptyDivForSpinner={"ball-8bits":16,"ball-atom":4,"ball-beat":3,"ball-circus":5,"ball-climbing-dot":1,"ball-clip-rotate":1,"ball-clip-rotate-multiple":2,"ball-clip-rotate-pulse":2,"ball-elastic-dots":5,"ball-fall":3,"ball-fussion":4,"ball-grid-beat":9,"ball-grid-pulse":9,"ball-newton-cradle":4,"ball-pulse":3,"ball-pulse-rise":5,"ball-pulse-sync":3,"ball-rotate":1,"ball-running-dots":5,"ball-scale":1,"ball-scale-multiple":3,"ball-scale-pulse":2,"ball-scale-ripple":1,"ball-scale-ripple-multiple":3,"ball-spin":8,"ball-spin-clockwise":8,"ball-spin-clockwise-fade":8,"ball-spin-clockwise-fade-rotating":8,"ball-spin-fade":8,"ball-spin-fade-rotating":8,"ball-spin-rotate":2,"ball-square-clockwise-spin":8,"ball-square-spin":8,"ball-triangle-path":3,"ball-zig-zag":2,"ball-zig-zag-deflect":2,cog:1,"cube-transition":2,fire:3,"line-scale":5,"line-scale-party":5,"line-scale-pulse-out":5,"line-scale-pulse-out-rapid":5,"line-spin-clockwise-fade":8,"line-spin-clockwise-fade-rotating":8,"line-spin-fade":8,"line-spin-fade-rotating":8,pacman:6,"square-jelly-box":2,"square-loader":1,"square-spin":1,timer:1,"triangle-skew-spin":1},this.originalBodyPosition="",this.originalBodyTop="",this.originalBodywidth=""}var e,i;return e=t,(i=[{key:"show",value:function(t){this.setOptions(t),this.addSpinnerStylesheet(),this.generateSpinnerElement(),this.options.lockScroll&&(document.body.style.overflow="hidden",document.documentElement.style.overflow="hidden"),this.generateAndAddOverlayElement()}},{key:"hide",value:function(){this.options.lockScroll&&(document.body.style.overflow="",document.documentElement.style.overflow="");var t=document.getElementById("loading-overlay-stylesheet");t&&(t.disabled=!0,t.parentNode.removeChild(t),document.getElementById(this.options.overlayIDName).remove(),document.getElementById(this.options.spinnerIDName).remove())}},{key:"setOptions",value:function(t){if(void 0!==t)for(var e in t)this.options[e]=t[e]}},{key:"generateAndAddOverlayElement",value:function(){var t="50%";0!==this.options.offsetX&&(t="calc(50% + "+this.options.offsetX+")");var e="50%";if(0!==this.options.offsetY&&(e="calc(50% + "+this.options.offsetY+")"),this.options.containerID&&document.body.contains(document.getElementById(this.options.containerID))){var n='<div id="'.concat(this.options.overlayIDName,'" style="display: block !important; position: absolute; top: 0; left: 0; overflow: auto; opacity: ').concat(this.options.overlayOpacity,"; background: ").concat(this.options.overlayBackgroundColor,'; z-index: 50; width: 100%; height: 100%;"></div><div id="').concat(this.options.spinnerIDName,'" style="display: block !important; position: absolute; top: ').concat(e,"; left: ").concat(t,'; -webkit-transform: translate(-50%); -ms-transform: translate(-50%); transform: translate(-50%); z-index: 9999;">').concat(this.spinner,"</div>"),i=document.getElementById(this.options.containerID);return i.style.position="relative",void i.insertAdjacentHTML("beforeend",n)}var o='<div id="'.concat(this.options.overlayIDName,'" style="display: block !important; position: fixed; top: 0; left: 0; overflow: auto; opacity: ').concat(this.options.overlayOpacity,"; background: ").concat(this.options.overlayBackgroundColor,'; z-index: 50; width: 100%; height: 100%;"></div><div id="').concat(this.options.spinnerIDName,'" style="display: block !important; position: fixed; top: ').concat(e,"; left: ").concat(t,'; -webkit-transform: translate(-50%); -ms-transform: translate(-50%); transform: translate(-50%); z-index: 9999;">').concat(this.spinner,"</div>");document.body.insertAdjacentHTML("beforeend",o)}},{key:"generateSpinnerElement",value:function(){var t=this,e=Object.keys(this.numberOfEmptyDivForSpinner).find((function(e){return e===t.options.spinnerIcon})),n=this.generateEmptyDivElement(this.numberOfEmptyDivForSpinner[e]);this.spinner='<div style="color: '.concat(this.options.spinnerColor,'" class="la-').concat(this.options.spinnerIcon," la-").concat(this.options.spinnerSize,'">').concat(n,"</div>")}},{key:"addSpinnerStylesheet",value:function(){this.setSpinnerStylesheetURL();var t=document.createElement("link");t.setAttribute("id","loading-overlay-stylesheet"),t.setAttribute("rel","stylesheet"),t.setAttribute("type","text/css"),t.setAttribute("href",this.spinnerStylesheetURL),document.getElementsByTagName("head")[0].appendChild(t)}},{key:"setSpinnerStylesheetURL",value:function(){this.spinnerStylesheetURL=this.stylesheetBaseURL+this.options.spinnerIcon+".min.css"}},{key:"generateEmptyDivElement",value:function(t){for(var e="",n=1;n<=t;n++)e+="<div></div>";return e}}])&&n(e.prototype,i),t}();window.JsLoadingOverlay=new i,t.exports=JsLoadingOverlay}]),function(t,e,n,i,o,r,a){t.fbq||(o=t.fbq=function(){o.callMethod?o.callMethod.apply(o,arguments):o.queue.push(arguments)},t._fbq||(t._fbq=o),o.push=o,o.loaded=!0,o.version="2.0",o.queue=[],(r=e.createElement(n)).async=!0,r.src="https://connect.facebook.net/en_US/fbevents.js",(a=e.getElementsByTagName(n)[0]).parentNode.insertBefore(r,a))}(window,document,"script"),function(t,e,n){t.TiktokAnalyticsObject=n;var i=t[n]=t[n]||[];i.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"],i.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var o=0;o<i.methods.length;o++)i.setAndDefer(i,i.methods[o]);i.instance=function(t){for(var e=i._i[t]||[],n=0;n<i.methods.length;n++)i.setAndDefer(e,i.methods[n]);return e},i.load=function(t,e){var o="https://analytics.tiktok.com/i18n/pixel/events.js";i._i=i._i||{},i._i[t]=[],i._i[t]._u=o,i._t=i._t||{},i._t[t]=+new Date,i._o=i._o||{},i._o[t]=e||{},(e=document.createElement("script")).type="text/javascript",e.async=!0,e.src=o+"?sdkid="+t+"&lib="+n,(t=document.getElementsByTagName("script")[0]).parentNode.insertBefore(e,t)}}(window,document,"ttq"),"function"!=typeof Array.prototype.indexOf&&(Array.prototype.indexOf=function(t){for(var e=0;e<this.length;e++)if(this[e]===t)return e;return-1}),window.addEventListener("DOMContentLoaded",(function(){for(var t=document.querySelectorAll("a"),e=0;e<t.length;e++)t[e].href.indexOf("/cta")}),!0),window.gass=function(){function t(t){for(var e=0;e<t.length;e++)this[e]=t[e];this.length=t.length}t.prototype.forEach=function(t){return this.map(t),this},t.prototype.map=function(t){for(var e=[],n=0;n<this.length;n++)e.push(t.call(this,this[n],n));return e},t.prototype.mapOne=function(t){var e=this.map(t);return e.length>1?e:e[0]},t.prototype.text=function(t){return void 0!==t?this.forEach((function(e){e.innerText=t})):this.mapOne((function(t){return t.innerText}))},t.prototype.html=function(t){return void 0!==t?this.forEach((function(e){e.innerHTML=t})):this.mapOne((function(t){return t.innerHTML}))},t.prototype.addClass=function(t){var e="";if("string"!=typeof t)for(var n=0;n<t.length;n++)e+=" "+t[n];else e=" "+t;return this.forEach((function(t){t.className+=e}))},t.prototype.removeClass=function(t){return this.forEach((function(e){for(var n,i=e.className.split(" ");(n=i.indexOf(t))>-1;)i=i.slice(0,n).concat(i.slice(++n));e.className=i.join(" ")}))},t.prototype.attr=function(t,e){return void 0!==e?this.forEach((function(n){n.setAttribute(t,e)})):this.mapOne((function(e){return e.getAttribute(t)}))},t.prototype.append=function(t){return this.forEach((function(e,n){t.forEach((function(t){e.appendChild(n>0?t.cloneNode(!0):t)}))}))},t.prototype.prepend=function(t){return this.forEach((function(e,n){for(var i=t.length-1;i>-1;i--)e.insertBefore(n>0?t[i].cloneNode(!0):t[i],e.firstChild)}))},t.prototype.remove=function(){return this.forEach((function(t){return t.parentNode.removeChild(t)}))},t.prototype.on=document.addEventListener?function(t,e){return this.forEach((function(n){n.addEventListener(t,e,!1)}))}:document.attachEvent?function(t,e){return this.forEach((function(n){n.attachEvent("on"+t,e)}))}:function(t,e){return this.forEach((function(n){n["on"+t]=e}))},t.prototype.off=document.removeEventListener?function(t,e){return this.forEach((function(n){n.removeEventListener(t,e,!1)}))}:document.detachEvent?function(t,e){return this.forEach((function(n){n.detachEvent("on"+t,e)}))}:function(t,e){return this.forEach((function(e){e["on"+t]=null}))},t.prototype.updatelink=function(t){if(e.countpost<2)if(null==e.getCookie("visitor_id")&&0==e.countpost){try{e.post_visit(e.campaign_id,"v_visit",(function(){e.post_visit(e.campaign_id,"v_cron",(function(){}))}))}catch(t){(n=new FormData).append("message",t.message),n.append("stack",t.stack),n.append("url",window.location.href),n.append("project_key",e.pkey),e.request_post("https://"+e.subdomain+"/api.html?act=bug_js_error",n,(function(t){}))}null!=t&&t(e)}else{try{e.visitor_id=e.getCookie("visitor_id"),e.updateCta(),e.post_visit(e.campaign_id,"v_visit",(function(){e.post_visit(e.campaign_id,"v_cron",(function(){}))}))}catch(t){var n;(n=new FormData).append("message",t.message),n.append("stack",t.stack),n.append("url",window.location.href),n.append("project_key",e.pkey),e.request_post("https://"+e.subdomain+"/api.html?act=bug_js_error",n,(function(t){}))}null!=t&&t(e)}};var e={version:"0.0.1",interval:2e3,timer:null,timer1:null,timer2:null,timer3:null,param_get:{},connector:[],pkey:null,page_url:null,ip:null,browser_agent:null,domain:null,subdomain:null,id:null,visitor_id:null,fbp:null,fbc:null,ref:null,fbclid:null,gclid:null,ttclid:null,adw_tag:null,_ttp:null,countpost:0,getCookie:function(t){const e=`; ${document.cookie}`.split(`; ${t}=`);if(2===e.length)return e.pop().split(";").shift()},run:function(e,n){var i=this;e="object"==typeof e&&e||{},this.connector=e.connector||this.connector,this.pkey=e.pkey||this.pkey,this.interval=e.interval||this.interval,this.subdomain=e.subdomain,null!=e.adw_tag&&(this.adw_tag=e.adw_tag);var o=window.location.href;o=(o=o.replace("https://www.","")).replace("http://www.","");try{this.domain=o.replace("http://","").replace("https://","").split(/[/?#]/)[0];for(var r=window.location.search.substr(1).split("&"),a=0;a<r.length;a++){var s=r[a].split("=");this.param_get[decodeURIComponent(s[0])]=decodeURIComponent(s[1])}void 0!==document.referrer&&(this.ref=document.referrer),window.clearInterval(this.timer),window.clearInterval(this.timer1),window.clearInterval(this.timer2);var l=new t(this.get("a"));i.request_get("https://ip.gass.co.id/",(function(t){if(1==t.code){i.ip=t.msg;var e=t.msg;let n=new Date;n.setTime(n.getTime()+864e5);const o="expires="+n.toUTCString();document.cookie="ip_gass="+e+"; "+o+"; path=/"}})),l.updatelink((function(t){void 0!==n&&n(t)})),i.timer=window.setInterval((function(){i.fbp=i.getCookie("_fbp"),void 0!==i.fbp&&(i.post_visit(i.campaign_id,"v_update",(function(){})),window.clearInterval(i.timer))}),2e3),i.timer1=window.setInterval((function(){i.fbc=i.getCookie("_fbc"),void 0!==i.fbc&&(i.post_visit(i.campaign_id,"v_update",(function(){})),window.clearInterval(i.timer1))}),2e3),i.timer2=window.setInterval((function(){void 0!==i.getCookie("client_id")&&(i.post_visit(i.campaign_id,"v_update",(function(){})),window.clearInterval(i.timer2))}),2e3),i.timer3=window.setInterval((function(){var t=i.getCookie("_ttp");void 0!==t&&(i._ttp=t,i.post_visit(i.campaign_id,"v_update",(function(){})),window.clearInterval(i.timer3))}),2e3),i.timer_link(i)}catch(t){var c=new FormData;c.append("message",t.message),c.append("stack",t.stack),c.append("url",o),c.append("project_key",i.pkey),i.request_post("https://"+i.subdomain+"/api.html?act=bug_js_error",c,(function(t){}))}},timer_link:function(t){setInterval((function(){null!=t&&t.updateCta()}),3e3)},append_googletag:function(t,e,n){var i=t.getElementsByTagName(e)[0],o=t.createElement(e);o.async=!0,o.src="https://www.googletagmanager.com/gtag/js?id="+n,i.parentNode.insertBefore(o,i)},gtag:function(){window.dataLayer=window.dataLayer||[],dataLayer.push(arguments)},check_src:function(t){for(var e=document.getElementsByTagName("script"),n=0;n<e.length;n++)if(e[n].getAttribute("src")===t)return!0;return!1},post_visit:function(e,n,i){var o=this;o.countpost++,o.ip=o.getCookie("ip_gass"),o.fbp=o.getCookie("_fbp"),o.fbc=o.getCookie("_fbc");var r=new FormData;r=o.appendFormdata(r),o.request_post("https://"+o.subdomain+"/api.html?act="+n,r,(function(e){if(null!=e.visitor_id){o.visitor_id=e.visitor_id;var n=e.visitor_id;let i=new Date;i.setTime(i.getTime()+2592e6);const r="expires="+i.toUTCString();document.cookie="visitor_id="+n+"; "+r+"; path=/",null!=e.connector&&Object.keys(e.connector).forEach((t=>{if("google ads"==e.connector[t].type){var n=e.connector[t].data.global_tag;0==o.check_src("https://www.googletagmanager.com/gtag/js?id="+n)&&(o.append_googletag(document,"script",n),o.gtag("js",new Date),o.gtag("config",n),o.gtag("event","page_view",{send_to:n,user_id:e.connector[t].data.customer_id}))}else if("google analytic"==e.connector[t].type){var i=e.connector[t].data.measurement_id;0==o.check_src("https://www.googletagmanager.com/gtag/js?id="+i)&&(o.append_googletag(document,"script",i),o.gtag("js",new Date),o.gtag("config",i),o.gtag("get",i,"client_id",(function(t){o.param_get.clientId=t,document.cookie="client_id="+t+"; "+r+"; path=/"})))}else"facebook"==e.connector[t].type?(o.fbq=fbq,o.fbq("init",e.connector[t].data.pixel_id,{extern_id:o.visitor_id,eventID:"ViewContent-"+o.visitor_id}),o.fbq("track","ViewContent")):"tiktok"==e.connector[t].type&&(o.ttq=ttq,o.ttq.load(e.connector[t].data.pixel_id),o.ttq.page(),o.ttq.track("ViewContent",{user:[{external_id:e.connector[t].data.visitor_id_hash}],event_id:"ViewContent-"+o.visitor_id}))})),o.updateCta();new t(o.get("a"))}null!=i&&i(o)}))},post_click:function(t){for(var e=this,n=t.substr(1).split("&"),i={},o=0;o<n.length;o++){var r=n[o].split("=");i[decodeURIComponent(r[0])]=decodeURIComponent(r[1])}e.countpost++,e.fbp=e.getCookie("_fbp"),e.fbc=e.getCookie("_fbc"),e.visitor_id=e.getCookie("visitor_id");var a=new FormData;if(void 0!==typeof e.visitor_id&&null!==e.visitor_id&&a.append("visitor_id",e.visitor_id),void 0!==i.p&&a.append("project_key",i.p),void 0!==i.divisi&&a.append("divisi",i.divisi),void 0!==i.msg){var s=i.msg;s=s.replace(/\+/g," "),a.append("msg",s)}e.isMobileDevice()?a.append("use_deeplink",1):a.append("use_deeplink",0),a=e.appendFormdata(a),e.request_post("https://"+e.subdomain+"/api.html?act=v_cta",a,(function(t){window.location=t.wa_url}))},isMobileDevice:function(t){return void 0!==window.orientation||-1!==navigator.userAgent.indexOf("IEMobile")},request_post:function(t,e,n){var i=new XMLHttpRequest;i.open("POST",t,!0),i.onreadystatechange=function(){if(i.readyState===XMLHttpRequest.DONE)if(i.status>=200&&i.status<400){var t=i.responseText;t&&n(JSON.parse(t))}else{n({code:0,msg:"Request failed"})}},i.onerror=function(){n({code:0,msg:"Request Error"})},i.send(e)},request_get:function(t,e){var n=new XMLHttpRequest;n.open("GET",t,!0),n.onreadystatechange=function(){if(n.readyState===XMLHttpRequest.DONE)if(n.status>=200&&n.status<400){var t=n.responseText;if(t)e({code:1,msg:t})}else{e({code:0,msg:"Request failed"})}},n.onerror=function(){e({code:0,msg:"Request Error"})},n.send()},appendFormdata:function(t){var e=this;e.ip=e.getCookie("ip_gass"),t.append("domain",e.domain),t.append("page_url",window.location),void 0!==e.ip&&t.append("ip",e.ip),void 0!==typeof e.visitor_id||null!==typeof e.visitor_id?t.append("visitor_id",e.visitor_id):(e.visitor_id=e.getCookie("visitor_id"),void 0===typeof e.visitor_id&&null===typeof e.visitor_id||t.append("visitor_id",e.visitor_id)),void 0===e.key&&null===e.key||t.append("project_key",e.pkey),void 0!==e.fbc&&void 0!==e.fbc&&null!==e.fbc&&t.append("fbc",e.fbc),void 0!==e.fbp&&void 0!==e.fbp&&null!==e.fbp&&t.append("fbp",e.fbp),void 0!==e._ttp&&void 0!==e._ttp&&null!==e._ttp&&t.append("_ttp",e._ttp),null!==e.ref&&t.append("ref",e.ref);var n=e.getCookie("client_id");return void 0!==n&&t.append("clientId",n),e.connector.forEach((function(e){t.append("connector[]",e)})),Object.keys(e.param_get).forEach((n=>{""!=n&&null!=e.param_get[n]&&t.append(n,e.param_get[n])})),t},updateCta:function(){var t=this,e=this.get("a");t.getCookie("visitor_id");e.forEach((function(e){if(void 0!==t.visitor_id&&(-1!=e.href.indexOf("https://"+t.subdomain+"/cta")||-1!=e.href.indexOf("http://"+t.subdomain+"/cta"))){for(var n=e.href.substr(1).split("&"),i={},o=0;o<n.length;o++){var r=n[o].split("=");i[decodeURIComponent(r[0])]=decodeURIComponent(r[1])}if(void 0!==i.v)var a=i.v;if(null!=a);else if(null==a&&null!=t.visitor_id){var s=e.cloneNode(!0);-1==e.href.indexOf("p=")?s.href=e.href+"&v="+t.visitor_id+"&p="+t.pkey:s.href=e.href+"&v="+t.visitor_id,s.addEventListener("click",(e=>{e.preventDefault(),t.post_click(s.href)})),s.style.display="",e.replaceWith(s)}t.get(e).attr("target","_self")}}))},get:function(e){return new t("string"==typeof e?document.querySelectorAll(e):e.length?e:[e])},create:function(e,n){var i=new t([document.createElement(e)]);if(n)for(var o in n.className&&(i.addClass(n.className),delete n.className),n.text&&(i.text(n.text),delete n.text),n)n.hasOwnProperty(o)&&i.attr(o,n[o]);return i}};return e}();