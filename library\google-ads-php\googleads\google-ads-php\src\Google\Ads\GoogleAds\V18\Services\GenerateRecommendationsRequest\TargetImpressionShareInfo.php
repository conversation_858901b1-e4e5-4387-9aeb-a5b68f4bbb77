<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/services/recommendation_service.proto

namespace Google\Ads\GoogleAds\V18\Services\GenerateRecommendationsRequest;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Current Target Impression Share information of the campaign.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.services.GenerateRecommendationsRequest.TargetImpressionShareInfo</code>
 */
class TargetImpressionShareInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * Required. The targeted location on the search results page.
     * This is required for campaigns where the AdvertisingChannelType is SEARCH
     * and the bidding strategy type is TARGET_IMPRESSION_SHARE.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.TargetImpressionShareLocationEnum.TargetImpressionShareLocation location = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    protected $location = null;
    /**
     * Required. The chosen fraction of targeted impression share in micros. For
     * example, 1% equals 10,000. It must be a value between 1 and 1,000,000.
     * This is required for campaigns with an AdvertisingChannelType of SEARCH
     * and a bidding strategy type of TARGET_IMPRESSION_SHARE.
     *
     * Generated from protobuf field <code>optional int64 target_impression_share_micros = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    protected $target_impression_share_micros = null;
    /**
     * Optional. Ceiling of max CPC bids in micros set by automated bidders.
     * This is optional for campaigns with an AdvertisingChannelType of SEARCH
     * and a bidding strategy type of TARGET_IMPRESSION_SHARE.
     *
     * Generated from protobuf field <code>optional int64 max_cpc_bid_ceiling = 3 [(.google.api.field_behavior) = OPTIONAL];</code>
     */
    protected $max_cpc_bid_ceiling = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $location
     *           Required. The targeted location on the search results page.
     *           This is required for campaigns where the AdvertisingChannelType is SEARCH
     *           and the bidding strategy type is TARGET_IMPRESSION_SHARE.
     *     @type int|string $target_impression_share_micros
     *           Required. The chosen fraction of targeted impression share in micros. For
     *           example, 1% equals 10,000. It must be a value between 1 and 1,000,000.
     *           This is required for campaigns with an AdvertisingChannelType of SEARCH
     *           and a bidding strategy type of TARGET_IMPRESSION_SHARE.
     *     @type int|string $max_cpc_bid_ceiling
     *           Optional. Ceiling of max CPC bids in micros set by automated bidders.
     *           This is optional for campaigns with an AdvertisingChannelType of SEARCH
     *           and a bidding strategy type of TARGET_IMPRESSION_SHARE.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Services\RecommendationService::initOnce();
        parent::__construct($data);
    }

    /**
     * Required. The targeted location on the search results page.
     * This is required for campaigns where the AdvertisingChannelType is SEARCH
     * and the bidding strategy type is TARGET_IMPRESSION_SHARE.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.TargetImpressionShareLocationEnum.TargetImpressionShareLocation location = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return int
     */
    public function getLocation()
    {
        return isset($this->location) ? $this->location : 0;
    }

    public function hasLocation()
    {
        return isset($this->location);
    }

    public function clearLocation()
    {
        unset($this->location);
    }

    /**
     * Required. The targeted location on the search results page.
     * This is required for campaigns where the AdvertisingChannelType is SEARCH
     * and the bidding strategy type is TARGET_IMPRESSION_SHARE.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.TargetImpressionShareLocationEnum.TargetImpressionShareLocation location = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param int $var
     * @return $this
     */
    public function setLocation($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\TargetImpressionShareLocationEnum\TargetImpressionShareLocation::class);
        $this->location = $var;

        return $this;
    }

    /**
     * Required. The chosen fraction of targeted impression share in micros. For
     * example, 1% equals 10,000. It must be a value between 1 and 1,000,000.
     * This is required for campaigns with an AdvertisingChannelType of SEARCH
     * and a bidding strategy type of TARGET_IMPRESSION_SHARE.
     *
     * Generated from protobuf field <code>optional int64 target_impression_share_micros = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return int|string
     */
    public function getTargetImpressionShareMicros()
    {
        return isset($this->target_impression_share_micros) ? $this->target_impression_share_micros : 0;
    }

    public function hasTargetImpressionShareMicros()
    {
        return isset($this->target_impression_share_micros);
    }

    public function clearTargetImpressionShareMicros()
    {
        unset($this->target_impression_share_micros);
    }

    /**
     * Required. The chosen fraction of targeted impression share in micros. For
     * example, 1% equals 10,000. It must be a value between 1 and 1,000,000.
     * This is required for campaigns with an AdvertisingChannelType of SEARCH
     * and a bidding strategy type of TARGET_IMPRESSION_SHARE.
     *
     * Generated from protobuf field <code>optional int64 target_impression_share_micros = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param int|string $var
     * @return $this
     */
    public function setTargetImpressionShareMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->target_impression_share_micros = $var;

        return $this;
    }

    /**
     * Optional. Ceiling of max CPC bids in micros set by automated bidders.
     * This is optional for campaigns with an AdvertisingChannelType of SEARCH
     * and a bidding strategy type of TARGET_IMPRESSION_SHARE.
     *
     * Generated from protobuf field <code>optional int64 max_cpc_bid_ceiling = 3 [(.google.api.field_behavior) = OPTIONAL];</code>
     * @return int|string
     */
    public function getMaxCpcBidCeiling()
    {
        return isset($this->max_cpc_bid_ceiling) ? $this->max_cpc_bid_ceiling : 0;
    }

    public function hasMaxCpcBidCeiling()
    {
        return isset($this->max_cpc_bid_ceiling);
    }

    public function clearMaxCpcBidCeiling()
    {
        unset($this->max_cpc_bid_ceiling);
    }

    /**
     * Optional. Ceiling of max CPC bids in micros set by automated bidders.
     * This is optional for campaigns with an AdvertisingChannelType of SEARCH
     * and a bidding strategy type of TARGET_IMPRESSION_SHARE.
     *
     * Generated from protobuf field <code>optional int64 max_cpc_bid_ceiling = 3 [(.google.api.field_behavior) = OPTIONAL];</code>
     * @param int|string $var
     * @return $this
     */
    public function setMaxCpcBidCeiling($var)
    {
        GPBUtil::checkInt64($var);
        $this->max_cpc_bid_ceiling = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(TargetImpressionShareInfo::class, \Google\Ads\GoogleAds\V18\Services\GenerateRecommendationsRequest_TargetImpressionShareInfo::class);

