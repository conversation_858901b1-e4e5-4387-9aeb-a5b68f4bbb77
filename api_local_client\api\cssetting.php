<?php 

function get($param)
{
	

    extract($param);
    assign_child_db($project_id);

    $m = new meta();
    $ret["pembagian_cs"] = $m->get_meta("pembagian_cs")["result"]["data"];
    $ret["mql"] = $m->get_meta("mql")["result"]["data"];
    $ret["default_msg"] = $m->get_meta("default_msg")["result"]["data"];
    $ret["format_id"] = $m->get_meta("format_id")["result"]["data"];
    $ret["format_prospek"] = $m->get_meta("format_prospek")["result"]["data"];
    $ret["format_purchase"] = $m->get_meta("format_purchase")["result"]["data"];
    $ret["format_purchase_value"] = $m->get_meta("format_purchase_value")["result"]["data"];

    $tmp = $m->get_meta("format_purchase_nama");
    if($tmp["code"] == 0){
      $ret["format_purchase_nama"] = "";
    }else{
      $ret["format_purchase_nama"] = $tmp["result"]["data"];
    }

    $tmp = $m->get_meta("format_purchase_nama");
    if($tmp["code"] == 0){
      $ret["format_purchase_nama"] = "";
    }else{
      $ret["format_purchase_nama"] = $tmp["result"]["data"];
    }

    $tmp = $m->get_meta("format_purchase_kota");
    if($tmp["code"] == 0){
      $ret["format_purchase_kota"] = "";
    }else{
      $ret["format_purchase_kota"] = $tmp["result"]["data"];
    }

    $tmp = $m->get_meta("format_purchase_alamat");
    if($tmp["code"] == 0){
      $ret["format_purchase_alamat"] = "";
    }else{
      $ret["format_purchase_alamat"] = $tmp["result"]["data"];
    }

    $tmp = $m->get_meta("format_purchase_provinsi");
    if($tmp["code"] == 0){
      $ret["format_purchase_provinsi"] = "";
    }else{
      $ret["format_purchase_provinsi"] = $tmp["result"]["data"];
    }

    $tmp = $m->get_meta("format_purchase_qty_sku");
    if($tmp["code"] == 0){
      $ret["format_purchase_qty_sku"] = "";
    }else{
      $ret["format_purchase_qty_sku"] = $tmp["result"]["data"];
    }

    $ret["trigger_type"] = file_get_contents("config/trigger_type/".$project_id.".txt") ?? "manual";
    $systemPrompt = file_exists("config/prompt_project/".$project_id.".md") ? json_decode(file_get_contents("config/prompt_project/".$project_id.".md"),true) : null;
    $ret["event_yang_didukung_lp"] = $systemPrompt["system_prompt"]["event_yang_didukung_lp"] ?? "";
    $ret["event_yang_didukung_wa"] = $systemPrompt["system_prompt"]["event_yang_didukung_wa"] ?? "";
    $ret["additional_rules"] = $systemPrompt["system_prompt"]["additional_rules"] ?? "";

    $return["code"] = 1;
    $return["result"]["msg"] = "sukses";
    $return["result"]["data"] = $ret;
    return $return;
}

function set($param)
{ 
    extract($param);
    assign_child_db($project_id);
    $update = false;
    $m = new meta();

    if(isset($trigger_type) && $trigger_type == "ai"){
      file_put_contents("config/trigger_type/".$project_id.".txt","ai");
      $m->set_meta("trigger_type","ai");
      if(file_exists("config/prompt_project/".$project_id.".md")){
        $prompt = json_decode(file_get_contents("config/prompt_project/".$project_id.".md"),true);
        $prompt["system_prompt"]["event_yang_didukung_lp"] = $event_yang_didukung_lp;
        $prompt["system_prompt"]["event_yang_didukung_wa"] = $event_yang_didukung_wa;
        $prompt["system_prompt"]["additional_rules"] = $additional_rules;
        file_put_contents("config/prompt_project/".$project_id.".md",json_encode($prompt, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        $update = true;
      }else{
        if(!isset($description) && !isset($additional_rules)){
          $return["code"] = 0;
          $return["result"]["msg"] = "description and additional_rules is required";
          return $return;
        }
        if (is_array($additional_rules)) {
            $additional_rules = implode("\n", $additional_rules);
        }
        $router = new AIRouter();        
        // Initialize SystemPromptGenerator
        $generator = new SystemPromptGenerator($router);
        $systemPrompt = $generator->generateSystemPrompt($description, $additional_rules);
        file_put_contents("config/prompt_project/".$project_id.".md",json_encode($systemPrompt, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        $update = true;
      }
    }else{
      file_put_contents("config/trigger_type/".$project_id.".txt","manual");
      $m->set_meta("trigger_type","manual");
      if(isset($format_id)){
        if(trim($format_id) != ""){
          $m->set_meta("format_id",$format_id);$update = true;
        }
      }
      if(isset($format_prospek)){
        if(trim($format_prospek) != ""){
          $m->set_meta("format_prospek",$format_prospek);$update = true;
        }
      }
      if(isset($format_purchase)){
        if(trim($format_purchase) != ""){
          $m->set_meta("format_purchase",$format_purchase);$update = true;
        }
      }
      if(isset($format_purchase_value)){
        if(trim($format_purchase_value) != ""){
          $m->set_meta("format_purchase_value",$format_purchase_value);$update = true;
        }
      }
  
      if(isset($format_purchase_nama)){
        if(trim($format_purchase_nama) != ""){
          $m->set_meta("format_purchase_nama",$format_purchase_nama);$update = true;
        }
      }
  
      if(isset($format_purchase_kota)){
        if(trim($format_purchase_kota) != ""){
          $m->set_meta("format_purchase_kota",$format_purchase_kota);$update = true;
        }
      }
  
      if(isset($format_purchase_alamat)){
        if(trim($format_purchase_alamat) != ""){
          $m->set_meta("format_purchase_alamat",$format_purchase_alamat);$update = true;
        }
      }
  
      if(isset($format_purchase_provinsi)){
        if(trim($format_purchase_provinsi) != ""){
          $m->set_meta("format_purchase_provinsi",$format_purchase_provinsi);$update = true;
        }
      }
  
      if(isset($format_purchase_qty_sku)){
        if(trim($format_purchase_qty_sku) != ""){
          $m->set_meta("format_purchase_qty_sku",$format_purchase_qty_sku);$update = true;
        }
      }
    }
    if(isset($pembagian_cs)){
      if(trim($pembagian_cs) != ""){
        $m->set_meta("pembagian_cs",$pembagian_cs);$update = true;
      }
    }
    if(isset($mql)){
      if(trim($mql) != ""){
        $m->set_meta("mql",$mql);$update = true;
      }
    }
    if(isset($default_msg)){
      if(trim($mql) != ""){
        $m->set_meta("default_msg",$default_msg);$update = true;
      }
    }
    
    if($update){
      $return["code"] = 1;
      $return["result"]["msg"] = "sukses";
    }
    else{
      $return["code"] = 0;
      $return["result"]["msg"] = "update failed";
    }
    return $return;
}

function generate_ai($param){
  $rules = array(
    'description' => 'required',
    'additional_rules' => 'required',
  );
  validate_param($rules, $param); 
  extract($param);
  assign_child_db($project_id);
  // Initialize AIRouter client
  
  if (is_array($additional_rules)) {
      $additional_rules = implode("\n", $additional_rules);
  }
  $router = new AIRouter();
  
  // Initialize SystemPromptGenerator
  $generator = new SystemPromptGenerator($router);

  // Example usage
  try {  
      $systemPrompt = $generator->generateSystemPrompt($description, $additional_rules);
      file_put_contents("config/prompt_project/".$project_id.".md",json_encode($systemPrompt, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
      $res['event_yang_didukung_lp'] = $systemPrompt['system_prompt']['event_yang_didukung_lp'];
      $res['event_yang_didukung_wa'] = $systemPrompt['system_prompt']['event_yang_didukung_wa'];
      $res['aturan_deteksi'] = $systemPrompt['system_prompt']['aturan_deteksi'];
      $msg["code"] = 1;
      $msg["result"]["msg"] =  "Success generate seeting ai";
      $msg["result"]["data"] =  $res;
      return $msg;
  } catch (Exception $e) {
      $msg["code"] = 0;
      $msg["result"]["msg"] =  "Error: " . $e->getMessage();
      return $msg;
  }
}