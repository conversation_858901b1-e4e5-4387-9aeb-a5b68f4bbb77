<?php

class AIRouter {
    private $clients = [];
    private $config;
    
    /**
     * Constructor to initialize AI clients from configuration
     * 
     * @param array $config Configuration array of AI platforms
     */
    public function __construct($config = null) {
        global $app;
        
        if ($config === null) {
            $config = $app->config->aiconfig;
        }
        
        $this->config = $config;
        $this->initializeClients();
    }
    
    /**
     * Initialize AI clients based on configuration
     */
    private function initializeClients() {
        foreach ($this->config as $platform) {
            if (!$platform['enabled'] || empty($platform['api_key'])) {
                continue;
            }
            
            $className = $platform['class'];
            $apiKey = $platform['api_key'];
            $options = [
                'model' => $platform['model'] ?? 'gpt-5-nano'
            ];  
            
            // Load the class file if it exists
            $classFile = 'app/' . $className . '.php';
            if (file_exists($classFile)) {
                require_once $classFile;
                
                if (class_exists($className)) {
                    $this->clients[] = [
                        'client' => new $className($apiKey, $options),
                        'name' => $platform['name'],
                        'model' => $platform['model']
                    ];
                }
            }
        }
    }
    
    /**
     * Send a chat completion request with fallback routing
     * 
     * @param array $messages Array of message objects
     * @param array $options Additional options for the request
     * @return array Response from the API
     */
    public function chatCompletion($messages, $options = []) {
        $lastException = null;
        
        // Try each client in order of priority (index 0 first)
        foreach ($this->clients as $clientInfo) {
            try {
                $client = $clientInfo['client'];
                $clientOptions = $options;
                
                // Set model if not specified
                if (!isset($clientOptions['model'])) {
                    $clientOptions['model'] = $clientInfo['model'];
                }
                
                return $client->chatCompletion($messages, $clientOptions);
            } catch (Exception $e) {
                // Log the error and continue to next client
                error_log($clientInfo['name'] . ' request failed: ' . $e->getMessage());
                $lastException = $e;
            }
        }
        
        // All clients failed
        if ($lastException) {
            throw new Exception('All AI services failed: ' . $lastException->getMessage(), 0, $lastException);
        }
        
        throw new Exception('No available AI service to handle the request');
    }
    
    /**
     * Get the list of initialized clients
     * 
     * @return array List of client information
     */
    public function getClients() {
        return $this->clients;
    }
    
    /**
     * Get configuration
     * 
     * @return array Configuration array
     */
    public function getConfig() {
        return $this->config;
    }
}
