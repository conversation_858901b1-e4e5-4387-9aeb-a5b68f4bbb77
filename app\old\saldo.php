<?php
class saldo{
    
    public function get_saldo($user_id)
    {
		global $keya, $c_time, $app;
		$db = $app->db;
        $db->orderBy("id", "desc");
        $db->where('user_id = UNHEX(?) and status > 0 and type = 1', array($user_id));
		$pendapatan = $db->getValue ("x_saldo", "SUM(nominal)");
		$db->where('user_id = UNHEX(?) and status > 0 and type = 0', array($user_id));
		$pengeluaran = $db->getValue ("x_saldo", "SUM(nominal)");
		$saldo = ($pendapatan - $pengeluaran);
        if ($saldo == NULL) {
            return 0;
        } else {
            return $saldo;
        }
    }
    
    public function get($user_id, $start=NULL,$limit=NULL){
		$stli = NULL;
		if($start !== NULL && $limit !== NULL){
			$stli = array($start*$limit,$limit);
		}
    	global $keya, $c_time, $app;
        $db = $app->db;
        $db->where("user_id = UNHEX(?)", array($user_id));
		$db->orderBy("tanggal", "Desc");	
    	$res = $db->get("x_saldo", $stli);
		//echo $db->getLastQuery();
		$pos = array();$i = 0;
		foreach($res as $r){
			$pos[$i]['id']= $r["id"];
            $pos[$i]['keterangan']= $r["keterangan"];
            $pos[$i]['nominal']= $r["nominal"];
            if($r["type"]==0){
                $pos[$i]['nominal']= -$r["nominal"];
            }
            $pos[$i]['icon']= $r["icon"];
            $pos[$i]['type']= $r["type"];
			$pos[$i]['status']= $r["status"];
            $pos[$i]['paymentUrl']= $r["paymentUrl"];
			$pos[$i]['timestamp']= strtotime($r["tanggal"]);
            $pos[$i]['tanggal']= $r["tanggal"];
			$i++;
		}
    	return $pos;
    }
    public function topup($user_id, $keterangan, $nominal, $type, $kode = '', $icon = 'currency-dollar', $diskon=0, $cashback = 0, $bank='', $order_id=0)
    {
    	// type 1 = debet
    	// type 0 = kredit
    	// kode = voucher
		global $keya, $c_time, $app;
        //$kode = str_replace("-", "", $kode);
//        $kode = strtoupper($kode);
//        $kode = trim($kode);
		$db = $app->db;
		$dt = new DateTime("now", new DateTimeZone('Asia/Jakarta'));
		$timestamp = $dt->format('Y-m-d H:i:s');		
		$data["user_id"] = $db->func("UNHEX(?)", array($user_id));
		$data["keterangan"] = $keterangan;
		$data["nominal"] = $nominal;
		$data["type"]    = $type;
		$data["kode"]    = $kode;
        $data["diskon"]    = $diskon;
        $data["cashback"]    = $cashback;
		$data["tanggal"] = $timestamp;
        $data["tanggal_approve"] = $timestamp;
		$data["status"]   = 0;
        $data["bank"]   = $bank;
        $data["icon"]     = $icon;
        $data["paymentUrl"]     = '';
        $data["transaction_status"]     = '';
        $data["order_id"] = $order_id;
        $id = $db->insert("x_saldo", $data);
        //echo $db->getLastQuery();
		if($id){
            
            $ret["id"] = $id;
			$ret["code"] = 1;
			$ret["nominal"] = $nominal;
			$ret["msg"] = "sukses topup saldo"; 
			return $ret;
		}else{
			$ret["code"] =0;  
			$ret["msg"] = "gagal topup saldo"; 
			return $ret;
		}  
    } 
    
    public function konfirmasi_saldo($id, $komisi=true){
        global $keya, $c_time, $app;
        $bc = new broadcast();
		$db = $app->db;
        $db->join("x_user u", "o.user_id=u.user_id", "LEFT");
        $db->where("o.id = ?", array($id));
        $order = $db->getone("x_saldo o", "u.uid, u.user_id, u.phone, u.email, u.nama, o.*");
        if($order['status']==0){
            $user_id = strtolower(bin2hex($order['user_id']));
            $db->where("id = ?", array($id));
            if($db->update("x_saldo",array("status" => 1))){
                $data2["uid"] = $order["uid"];
                $data2["event"] = "complete_payment";
                $db->insert("x_aff_fb_ads",$data2);
                $nominal = ($order['nominal'] - $order['diskon']);
                $inout = new inout();
                $inout->add(date('Y-m-d'), 'Pembelian saldo OrderID '.$id, $nominal, 1, 1);
            }
            //$nt = new notif($db);
            //$nt->create($user_id, 'Topup Saldo' , 'Success topup saldo '.$order['nominal'], 'topup_saldo');
            //$data["type"] = 'saldo_approve';
            //$data["nominal"] = strval($order['nominal']);
            //$data['waktu'] = date('Y-m-d H:i:s');
            //$nt->create($user_id, 'Topup Saldo' , 'Success topup saldo '.$order['nominal'], 'saldo_approve', '', 'notif.png', $data);
            //if($order['nominal'] >= 200000 && $order['diskon'] == 0){
//                $nominal = ($order['nominal'] / 100 ) * 50;
//                $this->add($user_id, "Bonus topup saldo", $nominal, 1);
//            }
            if($order['cashback'] > 0){  
                $this->add($user_id, "Bonus topup saldo", $order['cashback'], 1);
                $pesan = 'Saldo Gratis Rp '.$order['cashback'].' berhasil ditambahkan. Video Panduan Instalasi Anda Bisa Klik Disini https://www.youtube.com/watch?v=0qmNVuH5s_I&list=PLzYuxEtcA1axnSG6Jpzpb8AsfssN3j69x.

Kami juga menawarkan Panduan One on One via Gmeet bagi Anda yang kesulitan untuk instalasi GASS & Implementasi Campaign Anda.

Jika Ada Pertanyaan Bisa menghubungi Whatsapp 08113608550 (https://bit.ly/43bFFMR)
                
                ( Ini merupakan pesan otomatis )';
                $bc->add($o["phone"], $o["name"], $pesan);
            }
            if($komisi){
                $this->komisi_aff($id);
            }
            if($order['order_id']!=0){
                $db->where("id = ?", array($order['order_id']));
                $o = $db->getone("x_order");
                if($db->count>0){
                    if($o['pass']==''){
                        $db->where("phone = ? and pass != ''", array($o["phone"]));
                        $rek = $db->getone("x_order");
                        if($db->count>0){
                            $o['pass'] = $rek['pass'];
                        }
                    }
                    
                    
                    $pesan = 'Halo '.$o['name'].' Terima kasih sudah melakukan pembelian pertama dengan GASS. 

Anda bisa login ke situs GASS pada halaman https://panel.gass.co.id/login.html 
User: '.$o['phone'].'
Pass: '.$o['pass'].'


Untuk panduan instalasi Anda bisa : 
1. Melihat Video --> https://panel.gass.co.id/tutorial
2. Membaca Panduan --> https://bacacarapakai.my.id/

Jika Ada Pertanyaan, Anda bisa membalas pesan ini untuk terhubung dengan Account Manager GASS.
';

                    $bc->add($o["phone"], $o["name"], $pesan);
                    $db->where("id = ?", array($order['order_id']));
                    $db->update("x_order", array('status'=>1));

                    $db->where("phone = ?", array($o["phone"]));
                    $db->update("x_user", array('verified_phone'=>1));
                    
                    if($o["data"] !=NULL){                       
                        
                        $c_data = json_decode(base64_decode($o["data"]), true);
                        $site_id = $c_data["site_id"];
                        $db->where("id",$site_id);
                        $site  = $db->getone("x_site");
                        if($db->count>0){
                            $param = array();
                            $param["pixel"] = $site["fb_pixel"];
                            $param["apikey"] = $site["fb_token"];
                            $param["adw_global_tag_id"] = $site["adw_tag"];
                            $param["adw_conv_id"] = $site["adw_conv_id"];
                            $param["gtm"] = $site["gtm"];
                            $param["tiktok"] = $site["tiktok_pixel"];
                            $param["vid"] =  $c_data["id"];
                            if(isset($o['phone'])){
                                $custom["phone"] = hp($o['phone']);
                            }
                            if(isset($o['email'])){
                                $custom["email"] = $o['email'];
                            }
                            if(isset($o["nominal"])){
                                $custom["value"] = $o["nominal"];
                                $param["value"] =  $o["nominal"];
                            }
                            $et = new eztrack($site_id);
                            if(isset($custom)){
                                $res = $et->purchase($site,$param,$custom);
                            }else{
                                $res = $et->purchase($site,$param);
                            }
                        }
                    }                    
                }
            }else{
                $total_saldo = $this->get_saldo($user_id);
                $db->where("user_id = UNHEX(?) and status=0", array($user_id));
                $db->orderBy("tanggal", "Desc");	
                $bills = $db->get("x_bill");
                if($db->count>0){
                    foreach($bills as $i=>$v){
                        if($v['total'] < $total_saldo){
                            $pay = $this->add($user_id, 'Pay bill #'.$v['id'], $v['total'], 0, 'shop');
                            if($pay['code']==1){
                                $db->where("id = ? ", array($v['id']));
                                $db->update("x_bill", array('status'=>1));
                            }
                        }
                    }
                }                
            }
            $fields['act'] = 'stage_set';
            $fields['token'] = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTA5LCJ1c2VyX2lkIjoiMjdjZjFmYzU1ODczYWFlZTNkMTFkMThkNmY0ZjM3YmYiLCJuYW1lIjoiZGV0aG8iLCJyb2xlIjoxLCJjb2RlIjoiOWExZDAxMjk4MjJjNTIyMjAyYWMxMTNmN2E0ZjI1NjcifQ.6UN3V5JxMD5pa4xbGpKgmHCKz-S07-4LAHRg9NYwBv8';
            $fields['project_key'] = '21F1B79B5C5A4162316CA55D77266A29';
            $fields['nope'] = $order["phone"];
            $fields['stage_id'] = 18 ;
            $fields['value'] = $nominal;
            $fields['type'] = 'wa';
            $x = post_x_contents($fields, 'http://10.104.0.13/api.html');
            file_put_contents('log.txt', $x);
            $ret["code"] = 1;  
			$ret["msg"] = "Sukses konfirmasi saldo"; 
        }else{
            $ret["code"] =0;  
			$ret["msg"] = "gagal konfirmasi saldo"; 
        }
        return $ret;
    }
    
    function komisi_aff($order_id){
		global $keya, $c_time, $app;
        $db = $app->db;
		$u = new user();
		$wa = new wa();
        $bc = new broadcast();
		$db->join("x_user u", "o.user_id=u.user_id", "LEFT");
        $db->where("o.id = ?", array($order_id));
        $order = $db->getone("x_saldo o", "u.user_id, u.phone, u.email, u.nama, u.komisi_aff, u.aff_id, o.*");
		if ($db->count > 0) {
            $komisi =  0;
            $voucher_aff = $order['kode'];
			$aff_id = $order['aff_id'];
			if($aff_id !=='' && $komisi > 0){
                /// aff lama
				$db->where("aff_code = ?", array($aff_id));
				$aff = $db->getone("x_user");
				if ($db->count > 0) {
                    $nominal = ($order['nominal'] - $order['diskon'] - $order['cashback']);
                    $komisi =  ( $nominal / 100 ) * $aff['komisi_aff'];

					$keterangan = "Komisi topup saldo user #".$order_id."  Rp. " . number_format($komisi);
					$sal = $this->add_pending(bin2hex($aff["user_id"]), $keterangan, $komisi, 30);
					//$sal['data'] = $db->getLastQuery(); 
					$message =  "Anda mendapatakan\n";
					$message .= $keterangan . "\n\n";
					$message .= "( Ini merupakan pesan otomatis ) \n\n";	
					if($aff['phone'] != ''){
                        $bc->add($aff['phone'], $aff['nama'], $message);
					}
					return $sal;
				}else{
                    //aff new
                    // $db->where("kode = ?", array($aff_id));
                    // $aff = $db->getone("x_aff_code");
                    // if ($db->count > 0) {
                    //     $keterangan = "Komisi topup saldo user #".$order_id."  Rp. " . number_format($komisi);
                    //     $sal = $this->add_pending(bin2hex($aff["user_id"]), $keterangan, $komisi, 7);
                    //     //$sal['data'] = $db->getLastQuery(); 
                    //     $message =  "Anda mendapatakan\n";
                    //     $message .= $keterangan . "\n\n";
                    //     $message .= "( Ini merupakan pesan otomatis ) \n\n";	
                    //     if($aff['phone'] != ''){
                    //         $bc->add($aff['phone'], $aff['nama'], $message);
                    //     }
                    //     return $sal;
                    // }
                }        
			}	
            
            /// komisi by voucher
            if($voucher_aff != '' && $komisi > 0){
                $db->join("x_user u", "v.user_id=u.user_id", "LEFT");
                $db->where("v.kode = ? and v.user_id != 0x00000000000000000000000000000000", array($voucher_aff));
                $aff = $db->getone("x_voucher v", 'u.phone, u.email, u.nama, v.*');
                if ($db->count > 0) {
                    $keterangan = "Komisi penjualan apps #".$order_id."  Rp. " . number_format($komisi);
                    $sal = $this->add_pending(bin2hex($aff["user_id"]), $keterangan, $komisi, 30);
                    //$sal['data'] = $db->getLastQuery(); 
                    $message =  "Anda mendapatakan\n";
                    $message .= $keterangan . "\n\n";
                    $message .= "( Ini merupakan pesan otomatis ) \n\n";	
                    if($aff['phone'] != ''){
                        $bc->add($aff['phone'], $aff['nama'], $message);
                    }
                    return $sal;
                }
            }     
		}
	}
    
    public function create_tripay($id, $bank, $nominal, $keterangan){
        global $keya, $c_time, $app;
		$db = $app->db;
        $db->join("x_user u", "o.user_id=u.user_id", "LEFT");
        $db->where("o.id = ?", array($id));
        $order = $db->getone("x_saldo o", "u.phone, u.email, u.nama, o.*");
        //$privateKey = 'yTrTz-5z04A-PHqBo-h5Apt-fMGrT';
        //$apiKey = 'DEV-lyK7F2dgkoSid8IkgbezV27dJXQMMJvVHHFlP0bC';
        //$merchantCode = 'T6379';
        $privateKey = '07rPp-dLTlJ-tjexE-uecF4-Wx5vc';
        $apiKey = 'aRyDlX81maEkGxlFqFXvxQ9WeiJLDMUP0wP6W8Sr';
        $merchantCode = 'T12615';
        if(file_exists('datatripay/'.$id.'.json')){
            $res = json_decode(file_get_contents('datatripay/'.$id.'.json'), true);
            $res['icon_bank'] = get_icon_bank($res['payment_method']);
            unset($res['callback_url']);
            $msg['msg'] = "Sukses proses payment";
            $msg['code'] = 1 ;
            $msg['data'] = $res;
        }else{  
            //$signature = hash_hmac('sha256', $merchantCode.$channel.$order['id'], $privateKey);
            $signature = hash_hmac('sha256', $merchantCode.$id.$nominal, $privateKey);
            $data = [
              'method'            => $bank,
              'merchant_ref'      => $id,
              'amount'            => $nominal,
              'customer_name'     => $order['nama'],
              'customer_email'    => $order['email'],
              'customer_phone'    => $order['phone'],
              'order_items'       => [
                [
                  'sku'       => $id,
                  'name'      => $keterangan,
                  'price'     => $nominal,
                  'quantity'  => 1
                ]
              ],
              'callback_url'      => 'https://panel.gass.co.id/callback.html',
              'return_url'        => '' ,
              'expired_time'      => (time()+(168*60*60)), // 7hari
              'signature'         => $signature
            ];  
            $res = json_decode(post_tripay($data, "https://tripay.co.id/api/transaction/create", $apiKey), true);
            //$res = json_decode(post_tripay($data, "https://tripay.co.id/api-sandbox/transaction/create", $apiKey), true);
            if($res['success']==true){
                file_put_contents("datatripay/".$id.'.json', json_encode($res['data']));
                $res['data']['icon_bank'] = get_icon_bank($res['data']['payment_method']);
                $db->where("id = ?", array($id));
                $db->update("x_saldo",array("paymentUrl" => $res['data']['checkout_url']));
                unset($res['data']['callback_url']);
                $msg['msg'] = "Sukses proses payment";
                $msg['code'] = 1 ;
                $msg['data'] = $res['data'];
            }else{  
                $db->where("id = ?", array($id));
                $db->delete("x_saldo");
                file_put_contents('.error-tripay.txt', '[' . date('Y-m-d H:i:s') . "]\n" . json_encode($res) . "\n\n", FILE_APPEND);
                $msg['msg'] = "Gagal proses payment";
                $msg['code'] = 0 ;
            }
        }
        return $msg;
    }
    
    public function create_flip($id, $nominal, $testMode=false){
        global $keya, $c_time, $app;
		$db = $app->db;
        $db->join("x_user u", "o.user_id=u.user_id", "LEFT");
        $db->where("o.id = ?", array($id));
        $order = $db->getone("x_saldo o", "u.phone, u.email, u.nama, o.*");
        if($db->count==0){
            $msg['code'] = 0 ;
            $msg['msg'] = "Transaksi tidak ditemukan";	
            return $msg;
        }
        $total = $nominal + 3330;
        $nama = preg_replace('/[^A-Za-z0-9. -]/', ' ', $order['nama']);
        // $flip = new flip($testMode);
        // $payloads = [
        //     "title" => 'Gass '.$order['id'],
        //     "amount" => $total,
        //     "type" => "SINGLE",
        //     "redirect_url" => 'https://panel.gass.co.id/saldo.html',
        //     "step" => 2,
        //     "sender_name" => $nama,
        //     "sender_email" => $order['email'],
        //     "sender_phone_number" => $order['phone'],
        // ];  
        // $res = $flip->create_bill($payloads);
 
        $postdata = [
            'act' => 'order_create',
            'order_id' => $order['id'],
            'title' =>  'Gass '.$order['id'],
            'nominal' => ($total) ,
            'name' => $nama,
            'email' => $order['email'],
            'phone' => $order["phone"],
            'site' => 'gass.co.id',
            'redirect' => 'https://panel.gass.co.id/saldo.html',
        ];
        $res = post_flip($postdata);

        if($res['code']==1){		
            $datUp["link_id"]    = $order['id'];
            $datUp["paymentUrl"] = $res['link_pay'];
            $datUp["amount"] = ($total);
            $datUp["total_fee"] = 3330;
            $datUp["amount_received"] = ($total) ;
            $db->where("id = ?", array($order['id'],));
            $id = $db->update("x_saldo", $datUp); 
            if($id){
                $msg['code'] = 1 ;
                $msg['msg'] = "Success create bill";	
                $msg['link_url'] = $res["link_pay"];
                $fields['act'] = 'stage_set';
                $fields['token'] = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTA5LCJ1c2VyX2lkIjoiMjdjZjFmYzU1ODczYWFlZTNkMTFkMThkNmY0ZjM3YmYiLCJuYW1lIjoiZGV0aG8iLCJyb2xlIjoxLCJjb2RlIjoiOWExZDAxMjk4MjJjNTIyMjAyYWMxMTNmN2E0ZjI1NjcifQ.6UN3V5JxMD5pa4xbGpKgmHCKz-S07-4LAHRg9NYwBv8';
                $fields['project_key'] = '21F1B79B5C5A4162316CA55D77266A29';
                $fields['nope'] = $order["phone"];
                $fields['stage_id'] = 30 ;
                $fields['type'] = 'wa';
                $x = post_x_contents($fields, 'http://10.104.0.13/api.html');
            }else{
                $msg['code'] = 0 ;
                $msg['msg'] = "Error create bill database";	
            }
        }else{
            $msg['code'] = 0 ;
            $msg['msg'] = "Error create bill";	
        }  
        return $msg;  
    }

    public function add($user_id, $keterangan, $nominal, $type, $icon = 'currency-dollar')
    {
    	// type 1 = debet
    	// type 0 = kredit
    	// kode 0 = dari rebate
    	// kode 1 = dari affiliate
		global $keya, $c_time, $app;
		$db = $app->db;
		$dt = new DateTime("now", new DateTimeZone('Asia/Jakarta'));
		$timestamp = $dt->format('Y-m-d H:i:s');		
		$data["user_id"] = $db->func("UNHEX(?)", array($user_id));
		$data["keterangan"] = $keterangan;
		$data["nominal"] = $nominal;
		$data["type"]    = $type;
		$data["kode"]    = '';
        $data["diskon"]    = 0;
        $data["cashback"]    = 0;
		$data["tanggal"] = $timestamp;
        $data["tanggal_approve"] = $timestamp;
		$data["status"]   = 1;
        $data["icon"]     = $icon;
        $id = $db->insert("x_saldo", $data);
        //echo $db->getLastQuery();
		if($id){
            $ret["id"] = $id;
			$ret["code"] = 1;
			$ret["nominal"] = $nominal;
			$ret["msg"] = "sukses add saldo"; 
			return $ret;
		}else{
			$ret["code"] =0;  
			$ret["msg"] = "gagal add saldo"; 
			return $ret;
		}  
    }
    
    public function create_aff_code($user_id, $kode)
    {
		global $keya, $c_time, $app;
        $kode = str_replace("-", "", $kode);
        $kode = strtoupper($kode);
        $kode = trim($kode);
        
		$db = $app->db;
		$dt = new DateTime("now", new DateTimeZone('Asia/Jakarta'));
		$timestamp = $dt->format('Y-m-d H:i:s');		
        
        $db->where("aff_code = ?", array($kode));
        $aff = $db->getone("x_user");
        if ($db->count == 0) {
            $db->where("kode = ?", array($kode));
            $aff = $db->getone("x_aff_code");
            if ($db->count == 0) {
                $data["user_id"] = $db->func("UNHEX(?)", array($user_id));
                $data["kode"] = $kode;
                $id = $db->insert("x_aff_code", $data);
                if($id){
                    $ret["code"] =1; 
                    $ret["msg"] = "success create code affiliate"; 
                    
                }else{
                    $ret["code"] =0;  
                    $ret["msg"] = "Fail create code affiliate"; 
                } 
            }else{
                $ret["code"] =0;  
                $ret["msg"] = "Affiliate code already exists"; 
            }
        }else{
            $ret["code"] =0;  
            $ret["msg"] = "Affiliate code already exists"; 
        }
        return $ret;
    } 
    
    
    function add_pending($user_id,$keterangan,$nominal,$pending_day,$kode = 1)
    {
    	// status 1 = debet
    	// status 0 = kredit
    	// kode 0 = dari rebate
    	// kode 1 = dari affiliate
    	global $keya, $c_time, $app;
		$db = $app->db;
    	$tz = 'Asia/Jakarta';
		$dt = new DateTime("now", new DateTimeZone($tz));
		$timestamp = $dt->format('Y-m-d H:i:s');
		$cair =  date('Y-m-d ',strtotime('+'.$pending_day.' day', strtotime($timestamp)));
	
		$data["user_id"] = $db->func("UNHEX(?)", array($user_id));
		$data["tanggal"] = $timestamp;
		$data["cair"] = $cair;
		$data["keterangan"] = $keterangan;
		$data["jumlah"]     = $nominal;
		$data["kode"]     = $kode;
		
		if($db->insert("x_user_saldo_pending", $data)){
			$ret["code"] = 1;
			$ret["jumlah"] = $nominal;
			$ret["msg"] = "sukses add pending saldo"; 
			return $ret;
		}else{
			//echo $db->getLastQuery();
			//echo "<hr/>";
			$ret["code"] =0;  
			$ret["msg"] = "gagal add pending saldo"; 
			return $ret;
		}  
    }
}
