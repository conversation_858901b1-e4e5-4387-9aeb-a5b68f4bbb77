<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/services/recommendation_service.proto

namespace Google\Ads\GoogleAds\V18\Services\ApplyRecommendationOperation;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Parameters to use when applying a set target CPA recommendation.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.services.ApplyRecommendationOperation.ForecastingSetTargetCpaParameters</code>
 */
class ForecastingSetTargetCpaParameters extends \Google\Protobuf\Internal\Message
{
    /**
     * Average CPA to use for Target CPA bidding strategy.
     *
     * Generated from protobuf field <code>optional int64 target_cpa_micros = 1;</code>
     */
    protected $target_cpa_micros = null;
    /**
     * New campaign budget amount to set for a campaign resource.
     *
     * Generated from protobuf field <code>optional int64 campaign_budget_amount_micros = 2;</code>
     */
    protected $campaign_budget_amount_micros = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $target_cpa_micros
     *           Average CPA to use for Target CPA bidding strategy.
     *     @type int|string $campaign_budget_amount_micros
     *           New campaign budget amount to set for a campaign resource.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Services\RecommendationService::initOnce();
        parent::__construct($data);
    }

    /**
     * Average CPA to use for Target CPA bidding strategy.
     *
     * Generated from protobuf field <code>optional int64 target_cpa_micros = 1;</code>
     * @return int|string
     */
    public function getTargetCpaMicros()
    {
        return isset($this->target_cpa_micros) ? $this->target_cpa_micros : 0;
    }

    public function hasTargetCpaMicros()
    {
        return isset($this->target_cpa_micros);
    }

    public function clearTargetCpaMicros()
    {
        unset($this->target_cpa_micros);
    }

    /**
     * Average CPA to use for Target CPA bidding strategy.
     *
     * Generated from protobuf field <code>optional int64 target_cpa_micros = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setTargetCpaMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->target_cpa_micros = $var;

        return $this;
    }

    /**
     * New campaign budget amount to set for a campaign resource.
     *
     * Generated from protobuf field <code>optional int64 campaign_budget_amount_micros = 2;</code>
     * @return int|string
     */
    public function getCampaignBudgetAmountMicros()
    {
        return isset($this->campaign_budget_amount_micros) ? $this->campaign_budget_amount_micros : 0;
    }

    public function hasCampaignBudgetAmountMicros()
    {
        return isset($this->campaign_budget_amount_micros);
    }

    public function clearCampaignBudgetAmountMicros()
    {
        unset($this->campaign_budget_amount_micros);
    }

    /**
     * New campaign budget amount to set for a campaign resource.
     *
     * Generated from protobuf field <code>optional int64 campaign_budget_amount_micros = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCampaignBudgetAmountMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->campaign_budget_amount_micros = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ForecastingSetTargetCpaParameters::class, \Google\Ads\GoogleAds\V18\Services\ApplyRecommendationOperation_ForecastingSetTargetCpaParameters::class);

