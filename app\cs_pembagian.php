<?php

class cs_pembagian
{

	public function get_cs($divisi_id)
	{
		global $app;
		$db2 = $app->db2;

		$project_id = $app->project_id;

		$m = new meta();

		$mode_pembagian = $m->get_meta("mode_pembagian_divisi_" . $divisi_id);
		$persen_pembagian = NULL;

		if ($mode_pembagian["code"] == 0) {
			$type_pembagian = $m->get_meta("pembagian_cs");

		} else {
			if ($mode_pembagian["result"]["data"] == "auto") {
				$type_pembagian = $m->get_meta("pembagian_cs");

			} else {
				$persen_pembagian = $m->get_meta("pembagian_divisi_" . $divisi_id);

				if ($persen_pembagian["code"] == 0) {
					$type_pembagian = $m->get_meta("pembagian_cs");
				} else {

					$persen_pembagian = $persen_pembagian["result"]["data"];
					//$type_pembagian = "manual_persen";
					$type_pembagian["result"]["data"] = "manual_persen";
				}

			}
		}



		if (isset($type_pembagian["result"]["data"])) {
			$type_pembagian = $type_pembagian["result"]["data"];
		} else {
			return "pembagian not found";
		}

		if ($type_pembagian == "manual_persen") {
			$cs_phone = $this->persen($persen_pembagian);
		} elseif ($type_pembagian == "urut_impression") {
			$cs_phone = $this->urut($divisi_id, "imp");
		} elseif ($type_pembagian == "urut_contact") {
			$cs_phone = $this->urut($divisi_id, "contact");
		} elseif ($type_pembagian == "rata_impression") {
			$cs_phone = $this->rata($divisi_id, "impression");
		} elseif ($type_pembagian == "rata_contact") {
			$cs_phone = $this->rata($divisi_id, "contact");
		} elseif ($type_pembagian == "rata_mql") {
			$cs_phone = $this->rata($divisi_id, "mql");
		}

		return $cs_phone;

	}

	public function persen($weightedValues)
	{
		global $app;
		$db2 = $app->db2;

		if ($weightedValues == NULL) {
			echo "Divide Cs by percent setting empty";
			die();
		}
		/////////////////////////////////////////////////////////////////////////////////////////////////////
		// SELECT * FROM `cs` WHERE cs_key IN (UNHEX('8287D7DE5946567BBE26BC84E34A1649'),UNHEX('63263FDB7E8F3F12957F1D6BA957686F')) and status = 0
		$cs_keys = array();

		foreach ($weightedValues as $key => $value) {
			$temp_key = "UNHEX(\"" . md5($key) . "\")";
			//$temp_key = $db2->func("UNHEX(?)",array(md5($key)));
			array_push($cs_keys, $temp_key);
		}
		$c_query = 'cs_key IN (' . implode(",", $cs_keys) . ")";

		$db2->where($c_query);
		$db2->where('status', 0);
		$inactive_cs = $db2->get("cs");

		if (count($inactive_cs) > 0) {
			foreach ($inactive_cs as $key => $value) {
				unset($weightedValues[$value["phone"]]);
			}
		}
		///////////////////////////////////////////////////////////////////////////////

		$array = array();
		foreach ($weightedValues as $key => $weight) {
			$array = array_merge(array_fill(0, $weight, $key), $array);
		}

		if (count($array) == 0) {
			foreach ($weightedValues as $key => $weight) {
				$array = array_merge(array_fill(0, 1, $key), $array);
			}
		}

		$phone = $array[array_rand($array)];

		$cs_log = new cs_log($phone);
		$cs_log->add_stat("impression");

		return $phone;
	}

	function initializeDataCs($cs) {
		$data_cs = [];
		foreach ($cs as $value) {
			$data_cs[$value["phone"]] = [
				"name" => $value["name"],
				"dapat_imp" => 0,
				"dapat_contact" => 0
			];
		}
		return $data_cs;
	}

	function arraysAreEqual($array1, $array2) {
		return !array_diff($array1, $array2) && !array_diff($array2, $array1);
	}

	function updateDataCs($cs, $data_cs) {
		$data_cs = array();
		foreach ($cs as $value) {
			$data_cs[$value["phone"]] = [
				"name" => $value["name"],
				"dapat_imp" => 0,
				"dapat_contact" => 0
			];
		}
		return $data_cs;
	}

	public function urut($divisi_id, $by)
	{
		global $app;
		$db2 = $app->db2;
		$project_id = $app->project_id;

		$valid = false;
		if ($by == "imp") {
			$valid = true;
		}
		if ($by == "contact") {
			$valid = true;
		}

		if ($valid == false) {
			return false;
		}

		$db2->join("cs_divisi", "cs_divisi.cs_key = cs.cs_key");
		$db2->where("cs_divisi.divisi_id", $divisi_id);
		$db2->where("cs.status", 1);
		$cs = $db2->get("cs");

		if (count($cs) > 0) {

			$data_cs = NULL;
			$file = "pembagian-" . $project_id . "-" . $divisi_id . ".txt";


			// Cek apakah file pembagian sudah ada
			if (!file_exists($file)) {
				// Jika file tidak ada, inisialisasi data CS dan simpan ke file
				$data_cs = $this->initializeDataCs($cs);
				file_put_contents($file, json_encode($data_cs));
			} else {
				// Jika file sudah ada, ambil data dari file
				$data_cs = json_decode(file_get_contents($file), true);
				// Cek apakah json_decode berhasil
				if (json_last_error() !== JSON_ERROR_NONE) {
					// Jika terjadi error, inisialisasi data CS
					$data_cs = $this->initializeDataCs($cs);
					file_put_contents($file, json_encode($data_cs));
				} else {
					// Ambil nomor telepon dari data CS yang ada
					$tmp_phone_numbers_data_cs = array_keys($data_cs);
					// Ambil nomor telepon dari CS yang baru
					$tmp_phone_numbers_cs = array_column($cs, 'phone');

					// Cek apakah nomor telepon di data CS sama dengan yang baru
					if (!$this->arraysAreEqual($tmp_phone_numbers_data_cs, $tmp_phone_numbers_cs)) {
						// Jika tidak sama, perbarui data CS
						$data_cs = $this->updateDataCs($cs, $data_cs);
						// Simpan data CS yang diperbarui ke file
						file_put_contents($file, json_encode($data_cs));
					}
				}
			}



			if ($data_cs == NULL) {
				$i = 0;
				foreach ($cs as $key => $value) {
					$data_cs[$value["phone"]]["name"] = $value["name"];
					$data_cs[$value["phone"]]["dapat_" . $by] = 0;
					//$data_cs[$value["phone"]]["dapat_contact"] = 0;
					$i++;
				}
				file_put_contents($file, json_encode($data_cs));
			}

			//////////////////// reset pembagian
			$reset_file_pembagian = true;
			foreach ($cs as $key => $value) {
				if ($value["status"] == 1) {
					if ($data_cs[$value["phone"]]["dapat_" . $by] == 0) {
						$reset_file_pembagian = false;
					}
				}
			}
			if ($reset_file_pembagian) {
				$data_cs = array();
				$i = 0;
				foreach ($cs as $key => $value) {
					$data_cs[$value["phone"]]["name"] = $value["name"];
					$data_cs[$value["phone"]]["dapat_" . $by] = 0;
					//$data_cs[$value["phone"]]["dapat_contact"] = 0;
					$i++;
				}
				file_put_contents($file, json_encode($data_cs));
			}

			//////////////////////////////////////////////////////////////////
			foreach ($cs as $key => $value) {
				if ($value["status"] == 1) {
					if ($data_cs[$value["phone"]]["dapat_" . $by] == 0) {

						$data_cs[$value["phone"]]["dapat_" . $by] = 1;

						file_put_contents($file, json_encode($data_cs));

						$cs_log = new cs_log($value["phone"]);
						$cs_log->add_stat("impression");

						$selected_cs = $value;

						return $value["phone"];
					}
				}
			}

		}

		return $this->random();

	}

	public function random()
	{
		global $app;
		$db2 = $app->db2;

		$db2->where("status", 1);
		$db2->orderBy("rand()");
		$cs = $db2->getone("cs");

		if ($cs == NULL) {
			$db2->orderBy("rand()");
			$cs = $db2->getone("cs");
		}

		$cs_log = new cs_log($cs["phone"]);
		$cs_log->add_stat("impression");

		return $cs["phone"];
	}

	public function rata($divisi_id, $by)
	{
		global $app;
		$db2 = $app->db2;

		$db2->join("cs_divisi", "cs_divisi.cs_key = cs.cs_key");
		$db2->where("cs_divisi.divisi_id", $divisi_id);
		$db2->where("cs.status", 1);
		$tmp = $db2->get("cs");

		if ($tmp != NULL) {
			if (count($tmp) == 1) {
				$cs = $tmp[0]["phone"];
			} else {
				$cs = array();
				foreach ($tmp as $key => $value) {
					array_push($cs, $value["phone"]);
				}
			}
		} else {
			return $this->random();
		}

		if (!is_array($cs)) {
			return $cs;
		}

		$valid = false;
		if ($by == "impression") {
			$valid = true;
		}
		if ($by == "contact") {
			$valid = true;
		}
		if ($by == "mql") {
			$valid = true;
		}
		if ($by == "prospek") {
			$valid = true;
		}
		if ($by == "purchase") {
			$valid = true;
		}

		if ($valid == false) {
			return false;
		}

		$tanggal = date("Y-m-d");
		//$tanggal = "2023-09-03";

		$q = "select x.phone from (SELECT * FROM `cs_log` where tanggal = '" . $tanggal . "') as x where ";
		$tmp = array();
		foreach ($cs as $key => $value) {
			array_push($tmp, 'x.cs_key = UNHEX("' . md5($value) . '")');
		}
		$q = $q . implode(" or ", $tmp);

		$q .= " order by " . $by . " asc LIMIT 1";

		$res = $db2->rawQuery($q);
		// @file_put_contents('log/cs_pembagian_sql.txt', '[' . date('Y-m-d H:i:s') . "]\n\n" . $db2->getLastQuery() . "\n\n\n\n", FILE_APPEND);
		if ($res == NULL) {
			//$db2->where("cs_key = UNHEX(?)",array([md5($cs[0])]));
			//$tmp = $db2->getone("cs");
			$phone = $cs[0];
		} else {
			$phone = $res[0]["phone"];
		}

		$cs_log = new cs_log($phone);
		$cs_log->add_stat("impression");

		//$db2->where("cs_key = UNHEX(?)",[md5($phone)]);
		//$tmp = $db2->getone("cs");
		return $phone;
	}
}
?>