<?php



function set_pembagian($param)
{

    $rules = [
        "divisi_key" => "required",
        "mode_pembagian" => "required"
    ];
    validate_param($rules, $param);

    extract($param);

    assign_child_db($project_id);

    $input_data_pembagian = NULL;
    if(isset($data_pembagian)){
        $input_data_pembagian = $data_pembagian;
    }

    $d = new divisi();
    $msg = $d->set_pembagian($project_id,$divisi_key,$mode_pembagian,$input_data_pembagian);

    return $msg;
}

function get_pembagian($param)
{

    $rules = [
        "divisi_key" => "required",
    ];
    validate_param($rules, $param);

    extract($param);

    assign_child_db($project_id);

    $d = new divisi();
    $msg = $d->get_pembagian($divisi_key);

    return $msg;
}

function get_pembagian2($param)
{

    $rules = [
        "divisi_key" => "required",
    ];
    validate_param($rules, $param);

    extract($param);

    assign_child_db($project_id);

    $d = new divisi();
    $msg = $d->get_pembagian2($divisi_key);

    return $msg;
}

function get($param)
{
    extract($param);

    assign_child_db($project_id);

    $d = new divisi();
    $msg = $d->get_divisi();

    return $msg;
}



function get_cs($param)
{

    $rules = [
        "divisi_key" => "required",
    ];
    validate_param($rules, $param);

    extract($param);

    assign_child_db($project_id);

    $d = new divisi();
    $msg = $d->get_cs($divisi_key);

    return $msg;
}

function add($param)
{
    $rules = [
        "name" => "required",
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $d = new divisi();
    $msg = $d->add_divisi($name);

    return $msg;
}

function edit($param)
{
    $rules = [
        "name" => "required",
        "divisi_key" => "required",
    ];

    extract($param);

    assign_child_db($project_id);

    $d = new divisi();
    $msg = $d->edit_divisi($name, $divisi_key);

    return $msg;
}

function delete($param)
{
    $rules = [
        "divisi_key" => "required",
    ];

    extract($param);
    assign_child_db($project_id);
    $d = new divisi();
    $msg = $d->delete_divisi($divisi_key);

    return $msg;
}
