<?php

require_once __DIR__ . '/../autoload.php';

use DynamicWebhook\PlatformDetector;
use DynamicWebhook\MessageNormalizer;
use DynamicWebhook\CTWAProcessor;
use DynamicWebhook\Config\PlatformConfig;
use DynamicWebhook\DataExtractor\GenericDataExtractor;

/**
 * Basic tests for the refactored DynamicWebhook system
 */
class BasicTest
{
    public function runAllTests(): array
    {
        $results = [];
        
        $results['platform_detector'] = $this->testPlatformDetector();
        $results['message_normalizer'] = $this->testMessageNormalizer();
        $results['platform_config'] = $this->testPlatformConfig();
        $results['data_extractor'] = $this->testDataExtractor();
        $results['ctwa_processor'] = $this->testCTWAProcessor();
        
        return $results;
    }

    private function testPlatformDetector(): array
    {
        $detector = new PlatformDetector();
        $results = [];
        
        // Test explicit provider
        $results['explicit_provider'] = $detector->detect([], 'waba') === 'waba';
        
        // Test auto-detection for Konekwa
        $konekwaData = ['raw' => '{"test": "data"}'];
        $results['konekwa_detection'] = $detector->detect($konekwaData) === 'konekwa';
        
        // Test auto-detection for WABA
        $wabaData = ['entry' => [['changes' => [['value' => ['messages' => [['from' => '123']]]]]]];
        $results['waba_detection'] = $detector->detect($wabaData) === 'waba';
        
        // Test default fallback
        $emptyData = [];
        $results['default_fallback'] = $detector->detect($emptyData) === 'default';
        
        // Test supported platforms
        $results['is_supported'] = $detector->isSupported('waba') && !$detector->isSupported('unknown');
        
        return $results;
    }

    private function testMessageNormalizer(): array
    {
        $normalizer = new MessageNormalizer();
        $results = [];
        
        // Test phone number cleaning
        $results['clean_phone_basic'] = $normalizer->cleanPhoneNumber('081234567890') === '081234567890';
        $results['clean_phone_whatsapp'] = $normalizer->cleanPhoneNumber('<EMAIL>') === '081234567890';
        $results['clean_phone_formatted'] = $normalizer->cleanPhoneNumber('+62-812-3456-7890') === '6281234567890';
        $results['clean_phone_null'] = $normalizer->cleanPhoneNumber(null) === null;
        $results['clean_phone_empty'] = $normalizer->cleanPhoneNumber('') === null;
        
        // Test phone validation
        $results['valid_phone'] = $normalizer->isValidPhoneNumber('081234567890');
        $results['invalid_phone_short'] = !$normalizer->isValidPhoneNumber('123');
        $results['invalid_phone_long'] = !$normalizer->isValidPhoneNumber('12345678901234567890123');
        
        // Test message normalization
        $results['normalize_message'] = $normalizer->normalizeMessage('  Hello   World  ') === 'Hello World';
        $results['normalize_empty'] = $normalizer->normalizeMessage('') === '';
        $results['normalize_null'] = $normalizer->normalizeMessage(null) === '';
        
        // Test nested value extraction
        $testData = ['level1' => ['level2' => ['level3' => 'value']]];
        $results['extract_nested'] = $normalizer->extractNestedValue($testData, 'level1.level2.level3') === 'value';
        $results['extract_missing'] = $normalizer->extractNestedValue($testData, 'missing.path') === null;
        $results['extract_null_path'] = $normalizer->extractNestedValue($testData, null) === null;
        
        return $results;
    }

    private function testPlatformConfig(): array
    {
        $results = [];
        
        // Test getting existing config
        $wabaConfig = PlatformConfig::get('waba');
        $results['get_waba_config'] = isset($wabaConfig['phone_field']) && $wabaConfig['phone_field'] === 'entry.0.changes.0.value.messages.0.from';
        
        // Test getting non-existent config (should return default)
        $unknownConfig = PlatformConfig::get('unknown_platform');
        $results['get_unknown_config'] = $unknownConfig === PlatformConfig::get('default');
        
        // Test platform existence
        $results['platform_exists'] = PlatformConfig::exists('waba');
        $results['platform_not_exists'] = !PlatformConfig::exists('unknown_platform');
        
        // Test getting all configs
        $allConfigs = PlatformConfig::getAll();
        $results['get_all_configs'] = is_array($allConfigs) && isset($allConfigs['waba']) && isset($allConfigs['default']);
        
        // Test supported platforms
        $supportedPlatforms = PlatformConfig::getSupportedPlatforms();
        $results['supported_platforms'] = is_array($supportedPlatforms) && in_array('waba', $supportedPlatforms);
        
        // Test config validation
        $validConfig = [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'in',
            'outgoing_condition' => 'out'
        ];
        $validationErrors = PlatformConfig::validate($validConfig);
        $results['valid_config'] = empty($validationErrors);
        
        $invalidConfig = ['phone_field' => 'phone']; // Missing required fields
        $validationErrors = PlatformConfig::validate($invalidConfig);
        $results['invalid_config'] = !empty($validationErrors);
        
        return $results;
    }

    private function testDataExtractor(): array
    {
        $normalizer = new MessageNormalizer();
        $configs = PlatformConfig::getAll();
        $extractor = new GenericDataExtractor($normalizer, $configs);
        $results = [];
        
        // Test supported platforms
        $supportedPlatforms = $extractor->getSupportedPlatforms();
        $results['supported_platforms'] = is_array($supportedPlatforms) && in_array('default', $supportedPlatforms);
        
        // Test basic extraction
        $testData = [
            'phone' => '081234567890',
            'message' => 'Hello World',
            'type' => 'message_in'
        ];
        
        $extracted = $extractor->extract($testData, 'default');
        $results['extract_basic'] = $extracted['phone'] === '081234567890' && $extracted['message'] === 'Hello World';
        
        // Test validation
        $results['validate_good_data'] = $extractor->validate($extracted);
        
        $badData = ['phone' => '', 'message' => ''];
        $results['validate_bad_data'] = !$extractor->validate($badData);
        
        return $results;
    }

    private function testCTWAProcessor(): array
    {
        $normalizer = new MessageNormalizer();
        $processor = new CTWAProcessor($normalizer);
        $results = [];
        
        // Test supported platforms
        $supportedPlatforms = $processor->getSupportedPlatforms();
        $results['supported_platforms'] = is_array($supportedPlatforms) && in_array('qontak', $supportedPlatforms);
        
        // Test Qontak CTWA extraction
        $qontakData = [
            'room' => [
                'description' => 'ctwa_clid=test123&source_id=source456&campaign_id=camp789'
            ]
        ];
        
        $ctwaData = $processor->extractCTWAData($qontakData, 'qontak');
        $results['qontak_extraction'] = $ctwaData['ctwa_clid'] === 'test123' && $ctwaData['source_id'] === 'source456';
        
        // Test validation
        $validatedData = $processor->validateAndProcess($ctwaData);
        $results['validation'] = $validatedData['ctwa_clid'] === 'test123';
        
        // Test has CTWA data
        $results['has_ctwa_data'] = $processor->hasCTWAData($qontakData, 'qontak');
        
        $emptyData = [];
        $results['no_ctwa_data'] = !$processor->hasCTWAData($emptyData, 'default');
        
        return $results;
    }

    public function printResults(array $results): void
    {
        echo "=== DynamicWebhook Refactoring Test Results ===\n\n";
        
        foreach ($results as $testGroup => $tests) {
            echo strtoupper(str_replace('_', ' ', $testGroup)) . ":\n";
            
            $passed = 0;
            $total = count($tests);
            
            foreach ($tests as $testName => $result) {
                $status = $result ? 'PASS' : 'FAIL';
                echo "  {$testName}: {$status}\n";
                if ($result) $passed++;
            }
            
            echo "  Summary: {$passed}/{$total} tests passed\n\n";
        }
    }
}

// Run tests if this file is executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $test = new BasicTest();
    $results = $test->runAllTests();
    $test->printResults($results);
}
