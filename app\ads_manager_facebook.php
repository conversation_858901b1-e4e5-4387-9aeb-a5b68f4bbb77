<?php
//require_once __DIR__ . '/vendor/autoload.php'; // Include Facebook PHP SDK

use FacebookAds\Api;
use FacebookAds\Object\Campaign;
use FacebookAds\Object\AdSet;
use FacebookAds\Object\Ad;
use FacebookAds\Object\Fields\CampaignFields;
use FacebookAds\Object\Fields\AdSetFields;
use FacebookAds\Object\Fields\AdFields;

/*
// Usage Example:
$access_token = 'YOUR_ACCESS_TOKEN';
$app_secret = 'YOUR_APP_SECRET';
$app_id = 'YOUR_APP_ID';

$adsManager = new FacebookAdsManager($access_token, $app_secret, $app_id);

// Example usage to start a campaign with ID 'CAMPAIGN_ID'
echo $adsManager->startCampaign('CAMPAIGN_ID');
// Similarly, you can use other methods for different actions.

*/

class FacebookAdsManager {
    private $access_token;
    private $app_secret;
    private $app_id;

    public function __construct($access_token, $app_secret, $app_id) {
        $this->access_token = $access_token;
        $this->app_secret = $app_secret;
        $this->app_id = $app_id;

        Api::init($this->app_id, $this->app_secret, $this->access_token);
    }

    public function startCampaign($campaign_id) {
        $campaign = new Campaign($campaign_id);
        $campaign->{CampaignFields::STATUS} = Campaign::STATUS_ACTIVE;
        $campaign->update();
        return 'Campaign started successfully.';
    }

    public function pauseCampaign($campaign_id) {
        $campaign = new Campaign($campaign_id);
        $campaign->{CampaignFields::STATUS} = Campaign::STATUS_PAUSED;
        $campaign->update();
        return 'Campaign paused successfully.';
    }

    public function renameCampaign($campaign_id, $new_name) {
        $campaign = new Campaign($campaign_id);
        $campaign->{CampaignFields::NAME} = $new_name;
        $campaign->update();
        return 'Campaign renamed successfully.';
    }

    public function changeBudgetCampaign($campaign_id, $new_budget_amount) {
        $campaign = new Campaign($campaign_id);
        $campaign->{CampaignFields::DAILY_BUDGET} = $new_budget_amount;
        $campaign->update();
        return 'Campaign budget changed successfully.';
    }

    public function startAdSet($adset_id) {
        $adSet = new AdSet($adset_id);
        $adSet->{AdSetFields::STATUS} = AdSet::STATUS_ACTIVE;
        $adSet->update();
        return 'Ad Set started successfully.';
    }

    public function pauseAdSet($adset_id) {
        $adSet = new AdSet($adset_id);
        $adSet->{AdSetFields::STATUS} = AdSet::STATUS_PAUSED;
        $adSet->update();
        return 'Ad Set paused successfully.';
    }

    // Similar methods for renaming and changing budget for ad sets...

    public function startAd($ad_id) {
        $ad = new Ad($ad_id);
        $ad->{AdFields::STATUS} = Ad::STATUS_ACTIVE;
        $ad->update();
        return 'Ad started successfully.';
    }

    public function pauseAd($ad_id) {
        $ad = new Ad($ad_id);
        $ad->{AdFields::STATUS} = Ad::STATUS_PAUSED;
        $ad->update();
        return 'Ad paused successfully.';
    }

    // Similar methods for renaming and changing budget for ads...
}

?>