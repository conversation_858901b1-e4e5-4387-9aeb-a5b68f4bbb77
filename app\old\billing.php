<?php  
class billing{
    public function get_data($user_id, $start=NULL,$limit=NULL){
		$stli = NULL;
		if($start !== NULL && $limit !== NULL){
			$stli = array($start*$limit,$limit);
		}
    	global $keya, $c_time, $app;
        $db = $app->db;
        $db->where("user_id = UNHEX(?)", array($user_id));
		$db->orderBy("tanggal", "Desc");	
    	$res = $db->get("x_bill", $stli);
		//echo $db->getLastQuery();
		$pos = array();$i = 0;
		foreach($res as $r){
            $pos[$i]['id']= $r["id"];
            $pos[$i]['tanggal']= $r["tanggal"];
            $pos[$i]['expired']= $r["expired"];
            $pos[$i]['total']= $r["total"];
			$pos[$i]['status']= $r["status"];
            $pos[$i]['split_bill']= $r["split_bill"];
			$i++;
		}
    	return $pos;
    }
    
    public function get_detail($user_id, $id){
        global $keya, $c_time, $app;
        $db = $app->db;
        if($user_id =='bde2fec8d13ee5289a42a77192a41ac5'){
            $db->where("id = ?", array($id));
        }else{
            $db->where("user_id = UNHEX(?) and id = ?", array($user_id, $id));
        }        
		$db->orderBy("tanggal", "Desc");	
        $res = $db->getone("x_bill");
        if($db->count > 0){
            $db->where("bill_id = ?", array($id));
            $dat = $db->get("x_bill_detail");
            if($db->count > 0){
                foreach($dat as $i => $d){
                    $dat[$i]['datas'] = json_decode($dat[$i]['datas'], true);
                }
            }
            $res['last_date'] = date("Y-m-d", strtotime($res['last_date']));
            $res['tanggal'] = date("Y-m-d", strtotime($res['tanggal']));
            $res['expired'] = date("Y-m-d", strtotime($res['expired']));
            if($res['split_bill']==1){
                $db->where("bill_id = ?", array($id));
                $data['split'] = $db->get("x_bill_split");
            }
            $data['bill'] = $res;
            $data['detail'] = $dat;
            //echo $app->db->getLastQuery();
            return $data;
        }else{
            return array();
        }
    }

    public function get_site($user_id, $site_id, $wingi = false, $filterpay = true, $start='', $end=''){
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
        $db->where("id = ?",array($site_id));
        $sites = $db->getone("x_site");
        if($db->count > 0){
            if($wingi){
                $yesterday =  date('Y-m-d ',strtotime('-1 day', strtotime(date("Y-m-d"))));
                if($filterpay){
                    $db2->where("pay = ? ", array(0));
                }
                if($start !='' && $end != ''){
                    $db2->where("DATE(tanggal) BETWEEN ? AND ?", array($start, $end));  
                }else{
                    $db2->where("tanggal < ?", array($yesterday));
                }
            }else{
                if($start !='' && $end != ''){
                    $db2->where("DATE(tanggal) BETWEEN ? AND ?", array($start, $end));  
                }
                if($filterpay){
                    $db2->where("pay = ? ", array(0));
                }
            }  
            $campaign_detail = $db2->get($site_id."_campaign_detail", null, "campaign_id, tanggal, impression, lead, contact, purchase, value, pay");
            //echo $db2->getLastQuery(); 
            $sites['campaign_detail'] = $campaign_detail;
            $sites['total_impression'] = 0;
            $sites['total_lead'] = 0;
            $sites['total_contact'] = 0;
            $sites['total_purchase'] = 0;
            if($db2->count>0){
                foreach($campaign_detail as $i => $z){
                    $campaign_detail[$i]['site_id'] = $site_id;
                    $campaign_detail[$i]['tanggal'] = $z['tanggal'];
                    $sites['total_impression'] = $sites['total_impression'] + $z['impression'];
                    $sites['total_lead'] = $sites['total_lead'] + $z['lead'];
                    $sites['total_contact'] = $sites['total_contact'] + $z['contact'];
                    $sites['total_purchase'] = $sites['total_purchase'] + $z['purchase'];
                }
            }else{
                $sites['campaign_detail'] = array();
            }            
        }
        return $sites;
    }

    public function get_lead_billing($user_id, $site_id, $start='', $end='', $filterpay=true){
        $lead = 0;$id = 0;$totday = 0;
        $datas = array();
        $data = array();
        if($start =='' && $end =='' ){
            $start = date("Y-m-1", strtotime("-1 month"));
            $end = date("Y-m-d"); //$end = date("Y-m-d", strtotime($end." -1 day"));
        }        
        $tglstart = ''; $strtgl = '';
        $sites = $this->get_site($user_id, $site_id, false, $filterpay, $start, $end);
        if(!empty($sites['campaign_detail']) && is_countable($sites['campaign_detail'])){
            foreach($sites['campaign_detail'] as $i => $z){
                if($tglstart==''){
                    $tglstart = 1;
                    $start = $z['tanggal'];
                    
                }
                if($strtgl != $z['tanggal']){
                    $totday++;
                    $strtgl = $z['tanggal'];
                }
                $lead = $lead + $z['lead'];
                array_push($data, array('campaign_id'=>$z['campaign_id'], 'campaign_name'=> $this->get_name_campaign($z['campaign_id'], $site_id), 'site_id'=>$sites['id'],'domain'=>$sites['domain'], 'tanggal'=>$z['tanggal'], 'lead'=>$z['lead']));
                
            }
            $datas['site']= $sites['domain'];
        }
        $datas['lead']= $lead;
        $datas['total_day']= $totday;
        $datas['total_tagihan_lead']= round($lead * 200);
        $datas['total_tagihan_site']= round((35000 / date("t",strtotime("-1 month"))) * $totday);        
        $datas['data']= $data;
        $datas['start']= $start;
        $datas['end']= $end;
        return $datas;
    }

    public function get_name_campaign($campaign_id, $site_id){
        global $app;
        $db = $app->db;
        $db->where("campaign_id = ? and site_id = ?",array($campaign_id, $site_id));
        $campaign = $db->getone("x_campaign", "name");
        if($db->count>0){
            return $campaign['name'];
        }else{
            return '';
        }
    }

    public function bill_cs_dom_by_site($user_id, $site_id, $end='', $pay=0){
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
        $data = array();
        $total_tagihan_site=0;$total_tagihan_cs=0;
        $total_day_site=0;$total_day_cs=0;
        if($end==''){
            $end = date('Y-m-d');
        }
        if($pay==0){
            $db->where("user_id = ? and pay = 0 and id = ?",array($user_id, $site_id));
        }else{
            $db->where("user_id = ? and id = ?",array($user_id, $site_id));
        }        
        $sites = $db->get("x_site", NULL, "id, domain, date_last, status, last_delete");
        //echo $app->db->getLastQuery();
        foreach($sites as $k => $v){
            if($v['status']==1){
                $time1 = date_create(date('Y-m-d', strtotime($v['date_last'])));
                $time2 = date_create($end);  
            }else{
                $time1 = date_create(date('Y-m-d', strtotime($v['date_last'])));
                $time2 = date_create(date('Y-m-d', strtotime($v['last_delete'])));  
            }
            if($time1 < $time2){
                $sites[$k]['days'] = date_diff($time1, $time2)->days;
                $sites[$k]['total_tagihan'] = round((35000 / date("t",strtotime("-1 month"))) * $sites[$k]['days']);
                $total_tagihan_site = $total_tagihan_site + $sites[$k]['total_tagihan'];
            }else{
                $sites[$k]['days'] = 0;
                $sites[$k]['total_tagihan'] = 0;
            }
            $total_day_site=($total_day_site+$sites[$k]['days']);
        }
        $db->where("user_id = ? and site_id = ?",array($user_id, $site_id));
        $cs = $db->get("x_cs", NULL, "id, cs_id, site_id, date_last, last_delete, status, pay, name, nope as phone");
        foreach($cs as $k => $v){
            if($v['pay']==0){
                if($v['status']==1){
                    $time1 = date_create(date('Y-m-d', strtotime($v['date_last'])));
                    $time2 = date_create($end);  
                    $cs[$k]['date_range'] = date_format($time1, "Y-m-d").' to '.date_format($time2, "Y-m-d");
                }else{
                    $time1 = date_create(date('Y-m-d', strtotime($v['date_last'])));
                    $time2 = date_create(date('Y-m-d', strtotime($v['last_delete'])));  
                    $cs[$k]['date_range'] = date_format($time1, "Y-m-d").' to '.date_format($time2, "Y-m-d");
                }
                if($time1 < $time2){
                    $cs[$k]['days'] = date_diff($time1, $time2)->days;
                    //$cs[$k]['total_tagihan'] = round((15000 / date("t",strtotime("-1 month"))) * $cs[$k]['days']);
                    $cs[$k]['total_tagihan'] = round(1000 * $cs[$k]['days']);
                    $total_tagihan_cs = $total_tagihan_cs + $cs[$k]['total_tagihan'];
                    $total_day_cs=($total_day_cs+$cs[$k]['days']);
                }else{
                    //$cs[$k]['days'] = 0;
                    //$cs[$k]['total_tagihan'] = 0;
                    unset($cs[$k]);
                }
                
            }
        }
        $data['site'] = $sites;
        $data['cs'] = $cs;
        $data['total_tagihan_site'] = $total_tagihan_site;
        $data['total_tagihan_cs'] = $total_tagihan_cs;
        $data['total_day_site'] = $total_day_site;
        $data['total_day_cs'] = $total_day_cs;
        return $data;
    }

    public function create_invoice2($user_id, $id, $site_id, $test=false, $start='', $end='', $pay_x=1){
        global $app;
        if($end==''){
            $end = date('Y-m-d');
        }
        $saldo = new saldo();
        $total_saldo = $saldo->get_saldo($user_id);
        $db = $app->db;
        $db2 = $app->db2;
        $res = $this->get_lead_billing($id, $site_id, $start, $end);
        $rex = $this->bill_cs_dom_by_site($id, $site_id, $end);
        $total = 0;
        $last_date = $res['start'];
        $total = $res['total_tagihan_lead'] + $rex['total_tagihan_site'] + $rex['total_tagihan_cs'];
        $total_bill = $total;
        if($total == 0 || $total < (250000 * $pay_x)){
            $ret["code"] =0;  
            $ret["msg"] = "Total bill masih 0.";
        }else{
            $lunas = false;
            if($total_saldo > 0){
                if($total_saldo > $total){
                    $total = 0;
                    $lunas = true;
                }
//                if($total > $total_saldo){
//                    $total = $total - $total_saldo;
//                }else{
//                    $total = 0;
//                    $lunas = true;
//                }
            }
            if($test==true){
                $ret["total_bill"] = $total_bill; 
                $ret["saldo"] = $total_saldo; 
                $ret["lead"] = $res['data']; 
                $ret["site"] = $res['site']; 
                $ret["cs"] = $rex['cs']; 
                $ret["code"] = 1;  
                $ret["msg"] = "Total bill ".$total;
                return $ret;
            }
            if($total < 200000 && $lunas == false){
                $ret["code"] =0;  
                $ret["msg"] = "Total bill masih kurang dari 100,000.";
                return $ret;
            }
            $bil = $this->create_bill($user_id, $total, $last_date, 1);
            if($bil['code']==1){
                if($res['total_tagihan_lead'] > 0){
                    $this->create_bill_detail($bil['id'], $res['lead'].' Lead' , $res['lead'], $res['total_tagihan_lead'], 4, json_encode($res['data']));
                    foreach($res['data'] as $k => $v){
                        $db2->where("tanggal = ? ", array($v['tanggal']));
                        $db2->update($site_id."_campaign_detail", array('pay'=>1));
                    }
                }
                if($rex['total_tagihan_site'] > 0){

                    $this->create_bill_detail($bil['id'], $res['site'] , $rex['total_day_site'], $rex['total_tagihan_site'], 3, json_encode($rex['site']));
                    $db->where("id = ? ", array($site_id));
                    $db->update("x_site", array('date_last'=>$end));
                }
                if($rex['total_tagihan_cs'] > 0){

                    $this->create_bill_detail($bil['id'], count($rex['cs']).' CS' , $rex['total_day_cs'], $rex['total_tagihan_cs'], 2, json_encode($rex['cs']));
                    foreach($rex['cs'] as $k => $v){
                        $db->where("id = ? ", array($v['id']));
                        $db->update("x_cs", array('date_last'=>$end));
                    }
                }
                if($total_saldo > 0){
                    if($total_bill > $total_saldo){
                        $pay['code']= 0;
                        //$this->create_bill_detail($bil['id'], 'Saldo' , $total_saldo, -$total_saldo, 5, '[]');
                        //$pay = $saldo->add($user_id, 'Pay bill #'.$bil['id'], $total_saldo, 0, 'shop');
                    }else{
                        $this->create_bill_detail($bil['id'], 'Saldo' , $total_bill, -$total_bill, 5, '[]');
                        $pay = $saldo->add($user_id, 'Pay bill #'.$bil['id'], $total_bill, 0, 'shop');
                    }
                }else{
                    $pay['code']= 0;
                }
                $bc = new broadcast();
                $db->where("user_id = UNHEX(?)", array($user_id));
                $user = $db->getone("x_user");
                if($pay['code']==1 && $lunas == true){
                    $db->where("id = ? ", array($bil['id']));
                    $db->update("x_bill", array('status'=>1));
                    $pesan = 'Halo '.$user['nama'].',
Akun Gass Anda yang terhubung dengan nomor '.$user['phone'].' sudah memiliki tagihan sebesar '.$total_bill.'.

Billing tersebut sudah *lunas* karena sudah terpotong sisa saldo anda.

Jika Ada pertanyaan, Anda bisa menghubungi Gass support di nomor 62811-360-8550.
_Kami juga menyediakan Jasa Konsultasi Gratis Bagi Anda yang membutuhkan hasil yang lebih optimal dalam beriklan dengan menggunakan Gass_

Terima kasih.
';
                    $bc->add($user['phone'], $user['nama'], $pesan);
                }else{
                    $dayexp = date("Y-m-d", strtotime("+7 days"));
                    $pesan = 'Halo '.$user['nama'].',
Akun Gass Anda yang terhubung dengan nomor '.$user['phone'].' sudah memiliki tagihan sebesar '.$total.'.
Anda bisa melakukan pembayaran dengan topup saldo terlebih dahulu.

https://panel.gass.co.id/pay.html?saldo='.$total.'.
Batas waktu pembayaran bisa kami terima paling lambat '.$dayexp.'.

Berikut invoice nya https://panel.gass.co.id/invoice.html?id='.$bil['id'].'.

Jika Ada pertanyaan, Anda bisa menghubungi Gass support di nomor 62811-360-8550.
_Kami juga menyediakan Jasa Konsultasi Gratis Bagi Anda yang membutuhkan hasil yang lebih optimal dalam beriklan dengan menggunakan Gass_

Terima kasih.
';
                    $bc->add($user['phone'], $user['nama'], $pesan);
                }
                $ret["code"] =1;  
                $ret['bill_id'] = $bil['id'];
                $ret["msg"] = "success add create invoice"; 
            }else{
                $ret["code"] =0;  
                $ret["msg"] = "fail add create invoice"; 
            }
        }
        return $ret;
    }

    public function get($user_id, $wingi = false, $filterpay = true, $start='', $end=''){
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
        $db->where("user_id = ? and status = ?",array($user_id, 1));
        $sites = $db->get("x_site");
        if($wingi){
            $yesterday =  date('Y-m-d ',strtotime('-1 day', strtotime(date("Y-m-d"))));
        }        
        foreach($sites as $k => $v){
            if($wingi){
                if($filterpay){
                    $db2->where("pay = ? ", array(0));
                }
                if($start !='' && $end != ''){
                    $db2->where("DATE(tanggal) BETWEEN ? AND ?", array($start, $end));  
                }else{
                    $db2->where("tanggal < ?", array($yesterday));
                }
                
            }else{
                if($start !='' && $end != ''){
                    $db2->where("DATE(tanggal) BETWEEN ? AND ?", array($start, $end));  
                }
                if($filterpay){
                    $db2->where("pay = ? ", array(0));
                }
            }
            $campaign_detail = $db2->get($v['id']."_campaign_detail");
            //echo $db2->getLastQuery(); 
            $sites[$k]['campaign_detail'] = $campaign_detail;
            $sites[$k]['total_impression'] = 0;
            $sites[$k]['total_lead'] = 0;
            $sites[$k]['total_contact'] = 0;
            $sites[$k]['total_purchase'] = 0;
            foreach($campaign_detail as $i => $z){
                $campaign_detail[$i]['site_id'] = $v['id'];
                $campaign_detail[$i]['tanggal'] = $z['tanggal'];
                $sites[$k]['total_impression'] = $sites[$k]['total_impression'] + $z['impression'];
                $sites[$k]['total_lead'] = $sites[$k]['total_lead'] + $z['lead'];
                $sites[$k]['total_contact'] = $sites[$k]['total_contact'] + $z['contact'];
                $sites[$k]['total_purchase'] = $sites[$k]['total_purchase'] + $z['purchase'];
            }
        }
        return $sites;
    }
    
    public function get_impression_billing($user_id){
        $impression = 0;$id = 0;$totimpression = 0;
        $datas = array();
        $data = array();
        $start = date("Y-m-1", strtotime("-1 month"));
        $end = date("Y-m-d"); //$end = date("Y-m-d", strtotime($end." -1 day"));
        $tglstart = '';
        $sites = $this->get($user_id, false, true, $start, $end);
        foreach($sites as $k => $v){
            foreach($sites[$k]['campaign_detail'] as $i => $z){
                if($tglstart==''){
                    $tglstart = 1;
                    $start = $z['tanggal'];
                }
                //if($impression <= 1000){
                    $impression = $impression + $z['impression'];
                    array_push($data,array('site_id'=>$sites[$k]['id'],'domain'=>$sites[$k]['domain'], 'tanggal'=>$z['tanggal'], 'impression'=>$z['impression']));
                //}
                //if($impression >= 1000){
//                    $totimpression = $totimpression + $impression;
//                    $datas[$i]['id']= $id;
//                    $datas[$i]['impression']= $totimpression;
//                    $datas[$i]['data']= $data;
//                    $data = array();
//                    $impression =0;
//                    $id++;
//                }
            }
        }
        $datas['impression']= $impression;
        $datas['data']= $data;
        $datas['start']= $start;
        $datas['end']= $end;
        //print_r($datas);
        //die();
        return $datas;
    }
    
    public function bill_cs_dom($user_id){
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
        $data = array();
        $total_tagihan_site=0;$total_tagihan_cs=0;
        $db->where("user_id = ? and pay = 0",array($user_id));
        $sites = $db->get("x_site", NULL, "id, domain, date_last, status, last_delete");
        //echo $app->db->getLastQuery();
        foreach($sites as $k => $v){
            if($v['status']==1){
                $time1 = date_create(date('Y-m-d', strtotime($v['date_last'])));
                $time2 = date_create(date('Y-m-d'));  
            }else{
                $time1 = date_create(date('Y-m-d', strtotime($v['date_last'])));
                $time2 = date_create(date('Y-m-d', strtotime($v['last_delete'])));  
            }
            if($time1 < $time2){
                $sites[$k]['days'] = date_diff($time1, $time2)->days;
                $sites[$k]['total_tagihan'] = round((25000 / date("t",strtotime("-1 month"))) * $sites[$k]['days']);
                $total_tagihan_site = $total_tagihan_site + $sites[$k]['total_tagihan'];
            }else{
                $sites[$k]['days'] = 0;
                $sites[$k]['total_tagihan'] = 0;
            }
        }
        $db->where("user_id = ?",array($user_id));
        $cs = $db->get("x_cs", NULL, "id, cs_id, site_id, date_last, last_delete, status, pay, name, nope as phone");
        foreach($cs as $k => $v){
            if($v['pay']==0){
                if($v['status']==1){
                    $time1 = date_create(date('Y-m-d', strtotime($v['date_last'])));
                    $time2 = date_create(date('Y-m-d'));  
                }else{
                    $time1 = date_create(date('Y-m-d', strtotime($v['date_last'])));
                    $time2 = date_create(date('Y-m-d', strtotime($v['last_delete'])));  
                }
                if($time1 < $time2){
                    $cs[$k]['days'] = date_diff($time1, $time2)->days;
                    $cs[$k]['total_tagihan'] = round((15000 / date("t",strtotime("-1 month"))) * $cs[$k]['days']);
                    $total_tagihan_cs = $total_tagihan_cs + $cs[$k]['total_tagihan'];
                }else{
                    $cs[$k]['days'] = 0;
                    $cs[$k]['total_tagihan'] = 0;
                }
            }
        }
        $data['site'] = $sites;
        $data['cs'] = $cs;
        $data['total_tagihan_site'] = $total_tagihan_site;
        $data['total_tagihan_cs'] = $total_tagihan_cs;
        return $data;
    }
    
    public function create_invoice($user_id, $id){
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
        $res = $this->get_impression_billing($id);
        $rex = $this->bill_cs_dom($id);
        $total = 0;
        if(count($res) > 0){
            if($res['impression'] > 1000){
                $qty = round($res['impression'] / 1000);
                $total = $qty * 35000;
            }else{
                return false;
            }
        }
        $last_date = $res['start'];
        $total = $total + $rex['total_tagihan_site'] + $rex['total_tagihan_cs'];
        if($total == 0 || $total < 250000){
            $ret["code"] =0;  
            $ret["msg"] = "Total bill masih 0.";
        }else{
            $bil = $this->create_bill($user_id, $total, $last_date);
            //echo $db->getLastQuery();
            if($bil['code']==1){
                if(isset($res['impression'])){
                    if($res['impression'] > 1000){
                        $this->create_bill_detail($bil['id'], $res['impression'].' impression' , $qty, ($qty * 25000), 1, json_encode($res['data']));
                        foreach($res['data'] as $k => $v){
                            $db2->where("tanggal = ? ", array($v['tanggal']));
                            $db2->update($v['site_id']."_campaign_detail", array('pay'=>1));
                            //echo $db2->getLastQuery();
                        }
                    }
                }
                $this->create_bill_detail($bil['id'], count($rex['site']).' Site' , count($rex['site']), $rex['total_tagihan_site'], 3, json_encode($rex['site']));
                foreach($rex['site'] as $k => $v){
                    $db->where("id = ? ", array($v['id']));
                    $db->update("x_site", array('date_last'=>date('Y-m-1')));
                }
                $this->create_bill_detail($bil['id'], count($rex['cs']).' CS' , count($rex['cs']), $rex['total_tagihan_cs'], 2, json_encode($rex['cs']));
                foreach($rex['cs'] as $k => $v){
                    $db->where("id = ? ", array($v['id']));
                    $db->update("x_cs", array('date_last'=>date('Y-m-1')));
                }
                $db->where("user_id = UNHEX(?)", array($user_id));
                $user = $db->getone("x_user");
                if($db->count >0){
                    $dayexp = date("Y-m-d", strtotime("+7 days"));
                    $bc = new broadcast();
                    $pesan = 'Halo '.$user['nama'].',

Akun Gass Anda yang terhubung dengan nomor '.$user['phone'].' sudah memiliki tagihan sebesar '.$total.'.

Anda bisa melakukan pembayaran dengan topup saldo terlebih dahulu.

Batas waktu pembayaran bisa kami terima paling lambat '.$dayexp.'.

Jika Ada pertanyaan, Anda bisa menghubungi Gass support di nomor 0811-360-8550.


Terima kasih.
';
                    $bc->add($user['phone'], $user['nama'], $pesan);
                    
                }
                $ret["code"] =1;  
                $ret["msg"] = "success add create invoice"; 
                
            }else{
                $ret["code"] =0;  
                $ret["msg"] = "fail add create invoice"; 
            }   
        }   
        return $ret;
    }
    
    public function create_bill($user_id, $total, $last_date, $split_bill=0){
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
        $s = new saldo();
        $saldo = $s->get_saldo($user_id);        
		$dt = new DateTime("now", new DateTimeZone('Asia/Jakarta'));
		$timestamp = $dt->format('Y-m-d H:i:s');		
		$data["user_id"] = $db->func("UNHEX(?)", array($user_id));
		$data["total"] = $total;
        $data['last_date'] = date("Y-m-d H:i:s", strtotime($last_date));
		$data["tanggal"] = $timestamp;
        $data["expired"] = $dt->modify('+7 day')->format('Y-m-d H:i:s');
		$data["status"]   = 0;
        if($split_bill==1){
            $data["split_bill"]   = 1;
        }
        $id = $db->insert("x_bill", $data);
		if($id){
            $ret["code"] =1; 
            $ret["id"] = $id; 
			$ret["msg"] = "sukses add create bill"; 
			return $ret;
		}else{
			$ret["code"] =0;  
			$ret["msg"] = "fail add create bill"; 
			return $ret;
		} 
    }
    
    public function create_bill_detail($bill_id, $keterangan, $qty, $harga, $produk_id, $datas){
        global $app;
        $db = $app->db;
        $db2 = $app->db2;		
		$data["bill_id"] = $bill_id;
		$data["keterangan"] = $keterangan;
		$data["qty"]   = $qty;
        $data["harga"]   = $harga;
        $data["produk_id"]   = $produk_id;
        $data["status"]   = 0;
        $data["datas"]   = $datas;
        $id = $db->insert("x_bill_detail", $data);
		if($id){
            $ret["code"] =1;  
			$ret["msg"] = "sukses add create bill detail"; 
			return $ret;
		}else{
			$ret["code"] =0;  
			$ret["msg"] = "fail add create bill detail"; 
			return $ret;
		} 
    }

    public function add_cron_bill($user_id, $id_user, $site_id, $start_date, $end_date, $pay_x=1){
        global $app;
        $db = $app->db;
		$data["user_id"] = $user_id;
		$data["id_user"] = $id_user;
        $data['site_id'] = $site_id;
		$data["start_date"] = $start_date;
        $data["end_date"] = $end_date;
        $data["bill_id"]   = 0;
		$data["pay_x"]   = $pay_x;
        $data["status"]   = 0;
        $id = $db->insert("x_cron_bill", $data);
		if($id){
            $ret["code"] =1; 
            $ret["id"] = $id; 
			$ret["msg"] = "sukses add cron bill"; 
			return $ret;
		}else{
			$ret["code"] =0;  
			$ret["msg"] = "fail add cron bill"; 
			return $ret;
		} 
    }

    public function create_split_bill($bill_id){
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
        $db->where("id = ?", array($bill_id));
        $bill = $db->getone("x_bill");
        if($db->count > 0){
            $db->where("bill_id = ?", array($bill_id));
            $bill_detail = $db->get("x_bill_detail");
            $domains = [];$impression = []; $cs = [];
            foreach($bill_detail as $k => $v){
                if($v['produk_id']==3){
                    $domains = json_decode($v['datas'], true);
                }else if($v['produk_id']==2){
                    $cs = json_decode($v['datas'], true);
                }else if($v['produk_id']==1){
                    $impression = json_decode($v['datas'], true);
                }
            }
            foreach($domains as $k => $v){
                $datacs=[];$c=0;$total=0;$total_cs=0; $s_cs = array();
                foreach($cs as $i => $g){
                    if($v['id'] == $g['site_id']){
                        $datacs[$c] = $g;
                        $total = $total + $g['total_tagihan'];
                        $c++;            
                        if (!in_array($g['phone'], $s_cs)){
                            $total_cs++;
                            array_push($s_cs, $g['phone']);
                        }
                    }
                }
                $domains[$k]['cs'] = $datacs;
                $domains[$k]['total_tagihan_cs'] = $total;
                $domains[$k]['total_cs'] = $total_cs;
                $dataimpression=[];$m=0;$total=0; $total_impression=0;
                foreach($impression as $i => $p){
                    if($v['id'] == $p['site_id']){
                        $dataimpression[$m] = $p;
                        $total = $total + $p['impression'];
                        $total_impression = $total_impression + $p['impression'];
                        $m++;
                    }
                }
                $domains[$k]['impression'] = $dataimpression;
                $qty = round($total_impression / 1000);
                $domains[$k]['total_tagihan_impression'] = ($qty * 25000);
                if($domains[$k]['total_tagihan_cs'] > 0 || $dataimpression > 0){
                    // add invoice split bill
                    if(!isset($domains[$k]['total_tagihan'])){
                        $domains[$k]['total_tagihan'] = 0;
                    }
                    if(!isset($domains[$k]['days'])){
                        $domains[$k]['days'] = 0;
                    }
                    $tot = $domains[$k]['total_tagihan'] + $domains[$k]['total_tagihan_cs'] + $domains[$k]['total_tagihan_impression'];
                    $data = $bill; 
                    $data['domain'] = $domains[$k]['domain'];
                    $data['total'] = $tot;
                    $data['bill_id'] = $data['id'];
                    unset($data['id']);
                    unset($data['split_bill']);
                    $id = $db->insert("x_bill_split", $data);
                    if($id){
                        $this->create_split_bill_detail($id, 'Domain '.$domains[$k]['days'].' Days' , 1, $domains[$k]['total_tagihan'], 3, json_encode(array()));
                        
                        $this->create_split_bill_detail($id, $domains[$k]['total_cs'].' CS' , $domains[$k]['total_cs'], $domains[$k]['total_tagihan_cs'], 2, json_encode($domains[$k]['cs']));
                        
                        $this->create_split_bill_detail($id, $total_impression.' impression' , $qty, $domains[$k]['total_tagihan_impression'], 1, json_encode($domains[$k]['impression']));
                    }
                }
            }
            //echo $db->getLastQuery();
            //print_r($domains);
            $ret["code"] =1;  
			$ret["msg"] = "success add create split bill"; 
			return $ret;
        }else{
            $ret["code"] =0;  
			$ret["msg"] = "Error, bill not found"; 
			return $ret;
        }
        
    }
    
     public function create_split_bill_detail($split_bill_id, $keterangan, $qty, $harga, $produk_id, $datas){
        global $app;
        $db = $app->db;
        $db2 = $app->db2;		
		$data["split_bill_id"] = $split_bill_id;
		$data["keterangan"] = $keterangan;
		$data["qty"]   = $qty;
        $data["harga"]   = $harga;
        $data["produk_id"]   = $produk_id;
        $data["status"]   = 0;
        $data["datas"]   = $datas;
        $id = $db->insert("x_bill_split_detail", $data);
		if($id){
			$ret["msg"] = "sukses add create split bill detail"; 
			return $ret;
		}else{
			$ret["code"] =0;  
			$ret["msg"] = "fail add create split bill detail"; 
			return $ret;
		} 
    }
    
    public function get_split_detail($user_id, $id){
        global $keya, $c_time, $app;
        $db = $app->db;
        $db->where("user_id = UNHEX(?) and id = ?", array($user_id, $id));
		$db->orderBy("tanggal", "Desc");	
    	$res = $db->getone("x_bill_split");
        if($db->count > 0){
            $db->where("split_bill_id = ?", array($id));
            $dat = $db->get("x_bill_split_detail");
            if($db->count > 0){
                foreach($dat as $i => $d){
                    $dat[$i]['datas'] = json_decode($dat[$i]['datas'], true);
                }                
            }
            $res['last_date'] = date("Y-m-d", strtotime($res['last_date']));
            $res['tanggal'] = date("Y-m-d", strtotime($res['tanggal']));
            $res['expired'] = date("Y-m-d", strtotime($res['expired']));
            $data['bill'] = $res;
            $data['detail'] = $dat;
            return $data;
        }else{
            return array();
        }
    }
}
