<?php

namespace DynamicWebhook\Interfaces;

/**
 * Interface for visitor management
 */
interface VisitorManagerInterface
{
    /**
     * Process visitor creation and tracking
     *
     * @param array $data Extracted webhook data
     * @param array $context Processing context
     * @return array|null Visitor data
     */
    public function processVisitor(array $data, array $context): ?array;

    /**
     * Create new visitor
     *
     * @param string $phone Phone number
     * @param bool $isNewContact Whether this is a new contact
     * @return array|null Visitor data
     */
    public function createVisitor(string $phone, bool $isNewContact): ?array;

    /**
     * Get existing visitor
     *
     * @param string|null $visitorId Visitor ID
     * @param string $phone Phone number
     * @return array|null Visitor data
     */
    public function getVisitor(?string $visitorId, string $phone): ?array;

    /**
     * Update visitor with CTWA data
     *
     * @param array $visitor Visitor data
     * @param array $ctwaData CTWA data
     * @return bool Success status
     */
    public function updateVisitorWithCTWA(array $visitor, array $ctwaData): bool;
}
