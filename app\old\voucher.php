<?php 
class voucher{
	public function random_str($length, $keyspace = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ')
    {
        $str = '';
        $max = mb_strlen($keyspace, '8bit') - 1;
        for ($i = 0; $i < $length; ++$i) {
            $str .= $keyspace[rand(0, $max)];
        }
        return $str;
    }
    public function voucher_gen($diskon)
    {
    	global $app;
        $db = $app->db;
        
        ////////////// generate voucher code
        $rcode = strtolower($this->random_str(16));
        $rcode = str_split($rcode, 4);
        $rcode = strtoupper(implode("-", $rcode));
        $ind = md5($rcode);
        $db->where("ind = UNHEX(?)", array($ind));
		$db->getone("x_coupon");
        if ($db->count == 0){  
            $data["code"]       = $rcode;
            $data["diskon"] = $diskon;
            $data["status"]     = 0;
            $data["ind"] = $db->func("UNHEX(?)", array($ind));
            $db->insert("x_coupon", $data);
            return $rcode;
        } else {
            $this->voucher_gen($diskon);
        }
    }
    public function cek_valid($voucher)
    {
    	global $app;
        $db = $app->db;
        $voucher = strtoupper(trim($voucher));
    	$ind = md5($voucher);
    	$db->where("ind = UNHEX(?)", array($ind));
    	$coupon = $db->getone("x_coupon");

    	if ($db->count > 0) {
            if($coupon["status"] == 1){
                $ret["code"] = 0;
                $ret["msg"] = "Already Used";
                return $ret;
            }
			$ret["code"] = 1;
    		$ret["msg"] = "Coupon Valid";
    		$ret["diskon"] = $coupon["diskon"];
    		$_SESSION["voucher"] = $voucher;
    		$_SESSION["diskon"] =$coupon["diskon"];
    		return $ret;
    	}
    	else
    	{
			$ret["code"] = 0;
    		$ret["msg"] = "Coupon Not Exist";
    		return $ret;
    	}
    }
    public function use_voucher($voucher)
    {
    	global $app;
        $db = $app->db;
        //$db->where("user_id = UNHEX(?)",array($user_id));
        //$db->update("x_user",array("under_ib"=>1));
        $voucher = strtoupper(trim($voucher));
    	$ind = md5($voucher);
    	$db->where("ind = UNHEX(?)", array($ind));
    	$coupon = $db->getone("x_coupon");
    	if ($db->count > 0) {
    		$db->where("ind = UNHEX(?)", array($ind));
    		$db->update("x_coupon", array("status"=>1));
    		$ret["msg"] = "Apply Coupon Success";
    		$ret["diskon"] = $coupon["diskon"];
    		return $ret;
    	}
    	elseif($coupon["status"] == 1)
    	{
    		$ret["msg"] = "Already Used";
    		return $ret;
    	}
    	else
    	{
    		$ret["msg"] = "Coupon Not Exist";
    		return $ret;
    	}
    }
}