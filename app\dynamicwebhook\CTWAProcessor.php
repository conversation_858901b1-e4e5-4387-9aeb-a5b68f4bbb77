<?php

namespace DynamicWebhook;

use DynamicWebhook\Interfaces\CTWAProcessorInterface;
use DynamicWebhook\Interfaces\MessageNormalizerInterface;

/**
 * Click-to-WhatsApp (CTWA) processor
 */
class CTWAProcessor implements CTWAProcessorInterface
{
    private MessageNormalizerInterface $normalizer;
    private array $supportedPlatforms = [
        'qontak', 'waba', 'pancake', 'konekwa', 'halosis'
    ];

    public function __construct(MessageNormalizerInterface $normalizer)
    {
        $this->normalizer = $normalizer;
    }

    /**
     * Extract CTWA data from webhook payload
     */
    public function extractCTWAData(array $data, string $platform): array
    {
        $ctwaData = [
            'ctwa_clid' => null,
            'source_id' => null,
            'adcopy_id' => null,
            'campaign_data' => []
        ];

        switch ($platform) {
            case 'qontak':
                return $this->extractQontakCTWA($data);
            case 'waba':
            case 'pancake':
                return $this->extractWABACTWA($data);
            case 'konekwa':
                return $this->extractKonekwaCTWA($data);
            case 'halosis':
                return $this->extractHalosisCTWA($data);
            default:
                return $this->extractGenericCTWA($data);
        }
    }

    /**
     * Validate CTWA data
     */
    public function validateAndProcess(array $ctwaData): array
    {
        // Validate ctwa_clid format (usually UUID or alphanumeric)
        if ($ctwaData['ctwa_clid']) {
            $ctwaData['ctwa_clid'] = preg_replace('/[^a-zA-Z0-9\-_]/', '', $ctwaData['ctwa_clid']);
            if (strlen($ctwaData['ctwa_clid']) < 5) {
                $ctwaData['ctwa_clid'] = null;
            }
        }

        // Validate source_id
        if ($ctwaData['source_id']) {
            $ctwaData['source_id'] = preg_replace('/[^a-zA-Z0-9\-_]/', '', $ctwaData['source_id']);
            if (strlen($ctwaData['source_id']) < 3) {
                $ctwaData['source_id'] = null;
            }
        }

        // Set adcopy_id same as source_id if not set
        if (!$ctwaData['adcopy_id'] && $ctwaData['source_id']) {
            $ctwaData['adcopy_id'] = $ctwaData['source_id'];
        }

        return $ctwaData;
    }

    /**
     * Check if webhook contains CTWA data
     */
    public function hasCTWAData(array $data, string $platform): bool
    {
        $ctwaData = $this->extractCTWAData($data, $platform);
        return !empty($ctwaData['ctwa_clid']) || !empty($ctwaData['source_id']);
    }

    /**
     * Get supported platforms for CTWA extraction
     */
    public function getSupportedPlatforms(): array
    {
        return $this->supportedPlatforms;
    }

    /**
     * Extract CTWA data from Qontak webhook
     */
    private function extractQontakCTWA(array $data): array
    {
        $ctwaData = [
            'ctwa_clid' => null,
            'source_id' => null,
            'adcopy_id' => null,
            'campaign_data' => []
        ];

        // Check room description for CTWA data
        if (isset($data["room"]["description"])) {
            $description = $data["room"]["description"];
            
            // Extract ctwa_clid
            if (preg_match('/ctwa_clid=([^;&\s]+)/', $description, $matches)) {
                $ctwaData['ctwa_clid'] = trim($matches[1]);
            }
            
            // Extract source_id (equivalent to adcopy_id)
            if (preg_match('/source_id=([^;&\s]+)/', $description, $matches)) {
                $ctwaData['source_id'] = trim($matches[1]);
                $ctwaData['adcopy_id'] = trim($matches[1]);
            }
            
            // Extract additional campaign data
            if (preg_match('/campaign_id=([^;&\s]+)/', $description, $matches)) {
                $ctwaData['campaign_data']['campaign_id'] = trim($matches[1]);
            }
            
            if (preg_match('/ad_id=([^;&\s]+)/', $description, $matches)) {
                $ctwaData['campaign_data']['ad_id'] = trim($matches[1]);
            }
        }

        return $ctwaData;
    }

    /**
     * Extract CTWA data from WABA/Pancake webhook
     */
    private function extractWABACTWA(array $data): array
    {
        $ctwaData = [
            'ctwa_clid' => null,
            'source_id' => null,
            'adcopy_id' => null,
            'campaign_data' => []
        ];

        // Check in referral or context data
        if (isset($data['entry'][0]['changes'][0]['value']['messages'][0]['referral'])) {
            $referral = $data['entry'][0]['changes'][0]['value']['messages'][0]['referral'];
            
            // Extract directly from referral fields (highest priority)
            if (isset($referral['ctwa_clid'])) {
                $ctwaData['ctwa_clid'] = $referral['ctwa_clid'];
            }
            
            if (isset($referral['source_id'])) {
                $ctwaData['source_id'] = $referral['source_id'];
                $ctwaData['adcopy_id'] = $referral['source_id'];
            }
            
            // Extract from referral source_url if has parameters
            if (isset($referral['source_url'])) {
                $ctwaData = $this->extractFromUrl($referral['source_url'], $ctwaData);
            }
            
            // Extract from referral body if available
            if (isset($referral['body'])) {
                $ctwaData = $this->extractFromText($referral['body'], $ctwaData);
            }
            
            // Extract additional campaign data from referral
            if (isset($referral['source_type'])) {
                $ctwaData['campaign_data']['source_type'] = $referral['source_type'];
            }
            
            if (isset($referral['headline'])) {
                $ctwaData['campaign_data']['headline'] = $referral['headline'];
            }
        }

        // Check in context field
        if (isset($data['entry'][0]['changes'][0]['value']['messages'][0]['context'])) {
            $context = $data['entry'][0]['changes'][0]['value']['messages'][0]['context'];
            
            if (isset($context['ad_id'])) {
                $ctwaData['campaign_data']['ad_id'] = $context['ad_id'];
            }
            
            if (isset($context['campaign_id'])) {
                $ctwaData['campaign_data']['campaign_id'] = $context['campaign_id'];
            }
        }

        return $ctwaData;
    }

    /**
     * Extract CTWA data from Konekwa webhook
     */
    private function extractKonekwaCTWA(array $data): array
    {
        $ctwaData = [
            'ctwa_clid' => null,
            'source_id' => null,
            'adcopy_id' => null,
            'campaign_data' => []
        ];

        // Check in raw data
        if (isset($data['raw'])) {
            $rawData = json_decode($data['raw'], true);
            
            if ($rawData) {
                // Check at root level (backward compatibility)
                if (isset($rawData['ctwa_clid'])) {
                    $ctwaData['ctwa_clid'] = $rawData['ctwa_clid'];
                }
                
                if (isset($rawData['source_id'])) {
                    $ctwaData['source_id'] = $rawData['source_id'];
                    $ctwaData['adcopy_id'] = $rawData['source_id'];
                }
                
                // Check in WhatsApp Business structure (newer structure)
                if (isset($rawData['mei']['messages'][0]['message']['extendedTextMessage']['contextInfo'])) {
                    $contextInfo = $rawData['mei']['messages'][0]['message']['extendedTextMessage']['contextInfo'];
                    
                    // Check externalAdReply for CTWA data
                    if (isset($contextInfo['externalAdReply'])) {
                        $adReply = $contextInfo['externalAdReply'];
                        
                        if (isset($adReply['ctwaClid']) && !$ctwaData['ctwa_clid']) {
                            $ctwaData['ctwa_clid'] = $adReply['ctwaClid'];
                        }
                        
                        if (isset($adReply['sourceId']) && !$ctwaData['source_id']) {
                            $ctwaData['source_id'] = $adReply['sourceId'];
                            $ctwaData['adcopy_id'] = $adReply['sourceId'];
                        }
                        
                        // Extract additional campaign data
                        if (isset($adReply['sourceType'])) {
                            $ctwaData['campaign_data']['source_type'] = $adReply['sourceType'];
                        }
                        
                        if (isset($adReply['sourceUrl'])) {
                            $ctwaData['campaign_data']['source_url'] = $adReply['sourceUrl'];
                        }
                    }
                }
            }
        }

        return $ctwaData;
    }

    /**
     * Extract CTWA data from Halosis webhook
     */
    private function extractHalosisCTWA(array $data): array
    {
        return $this->extractGenericCTWA($data);
    }

    /**
     * Extract CTWA data from generic webhook
     */
    private function extractGenericCTWA(array $data): array
    {
        $ctwaData = [
            'ctwa_clid' => null,
            'source_id' => null,
            'adcopy_id' => null,
            'campaign_data' => []
        ];

        // Look for common CTWA fields in the data
        if (isset($data['ctwa_clid'])) {
            $ctwaData['ctwa_clid'] = $data['ctwa_clid'];
        }
        
        if (isset($data['source_id'])) {
            $ctwaData['source_id'] = $data['source_id'];
            $ctwaData['adcopy_id'] = $data['source_id'];
        }

        return $ctwaData;
    }

    /**
     * Extract CTWA data from URL parameters
     */
    private function extractFromUrl(string $url, array $ctwaData): array
    {
        $parsed = parse_url($url);
        if (isset($parsed['query'])) {
            parse_str($parsed['query'], $params);
            
            if (isset($params['ctwa_clid']) && !$ctwaData['ctwa_clid']) {
                $ctwaData['ctwa_clid'] = $params['ctwa_clid'];
            }
            
            if (isset($params['source_id']) && !$ctwaData['source_id']) {
                $ctwaData['source_id'] = $params['source_id'];
                $ctwaData['adcopy_id'] = $params['source_id'];
            }
        }

        return $ctwaData;
    }

    /**
     * Extract CTWA data from text content
     */
    private function extractFromText(string $text, array $ctwaData): array
    {
        // Look for ctwa_clid in text if not found yet
        if (!$ctwaData['ctwa_clid'] && preg_match('/ctwa_clid[=:]\s*([^&\s,]+)/i', $text, $matches)) {
            $ctwaData['ctwa_clid'] = trim($matches[1]);
        }
        
        // Look for source_id in text if not found yet
        if (!$ctwaData['source_id'] && preg_match('/source_id[=:]\s*([^&\s,]+)/i', $text, $matches)) {
            $ctwaData['source_id'] = trim($matches[1]);
            $ctwaData['adcopy_id'] = trim($matches[1]);
        }

        return $ctwaData;
    }
}
