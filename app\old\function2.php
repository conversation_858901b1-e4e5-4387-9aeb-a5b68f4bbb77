<?php


use FacebookAds\Api;
use FacebookAds\Logger\CurlLogger;
use FacebookAds\Object\ServerSide\Event;
use FacebookAds\Object\ServerSide\EventRequest;
use FacebookAds\Object\ServerSide\UserData;
use FacebookAds\Object\ServerSide\CustomData;
use FacebookAds\Object\ServerSide\ActionSource;
 
 

function gass_track_fb($vid,$event,$phone="",$email="")
{
    global $app;
	$db = $app->db;

    $access_token = "926537268797084";
    $pixel = "EAAHwVeoaZBPoBACjNnX1qY90oCzaNjYlRSl5Yg1IxwsLpGsSDYmkIJ4w3qTkZC3ngOGoDR1CaF7ORZAsGyfL6UohGQAdIuoyrjqzdrvvF74hGL03DgG2bHDLNb6JhRJ6uwDUqzZBR6Ys5M7ZASOK7N9VVJshMOxsPzWXGNiFi3KXiSatNEqrS";

    $waktu = time();

	$api = Api::init(null, null, $access_token);
	$api->setLogger(new CurlLogger());

    $db->where("vid", $vid);
	$data = $db->getone("x_visitor");

    $user_data = new UserData();
		if (isset($data["fbc"]) && $data["fbc"] != "") {
			$user_data->setFbc($data["fbc"]);
		} else {
			if (isset($data["fbclid"]) && $data["fbclid"] != "") {
				$fbc = "fb.1." . $waktu . "." . $data["fbclid"];
				$user_data->setFbc($fbc);
			}
		}
		if (isset($data["ip"]) && $data["ip"] != "") {
			$user_data->setClientIpAddress($data["ip"]);
		}
		if (isset($data["agent"]) && $data["agent"] != "") {
			$user_data->setClientUserAgent($data["agent"]);
		}
		if (isset($data["fbp"]) && $data["fbp"] != "") {
			$user_data->setFbp($data["fbp"]);
		}
		if (isset($data["email"]) && $data["email"] != "") {
			$user_data->setEmail($data["email"]);
		}
		if (isset($data["phone"]) && $data["phone"] != "") {
			$user_data->setPhone($data["phone"]);
		}



		if (isset($data["useragent"]) && $data["useragent"] != "") {
			$user_data->setClientUserAgent($data["useragent"]);
		}
		$user_data->setExternalId($data["vid"]);

		if (isset($phone)) {
			$phone = preg_replace('/[^0-9.]+/', '', $phone);
			$data_insert = array();
			$data_insert["phone"] = $phone;

			$db->where("vid", $data["vid"]);
			$db->update("x_visitor", $data_insert);

			$user_data->setPhone($phone);
		}

		if (isset($email)) {
			$data_insert["email"] = $email;

			$db->where("vid", $data["vid"]);
			$db->update("x_visitor", $data_insert);

			$user_data->setEmail(trim($email));
		}

    $event = new Event();
	$event->setEventId($event, "-" . $data["vid"]);
	$event->setEventName($event);
	$event->setEventTime($waktu);
	$event->setUserData($user_data);
	$event->setActionSource(ActionSource::WEBSITE);

    $events = array();
	array_push($events, $event);

    $request = (new EventRequest($pixel))->setEvents($events);
	$response = @$request->execute();

    return $response;
}