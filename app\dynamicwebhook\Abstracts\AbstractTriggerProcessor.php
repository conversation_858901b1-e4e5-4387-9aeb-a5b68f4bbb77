<?php

namespace DynamicWebhook\Abstracts;

use DynamicWebhook\Interfaces\TriggerProcessorInterface;

/**
 * Abstract base class for trigger processors
 */
abstract class AbstractTriggerProcessor implements TriggerProcessorInterface
{
    protected $metaService;
    protected $dbService;

    public function __construct($metaService, $dbService)
    {
        $this->metaService = $metaService;
        $this->dbService = $dbService;
    }

    /**
     * Process trigger based on extracted data
     */
    public function process(array $data, array $context): bool
    {
        if (!$this->shouldProcess($data['message_type'] ?? 'message_in')) {
            return false;
        }

        return $this->processSpecificTrigger($data, $context);
    }

    /**
     * Get trigger type name
     */
    abstract public function getTriggerType(): string;

    /**
     * Check if trigger should be processed for given message type
     */
    abstract public function shouldProcess(string $messageType): bool;

    /**
     * Process specific trigger logic - to be implemented by concrete classes
     */
    abstract protected function processSpecificTrigger(array $data, array $context): bool;

    /**
     * Get meta configuration
     */
    protected function getMeta(string $key): array
    {
        if (!$this->metaService) {
            return ['code' => 0, 'result' => ['data' => '']];
        }

        return $this->metaService->get_meta($key);
    }

    /**
     * Clean string helper
     */
    protected function cleanString(string $input): string
    {
        if (function_exists('clean_string')) {
            return clean_string($input);
        }
        
        return trim($input);
    }
}
