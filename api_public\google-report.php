<?php

if (!isset($_GET["debug"])) {

	header("Content-type: text/csv");

	header("Content-Disposition: attachment; filename=file.csv");

	header("Pragma: no-cache");

	header("Expires: 0");

}

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

global $app;
$db = $app->db;

if (!isset($_GET["pk"])) {
	die();
}

$db->where("project_key = UNHEX(?)", [$_GET["pk"]]);
$project = $db->getone("project");

if ($project == NULL) {die();}

assign_child_db($project["project_id"]);

global $app;
$db2 = $app->db2;

$db2->join("visitor_source", "visitor_source.visitor_id = visitor.visitor_id");
//$db2->where("visitor.data like ?",["%gclid%"]);
//$db2->orwhere("visitor.data like ?",["%WBRAID%"]);
//$db2->orwhere("visitor.data like ?",["%GBRAID%"]);

if (isset($_GET["type"])) {
	if ($_GET["type"] == "purchase" || $_GET["type"] == "prospek" || $_GET["type"] == "mql" || $_GET["type"] == "lead" || $_GET["type"] == "cta") {
		$type = $_GET["type"];
	} else {
		die();
	}

}

$db2->join("visitor_event", "visitor_event.visitor_id = visitor.visitor_id");
$db2->where("visitor_source.source = 'google'");
$db2->where("visitor_event.event", $_GET["type"]);

if(!isset($_GET["all"])){
    $db2->where("visitor_event.waktu >= DATE_ADD(CURDATE(), INTERVAL -5 DAY)");
}

if(isset($_GET["filter"]))
{
	$db2->where("visitor.page_url LIKE ?",["%".$_GET["filter"]."%"]);
}


$db2->orderBy("visitor_event.waktu", "desc");
$v = $db2->get("visitor");

//echo $db2->getLastQuery();

$m        = new meta();
$cta_name = $m->get_meta("google_cta_name");
if ($cta_name["code"] == 0) {$cta_name = "gass-cta";} else { $cta_name = $cta_name["result"]["data"];}

$lead_name = $m->get_meta("google_lead_name");
if ($lead_name["code"] == 0) {$lead_name = "gass-lead";} else { $lead_name = $lead_name["result"]["data"];}

$mql_name = $m->get_meta("google_mql_name");
if ($mql_name["code"] == 0) {$mql_name = "gass-mql";} else { $mql_name = $mql_name["result"]["data"];}

$prospek_name = $m->get_meta("google_prospek_name");
if ($prospek_name["code"] == 0) {$prospek_name = "gass-prospek";} else { $prospek_name = $prospek_name["result"]["data"];}

$purchase_name = $m->get_meta("google_purchase_name");
if ($purchase_name["code"] == 0) {$purchase_name = "gass-purchase";} else { $purchase_name = $purchase_name["result"]["data"];}

/*
foreach ($v as $key => $value) {
if($value["phone"] == NULL){
unset($v[$key]);
}

}
 */
 
 if($type == "cta"){ 
	$conversion_name = $cta_name;
 }elseif($type == "lead"){
	$conversion_name = $lead_name;
 }elseif($type == "mql"){
	$conversion_name = $mql_name;
 }elseif($type == "prospek"){
	$conversion_name = $prospek_name;
 }elseif($type == "purchase"){
	$conversion_name = $purchase_name;
 }else{
	$conversion_name = "gass-" . $type;
 }


$currency        = "IDR";

echo "Parameters:TimeZone=Asia/Jakarta";

echo "\n";

echo "Google Click ID,Conversion Name,Conversion Time,Conversion Value,Conversion Currency";

echo "\n";

foreach ($v as $key => $value) {

	$data = unserialize($value["data"]);

	if (isset($data["google_ads"]["gclid"])) {
		$display[0] = $data["google_ads"]["gclid"];
	} elseif (isset($data["google_ads"]["wbraid"])) {
		//$display[0] = $data["google_ads"]["wbraid"];
	} elseif (isset($data["google_ads"]["gbraid"])) {
		//$display[0] = $data["google_ads"]["gbraid"];
	}

	$display[1] = $conversion_name;

	$date = $value["waktu"];

	$date = date("Y-m-d H:i:s", strtotime('1 hours', strtotime($date)));

	$display[2] = $date;

	if (isset($_GET["type"])) {
		if ($_GET["type"] == "purchase") {

			$display[3] = $value["value"];

			$display[4] = $currency;
		}
	}

	echo implode(",", $display);

	echo "\n";

}

?>

