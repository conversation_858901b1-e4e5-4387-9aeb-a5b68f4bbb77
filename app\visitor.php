<?php

class visitor
{
	function add($data,$visitor_id = "")
	{
		global $app;
        $db2 = $app->db2;

        $data_visitor = array();

        if($visitor_id != "")
        {
           

            $db2->where("visitor_id",$visitor_id);
            $visitor = $db2->getone("visitor");
            if($visitor != NULL)
            {
                $data_visitor = unserialize($visitor["data"]);
            }else{
                 $data_insert["created"] = date("Y-m-d H:i:s");
            }
        }else{
            $data_insert["created"] = date("Y-m-d H:i:s");
        }
        $data_insert["created_unix"] = round(microtime(true) * 1000);

        $data_insert["visit"] = date("Y-m-d H:i:s");
         


        if (isset($data["ip"])) {
            $data_visitor["ip"] = $data["ip"];
        }elseif ($ip = get_client_ip()) {
            $data_visitor["ip"] = $ip;
        }

        if (isset($data["browser_agent"])) {
            $data_visitor["browser_agent"] = $data["browser_agent"];
        }elseif(isset($_SERVER['HTTP_USER_AGENT'])) {
            $data_visitor["browser_agent"] = $_SERVER['HTTP_USER_AGENT'];
        }


        

        ///////////////////////////// fb ads
        if (isset($data["fb"]["fbclid"])) {
            if($data["fb"]["fbclid"] != "undefined"){
                 $data_visitor["fb"]["fbclid"] = $data["fb"]["fbclid"];
            }
        }
        if (isset($data["fb"]["fbp"])) {
            if($data["fb"]["fbp"] != "undefined"){
                $data_visitor["fb"]["fbp"] = $data["fb"]["fbp"];
            }
        }
        if (isset($data["fb"]["fbc"])) {
            if($data["fb"]["fbc"] != "undefined"){
                $data_visitor["fb"]["fbc"] = $data["fb"]["fbc"];
            }
        }

        //////////////////////////// tiktok ads
        if (isset($data["tiktok"]["ttclid"])) {
            $data_visitor["tiktok"]["ttclid"] = $data["tiktok"]["ttclid"];
        }

        /////////////////////////// snack video ads
        if (isset($data["snack_video"]["click_id"])) {
            $data_visitor["snack_video"]["click_id"] =
                $data["snack_video"]["click_id"];
        }


        if (isset($data["mgid"]["click_id"])) {
            $data_visitor["mgid"]["click_id"] =
                $data["mgid"]["click_id"];
        }

        /////////////////////////////// google ads
        if (isset($data["google_ads"]["gclid"])) {
            $data_visitor["google_ads"]["gclid"] = $data["google_ads"]["gclid"];
        }
        if (isset($data["google_ads"]["wbraid"])) {
            $data_visitor["google_ads"]["wbraid"] =
                $data["google_ads"]["wbraid"];
        }
        if (isset($data["google_ads"]["gbraid"])) {
            $data_visitor["google_ads"]["gbraid"] =
                $data["google_ads"]["gbraid"];
        }

        /////////////////////////////////// google analytic
        if (isset($data["google_analytic"]["clientId"])) {
            $data_visitor["google_analytic"]["clientId"] =
                $data["google_analytic"]["clientId"];
        }

        if (isset($data["last_campaign"])) {
            $data_visitor["last_campaign"] = $data["last_campaign"];
        }

        if(isset($data_visitor))
        {
            if(is_array($data_visitor))
            {
                if (count($data_visitor) > 0) {
                    $data_insert["data"] = serialize($data_visitor);
                }

            }
        }
        
        
        if(isset($data["page_url"]))  
        {
        	$data_insert["page_url"] = $data['page_url'];
        }

        if(isset($data["site"]))  
        {
            $data_insert["site"] = $data['site'];
        }



      
        if(isset($data['connector']))
        {
            $data_insert["connector"] = json_encode($data['connector']);
        }


        $utm = array();
        if(isset($data["utm"]['utm_campaign']))
        {
            $utm["utm_campaign"] = $data["utm"]['utm_campaign'];
        }
        if(isset($data["utm"]['utm_source']))
        {
            $utm["utm_source"] = $data["utm"]['utm_source'];
        }
        if(isset($data["utm"]['utm_medium']))
        {
            $utm["utm_medium"] = $data["utm"]['utm_medium'];
        }
        if(isset($data["utm"]['utm_content']))
        {
            $utm["utm_content"] = $data["utm"]['utm_content'];
        }
        if(isset($data["utm"]['utm_term']))
        {
            $utm["utm_term"] = $data["utm"]['utm_term'];
        }

        if(count($utm) > 0)
        {
            $data_insert["utm"] = json_encode($utm);
            unset($data["utm"]);
        }

        if($visitor_id != "")
        {
        	$data_insert["visitor_id"] = $visitor_id;
    	}
      
        $db2->onDuplicate($data_insert);
        $visitor_id = $db2->insert("visitor",$data_insert); 


      

        return $visitor_id;
	}

    

    function create()
    {
        global $app;
        $db2 = $app->db2;

        if($visitor_id = $db2->insert("visitor", [
            "created" => date("Y-m-d H:i:s"),
        ]))
        {
        	return $visitor_id;	
        }
        else
        {
        	return false;
        }
        
    }

    function add_history($visitor_id, $data)
    {
        global $app;
        $db2 = $app->db2;

        $now = time();     
    	$session_id = ceil($now/300)*300;

        $data_visitor = array();

        if (isset($data["ip"])) {
            $data_visitor["ip"] = $data["ip"];
        }
        if (isset($data["browser_agent"])) {
            $data_visitor["browser_agent"] = $data["browser_agent"];
        }

        ///////////////////////////// fb ads
        if (isset($data["fb"]["fbclid"])) {
            $data_visitor["fb"]["fbclid"] = $data["fb"]["fbclid"];
        }
        if (isset($data["fb"]["fbp"])) {
            $data_visitor["fb"]["fbp"] = $data["fb"]["fbp"];
        }
        if (isset($data["fb"]["fbc"])) {
            $data_visitor["fb"]["fbc"] = $data["fb"]["fbc"];
        }

        //////////////////////////// tiktok ads
        if (isset($data["tiktok"]["ttclid"])) {
            $data_visitor["tiktok"]["ttclid"] = $data["tiktok"]["ttclid"];
        }

        /////////////////////////// snack video ads
        if (isset($data["snack_video"]["click_id"])) {
            $data_visitor["snack_video"]["click_id"] =
                $data["snack_video"]["click_id"];
        }

        /////////////////////////////// google ads
        if (isset($data["google_ads"]["gclid"])) {
            $data_visitor["google_ads"]["gclid"] = $data["google_ads"]["gclid"];
        }
        if (isset($data["google_ads"]["wbraid"])) {
            $data_visitor["google_ads"]["wbraid"] =
                $data["google_ads"]["wbraid"];
        }
        if (isset($data["google_ads"]["gbraid"])) {
            $data_visitor["google_ads"]["gbraid"] =
                $data["google_ads"]["gbraid"];
        }

        /////////////////////////////////// google analytic
        if (isset($data["google_analytic"]["clientId"])) {
            $data_visitor["google_analytic"]["clientId"] =
                $data["google_analytic"]["clientId"];
        }

        if (count($data_visitor) > 0) {
            $data_insert["data"] = serialize($data_visitor);
            $data_insert["visitor_id"] = $visitor_id;
            $data_insert["created"] = date("Y-m-d H:i:s");
            $data_insert["page_url"] = $url;
        }

        if(count($data_visitor) > 0)
        {
                $data_insert["data"] = serialize($data_visitor);
                $data_insert["visitor_id"] = $visitor_id;
                $data_insert["created"] = date("Y-m-d H:i:s");
                $data_insert["page_url"] = $url;
        }


        if(isset($data['connector']))
        {
            $data_insert["connector"] = json_encode($data['connector']);
        }


        $utm = array();
        if(isset($data['utm_campaign']))
        {
            $utm["utm_campaign"] = $data['utm_campaign'];
        }
        if(isset($data['utm_source']))
        {
            $utm["utm_source"] = $data['utm_source'];
        }
        if(isset($data['utm_medium']))
        {
            $utm["utm_medium"] = $data['utm_medium'];
        }
        if(isset($data['utm_content']))
        {
            $utm["utm_content"] = $data['utm_content'];
        }
        if(isset($data['utm_term']))
        {
            $utm["utm_term"] = $data['utm_term'];
        }

        if(count($utm) > 0)
        {
            $data_insert["campaign"] = json_encode($utm);
        }

        $data_insert["visitor_id"] = $visitor_id;
        $data_insert["session_id"] = $session_id;
        $db2->onDuplicate($data_insert);
        $db2->insert("visitor_history",$data_insert); 

        return $session_id;
    }
}
