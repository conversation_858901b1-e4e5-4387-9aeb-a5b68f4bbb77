{"interfaces": {"google.ads.googleads.v18.services.SmartCampaignSuggestService": {"retry_codes": {"no_retry_codes": [], "retry_policy_1_codes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}, "retry_params": {"no_retry_params": {"initial_retry_delay_millis": 0, "retry_delay_multiplier": 0.0, "max_retry_delay_millis": 0, "initial_rpc_timeout_millis": 0, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 0, "total_timeout_millis": 0}, "retry_policy_1_params": {"initial_retry_delay_millis": 5000, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 14400000, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 14400000, "total_timeout_millis": 14400000}}, "methods": {"SuggestKeywordThemes": {"timeout_millis": 14400000, "retry_codes_name": "retry_policy_1_codes", "retry_params_name": "retry_policy_1_params"}, "SuggestSmartCampaignAd": {"timeout_millis": 14400000, "retry_codes_name": "retry_policy_1_codes", "retry_params_name": "retry_policy_1_params"}, "SuggestSmartCampaignBudgetOptions": {"timeout_millis": 14400000, "retry_codes_name": "retry_policy_1_codes", "retry_params_name": "retry_policy_1_params"}}}}}