<?php

function add_project_data($param) {

	$rules = [
		"project_id" => "required",
		"project_key"  => "required",
		"token"  => "required",
		"data"       => "required",
	];
	validate_param($rules, $param);
	extract($param);
	assign_child_db($project_id);

	$data["project_key"] = $param["project_key"];
	$data["token"] = $param["token"];
	$p   = new project();
	$res = $p->AddProjectData($project_id, $data);

	if ($res["code"] == 1) {
		$msg["code"] = 1;
		$msg["msg"]  = "sukses";
	} else {
		$msg["code"] = 0;
		$msg["msg"]  = $res["msg"];
	}

	return $msg;
}

function add_project($param) {
    global $app;
	$rules = [
		"project_id"   => "required",
		"project_key"  => "required",
		"project_name" => "required",
	];
	validate_param($rules, $param);
	extract($param);
	$p   = new project();
	$res = $p->add_project($project_id, $project_key, $project_name);
	//$res = $p->create_table($project_id);
	if ($res["code"] == 1) {
//		$webhook = 'http://'. $server.'/api.html?act=bill_get&project_id='. $project_id;
//		$bt      = new bt_api();
//		$bt->add_cron('bill_'.$project_id, 1440, $webhook);
        $db = $app->db;
        $po['code']= 'bill_'.$project_id;
        $po['url']= 'http://'. $server.'/api.html?act=bill_get&project_id='. $project_id;
        $db->where("code", $po['code']);
        $db->getone("cron");
        if($db->count==0){
            $db->insert("cron", $po);
        }
		$msg["code"] = 1;
		$msg["msg"]  = "sukses";
	} else {
		$msg["code"] = 0;
		$msg["msg"]  = "error";
		$msg["data"] = $res["data"];
	}
	return $msg;
}

function create_table($param) {
	$rules = [
		"project_id" => "required",
	];
	validate_param($rules, $param);
	extract($param);

	$p   = new project();
	$res = $p->create_table($project_id);
	if ($res["code"] == 1) {
		$msg["code"] = 1;
		$msg["msg"]  = "sukses";
	} else {
		$msg["code"] = 0;
		$msg["msg"]  = "error";
		$msg["data"] = $res["data"];
	}
	return $msg;
}

function set_setting($param) {
	$rules = [
		"data"       => "required",
		"project_id" => "required",
	];
	validate_param($rules, $param);
	extract($param);
	assign_child_db($project_id);

	$d   = new project();
	$msg = $d->set_project_setting($data);

	return $msg;
}

function get_setting($param) {

	$rules = [
		"project_id" => "required",
	];
	validate_param($rules, $param);
	extract($param);

	assign_child_db($project_id);

	$d                 = new project();
	$msg               = $d->get_project_setting();
	$msg["ip_arecord"] = $server_ip;

	if(isset($msg["google_connector"])){
		$msg["data"]["google_connector"] = $msg["google_connector"];
		unset($msg["google_connector"]);
	}
	if(isset($msg["unknown_connector"])){
		$msg["data"]["unknown_connector"] = $msg["unknown_connector"];
		unset($msg["unknown_connector"]);
	}
	if(isset($msg["organic_connector"])){
		$msg["data"]["organic_connector"] = $msg["organic_connector"];
		unset($msg["organic_connector"]);
	}
	if(isset($msg["shortlink_connector"])){
		$msg["data"]["shortlink_connector"] = $msg["shortlink_connector"];
		unset($msg["shortlink_connector"]);
	}
	return $msg;
}

function set_google_link($param)
{
	$rules = [
		"project_id" => "required",
		"data"  => "required",
		"subdomain"  => "required"
	];

	validate_param($rules, $param);
	extract($param);

	assign_child_db($project_id);

	$m = new meta();
	if(count($data) > 0){
		foreach ($data as $key => $value) {
			if($key == "google_cta_name"
			or $key == "google_lead_name"
			or $key == "google_mql_name"
			or $key == "google_prospek_name"
			or $key == "google_purchase_name"
			){
				$m->set_meta($key,$value);
			}
		}
	}

	return get_google_link($param);
	//return true;
}

function get_google_link($param) {
	$rules = [
		"project_id" => "required",
		"subdomain"  => "required",
	];
	

	validate_param($rules, $param);
	extract($param);

	assign_child_db($project_id);

	$m = new meta();
	$cta_name = $m->get_meta("google_cta_name");
	if($cta_name["code"] == 0){$cta_name = "gass-cta";}else{$cta_name = $cta_name["result"]["data"];}

	$lead_name = $m->get_meta("google_lead_name");
	if($lead_name["code"] == 0){$lead_name = "gass-lead";}else{$lead_name = $lead_name["result"]["data"];}

	$mql_name = $m->get_meta("google_mql_name");
	if($mql_name["code"] == 0){$mql_name = "gass-mql";}else{$mql_name = $mql_name["result"]["data"];}

	$prospek_name = $m->get_meta("google_prospek_name");
	if($prospek_name["code"] == 0){$prospek_name = "gass-prospek";}else{$prospek_name = $prospek_name["result"]["data"];}

	$purchase_name = $m->get_meta("google_purchase_name");
	if($purchase_name["code"] == 0){$purchase_name = "gass-purchase";}else{$purchase_name = $purchase_name["result"]["data"];}

	$tmp = subdomain::get_subdomain($subdomain);

	if (isset($tmp["result"]["data"][0]["name"])) {
		$subdomain        = $tmp["result"]["data"][0]["name"];
		$link["cta"]      = "https://" . $subdomain . "/g-report.html?type=cta&pk=" . $project_key;
		$link["lead"]     = "https://" . $subdomain . "/g-report.html?type=lead&pk=" . $project_key;
		$link["mql"]      = "https://" . $subdomain . "/g-report.html?type=mql&pk=" . $project_key;
		$link["prospek"]  = "https://" . $subdomain . "/g-report.html?type=prospek&pk=" . $project_key;
		$link["purchase"] = "https://" . $subdomain . "/g-report.html?type=purchase&pk=" . $project_key;

		$link["google_cta_name"] = $cta_name;
		$link["google_lead_name"] = $lead_name;
		$link["google_mql_name"] = $mql_name;
		$link["google_prospek_name"] = $prospek_name;
		$link["google_purchase_name"] = $purchase_name;

		$msg["code"] = 1;
		$msg["data"] = $link;
		$msg["msg"]  = "sukses";
	} else {
		$msg["code"] = 0;
		$msg["msg"]  = "subdomain not found";
	}
	return $msg;
}

function grab_data_stat($param) {
	$rules = [
		"project_id" => "required",
	];
	validate_param($rules, $param);
    $pos["act"] = 'bill_get_project_user';
    $pos["project_id"] = $param['project_id'];
    $user = json_decode(post_x_contents($pos, 'http://10.104.0.27/api.html'), true);
    if(isset($user['setting_bill'])){
        $waktu = $user['data']['project_stat_grab_last'];
        $inputDate = new DateTime($waktu);
        $yesterday = new DateTime('yesterday');
        if($inputDate->format('Y-m-d') < $yesterday->format('Y-m-d')){
            $start = $inputDate->format('Y-m-d');
            $inputDate->modify('+1 day');
            $end = $inputDate->format('Y-m-d');
            $res = get_usage_range_new($param['project_id'], $start, $end, $user);
            
            $res["act"] = 'internal_update_grab_stat';
            $res["project_id"] = $param['project_id'];
            $res["start"] = $start;
            $res["end"] = $end;
            $rex = post_x_contents($res, 'http://10.104.0.27/api.html');
            return $rex;
        }else if($inputDate->format('Y-m-d') == $yesterday->format('Y-m-d')){
            return '';
        }

    }
	return '';
}

function get_usage_range_new($project_id, $start, $end, $user){
    global $app;
    assign_child_db($project_id);
    $db = $app->db2;
    $last_bill =  $start;  
    $yesterday =  $end;
    $db->where("DATE(created) BETWEEN ? AND ?", array($last_bill, $yesterday));  
    $site = $db->get("visitor", NULL, "site, DATE(created) as waktu");
    $sites = []; 
    foreach ($site as $item) {
        $sit = $item["site"];
        $sit = str_replace("www.","", $sit);
        if($sit!='' && $sit != 'b-cdn.net'){   ///// disable ctwa
            $waktu = $item["waktu"];
            if($sit==''){
                $sit = 'ctwa';
            }
            if (!isset($sites[$sit]['date'][$waktu])) {
                $sites[$sit]['date'][$waktu] = 0;
                $sites[$sit]['total']++;
            }
            $sites[$sit]['date'][$waktu]++;        
        }      
    }
    $total_day_site = 0;$total_day_ctwa = 0;
    foreach ($sites as $k => $v) {
        if($k != 'ctwa'){
            $total_day_site = $v['total'] + $total_day_site;
        }else{
            $total_day_ctwa = $v['total'] + $total_day_ctwa;
        }            
    }
    $total_bill_site = $user['setting_bill']['site'] * $total_day_site;
    $total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_day_ctwa;
    // hitung bill cs
    $db->where("DATE(tanggal) BETWEEN ? AND ?", array($last_bill, $yesterday));  
    $cs = $db->get("cs_log", NULL, "phone, DATE(tanggal) as waktu, impression, contact, mql, prospek, purchase, value");
    $nocs = []; 
    foreach ($cs as $item) {
        $c = $item["phone"];
        $waktu = $item["waktu"];
        if (!isset($nocs[$c]['date'][$waktu])) {
            $nocs[$c]['date'][$waktu] = 0;
            $nocs[$c]['total']++;
        }
        $nocs[$c]['date'][$waktu]++;        
    }
    $total_day_cs = 0;
    foreach ($nocs as $k => $v) {
        $total_day_cs = $v['total'] + $total_day_cs;
    }
    $total_bill_cs = $user['setting_bill']['cs'] * $total_day_cs;
    // hitung cta
    $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'unik_cta'", array($last_bill, $yesterday));  
    $db->orderBy("date","Asc");
    $cta = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
    $tcta = []; $total_click_cta = 0;$tcta_date = [];
    $last_cta = ''; $last_cta_value = 0;
    foreach ($cta as $item) {
        $c = $item["report_id"];
        $waktu = $item["waktu"];
        $last_cta = $waktu;
        if (!isset($tcta[$c]['date'][$waktu])) {
            $tcta[$c]['date'][$waktu] = 0;
            $tcta[$c]['total']++;
        }
        if (!isset($tcta_date[$waktu])) {
            $tcta_date[$waktu] = 0;
        }
        $tcta_date[$waktu] += $item["report_value"]; 
        $tcta[$c]['date'][$waktu] += $item["report_value"];    
        $total_click_cta += $item["report_value"];  
        $last_cta_value = $tcta[$c]['date'][$waktu];
    }
    $total_day_cta = 0;
    foreach ($tcta as $k => $v) {
        $total_day_cta = $v['total'] + $total_day_cta;
    }

    // hitung lp_view
    $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'lp_view'", array($last_bill, $yesterday));  
    $lp_view = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
    $tlp_view = []; $total_lp_view = 0;$tlp_view_date = [];
    foreach ($lp_view as $item) {
        $c = $item["report_id"];
        $waktu = $item["waktu"];
        if (!isset($tlp_view[$c]['date'][$waktu])) {
            $tlp_view[$c]['date'][$waktu] = 0;
            $tlp_view[$c]['total']++;
        }
        if (!isset($tlp_view_date[$waktu])) {
            $tlp_view_date[$waktu] = 0;
        }
        $tlp_view_date[$waktu] += $item["report_value"];  
        $tlp_view[$c]['date'][$waktu] += $item["report_value"];  
        $total_lp_view += $item["report_value"];  
    }
    $total_day_lp_view = 0;
    foreach ($tlp_view as $k => $v) {
        $total_day_lp_view= $v['total'] + $total_day_lp_view;
    }

    $total_bill_cta = $user['setting_bill']['lead'] * $total_click_cta;
    $total['total_bill_site'] = $total_bill_site;
    $total['total_bill_ctwa'] = $total_bill_ctwa;
    $total['total_bill_cs'] = $total_bill_cs;
    $total['total_bill_cta'] = $total_bill_cta;
    $total['total_day_site'] = $total_day_site;
    $total['total_day_ctwa'] = $total_day_ctwa;
    $total['total_day_cs'] = $total_day_cs;
    $total['total_day_cta'] = $total_day_cta;
    $total['total_click_cta'] = $total_click_cta;
    $total['total'] = $total_bill_ctwa + $total_bill_site + $total_bill_cs + $total_bill_cta;
    $alert_nominal = $user['data']['billing_alert'] - 5000;
    $create_bill = false;
    // cek minimal billing
    $msg['code']=1;        
    //$msg['data']['user'] = $user;

    $msg['data']['tcta_date'] = $tcta_date;
    $msg['data']['tlp_view_date'] = $tlp_view_date;

    $msg['data']['total'] = $total;
    $msg['last_cta'] = $last_cta;
    $msg['last_cta_value'] = $last_cta_value;

    return $msg;
    
}


function clen_chat_history($param) {

	$rules = [
		"project_id" => "required",
		"day"  => "required",
	];
	validate_param($rules, $param);
	extract($param);
	$p   = new ChatHistory($project_id);
	$res = $p->cleanupOldFiles($day);
	return $res;
}

function set_chat_history($param) {
	$rules = [
		"project_id" => "required",
		"save_history"  => "required",

	];
	validate_param($rules, $param);
	extract($param);
	file_put_contents('log/history/'.$project_id.'/setting.text', $save_history);
	return true;
}