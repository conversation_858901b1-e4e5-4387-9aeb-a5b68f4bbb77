<?php
use sqonk\phext\core\arrays;

class sync_db
{

    public function run_collation($source_coll, $dest, $project_id)
    {

        $dest_coll = $this->get_collation($dest, $project_id);

        $sourceData = []; // Initialize an empty array to store the new data
        foreach ($source_coll as $item) {
            $tableName = $item["TABLE_NAME"];
            $collation = $item["TABLE_COLLATION"];
            $sourceData[$tableName] = $collation;
        }

        $destData = []; // Initialize an empty array to store the new data
        foreach ($dest_coll as $item) {
            $tableName = $item["TABLE_NAME"];
            $collation = $item["TABLE_COLLATION"];
            $destData[$tableName] = $collation;
        }

        $x = array_diff_assoc($sourceData, $destData);

        if ($x != null) {
            foreach ($x as $key => $value) {

                $q = "ALTER TABLE $key CONVERT TO CHARACTER SET $value ";
                echo $q;
                $dest->rawQuery($q);
                echo "change collation table $key to $value \n";
            }
        }

    }
    public function run_engine($source_eng, $dest, $project_id)
    {

        $dest_eng = $this->get_engine($dest, $project_id);

        $sourceData = []; // Initialize an empty array to store the new data
        foreach ($source_eng as $item) {
            $tableName = $item["TABLE_NAME"];
            $engine = $item["ENGINE"];
            $sourceData[$tableName] = $engine;
        }

        $destData = []; // Initialize an empty array to store the new data
        foreach ($dest_eng as $item) {
            $tableName = $item["TABLE_NAME"];
            $engine = $item["ENGINE"];
            $destData[$tableName] = $engine;
        }
        $x = array_diff_assoc($sourceData, $destData);
        if ($x != null) {
            foreach ($x as $key => $value) {
                $q = "ALTER TABLE $key ENGINE = $value;";
                $dest->rawQuery($q);
                echo "change engine table $key to $value \n";
            }
        }

    }

    public function run_index($source_index, $dest, $project_id)
    {
        $dest_index = $this->get_index($dest, $project_id);
        $dest_index_data = array();

       
        $new = array_diff_key($source_index, $dest_index);
        $dropped = array_diff_key($dest_index, $source_index);
        $existing = array_diff_key($source_index, $new);

       
        

        $dest_index = $this->get_index($dest, $project_id);
        $dest_index_data = array();
        /////////////// add index /////////////////
        foreach ($source_index as $table => $indexs) {
            foreach ($indexs as $key => $index) {

                if (!isset($dest_index[$table][$key])) {
                    $index = unserialize($index);

                    $unique = "";
                    if ($index["Non_unique"] == 0) {
                        $unique = "UNIQUE";
                    }
                    if ($index["Index_type"] == "FULLTEXT") {
                        $unique = "FULLTEXT";
                    }
                    $indexName = $index["Key_name"];
                    $columnName = $index["Column_name"];
                    $sql = "ALTER TABLE $table ADD $unique INDEX $indexName ($columnName)";
                  
                    $dest->rawQuery($sql);
                    echo "add index $unique table $table key $indexName on coloumn $columnName  \n";
                }

            }
        }
        /////////////// remove dif index /////////////////
/*
         /////////////// remove dif index /////////////////
         foreach ($dest_index as $table => $indexs) {
            foreach ($indexs as $key => $index) {

                if (!isset($source_index[$table][$key])) {

                    $sql = "DROP INDEX $key ON $table";
                    
                   // $dest->rawQuery($sql);
                    
                   
                    
                    //echo "drop index table $table key $key \n";
                } else {
                    if ($source_index[$table][$key] != $index) {
                        $sql = "DROP INDEX $key ON $table";
                       $dest->rawQuery($sql);
                       
                       // echo "drop index table $table key $key \n";
                    }
                }
            }
        }
            */

    }

    public function run($source_tables, $dest, $project_id)
    {
        $dryRun = false;
        $dest_tables = $this->describe($dest, false);

      

        $new = array_diff_key($source_tables, $dest_tables);
        $dropped = array_diff_key($dest_tables, $source_tables);
        $existing = array_diff_key($source_tables, $new);

        $newStatements = [];
        $dropStatements = [];
        $alterStatements = [];

        println("\n===== TABLES TO REMOVE");
        if (count($dropped) > 0) {
            foreach ($dropped as $tblName => $cols) {
                $drop = "DROP TABLE `$tblName`";
                $dropStatements[] = $drop;
                if (!$dryRun) {
                    println("dropping $tblName");
                    $dest->rawQuery($drop);
                } else {
                    println("\n$drop");
                }
            }
        } else {
            println('There are no tables to drop');
        }

        println("\n===== EXISTING TABLES");
        foreach ($existing as $tblName => $master_cols) {
            
           
            $slave_cols = $dest_tables[$tblName];
            // compare columns
            $newCols = array_diff_key($master_cols, $slave_cols);
            $droppedCols = array_diff_key($slave_cols, $master_cols);
            $existingM = array_diff($master_cols, $newCols);
            $existingS = array_diff($slave_cols, $droppedCols);
            $alter = [];

           

            foreach ($newCols as $cmd) {
                $alter[] = "ADD COLUMN $cmd";
            }

            $previousDesc = [];
            foreach ($existingM as $fn => $descrption) {
                $slaveDescription = $existingS[$fn] ?? '';
                if ($descrption != $slaveDescription) {
                    $m = "MODIFY COLUMN $descrption";
                    $alter[] = $m;
                    $previousDesc[$m] = $slaveDescription;
                }
            }

            foreach ($droppedCols as $colName => $cmd) {
                $alter[] = "DROP COLUMN $colName";
            }

            if (count($alter) > 0) {
                if ($dryRun) {
                    $alterT = "ALTER TABLE `$tblName`";
                    println($alterT);
                    $alterStatements[] = $alterT;
                    foreach ($alter as $cmd) {
                        $alterStatements[] = $cmd;
                        println(trim($cmd));
                        $d = $previousDesc[$cmd] ?? '';
                        if ($d) {
                            println("\twas: [$d]");
                        }
                    }
                    println();

                    println('-------------');
                } else {
                    println("adjusting $tblName\n");
                    foreach ($alter as $modify) {
                        $cmd = "ALTER TABLE `$tblName` $modify";
                        $alterStatements[] = $cmd;
                        try {
                            $lastPart = substr($cmd, -strlen("timestamp"));
                            if ($lastPart === "timestamp") {
                                $cmd .= " NULL";
                                $dest->rawQuery($cmd);
                            }
                            else{
                            $dest->rawQuery($cmd);
                            }
                            echo "<br/>".$cmd."<br/>";
                        } catch (Exception $error) {
                            println("Statement failed: [$cmd]", $error->getMessage());
                        }
                    }
                }
            }
        }

        if (count($alterStatements) == 0) {
            println('There are no changes between existing tables.');
        }

        println();
    }

    public function describe($db, bool $ignoreColumnWidths)
    {
        $tables = [];

        foreach ($db->rawQuery("SHOW TABLES") as $row) {
            $tableName = arrays::first($row) ?? '';
            if (!$tableName) {
                continue;
            }

            if ($r = $db->rawQuery("DESCRIBE `$tableName`")) {
                $table = [];

                foreach ($r as $info) {

                    $info['Null'] = $info['Null'] == 'YES' ? '' : 'NOT NULL';
                    if (!empty($info['Default'])) {
                        $info['Default'] = 'DEFAULT ' . $info['Default'];
                    }

                    $type = $info['Type'] ?? '';
                    $widthFlag = strpos($type, '(');
                    if ($ignoreColumnWidths && $type && $widthFlag !== false) {
                        $info['Type'] = preg_replace("/(\(.+?\))/", "", $type);
                    }

                    $name = $info['Field'];
                    $info['FieldQ'] = "`$name`";

                    if ($info["Default"] == "0") {
                        $info["Default"] = "Default 0";
                    }

                    $table[$name] = $this->implode_only(' ', $info, 'FieldQ', 'Type', 'Null', 'Default', 'Extra');
                    //$table[$name] = implode(' ', $info);

                }
                $tables[$tableName] = $table;

            }
        }

        return $tables;
    }

    public function implode_only(string $delimiter, array $array, mixed ...$keys): string
    {
        return implode($delimiter, array_filter(self::values($array, ...$keys), function ($v) {
            return !empty($v);
        }));
    }
    public static function values(array $array, mixed ...$keys): array
    {
        $item_vals = [];
        foreach ($keys as $key) {
            if (array_key_exists($key, $array)) {
                $item_vals[] = $array[$key];
            }

        }
        return $item_vals;
    }

    public function get_table($db, $project_id,$old=false)
    {
        
        $q = "SELECT table_name  FROM information_schema.tables where TABLE_SCHEMA = 'client_" . $project_id . "'";

        if($old){
            $q = "SELECT table_name  FROM information_schema.tables where TABLE_SCHEMA = 'client_" . $project_id . "_old'";
        }

        $res = $db->rawQuery($q);

        $tables = array();
        $tables_create = array();
        $tables_alter = $this->describe($db, false);
        $tables_index = $this->get_index($db, $project_id);
       
        foreach ($res as $r) {

            
            $query = "SHOW CREATE TABLE `" . $r["table_name"] . "`";
           

            try {
                $res_create = $db->rawQuery($query)[0]["Create Table"];
               
                $res_create = preg_replace('/AUTO_INCREMENT=\d+/', '', $res_create);
                
                array_push($tables_create, $res_create);
                array_push($tables, $r["table_name"]);
            } catch (\Throwable $th) {
                throw $th;
            }
        }

        $res = array();
        $res_engine =
        $res["create"] = $tables_create;
        $res["engine"] = $this->get_engine($db, $project_id);
        $res["collation"] = $this->get_collation($db, $project_id);
        $res["alter"] = $tables_alter;
        $res["index"] = $tables_index;

        $compress_data = serialize($res);
        $compress_data = rtrim(strtr(base64_encode(gzdeflate($compress_data, 9)), '+/', '-_'), '=');

        return $res;
    }

    public function get_engine($db, $project_id)
    {
        $QUERY = "SELECT TABLE_NAME, ENGINE FROM information_schema.TABLES WHERE TABLE_TYPE = 'BASE TABLE' and TABLE_SCHEMA = 'client_" . $project_id . "'";
        $res_engine = $db->rawQuery($QUERY);

        return $res_engine;
    }

    public function get_collation($db, $project_id)
    {
        $QUERY = "SELECT TABLE_NAME, TABLE_COLLATION FROM information_schema.TABLES WHERE TABLE_TYPE = 'BASE TABLE' and TABLE_SCHEMA = 'client_" . $project_id . "'";
        $res_collaction = $db->rawQuery($QUERY);

        return $res_collaction;
    }

    public function get_index($db, $project_id)
    {
        $result_index = array();
        $q = "SELECT table_name  FROM information_schema.tables where TABLE_SCHEMA = 'client_" . $project_id . "'";
        $res = $db->rawQuery($q);
        foreach ($res as $r) {
            try {
                $query = "SHOW INDEX FROM `" . $r["table_name"] . "`";
                $res_index = $db->rawQuery($query);

                foreach ($res_index as $index) {

                    $key_name = $index["Key_name"];
                    unset($index["Cardinality"]);
                    $tmp = serialize($index);
                    $result_index[$r["table_name"]][$key_name] = $tmp;
                }

            } catch (\Throwable $th) {
                //throw $th;
            }

        }

        return $result_index;
    }

}
