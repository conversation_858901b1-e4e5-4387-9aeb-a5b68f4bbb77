<?php
class order{
//	status = 0  = new order
//	status = 1  = new konfirm pembayaran user	
//	status = 2  = new konfirmasi pembayaran diterima / harus dikirim
//	status = 3  = new sudah dikirim / input resi 
//	status = 4  = new delivered
	public function add_order($user_id, $total, $diskon, $unik,$vid = 0){
		global $keya, $c_time, $app;
			//id	date	pelanggan_id	voucher	expedisi	total_ongkir	total_diskon	total_bayar	status
		//$order_id = date("YmdHi").rand(10000,99999);
		$data = null;  
		//$data["id"] = $order_id;
		$data["user_id"] = $app->db->func("UNHEX(?)", array($user_id));
		$data["vid"] = $vid;
		$data["angka_unik"] = $unik;
		$data["total"] = $total;
		$data["diskon"] = $diskon;
		$order_id = $app->db->insert("x_order2", $data);
		if($order_id){
			$res["code"] = 1;
			$res["id"] = $order_id;
			$res["msg"] = 'Berhasil menambah order';
		}else{
			$res["code"] = 0;
			$res["msg"] = 'Gagal menambah order';
		}
		return $res;
	} 
	
	public function add_detail($order_id, $item_id, $qty){
		global $keya, $c_time, $app;		
		//id	order_id	produk_id	nama	image	hpp	harga	berat	qty
		$app->db->where('item_id = ?', array($item_id));
		$sc = $app->db->getone("x_item");		
		if($app->db->count>0){
			$data = null;	
			$data["harga"] = $sc['harga'];
			$data["order_id"] = $order_id;
			$data["item_id"] = $item_id;
			$data["keterangan"] = $sc['nama'].' '.$sc['keterangan'];
			$data["qty"] = $qty;
			$app->db->insert("x_order_detail", $data);
			$res["code"] = 1;
			$res["msg"] = 'Berhasil menambah detail order';
		}else{
			$res["code"] = 0;
			$res["msg"] = 'Produk tidak ditemukan';
		}
		return $res;
	} 
	  
	public function generate_license($user_id, $order_id, $expired)
	{
		global $app;
		$db = $app->db;
		$id = $db->insert("x_service", array("license" => ""));
		
		$code    = $id . "F";
		$codelen = 16; // change as needed
		for ($i = strlen($code); $i < $codelen; $i++) {
			$code .= dechex(rand(0, 15));
		}
		$data = null;
		$data["license"]     = strtoupper($code);
		$data["uid"] = $user_id;	
		$data["order_id"] = $order_id;
        $data['expired']  = $expired;
		$db->where("id", $id);
		if ($db->update("x_service", $data)) {
			$res["code"] = 1;
			$res["key"] = strtoupper(implode("-", str_split($code, 4)));
		}else{
			$res["code"] = 0;
			$res["msg"] = "error";
		}
		return $res;
	}
    
	public function extend_license($user_id, $service_id, $expired)
	{
        global $keya, $c_time, $app;
        $db = $app->db;
        $db->where("id = ?", array($service_id));
		$license = $db->getone("x_service");
        if($db->count > 0){
            $data['expired']  = date('Y-m-d', strtotime($license['expired'].' +1 month'));
            $db->where("id", $service_id);
            if ($db->update("x_service", $data)) {
                $res["code"] = 1;
                $res["msg"] = "success";
            }else{
                $res["code"] = 0;
                $res["msg"] = "error";
            }
        }else{
            $res["code"] = 0;
            $res["msg"] = "error";
        }
        return $res;
    }
    
	public function konfirmasiP($order_id){
		global $keya, $c_time, $app;
        $db = $app->db;
		$wa = new wa();
		$db->join("x_user u", "o.user_id=u.user_id", "LEFT");
		$db->where("o.status = ? and o.id = ?", array(0, $order_id));
		$order = $db->getone("x_order2 o", "u.phone, u.email, u.nama, u.aff_id, o.*");
		if ($db->count > 0) {
			$user_id =  md5($order['phone']);
			if($order["voucher"] !=''){
				$vocer = new voucher();
				$vocer->use_voucher($order["voucher"]);
			}
			$harga = $order["harga"] - $order["diskon"];
			if($order["source"] != ''){
				$url = "https://".$order["source"]."/panel/track?id=".$order["vid"]."&act=Purchase&value=".$harga;
			}else{
				$url = $app->fullpath."panel/track?id=".$order["vid"]."&act=Purchase&value=".$harga;
			}
			$track = file_get_contents($url);
			$restrak = 0;
			if (strpos($track, 'fbtrace_id') !== false) {
				$restrak = 1;
			}
			$db->where("id = ?", array($order_id));
			$db->update("x_order2",array("status" => 2, "user_id" => $db->func("UNHEX(?)", array($user_id)), 'tracking_purchase' => $restrak));	
			
            if($order['produk_id']==4){
                $u = new user();
                $resu = $u->add($order['email'], $order['nama'], $order['phone'], $ref='adsfb-v2');
                $ls = $this->generate_license($order["vid"], $order_id, date('Y-m-d', strtotime('+1 month')));  
                if($ls['code'] ==1){
                    $message =  'Trimakasih sudah membeli Convertion plugin. \n \nLink Download : '.$app->config->soft_download.' \n';
                    $message .= 'license : '.$ls['key'].'\n \n';
                    if($order['harga'] != 0){
                        $inout = new inout();
                        $inout->add(date('Y-m-d H:i:s'), 'Pembelian OrderID '.$order_id, $harga, 1, 1);
                    }
                    if($resu['code']==1){
                        $pass = new password();
                        $xxx = $pass->generatePassword(10);
                        $tmp = $keya.";" .$order['phone'].";".$xxx;
                        $paxx = md5($tmp);
                        $dx['pass'] = $db->func("UNHEX(?)", array($paxx));
                        $dx['verified_phone'] = 1;
                        $dx['aff_id'] = $order['aff_id'];
                        $db->where('user_id = UNHEX(?)', array($user_id));
                        if($db->update("x_user", $dx)){
                            $message .=  "Login panel.\n";
                            $message .= 'https://convertion.id/panel/login.html \nUser : '.$order['phone'].' \nPass : '.$xxx.'\n\n';								
                        }
                    }else{
                        $dx['aff_id'] = $order['aff_id'];
                        $db->where('user_id = UNHEX(?)', array($user_id));
                        $db->update("x_user", $dx);
                    }   
                    $message .= "( Ini merupakan pesan otomatis )";
                    if($message!='' && $order['phone'] != ''){
                        //echo $order['phone'];
                        //echo $message;
                        $ret = $wa->send('text', $app->config->phone_whatsapp, $order['phone'],$message);
                        //print_r($ret);
                    }
                }
            }else if($order['produk_id']==5){
                $ls = $this->extend_license($order["vid"], $order['service_id'], 1);
                if($ls['code'] ==1){
                    $message =  'Trimakasih sudah membeli extend license convertion plugin. \n \n';
                    $inout = new inout();
                    $inout->add(date('Y-m-d H:i:s'), 'Pembelian Extend license OrderID '.$order_id, $harga, 1, 1);
                    if($resu['code']==1){
                    }
                    $message .= "( Ini merupakan pesan otomatis )";
                    if($message!='')$ret = $wa->send('text', $app->config->phone_whatsapp, $order['phone'],$message);
                }
            }
			if($order['harga'] != 0){
                $this->komisi_aff($order_id);
            }
			$msg["code"] = 1;
			$msg["msg"] = "success approve";				
		}else{
			$msg["code"] = 0;
			$msg["msg"] = "error!! sudah di konfirmasi.";
		}
		return $msg;
	}
	
	function komisi_aff($order_id){
		global $keya, $c_time, $app;
        $db = $app->db;
		$u = new user();
		$wa = new wa();
		$db->join("x_user u", "o.user_id=u.user_id", "LEFT");
		$db->where("o.id = ?", array($order_id));
		$order = $db->getone("x_order2 o", "u.phone, u.email, u.nama, u.aff_id, o.*");
		if ($db->count > 0) {
			$komisi = $app->config->komisi_aff;
			$aff_id = $order['aff_id'];
			if($aff_id !==''){
				$db->where("aff_code = ?", array($aff_id));
				$aff = $db->getone("x_user");
				if ($db->count > 0) {
					$keterangan = "Komisi Penjualan #".$order_id."  Rp. " . number_format($komisi);
					$saldo = new saldo();
					$sal = $saldo->add_pending(bin2hex($aff["user_id"]), $keterangan, $komisi, 7);
					//$sal['data'] = $db->getLastQuery(); 
					$message =  "Anda mendapatakan\n";
					$message .= $keterangan . "\n\n";
					$message .= "( Ini merupakan pesan otomatis ) \n\n";	
					if($aff['phone'] != ''){
						$wa->send('text', $app->config->phone_whatsapp, $aff['phone'],$message);										
					}
					return $sal;
				}				
			}			
		}
	}
	
	function komisi_aff_tingkat($order_id){
		global $keya, $c_time, $app;
        $db = $app->db;
		$u = new user();
		$wa = new wa();
		$db->join("a_client u", "o.vid=u.vid", "LEFT");
		$db->where("o.id = ?", array($order_id));
		$order = $db->getone("x_order2 o", "u.phone, u.email, u.nama, u.vid, u.aff_id, o.*");
		if ($db->count > 0) {
			$aff_id = $order['aff_id'];
			///////////////////// cek komisi affiliate
			$jumlah = $order["harga"];
			$komisi["tingkat_1"] = $jumlah / 100 * $app->config->tingkat_1;
			$komisi["tingkat_2"] = $jumlah / 100 * $app->config->tingkat_2;
			$komisi["tingkat_3"] = $jumlah / 100 * $app->config->tingkat_3;
			$komisi["tingkat_4"] = $jumlah / 100 * $app->config->tingkat_4;
			$komisi["tingkat_5"] = $jumlah / 100 * $app->config->tingkat_5;
			$tingkat = $u->get_aff_tingkat($aff_id);
			foreach ($tingkat as $key => $value) {
				$db->where("uid",$value);
				$aff = $db->getone("x_user");
				if($aff == NULL){
					continue;
				}				
				$temp = "tingkat_".$key;
				if($key > 1){
					$keterangan = "Komisi Penjualan #".$order['id']." tingkat $key.  Rp. " . number_format($komisi[$temp]);
				}else{
					$keterangan = "Komisi Penjualan #".$order['id']." tingkat $key. ".$user["nama"]."  Rp. " . number_format($komisi[$temp]);
				}
				$nominal = $komisi[$temp];	
				///////////////////// notif
				$message =  "Anda mendapatakan\n";
				$message .= $keterangan . "\n\n";
				$message .= "( Ini merupakan pesan otomatis ) \n\n";	
				if($aff['phone'] != ''){
					$ret = $wa->send('text', $app->config->phone_whatsapp, $aff['phone'],$message);
				}	
				$saldo = new saldo();
				$saldo->add_pending(bin2hex($aff["user_id"]), $keterangan, $nominal, 7);
			}
		}
	}
}