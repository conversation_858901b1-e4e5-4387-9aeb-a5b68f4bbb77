<?php 

class shortlink{

  private $project_id;

    function __construct($project_id) {
       $this->project_id = $project_id;
    }


    function get_shortlink($page=0)
    {
       global $app;
       $db2 = $app->db2;

       $start = $page;

      //   if($page > 1)
      //   {
      //       $start = $page * 20;
      //   }
      $page = $page + 1;
		  $db2->pageLimit = 20;
      $db2->join("divisi","divisi.divisi_id = shortlink.cs_divisi_id");
      //$res = $db2->get("shortlink",[$start,20],"HEX(shortlink.shortlink_key) as shortlink_key,shortlink.url,shortlink.cs_divisi_id,shortlink.msg,divisi.name as divisi_name");
      $res = $db2->arraybuilder()->paginate("shortlink", $page,"HEX(shortlink.shortlink_key) as shortlink_key,shortlink.url,shortlink.cs_divisi_id,shortlink.msg,divisi.name as divisi_name");

      $msg['code']=1;  
      $msg["result"]['msg']='Success get shortlink';
      $msg["result"]['data']=$res;
      $msg["result"]['page']= $start;
      $msg["result"]['total_page']= $db2->totalPages;
      return $msg;
    }

        function delete_shortlink($shortlink_key)
        {
            global $app;
            $db2 = $app->db2;
            $db = $app->db;

            $db2->where("shortlink_key = UNHEX(?)",[$shortlink_key]);
            $s = $db2->getone("shortlink");
            if($s != NULL)
            {
                $db2->where("shortlink_key = UNHEX(?)",[$shortlink_key]);
                $db2->delete("shortlink");

                $db->where("shortlink_key = UNHEX(?)",[$shortlink_key]);
                $db->delete("shortlink");

                $return["code"] = 1;
                $return["result"]["msg"] = "sukses";
            }
            else{
                $return["code"] = 0;
                $return["result"]["msg"] = "shortlink not found";
            }

            return $return; 
        }

		function add_shortlink($subdomain,$shortlink,$divisi_key,$msg,$connector = NULL) 
		{
			global $app;
          $db = $app->db;
        	$db2 = $app->db2;

        	$subdomain = strtolower($subdomain);
      		$subdomain = str_replace("https://", "", $subdomain);
        	$subdomain = str_replace("http://", "", $subdomain);

        	$tmp = explode("/", $subdomain);
        	if(count($tmp) > 1)
        	{
        		$subdomain = $tmp[0];
        	}

          
          $db2->where("subdomain_key = UNHEX(?)",[md5($subdomain)]);
          $subdomain_data = $db2->getone("subdomain");
          if($subdomain_data == NULL)
          {
            $return["code"] = 0;
                $return["result"]["msg"] = "subdomain not found";
            return $return;
          }

          $shortlink = $this->slugify($shortlink);

          $url = $subdomain."/s/".$shortlink;

          $db2->where("divisi_key = UNHEX(?)",[$divisi_key]);
          $tmp = $db2->getone("divisi");
          $cs_divisi_id = $tmp["divisi_id"];

          $shortlink_key = hash('sha256', $url);
        	$data["shortlink_key"] = $db2->func("UNHEX(?)",[$shortlink_key]);
        	$data["url"] = $url;
          $data["cs_divisi_id"] = $cs_divisi_id;
          $data["msg"] = $msg;
          if($connector != NULL){
            if(is_array($connector)){
              $data["connector"] = json_encode($connector);
            }else{
              $data["connector"] = json_encode([$connector]);
            }
          }else{
            $data["connector"] = '';
          }
          
          $db2->where("shortlink_key = UNHEX(?)",[$shortlink_key]);
          $rex = $db2->getone("shortlink");
          if($db2->count == 0){
            if($id = $db2->insert("shortlink",$data)){
                $master["shortlink_key"] = $db->func("UNHEX(?)",[$shortlink_key]);
                $master["project_id"] = $this->project_id;
                $db->insert("shortlink", $master);
                //echo $db->getLastQuery();
                  $return["code"] = 1;
                  $return["result"]["data"]["shortlink_key"] = md5($url);
                  $return["result"]["data"]["url"] = $url;
                  $return["result"]["msg"] = "sukses";
            }else{
                  $return["code"] = 0;
                  $return["result"]["msg"] = "error";
            }
          }else{
            $return["code"] = 0;
            $return["result"]["msg"] = "error, shortlink already exis!";
          }
            return $return;
			

		
		}	

    function update_shortlink($subdomain,$shortlink,$divisi_key,$msg,$connector = NULL) {
			global $app;
      $db = $app->db;
      $db2 = $app->db2;

      $subdomain = strtolower($subdomain);
      $subdomain = str_replace("https://", "", $subdomain);
      $subdomain = str_replace("http://", "", $subdomain);
      $tmp = explode("/", $subdomain);
      if(count($tmp) > 1){
        $subdomain = $tmp[0];
      }

      $db2->where("subdomain_key = UNHEX(?)",[md5($subdomain)]);
      $subdomain_data = $db2->getone("subdomain");
      if($subdomain_data == NULL){
        $return["code"] = 0;
        $return["result"]["msg"] = "subdomain not found";
        return $return;
      }

      $shortlink = $this->slugify($shortlink);
      $url = $subdomain."/s/".$shortlink;
      $db2->where("divisi_key = UNHEX(?)",[$divisi_key]);
      $tmp = $db2->getone("divisi");
      $cs_divisi_id = $tmp["divisi_id"];

      $shortlink_key = hash('sha256', $url);
      //$data["shortlink_key"] = $db2->func("UNHEX(?)",[$shortlink_key]);
      $data["url"] = $url;
      $data["cs_divisi_id"] = $cs_divisi_id;
      $data["msg"] = $msg;
      if($connector != NULL){
        if(is_array($connector)){
          $data["connector"] = json_encode($connector);
        }else{
          $data["connector"] = json_encode([$connector]);
        }
      }else{
        $data["connector"] = '';
      }
      $db2->where("shortlink_key = UNHEX(?)",[$shortlink_key]);
      $rex = $db2->getone("shortlink");
      if($db2->count > 0){
        $db2->where("shortlink_key = UNHEX(?)",[$shortlink_key]);
        if($id = $db2->update("shortlink",$data)){
            //echo $db->getLastQuery();
            $return["code"] = 1;
            $return["result"]["data"]["shortlink_key"] = md5($url);
            $return["result"]["data"]["url"] = $url;
            $return["result"]["msg"] = "sukses";
        }else{
              $return["code"] = 0;
              $return["result"]["msg"] = "error";
        }
      }else{
        $return["code"] = 0;
        $return["result"]["msg"] = "error, data not found";
      }
      
      return $return;
		}	

    function slugify($text, $divider='-'){
          // replace non letter or digits by divider
          $text = preg_replace('~[^\pL\d]+~u', $divider, $text);

          // transliterate
          $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);

          // remove unwanted characters
          $text = preg_replace('~[^-\w]+~', '', $text);

          // trim
          $text = trim($text, $divider);

          // remove duplicate divider
          $text = preg_replace('~-+~', $divider, $text);

          // lowercase
          $text = strtolower($text);

          if (empty($text)) {
            return 'n-a';
          }

          return $text;
    }

}