<?php 

    $data["count"] = 1;
    $data["phone"] = $param["phone"];
    $data["created"] = date("Y-m-d H:i:s");
    $data["phone_hash"] = $db2->func("UNHEX(?)", [md5($param["phone"]) ]);
    $db2->setQueryOption(["IGNORE"])->insert("mql", $data);

    //////////////////////////////////////////////////
    $m = new meta();
    if ($param["type"] == "message_in")
    {

        $res = $m->get_meta("mql");
        if ($res["code"] == 1)
        {
            $mql_limit = $res["result"]["data"] - 1;
        }

        $db2->where("phone_hash = UNHEX(?)", [md5($param["phone"]) ]);
        $mql_data = $db2->getone("mql");
        $inc = true;
        if ($mql_data != null)
        {
            if ($mql_data["count"] == $mql_limit)
            {
            	 @file_put_contents('log/hooksas_mql.txt', '[' . date('Y-m-d H:i:s') . "]\n" . json_encode($param), FILE_APPEND);
                $visitor_id = $mql_data["visitor_id"];
                if ($visitor_id != 0)
                {

                    $db2->where("visitor_id", $visitor_id);
                    $visitor = $db2->getone("visitor");

                    if ($visitor != null)
                    {
                        $db2->where("visitor_id", $visitor_id);
                        $db2->update("visitor", ["mql" => 1]);

                        $visitor_data = unserialize($visitor["data"]);

                        if (isset($visitor_data["last_campaign"]["source"]))
                        {
                            $source = $visitor_data["last_campaign"]["source"];

                            if($source == "meta" || $source == "tiktok" || $source == "google" || $source== "organic"){
                                if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"]))
                                {
                                    $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];

                                    $campaign = new report();

                                    $key = $param["phone"] . ";mql";
                                    $db2->setLockMethod("WRITE")
                                        ->lock(array(
                                        "report_hash",
                                        "meta",
                                        "report_data",
                                        "visitor",
                                        "connector",
                                        "log_connector_hash",
                                        "log_connector"
                                    ));

                                    if ($campaign->get_hash($key) == null)
                                    {
                                        $con = new connector();
                                        $con->trigger($visitor_id, "mql");

                                        $campaign->add_report_data($visitor, $adcopy_id, "mql");
                                    }
                                    $campaign->add_hash($key);
                                    $db2->unlock();
                                }
                                else{hook_mql_unknown($param["phone"]);}
                            }
                            else{hook_mql_unknown($param["phone"]);}
                        }
                        else{hook_mql_unknown($param["phone"]);}
                    }
                    else{hook_mql_unknown($param["phone"]);}
                }
                else{hook_mql_unknown($param["phone"]);}
            }
            if ($mql_data["count"] > $mql_limit + 1)
            {
                $inc = false;
            }

            $cs_log = new cs_log($param["nope_cs"]);
            $cs_log->add_stat("mql");
        }
        if ($inc)
        {
            $data = [];
            $data_insert = [];

            $data["phone_hash"] = $db2->func("UNHEX(?)", [md5($param["phone"]) , ]);
            $data["count"] = 1;
            $data_insert["count"] = $db2->inc(1);
            $db2->onDuplicate($data_insert);
            $db2->insert("mql", $data);
        }
        //////////////////////////////////// end mql
        ////////////////////////////////////// end cek if purchase
        
    }


function hook_mql_unknown($phone){
        global $app;
    $db2 = $app->db2;
    
    $campaign = new report();

    $key = $phone . ";mql";
    $db2->setLockMethod("WRITE")
                        ->lock(array(
                        "report_hash",
                        "meta",
                        "report_data",
                        "visitor",
                        "connector",
                        "log_connector_hash",
                        "log_connector"
                    ));

    if ($campaign->get_hash($key) == null)
    {
        $con = new connector();
        $campaign->add_report_data(NULL, 1, "mql");
    }
    $campaign->add_hash($key);
    $db2->unlock();
}
?>