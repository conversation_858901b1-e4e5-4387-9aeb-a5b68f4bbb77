<?php
class campaign{
    function create_campaign($site_id, $param){
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
        if(!isset($param['name'])){
            $res["code"] = 0;
            $res["msg"] = "campaign name not set";
            return $res;
        }
        if(!isset($param['impression_test'])) {
            $param['imp_test'] = 100;
        }
        if(!isset($param['cta_test'])){
            $param['cta_test'] = 100;
        }
        if(!isset($param['traffic_test'])){
            $param['traffic_test'] = 10;
        }
        if(!isset($param['focus_contact'])){
            $param['focus_contact'] = 100;
        }
        if(!isset($param['focus_purchase'])){
            $param['focus_purchase'] = 100;
        }
        if(!isset($param['sapaan'])){
            $param['sapaan'] = "Halo, boleh minta info nya";
        }
        if(!isset($param['data_range'])){
            $param['data_range'] = 30;
        }
        if(!isset($param['pembagian_chat'])) {
            $param['pembagian_chat'] = "sama rata";
        }
        if(!isset($param['direct'])){
            $param['direct_link'] = 0;
        }
        $table = $site_id."_campaign";
        if($id = $db2->insert($table,$param)){
            $res["code"] = 1;
            $res["msg"] = $id;
        }else{
            $res["code"] = 0;
            $res["msg"] = "Eror insert";
        }
        return $res;
    }
}