<?php
class landing_page{
    private $tb_campaign = "";
    private $tb_campaign_detail = "";
    private $tb_landing_page = "";
    private $tb_landing_page_detail = "";
    private $db = NULL;
    private $db2 = NULL ;
    function __construct($site_id,$db,$db2)
    {
        global $app;
         $this->db = $app->db;
         $this->db2 = $app->db2;
          $this->site_id = $site_id;
        $this->tb_campaign = $site_id . '_campaign';
        $this->tb_campaign_detail = $site_id . '_campaign_detail';
        $this->tb_landing_page = $site_id . '_landing_page';
        $this->tb_landing_page_detail = $site_id . '_landing_page_detail';
       
    }
    function inc_convertion($campaign_id, $lp_id, $type = "lead",$tanggal= NULL,$value= NULL)
    {

        $log["site_id"] = $this->site_id;
        $param["campaign_id"] = $campaign_id;
        $param["lp_id"] = $lp_id;
        $param["type"] = $type;
        $param["tanggal"] = $tanggal;
        $param["value"] = $value;
        $log["param"] = @json_encode($param);
        
        $db3 = $this->db;
	 	$db = $this->db2;
        if($lp_id == 0)
        {
            $lp_id = NULL;
        }
        if($lp_id == NULL)
        {
            if($value != NULL)
            {
            	$q = "UPDATE {$this->tb_campaign} SET ".$type." = ".$type." + 1  , value = value + ?  WHERE campaign_id = ?";
                $db->rawQuery($q, array($value,$campaign_id));
                $log["type"] = "cam";
                @$db3->insert("x_log_inc_convertion",$log);
        		
            }else{
                $q = "UPDATE {$this->tb_campaign} SET ".$type." = ".$type." + 1   WHERE campaign_id = ?";
                $db->rawQuery($q, array($campaign_id));
                $log["type"] = "cam";
                @$db3->insert("x_log_inc_convertion",$log);
            }
           	
           
            if($tanggal != NULL)
            {
                $tanggal = $tanggal;
            }
            else
            {
                $tanggal = date('Y-m-d');    
            }
            
            $hash = md5($campaign_id . ";" . $tanggal);
            if($value != NULL)
            {
                $q = "INSERT INTO {$this->tb_campaign_detail}(campaign_id,tanggal, hash,".$type.",value) VALUES (?,?, UNHEX(?),?,?) ON DUPLICATE KEY UPDATE ".$type." = ".$type." + 1 , value = value + ?;";
        		$db->rawQuery($q, array($campaign_id, $tanggal, $hash, 1,$value,$value));
                $log["type"] = "cam_detail";
                @$db3->insert("x_log_inc_convertion",$log);
            }
            else{
               
                $q = "INSERT INTO {$this->tb_campaign_detail}(campaign_id,tanggal, hash,".$type.") VALUES (?,?, UNHEX(?),?) ON DUPLICATE KEY UPDATE ".$type." = ".$type." + 1;";
        		$db->rawQuery($q, array($campaign_id, $tanggal, $hash, 1));
                $log["type"] = "cam_detail";
                @$db3->insert("x_log_inc_convertion",$log);
            }
            
           
           
        }
        else
        {
           
            $db->where("id",$lp_id);
            $lp = $db->get($this->tb_landing_page);
            if (count($lp) > 0)
            {
                if($value != NULL)
                {
                	$q = "UPDATE {$this->tb_campaign} SET ".$type." = ".$type." + 1  , value = value + ?  WHERE campaign_id = ?";
                	$db->rawQuery($q, array($value,$campaign_id));
                }else{
                   	$q = "UPDATE {$this->tb_campaign} SET ".$type." = ".$type." + 1   WHERE campaign_id = ?";
                	$db->rawQuery($q, array($campaign_id));
                }
                
                if($tanggal != NULL)
                {
                    $tanggal = $tanggal;
                }
                else
                {
                    $tanggal = date('Y-m-d');    
                }
                
                $hash = md5($campaign_id . ";" . $tanggal);
                if($value != NULL)
                {
                    $q = "INSERT INTO {$this->tb_campaign_detail}(campaign_id,tanggal, hash,".$type.",value) VALUES (?,?, UNHEX(?),?,?) ON DUPLICATE KEY UPDATE ".$type." = ".$type." + 1 , value = value + ?;";
        			$db->rawQuery($q, array($campaign_id, $tanggal, $hash, 1,$value,$value));


                }
                else{
                    $q = "INSERT INTO {$this->tb_campaign_detail}(campaign_id,tanggal, hash,".$type.") VALUES (?,?, UNHEX(?),?) ON DUPLICATE KEY UPDATE ".$type." = ".$type." + 1;";
        			$db->rawQuery($q, array($campaign_id, $tanggal, $hash, 1));
                }
                
               
              
                ////////////////////////////////////////////////////////
                 if($value != NULL)
                {
                    $q = "UPDATE {$this->tb_landing_page} SET ".$type." = ".$type." + 1 , value = value + ?   WHERE id = ?";
        			$db->rawQuery($q, array( $value, $lp_id));
                }
                else
                {
                    $q = "UPDATE {$this->tb_landing_page} SET ".$type." = ".$type." + 1   WHERE id = ?";
        			$db->rawQuery($q, array($lp_id));
                }
                
               
                $hash = md5($campaign_id . ";" . $lp_id . ";" . $tanggal);
                if($value != NULL)
                {
                    $q = "INSERT INTO {$this->tb_landing_page_detail} (lp_id,tanggal, hash,".$type.",value) VALUES (?,?, UNHEX(?),?,?) ON DUPLICATE KEY UPDATE ".$type." = ".$type." + 1 , value = value + ?;";
        			$db->rawQuery($q, array($lp_id, $tanggal, $hash, 1,$value,$value));
                }
                else{
                    $q = "INSERT INTO {$this->tb_landing_page_detail} (lp_id,tanggal, hash,".$type.") VALUES (?,?, UNHEX(?),?) ON DUPLICATE KEY UPDATE ".$type." = ".$type." + 1;";
        			$db->rawQuery($q, array($lp_id, $tanggal, $hash, 1));
                }
                
               
               
            }
        }
        
    }
    function inc_lp_view($campaign_id, $lp_id = NULL)
    {
    
        $db = $this->db2;
        $q = "UPDATE {$this->tb_campaign} SET impression = impression + 1   WHERE campaign_id = ?";
        $db->rawQuery($q, array($campaign_id));
        $tanggal = date('Y-m-d');
        $hash = md5($campaign_id . ";" . $tanggal);
       $q = "INSERT INTO {$this->tb_campaign_detail}
          (campaign_id,tanggal, hash,impression)
        VALUES
          (?,?, UNHEX(?),?)
        ON DUPLICATE KEY UPDATE impression = impression + 1;";
        $db->rawQuery($q, array($campaign_id, $tanggal, $hash, 1));
        //////////////////////////////////////////////// LP
        
        if($lp_id != NULL or $lp_id != 0){
            $q = "UPDATE {$this->tb_landing_page} SET impression = impression + 1   WHERE id = ?";
        	$db->rawQuery($q, array($lp_id));
	        $q = "INSERT INTO {$this->tb_landing_page_detail}
	          (lp_id,tanggal, hash,impression)
	        VALUES
	          (?,?, UNHEX(?),?)
	        ON DUPLICATE KEY UPDATE impression = impression + 1;";
	        $db->rawQuery($q, array($lp_id, $tanggal, $hash, 1));
        }
    }
    function set_tracking($name, $value)
    {
        $_SESSION[$name] = $value;
        setcookie($name, $value, time() + (86400 * 30 * 30) , "/"); // 86400 = 1 day
        
    }
    function set_tracking_once($name, $value)
    {
        if (!isset($_SESSION[$name]))
        {
            $_SESSION[$name] = $value;
        }
        if (!isset($_COOKIE[$name]))
        {
            setcookie($name, $value, time() + (86400 * 30 * 30) , "/"); // 86400 = 1 day
            
        }
    }
    function get_tracking($name)
    {
        if (isset($_SESSION[$name]))
        {
            if ($_SESSION[$name] != "")
            {
                return $_SESSION[$name];
            }
        }
        if (isset($_COOKIE[$name]))
        {
            if ($_COOKIE[$name] != "")
            {
                return $_COOKIE[$name];
            }
        }
        return false;
    }
    function get_page($campaign_id, $setting)
    {
      
         $db = $this->db2;
        $db->where("campaign_id", $campaign_id);
   		$total = $db->getone($this->tb_landing_page,"sum(contact) as contact,sum(purchase) as purchase");
        $orderby = "lead";
        if ($total[0]["contact"] > $setting["focus_contact"] && $setting["focus_contact"] != 0)
        {
            $orderby = "contact";
        }
        if ($total[0]["purchase"] > $setting["focus_purchase"] && $setting["focus_purchase"] != 0)
        {
            $orderby = "purchase";
        }
        $trial = false;
        if ($setting["trial_limit_impression"] != 0)
        {
            $db->where("campaign_id", $campaign_id);
        	$db->where("impression", $setting["trial_limit_impression"], "<");
        	$db->where("status", 0);
        	$db->orderBy("impression", "ASC");
        	$page = $db->getone($this->tb_landing_page);
            if (@count($page) > 0)
            {
                $page = $page[0];
                $page["type"] = "trial_limit_impression";
                return $page;
            }
        }
        if ($setting["trial_limit_lead"] != 0)
        {
            $db->where("campaign_id", $campaign_id);
        	$db->where("lead", $setting["trial_limit_lead"], "<");
        	$db->where("status", 0);
        	$db->orderBy("impression", "ASC");
        	$page = $db->getone($this->tb_landing_page);
            if (@count($page) > 0)
            {
                $page = $page[0];
                if ($page["lead"] == 0 and $page["impression"] >= $setting["trial_limit_impression"] * 2)
                {
                    ////////////// tampilkan winning campaign
                    
                }
                else
                {
                    $page["type"] = "trial_limit_lead";
                    return $page;
                }
            }
        }
        if ($setting["percent_traffic_to_trial"] != 0)
        {
            $tmp = rand(0, 100);
            if ($tmp <= $setting["percent_traffic_to_trial"])
            {
                $db->where("campaign_id", $campaign_id);
            	$db->where("status", 0);
            	$db->orderBy("impression", "ASC");
            	$page = $db->getone($this->tb_landing_page);
                $page["type"] = "percent_traffic_to_trial";
                return $page;
            }
        }
        $db->where("campaign_id", $campaign_id);
    	$db->where("status", 0);
    	$db->orderBy("score", "DESC");
    	$page = $db->getone("sp_landing_page", $orderby." / impression * 100 as score ,id , campaign_id,name");
        $page["type"] = "winning campaign";
        return $page;
    }
    function clean($string)
    {
        $string = str_replace(' ', '-', $string); // Replaces all spaces with hyphens.
        return preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.
        
    }
}
