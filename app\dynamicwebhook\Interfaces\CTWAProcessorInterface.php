<?php

namespace DynamicWebhook\Interfaces;

/**
 * Interface for Click-to-WhatsApp (CTWA) processing
 */
interface CTWAProcessorInterface
{
    /**
     * Extract CTWA data from webhook payload
     *
     * @param array $data Webhook data
     * @param string $platform Platform name
     * @return array CT<PERSON> data containing ctwa_clid, source_id, etc.
     */
    public function extractCTWAData(array $data, string $platform): array;

    /**
     * Validate CTWA data
     *
     * @param array $ctwaData
     * @return array Validated and cleaned CTWA data
     */
    public function validateAndProcess(array $ctwaData): array;

    /**
     * Check if webhook contains CTWA data
     *
     * @param array $data Webhook data
     * @param string $platform Platform name
     * @return bool
     */
    public function hasCTWAData(array $data, string $platform): bool;

    /**
     * Get supported platforms for CTWA extraction
     *
     * @return array
     */
    public function getSupportedPlatforms(): array;
}
