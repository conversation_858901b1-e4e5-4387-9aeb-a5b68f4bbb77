<?php 
class fx
{
	function get_account($user_id)	
	{
		global $keya, $c_time, $app;
		$db = $app->db;
		//$db->join("x_broker b","b.id = a.broker_id");
		$db->orderBy("a.waktu","desc");
		$db->where("a.user_id = UNHEX(?)",array($user_id));
		$res = $db->get("x_forexacc a");
		if($res == NULL)
		{
			return NULL;
		}
		foreach ($res as $key => $value) {
			$date = DateTime::createFromFormat('Y-m-d H:i:s', $value["waktu"]);
			$output = $date->format('Y-m-d');
			$res[$key]["waktu"] = $output;
/*
			if($value["status"] == 0)
			{
				$res[$key]["status"] = "Non Aktif";
			}
			if($value["status"] == 1)
			{
				$res[$key]["status"] = "Aktif";
			}
*/
			
		}
		return $res;
	}
	function add_account($user_id,$broker,$account,$name="")
	{
		global $keya, $c_time, $app;
		$db = $app->db;

		$id = md5($broker.';'.$account.';'.bin2hex($user_id));
		$data["id"] = $db->func("UNHEX(?)",array($id));
		$data["user_id"] = $db->func("UNHEX(?)",array($user_id));
		$data["broker"] = $broker;
		$data["acc"] = $account;
		$data["name"] = $name;

		if($db->insert("x_forexacc",$data))
		{
			$msg["notif"] = $broker . " " . $account." baru saja ditambahkan ke panel goldigger";
			$msg['msg'] = "Berhasil Menambahkan FX Account";
			$msg['code'] = 1 ;
		}
		else
		{
			$msg['msg'] = "Gagal Add Fx Account";
			$msg['code'] = -1 ;
		}
		return $msg;
	}
	function get_rebate_history($acc_id)
	{
	}
	function update_rebate($acc_id)
	{
	}
	
}