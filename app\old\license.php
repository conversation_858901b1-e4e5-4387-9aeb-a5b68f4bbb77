<?php

class license

{

	public function create_new($order_id, $user_id, $expired, $ket)

    {

		global $keya, $c_time, $app;

		$db= $app->db;

		$id = $db->insert("x_license", array("code" => ""));

		$code    = $id . "F";

		$codelen = 16; // change as needed

		for ($i = strlen($code); $i < $codelen; $i++) {

			$code .= dechex(rand(0, 15));

		}

		$data = null;

		$data["code"]     = strtoupper($code);

		$data["order_id"] = $order_id;

		$data["user_id"] = $db->func("UNHEX(?)", array($user_id));

		$data["expired"] = $expired;

		$data["role"] = $ket;

		$db->where("id", $id);

		if ($db->update("x_license", $data)) {

			$res["code"] = 1;

			$res["key"] = strtoupper(hyphenate($code));

		}else{

			$res["code"] = 0;

			$res["msg"] = "error";

		}

		return $res;

	}

	public function re_new($order_id, $expired, $ket)

    {

		global $keya, $c_time, $app;

		$db= $app->db;

		$data["expired"] = $expired;

		$data["role"] = $ket;

		$db->where("order_id", $order_id);

		if ($db->update("x_license", $data)) {

			$db->where("order_id", $order_id);

			$lc = $db->getone("x_license");

			$res["code"] = 1;

			$res["msg"] = "success";

			if(count($lc) > 0){

				$res["key"] = $lc['code'];

			}else{

				$res["key"] = '';

			}

		}else{

			$res["code"] = 0;

			$res["msg"] = "error";

		}

		return $res;

	}

	

	public function reset($code)

    {

		global $keya, $c_time, $app;

		$db= $app->db;

		$license = str_replace("-", "", $code);

		$license = strtoupper($license);

		$license = trim($license);

		$dt['token'] = '';

		$dt['cpu']   = '';

		$dt['ox']    = '';

		$db->where("code", $license);

		if ($db->update("x_license", $dt)) {

			$res["code"] = 1;

			$res["msg"] = "success";

		} else {

			$res["code"] = 0;

			$res["msg"] = "error";

		}

		return $res;

	}

	public function cek_acc($code)

    {

		$jml =1;

		for ($x = 0; $x <= 100; $x++) {

			if(md5($x)==$code){

				$jml = $x;

				break;

			}

		} 

		return $jml;

	}

}

