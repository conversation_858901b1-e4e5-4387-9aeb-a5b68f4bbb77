<?php

namespace DynamicWebhook\Interfaces;

/**
 * Interface for platform detection
 */
interface PlatformDetectorInterface
{
    /**
     * Detect platform based on webhook data
     *
     * @param array $data Webhook data
     * @param string|null $provider Explicit provider if specified
     * @return string Platform name
     */
    public function detect(array $data, ?string $provider = null): string;

    /**
     * Check if platform is supported
     *
     * @param string $platform Platform name
     * @return bool
     */
    public function isSupported(string $platform): bool;

    /**
     * Get list of supported platforms
     *
     * @return array
     */
    public function getSupportedPlatforms(): array;
}
