<?php

/**
 * Dynamic Webhook Processor
 * Mendukung berbagai platform webhook dengan konfigurasi dinamis
 * Update 2025-09-04: Perbaiki deprecation strlen(null) pada perhitungan message_length
 */

class DynamicWebhookProcessor {
    private $app;
    private $db;
    private $db2;
    private $t;
    private $m;
    private $project_key;
    private $project_id;
    private $nope_cs;
    private $input;
    private $datax;
    private $provider;
    // Trigger flags
    private $trigger_kontak = false;
    private $trigger_mql = false;
    private $trigger_prospek = false;
    private $trigger_purchase = false;
    
    // Message data
    private $visitor_id = null;
    private $phone = null;
    private $pesan = '';
    private $msg_type = '';
    private $is_new_kontak = false;
    private $value = 0;
    private $data_order = [];
    
    // Process result storage
    private $process_result = null;
    
    // Platform configurations
    private $platform_configs = [
        'konekwa' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out',
            'raw_field' => 'raw'
        ],
        'waba' => [
            'phone_field' => 'entry.0.changes.0.value.messages.0.from',
            'phone_field_out' => 'entry.0.changes.0.value.messages.0.to',
            'message_field' => 'entry.0.changes.0.value.messages.0.text.body',
            'type_field' => 'entry.0.changes.0.value.messages.0.type',
            'incoming_condition' => 'from_exists',
            'outgoing_condition' => 'to_exists',
            'nested_structure' => true,
            'echo_field' => 'entry.0.changes.0.value.message_echoes'
        ],
        'halosis' => [
            'phone_field' => 'data.from_phone_number',
            'phone_field_out' => 'data.to',
            'message_field' => 'data.message',
            'message_field_out' => 'data.text.body',
            'type_field' => 'type',
            'incoming_condition' => 'message.received',
            'outgoing_condition' => 'message.sent',
            'cs_field' => 'data.to_phone_number',
            'cs_field_out' => 'from_phone_number'
        ],
        'sleekflow' => [
            'phone_field' => 'from',
            'phone_field_alt' => 'contact.PhoneNumber',
            'phone_field_out' => 'to',
            'message_field' => 'messageContent',
            'message_field_alt' => 'message.message_content',
            'type_field' => 'status',
            'type_field_alt' => 'message.message_status',
            'incoming_condition' => 'Received',
            'outgoing_condition' => 'Sent'
        ],
        'barantum' => [
            'phone_field' => 'message_users_id',
            'message_field' => 'message_text',
            'type_field' => 'company_uuid',
            'incoming_condition' => 'exists',
            'outgoing_condition' => 'not_exists'
        ],
        'chatdaddy' => [
            'phone_field' => 'data.0.senderContactId',
            'message_field' => 'data.0.text',
            'type_field' => 'data.0.fromMe',
            'incoming_condition' => false,
            'outgoing_condition' => true,
            'event_field' => 'event',
            'event_required' => 'message-insert'
        ],
        'fonnte' => [
            'phone_field' => 'sender',
            'message_field' => 'message',
            'type_field' => 'quick',
            'incoming_condition' => 0,
            'outgoing_condition' => 1
        ],
        'freshchat' => [
            'phone_field' => 'data.customer.phone',
            'message_field' => 'data.message.message_parts.0.text.content',
            'type_field' => 'data.message.actor_type',
            'incoming_condition' => 'user',
            'outgoing_condition' => 'agent'
        ],
        'smartchat' => [
            'phone_field' => 'no_customer',
            'message_field' => 'message',
            'type_field' => 'message_type',
            'incoming_condition' => 'inbound',
            'outgoing_condition' => 'outbound',
            'form_data' => true
        ],
        'botsailor' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'kommo' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'otoklix' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'pancake' => [
            'phone_field' => 'entry.0.changes.0.value.contacts.0.wa_id',
            'message_field' => 'entry.0.changes.0.value.messages.0.text.body',
            'type_field' => 'entry.0.changes.0.value.statuses',
            'incoming_condition' => 'not_exists',
            'outgoing_condition' => 'exists',
            'nested_structure' => true,
            'special_handling' => 'pancake_waba'
        ],
        'qiscus-omnichannel' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'rasayel' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'respondio' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'wabahalosis' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'hs' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ],
        'qontak' => [
            'phone_field' => 'room.account_uniq_id',
            'message_field' => 'text',
            'type_field' => 'sender_type',
            'incoming_condition' => 'Models::Contact',
            'outgoing_condition' => 'not_Models::Contact',
            'integration_field' => 'room.channel_integration_id',
            'special_handling' => 'qontak_integration'
        ],
        'default' => [
            'phone_field' => 'phone',
            'message_field' => 'message',
            'type_field' => 'type',
            'incoming_condition' => 'message_in',
            'outgoing_condition' => 'message_out'
        ]
    ];
    
    public function __construct($app, $project_key, $nope_cs, $input, $provider = null) {
        $this->app = $app;
        $this->db = isset($app->db) ? $app->db : null;
        
        // Ensure child DB is assigned based on project before setting db2
        if ($this->db) {
            $this->db->where("project_key = UNHEX(?)", [$project_key]);
            $project = $this->db->getone("project");
            if ($project == NULL) { echo "invalid link"; die(); }
            if (function_exists('assign_child_db')) {
                assign_child_db($project["project_id"]);
            }
            $this->project_id = $project["project_id"];
        }

        // Set db2 after assigning child DB
        $this->db2 = isset($app->db2) ? $app->db2 : null;
        
        // Log database connection status for debugging
        if ($this->db2) {
            //error_log("db2 connection established successfully");
            if (method_exists($this->db2, 'where') && method_exists($this->db2, 'getone')) {
                //error_log("db2 methods available: where, getone");
            } else {
                //error_log("Warning: db2 methods not available");
            }
        } else {
            //error_log("Warning: db2 connection not available");
        }
        
        // Initialize track and meta only after db2 is set
        $this->t = new track();
        $this->m = new meta();
        $this->project_key = $project_key;
        $this->nope_cs = $nope_cs;
        $this->input = $input;
        $this->datax = json_decode($input, true);
        $this->provider = $provider;
    }
    
    /**
     * Detect platform based on $_GET['provider'] or auto-detection
     */
    public function detectPlatform() {
        if ($this->provider) {
            return $this->provider;
        }
        
        // Fallback ke auto-detection jika provider tidak ada
        $data = $this->datax;
        
        // Konekwa - memiliki field 'raw'
        if (isset($data['raw'])) return 'konekwa';
        
        // WABA - struktur Meta WhatsApp Business API
        if (isset($data['entry'][0]['changes'][0]['value']['messages']) || 
            isset($data['entry'][0]['changes'][0]['value']['message_echoes'])) return 'waba';
        
        // Halosis - memiliki type message.received/sent
        if (isset($data['type']) && in_array($data['type'], ['message.received', 'message.sent'])) return 'halosis';
        
        // Sleekflow - memiliki messageContent atau nested message structure
        if (isset($data['messageContent']) || isset($data['message']['message_content'])) return 'sleekflow';
        
        // Barantum - memiliki message_users_id
        if (isset($data['message_users_id'])) return 'barantum';
        
        // ChatDaddy - memiliki event message-insert
        if (isset($data['event']) && $data['event'] == 'message-insert') return 'chatdaddy';
        
        // Fonnte - memiliki field 'quick'
        if (isset($data['quick'])) return 'fonnte';
        
        // Freshchat - struktur nested data.message.message_parts
        if (isset($data['data']['message']['message_parts'])) return 'freshchat';
        
        // SmartChat - memiliki field specific untuk form-data atau customData
        if (isset($data['no_customer']) && isset($data['message_type'])) return 'smartchat';
        if (isset($data['customData']['no_customer'])) return 'smartchat';
        
        // Qontak - struktur khas dengan room dan sender_type
        if (isset($data['room']['account_uniq_id']) && isset($data['sender_type'])) return 'qontak';
        if (isset($data['text']) && isset($data['room']['channel_integration_id'])) return 'qontak';
        
        // Pancake - struktur mirip WABA tapi dengan status field yang berbeda
        if (isset($data['entry'][0]['changes'][0]['value']) && 
            (isset($data['entry'][0]['changes'][0]['value']['messages']) || 
             isset($data['entry'][0]['changes'][0]['value']['statuses']))) {
            // Distinguish from regular WABA by checking for Pancake-specific patterns
            $value = $data['entry'][0]['changes'][0]['value'];
            if (isset($value['statuses']) && !isset($value['messages'])) {
                return 'pancake'; // Status update from Pancake
            }
            if (isset($value['messages']) && isset($value['contacts'])) {
                // Could be either WABA or Pancake, check for Pancake specific indicators
                // Assume Pancake if we can't determine otherwise
                return 'pancake';
            }
        }
        
        return 'default';
    }
    
    /**
     * Extract data based on platform configuration
     */
    public function extractData($platform) {
        $config = $this->platform_configs[$platform] ?? $this->platform_configs['default'];

        // Handle special platforms dengan form-data parsing
        if (isset($config['form_data']) && $config['form_data']) {
            return $this->extractFormData($config);
        }
        
        // Handle special validation for chatdaddy
        if ($platform === 'chatdaddy') {
            $event = $this->extractNestedValue($this->datax, $config['event_field']);
            if ($event !== $config['event_required']) {
                return false;
            }
        }
        
        // Handle special logic for Pancake
        if ($platform === 'pancake') {
            return $this->handlePancakeData($config);
        }
        
        // Handle special logic for Qontak
        if ($platform === 'qontak') {
            return $this->handleQontakData($config);
        }
        
        // Determine message type first untuk beberapa platform
        $type_value = $this->extractNestedValue($this->datax, $config['type_field']);
        $this->msg_type = $this->determineMessageType($type_value, $config, $platform);
        
        // Extract phone number berdasarkan message type
        if ($this->msg_type === 'message_out' && isset($config['phone_field_out'])) {
            $this->phone = $this->extractNestedValue($this->datax, $config['phone_field_out']);
        } else {
            $this->phone = $this->extractNestedValue($this->datax, $config['phone_field']);
            // Try alternative phone field if primary failed
            if (!$this->phone && isset($config['phone_field_alt'])) {
                $this->phone = $this->extractNestedValue($this->datax, $config['phone_field_alt']);
            }
        }
        
        // Extract message content berdasarkan message type
        if ($this->msg_type === 'message_out' && isset($config['message_field_out'])) {
            $this->pesan = $this->extractNestedValue($this->datax, $config['message_field_out']);
        } else {
            $this->pesan = $this->extractNestedValue($this->datax, $config['message_field']);
            // Try alternative message field if primary failed
            if (!$this->pesan && isset($config['message_field_alt'])) {
                $this->pesan = $this->extractNestedValue($this->datax, $config['message_field_alt']);
            }
        }
        
        // Handle special cases for WABA
        if ($platform === 'waba') {
            return $this->extractWABAData($config);
        }
        
        // Handle special cases for Halosis (CS extraction)
        if ($platform === 'halosis') {
            $this->extractHalosisCS($config);
        }
        
        // Clean phone number
        $this->phone = $this->cleanPhoneNumber($this->phone);
        
        return true;
    }
    
    /**
     * Extract nested values from array using dot notation
     */
    private function extractNestedValue($array, $path) {
        if (!$path) return null; // fix deprecated explode
        $keys = explode('.', $path);
        $value = $array;
        
        foreach ($keys as $key) {
            if (is_array($value) && isset($value[$key])) {
                $value = $value[$key];
            } else {
                return null;
            }
        }
        
        return $value;
    }
    
    /**
     * Determine message type based on platform configuration
     */
    private function determineMessageType($type_value, $config, $platform = null) {
        // Handle special cases untuk platform tertentu
        if ($platform === 'waba') {
            // WABA menggunakan presence of 'from' vs 'to' field
            if ($config['incoming_condition'] === 'from_exists') {
                return isset($this->datax['entry'][0]['changes'][0]['value']['messages'][0]['from']) ? 'message_in' : 'message_out';
            }
        }
        
        if ($config['incoming_condition'] === 'exists') {
            return isset($type_value) ? 'message_in' : 'message_out';
        } elseif ($config['incoming_condition'] === 'not_exists') {
            return !isset($type_value) ? 'message_in' : 'message_out';
        } elseif ($type_value === $config['incoming_condition']) {
            return 'message_in';
        } elseif ($type_value === $config['outgoing_condition']) {
            return 'message_out';
        }
        
        return 'message_in'; // Default to incoming
    }
    
    /**
     * Extract form data for platforms like SmartChat
     */
    private function extractFormData($config) {
        // Try $_POST first
        if (isset($_POST['message'])) {
            $this->pesan = $_POST['message'];
            $this->phone = $this->cleanPhoneNumber($_POST['no_customer'] ?? '');
            $this->msg_type = ($_POST['message_type'] ?? '') === 'inbound' ? 'message_in' : 'message_out';
            return true;
        }
        
        // Try customData if exists
        if (isset($this->datax['customData'])) {
            $data = $this->datax['customData'];
            $this->pesan = $data['message'] ?? '';
            $this->phone = $this->cleanPhoneNumber($data['no_customer'] ?? '');
            $this->msg_type = ($data['message_type'] ?? '') === 'inbound' ? 'message_in' : 'message_out';
            return true;
        }
        
        // Fallback to normal extraction
        $this->phone = $this->extractNestedValue($this->datax, $config['phone_field']);
        $this->pesan = $this->extractNestedValue($this->datax, $config['message_field']);
        $type_value = $this->extractNestedValue($this->datax, $config['type_field']);
        $this->msg_type = $this->determineMessageType($type_value, $config);
        
        return true;
    }
    
    /**
     * Extract WABA specific data
     */
    private function extractWABAData($config) {
        // Handle messages array
        if (isset($this->datax['entry'][0]['changes'][0]['value']['messages'])) {
            $messages = $this->datax['entry'][0]['changes'][0]['value']['messages'];
            foreach ($messages as $message) {
                if (isset($message['to'])) {
                    $this->phone = $message['to'];
                    $this->msg_type = "message_out";
                } else {
                    $this->phone = $message['from'];
                    $this->msg_type = "message_in";
                }
                
                $type = $message['type'];
                if ($type === 'text') {
                    $this->pesan = $message['text']['body'] ?? '';
                }
                break; // Process first message
            }
        }
        
        // Handle message_echoes array
        if (!$this->phone && isset($this->datax['entry'][0]['changes'][0]['value']['message_echoes'])) {
            $messages = $this->datax['entry'][0]['changes'][0]['value']['message_echoes'];
            foreach ($messages as $message) {
                if (isset($message['to'])) {
                    $this->phone = $message['to'];
                    $this->msg_type = "message_out";
                } else {
                    $this->phone = $message['from'];
                    $this->msg_type = "message_in";
                }
                
                $type = $message['type'];
                if ($type === 'text') {
                    $this->pesan = $message['text']['body'] ?? '';
                }
                break; // Process first message
            }
        }
        
        if (!$this->phone || !isset($this->pesan)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Extract CS info for Halosis
     */
    private function extractHalosisCS($config) {
        if ($this->msg_type === 'message_in' && isset($config['cs_field'])) {
            $cs_phone = $this->extractNestedValue($this->datax, $config['cs_field']);
            if ($cs_phone) {
                $this->nope_cs = $this->cleanPhoneNumber($cs_phone);
            }
        } elseif ($this->msg_type === 'message_out' && isset($config['cs_field_out'])) {
            $cs_phone = $this->extractNestedValue($this->datax, $config['cs_field_out']);
            if ($cs_phone) {
                $this->nope_cs = $this->cleanPhoneNumber($cs_phone);
            }
        }
    }
    
    /**
     * Handle Pancake-specific data extraction
     */
    private function handlePancakeData($config) {
        $data = $this->datax;
        
        // Check if this is status update (outgoing message confirmation)
        if (isset($data['entry'][0]['changes'][0]['value']['statuses'])) {
            $this->msg_type = 'message_out';
            // For status updates, we need to get data from pancake_out table
            $this->handlePancakeOutgoing();
            return true;
        }
        
        // Regular incoming message
        if (isset($data['entry'][0]['changes'][0]['value']['messages'])) {
            $this->msg_type = 'message_in';
            $this->phone = $data['entry'][0]['changes'][0]['value']['contacts'][0]['wa_id'] ?? null;
            $this->phone = preg_replace("/[^0-9]/", "", $this->phone);
            $this->pesan = $data['entry'][0]['changes'][0]['value']['messages'][0]['text']['body'] ?? '';
            
            $this->phone = $this->cleanPhoneNumber($this->phone);
            return true;
        }
        
        return false;
    }
    
    /**
     * Handle Pancake outgoing messages from pancake_out table
     */
    private function handlePancakeOutgoing() {
        // Get pancake account configuration
        $this->db2->where("pancake_cs_nope", $this->nope_cs);
        $pancake_acc = $this->db2->getone("pancake_waba");
        
        if ($pancake_acc == NULL) {
            return false;
        }
        
        // Get pending outgoing messages
        $this->db2->where("status", 0);
        $messages = $this->db2->get("pancake_out");
        
        if ($this->db2->count > 0) {
            foreach ($messages as $msg) {
                $this->pesan = $msg["msg"];
                $this->phone = $msg["phone"];
                $this->phone = $this->cleanPhoneNumber($this->phone);
                
                // Mark as processed
                $this->db2->where("pancake_key", $msg["pancake_key"]);
                $this->db2->update("pancake_out", ["status" => 1]);
            }
            
            // Clean up processed messages
            $this->db2->where("status", 1);
            $this->db2->delete("pancake_out");
        }
        
        return true;
    }
    
    /**
     * Handle Qontak-specific data extraction
     */
    private function handleQontakData($config) {
        $data = $this->datax;
        
        // Extract basic fields
        $this->phone = $this->extractNestedValue($data, $config['phone_field']);
        $this->pesan = $this->extractNestedValue($data, $config['message_field']);
        
        // Determine message type based on sender_type
        $sender_type = $this->extractNestedValue($data, $config['type_field']);
        if ($sender_type === $config['incoming_condition']) {
            $this->msg_type = 'message_in';
        } else {
            $this->msg_type = 'message_out';
        }
        
        // Handle integration-specific CS mapping
        $integration_id = $this->extractNestedValue($data, $config['integration_field']);
        if ($integration_id) {
            $this->updateCsFromIntegration($integration_id);
        }
        
        // Clean phone number
        $this->phone = $this->cleanPhoneNumber($this->phone);
        
        // Validate required fields
        if (!$this->phone || !$this->pesan) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Update CS number based on Qontak integration ID
     */
    private function updateCsFromIntegration($integration_id) {
        if (empty($integration_id)) return;
        if ($this->db2 == null) {
            // no secondary DB connection available; skip silently
            return;
        }
        
        $integration_hash = sha1($integration_id);
        $this->db2->where("integration_id_key = UNHEX(?)", [$integration_hash]);
        $integration = $this->db2->getone('qontak_waba');
        
        if ($integration) {
            $this->nope_cs = $integration["cs_nope"];
        }
    }
    
    /**
     * Clean phone number
     */
    private function cleanPhoneNumber($phone) {
        if (!$phone) return null;
        
        // Remove non-numeric characters for most cases
        $cleaned = preg_replace('/\D/', '', $phone);
        
        // Remove @s.whatsapp.net suffix for WhatsApp numbers
        $phone = str_replace('@s.whatsapp.net', '', $phone);
        $cleaned = preg_replace('/\D/', '', $phone);
        
        // Validate length
        if (strlen($cleaned) >= 20) {
            return null;
        }
        
        return $cleaned;
    }
    
    /**
     * Process new contact
     */
    public function processNewContact() {
        if ($this->db2 == null) {
            // No secondary DB connection; skip creating new_kontak
            $this->is_new_kontak = false;
            return;
        }
        $new_kontak = [
            "phone" => $this->phone,
            "phone_hash" => $this->db2->func("UNHEX(?)", [md5($this->phone)]),
            "created" => date("Y-m-d H:i:s")
        ];
        
        if ($this->db2->setQueryOption('IGNORE')->insert("new_kontak", $new_kontak)) {
            $this->is_new_kontak = true;
        }
    }
    
    /**
     * Process contact trigger
     */
    public function processContactTrigger() {
        $format_id = $this->m->get_meta("format_id");
        
        if ($format_id["code"] == 0) {
            // Default format: look for ID [xxx] pattern
            preg_match("/ID \[(.*?)\]/s", $this->input, $match);
            if (count($match) > 1) {
                $this->visitor_id = $match[1];
                $this->trigger_kontak = true;
            } else {
                if ($this->is_new_kontak) {
                    $this->trigger_kontak = true;
                }
            }
        } else {
            // Custom format
            $format_id = clean_string($format_id["result"]["data"]);
            $format_id = preg_quote(trim($format_id));
            $format_id = "/(?<=" . $format_id . " )\S+\b/is";
            $format_id = preg_replace('/\s+/', ' ', $format_id);
            $msg = preg_replace('/\s+/', ' ', $this->input);
            $msg = str_replace('\n', " ", $msg);
            
            preg_match($format_id, $msg, $match);
            if (count($match) > 0) {
                $this->visitor_id = trim($match[0]);
                $this->trigger_kontak = true;
            } else {
                // Fallback to default pattern
                preg_match("/ID \[(.*?)\]/s", $this->input, $match);
                if (count($match) > 1) {
                    $this->visitor_id = $match[1];
                    $this->trigger_kontak = true;
                } else {
                    if ($this->is_new_kontak) {
                        $this->trigger_kontak = true;
                    }
                }
            }
        }
    }
    
    /**
     * Process MQL trigger
     */
    public function processMQLTrigger() {
        if ($this->msg_type != "message_in") return;
        
        $data = [
            "count" => 1,
            "phone" => $this->phone,
            "created" => date("Y-m-d H:i:s"),
            "phone_hash" => $this->db2->func("UNHEX(?)", [md5($this->phone)])
        ];
        
        $this->db2->setQueryOption(["IGNORE"])->insert("mql", $data);
        
        $res = $this->m->get_meta("mql");
        if ($res["code"] == 1) {
            $mql_limit = $res["result"]["data"] - 1;
        }
        
        $this->db2->where("phone_hash = UNHEX(?)", [md5($this->phone)]);
        $mql_data = $this->db2->getone("mql");
        
        $inc = true;
        if ($mql_data != null) {
            if ($mql_data["count"] == $mql_limit) {
                $this->trigger_mql = true;
            }
            if ($mql_data["count"] > $mql_limit) {
                $inc = false;
            }
        }
        
        if ($inc) {
            $data = [];
            $data_insert = [];
            $data["phone_hash"] = $this->db2->func("UNHEX(?)", [md5($this->phone)]);
            $data["count"] = 1;
            $data_insert["count"] = $this->db2->inc(1);
            $this->db2->onDuplicate($data_insert);
            $this->db2->insert("mql", $data);
        }
    }
    
    /**
     * Process prospect trigger
     */
    public function processProspectTrigger() {
        if ($this->msg_type != "message_out") return;
        
        $res = $this->m->get_meta("format_prospek");
        if ($res["code"] == 1) {
            $rgxFormatCheckout = clean_string($res["result"]["data"]);
            $rgxFormatCheckout = preg_quote($rgxFormatCheckout, '/');
            $strregex = str_replace('%ID%', '(\d+)', $rgxFormatCheckout);
            $rgx = '/' . $strregex . '/';
            $res = preg_match($rgx, $this->pesan, $matches);
            if ($res !== false && $res > 0) {
                $this->trigger_prospek = true;
            }
        }
    }
    
    /**
     * Process purchase trigger
     */
    public function processPurchaseTrigger() {
        if ($this->msg_type != "message_out") return;
        
        $meta_result = $this->m->get_meta("format_purchase");
        $meta_result2 = $this->m->get_meta("format_purchase_value");
        
        if ($meta_result["code"] == 1 && $meta_result2["code"] == 1) {
            $rgxFormatPurchase = clean_string($meta_result["result"]["data"]);
            $rgxFormatPurchase = preg_quote($rgxFormatPurchase, '/');
            $strregex = str_replace('%ID%', '(\d+)', $rgxFormatPurchase);
            $rgx = '/' . $strregex . '/';
            $res = preg_match($rgx, $this->pesan, $matches);
            
            if ($res !== false && $res > 0) {
                $rgxFormatValuePurchase = clean_string($meta_result2["result"]["data"]);
                $rgxFormatValuePurchase = preg_quote($rgxFormatValuePurchase, '/');
                $strregex = str_replace('%VALUE%', '([0-9,.]*)', $rgxFormatValuePurchase);
                $rgx = '/' . $strregex . '/';
                $res2 = preg_match($rgx, $this->pesan, $matches);
                
                if ($res2 !== false && $res2 > 0) {
                    $this->value = preg_replace('/[.,]/', '', $matches[1]);
                    $this->trigger_purchase = true;
                    
                    // Extract additional purchase data
                    $this->extractPurchaseData();
                }
            }
        }
    }
    
    /**
     * Extract additional purchase data
     */
    private function extractPurchaseData() {
        $this->data_order['value'] = $this->value;
        
        // Extract nama
        $format_nama = $this->m->get_meta("format_purchase_nama")["result"]["data"] ?? "";
        if ($format_nama != "") {
            $rgxFormat = preg_quote($format_nama, '/');
            $strregex = str_replace('%NAMA%', '(.+)', $rgxFormat);
            $rgx = '/' . $strregex . '/';
            $res = preg_match($rgx, $this->pesan, $matches);
            if ($res !== false && $res > 0) {
                $this->data_order['nama'] = trim($matches[1]);
            }
        }
        
        // Extract kota
        $format_kota = $this->m->get_meta("format_purchase_kota")["result"]["data"] ?? "";
        if ($format_kota != "") {
            $rgxFormat = preg_quote($format_kota, '/');
            $strregex = str_replace('%KOTA%', '(.+)', $rgxFormat);
            $rgx = '/' . $strregex . '/';
            $res = preg_match($rgx, $this->pesan, $matches);
            if ($res !== false && $res > 0) {
                $this->data_order['kota'] = trim($matches[1]);
            }
        }
        
        // Extract alamat
        $format_alamat = $this->m->get_meta("format_purchase_alamat")["result"]["data"] ?? "";
        if ($format_alamat != "") {
            $rgxFormat = preg_quote($format_alamat, '/');
            $strregex = str_replace('%ALAMAT%', '(.+)', $rgxFormat);
            $rgx = '/' . $strregex . '/';
            $res = preg_match($rgx, $this->pesan, $matches);
            if ($res !== false && $res > 0) {
                $this->data_order['alamat'] = trim($matches[1]);
            }
        }
        
        // Extract provinsi
        $format_prov = $this->m->get_meta("format_purchase_provinsi")["result"]["data"] ?? "";
        if ($format_prov != "") {
            $rgxFormat = preg_quote($format_prov, '/');
            $strregex = str_replace('%PROVINSI%', '(.+)', $rgxFormat);
            $rgx = '/' . $strregex . '/';
            $res = preg_match($rgx, $this->pesan, $matches);
            if ($res !== false && $res > 0) {
                $this->data_order['provinsi'] = trim($matches[1]);
            }
        }
        
        // Extract items
        $this->data_order['items'] = array();
        $pesan_line = explode("\n", $this->pesan);
        $format_qty = $this->m->get_meta("format_purchase_qty_sku")["result"]["data"] ?? "";
        if ($format_qty != "") {
            $rgxFormat = preg_quote($format_qty, '/');
            $strregex = str_replace('%QTY%', '(.+)', $rgxFormat);
            $strregex = str_replace('%SKU%', '(.+)', $strregex);
            $rgx = '/' . $strregex . '/';
            
            foreach ($pesan_line as $line) {
                $res = preg_match($rgx, $line, $matches);
                if ($res !== false && $res > 0) {
                    $item['qty'] = trim($matches[1]);
                    $item['sku'] = trim($matches[2]);
                    array_push($this->data_order['items'], $item);
                }
            }
        }
    }
    
    /**
     * Process visitor creation and tracking
     */
    public function processVisitor() {

        $visitor = $this->t->get_visitor($this->visitor_id, $this->phone);
        
        if ($visitor == false) {
            if ($this->phone != null) {
                if ($this->is_new_kontak) {
                    $this->trigger_kontak = true;
                }
                
                $visitor_id = $this->t->create_visitor($this->phone, $this->is_new_kontak);
                $tmp_visitor_id = $visitor_id;
                
                $visitor_id = convBase($visitor_id, "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
                $visitor_id = str_split($visitor_id, 4);
                $visitor_id = implode(".", $visitor_id);
                $visitor = $this->t->get_visitor($visitor_id, $this->phone);
                
                // Insert CS tracking
                //file_put_contents("log/insert-cs-" . $this->nope_cs . ".txt", '[' . date('Y-m-d H:i:s') . "]\n" . $this->phone . "\n" . $this->msg_type . "\n\n", FILE_APPEND);
                $this->t->insert_cs($tmp_visitor_id, $this->nope_cs, $this->msg_type);
                
                // Check for CTWA based on platform
                $x = $this->checkCTWA();
                if ($x) {
                    $this->db2->setQueryOption(array('IGNORE'))->insert("visitor_source", ["visitor_id" => $visitor_id, "source" => "meta"]);    
                    $visitor = $this->t->get_visitor($visitor_id, $this->phone);               
                
                }
            }
        }
        $this->processCTWAData($visitor);
        $ctwa_data = $this->extractCTWAData();
        // Jika ada ctwa_clid, process connector lead
        if (!empty($ctwa_data['ctwa_clid'])) {
            $visitor_ctwa_data = [
                "last_campaign" => [
                    "source" => "meta",
                    "data" => [
                        "adcopy_id" => $ctwa_data["adcopy_id"],
                        "ctwa_clid" => $ctwa_data['ctwa_clid'],
                        "source_id" => $ctwa_data['source_id'],
                        "campaign_data" => $ctwa_data['campaign_data'],
                        "extraction_method" => "dynamic_webhook",
                        "extraction_timestamp" => date('Y-m-d H:i:s')
                    ]
                ]
            ];
            $ctwa_clid = $ctwa_data['ctwa_clid'];
            $key = hash("sha256", "ctwa;".$ctwa_clid.";lead;", true);
            $this->db2->where("hash", $key);
            $this->db2->get("log_connector_hash");
            if ($this->db2->count == 0) {
                $this->trigger_kontak = true;
                $this->db2->insert("log_connector_hash", ["hash" => $key, "value" => "ctwa;".$ctwa_clid.";lead;"]);
                //if($this->is_new_kontak==false){
                    $this->db2->where("visitor_id", $visitor["visitor_id"]);
                    $this->db2->update("visitor", ["data" => serialize($visitor_ctwa_data)]);
                //}                
                }                
        }else{
            
        }
        
        return $visitor;
    }
    
    /**
     * Check CTWA based on platform
     */
    private function checkCTWA() {
        $platform = $this->detectPlatform();
        
        // Extract CTWA data first
        $ctwa_data = $this->extractCTWAData();
        
        switch ($platform) {
            case 'waba':
                return $this->t->fbwa_waba($this->phone, ["raw" => $this->input], $this->is_new_kontak, false);
            case 'konekwa':
                return $this->t->fbwa_personal($this->phone, $this->datax);
            case 'qontak':
                // Use fbwa_qontak method from track class
                return $this->t->fbwa_qontak($this->phone, $this->datax);
            default:
                return $this->t->fbwa_personal($this->phone, $this->datax);
        }
    }
    
    /**
     * Extract CTWA data (ctwa_clid dan source_id) dari message input
     */
    public function extractCTWAData() {
        // Log method entry for debugging
        //error_log("extractCTWAData called for platform: " . $this->detectPlatform());
        
        $ctwa_data = [
            'ctwa_clid' => null,
            'source_id' => null,
            'adcopy_id' => null,
            'campaign_data' => []
        ];
        
        $platform = $this->detectPlatform();
        
        switch ($platform) {
            case 'qontak':
                $ctwa_data = $this->extractQontakCTWA();
                break;                
            case 'waba':
                $ctwa_data = $this->extractWABACTWA();
                break;
            case 'pancake':
                $ctwa_data = $this->extractWABACTWA();
                break;
                
            case 'konekwa':
                $ctwa_data = $this->extractKonekwaCTWA();
                break;
                
            case 'halosis':
                $ctwa_data = $this->extractHalosisCTWA();
                break;
                
            default:
                $ctwa_data = $this->extractGenericCTWA();
                break;
        }
        
        return $ctwa_data;
    }
    
    public function create_meta_unknown_report($adcopy_id) {
        // Create basic report structure without Facebook integration
        $source = "meta";
        $r = new report();                            
        // Generate default names if not available
        $adcopy_name = "Unknown Ad Copy " . $adcopy_id;
        $adset_name = "Unknown Ad Set ";
        $campaign_name = "Unknown CTWA";
        
        // Create campaign level
        $campaign_id = $r->add_report_kolom($source, $campaign_name, "campaign", "unknown_ctwa_campaign", null, true)["data"];
        
        if ($campaign_id) {
            // Create adset level
            $adset_id = $r->add_report_kolom($source, $adset_name, "adset", "unknown_adset_for_" . $adcopy_id, $campaign_id, true)["data"];
            
            if ($adset_id) {
                // Create adcopy level with the actual adcopy_id
                $adcopy_id = $r->add_report_kolom($source, $adcopy_name, "adcopy", "unknown_" .$adcopy_id, $adset_id, true);
            }
        }
        return $adset_id;
    }
    /**
     * Extract CTWA data dari Qontak webhook
     */
    private function extractQontakCTWA() {
        $ctwa_data = [
            'ctwa_clid' => null,
            'source_id' => null,
            'adcopy_id' => null,
            'campaign_data' => []
        ];
        
        // Check room description for CTWA data
        if (isset($this->datax["room"]["description"])) {
            $description = $this->datax["room"]["description"];
            
            // Extract ctwa_clid
            if (preg_match('/ctwa_clid=([^;&\s]+)/', $description, $matches)) {
                $ctwa_data['ctwa_clid'] = trim($matches[1]);
            }
            
            // Extract source_id (equivalent to adcopy_id)
            if (preg_match('/source_id=([^;&\s]+)/', $description, $matches)) {
                $ctwa_data['source_id'] = trim($matches[1]);
                $ctwa_data['adcopy_id'] = trim($matches[1]);
            }
            
            // Extract additional campaign data
            if (preg_match('/campaign_id=([^;&\s]+)/', $description, $matches)) {
                $ctwa_data['campaign_data']['campaign_id'] = trim($matches[1]);
            }
            
            if (preg_match('/ad_id=([^;&\s]+)/', $description, $matches)) {
                $ctwa_data['campaign_data']['ad_id'] = trim($matches[1]);
            }
        }
        
        return $ctwa_data;
    }
    
    /**
     * Extract CTWA data dari WABA/Pancake webhook
     */
    private function extractWABACTWA() {
        $ctwa_data = [
            'ctwa_clid' => null,
            'source_id' => null,
            'adcopy_id' => null,
            'campaign_data' => []
        ];
        
        // Debug logging untuk WABA CTWA extraction
        //error_log("WABA CTWA extraction started");
        if (isset($this->datax['entry'][0]['changes'][0]['value']['messages'][0]['referral'])) {
            //error_log("Referral data found in WABA webhook");
            $referral = $this->datax['entry'][0]['changes'][0]['value']['messages'][0]['referral'];
            //error_log("Referral data: " . json_encode($referral));
        } else {
            //error_log("No referral data found in WABA webhook");
        }
        
        // Check dalam referral atau context data
        if (isset($this->datax['entry'][0]['changes'][0]['value']['messages'][0]['referral'])) {
            $referral = $this->datax['entry'][0]['changes'][0]['value']['messages'][0]['referral'];
            
            // Extract langsung dari referral fields (prioritas tertinggi)
            if (isset($referral['ctwa_clid'])) {
                $ctwa_data['ctwa_clid'] = $referral['ctwa_clid'];
            }
            
            if (isset($referral['source_id'])) {
                $ctwa_data['source_id'] = $referral['source_id'];
                $ctwa_data['adcopy_id'] = $referral['source_id'];
            }
            
            // Extract dari referral source_url jika ada parameter
            if (isset($referral['source_url'])) {
                $url = $referral['source_url'];
                
                // Parse URL parameters untuk ctwa_clid
                $parsed = parse_url($url);
                if (isset($parsed['query'])) {
                    parse_str($parsed['query'], $params);
                    
                    if (isset($params['ctwa_clid']) && !$ctwa_data['ctwa_clid']) {
                        $ctwa_data['ctwa_clid'] = $params['ctwa_clid'];
                    }
                    
                    if (isset($params['source_id']) && !$ctwa_data['source_id']) {
                        $ctwa_data['source_id'] = $params['source_id'];
                        $ctwa_data['adcopy_id'] = $params['source_id'];
                    }
                }
            }
            
            // Extract dari referral body jika ada
            if (isset($referral['body'])) {
                $body = $referral['body'];
                
                // Look for ctwa_clid in body jika belum ditemukan
                if (!$ctwa_data['ctwa_clid'] && preg_match('/ctwa_clid[=:]\s*([^&\s,]+)/i', $body, $matches)) {
                    $ctwa_data['ctwa_clid'] = trim($matches[1]);
                }
                
                // Look for source_id in body jika belum ditemukan
                if (!$ctwa_data['source_id'] && preg_match('/source_id[=:]\s*([^&\s,]+)/i', $body, $matches)) {
                    $ctwa_data['source_id'] = trim($matches[1]);
                    $ctwa_data['adcopy_id'] = trim($matches[1]);
                }
            }
            
            // Extract additional campaign data dari referral
            if (isset($referral['source_type'])) {
                $ctwa_data['campaign_data']['source_type'] = $referral['source_type'];
            }
            
            if (isset($referral['headline'])) {
                $ctwa_data['campaign_data']['headline'] = $referral['headline'];
            }
            
            if (isset($referral['media_type'])) {
                $ctwa_data['campaign_data']['media_type'] = $referral['media_type'];
            }
        }
        
        // Check dalam context field
        if (isset($this->datax['entry'][0]['changes'][0]['value']['messages'][0]['context'])) {
            $context = $this->datax['entry'][0]['changes'][0]['value']['messages'][0]['context'];
            
            if (isset($context['ad_id'])) {
                $ctwa_data['campaign_data']['ad_id'] = $context['ad_id'];
            }
            
            if (isset($context['campaign_id'])) {
                $ctwa_data['campaign_data']['campaign_id'] = $context['campaign_id'];
            }
        }
        
        // Debug logging untuk hasil ekstraksi
        //error_log("WABA CTWA extraction result: " . json_encode($ctwa_data));
        
        return $ctwa_data;
    }
    
    /**
     * Extract CTWA data dari Konekwa webhook
     */
    private function extractKonekwaCTWA() {
        $ctwa_data = [
            'ctwa_clid' => null,
            'source_id' => null,
            'adcopy_id' => null,
            'campaign_data' => []
        ];
        
        // Debug logging untuk Konekwa CTWA extraction
        //error_log("Konekwa CTWA extraction started");
        
        // Check dalam raw data
        if (isset($this->datax['raw'])) {
            $raw_data = json_decode($this->datax['raw'], true);
            //error_log("Konekwa raw data decoded: " . json_encode($raw_data, JSON_PARTIAL_OUTPUT_ON_ERROR));
            
            if ($raw_data) {
                // Check di level root (backward compatibility)
                if (isset($raw_data['ctwa_clid'])) {
                $ctwa_data['ctwa_clid'] = $raw_data['ctwa_clid'];
            }
            
                if (isset($raw_data['source_id'])) {
                $ctwa_data['source_id'] = $raw_data['source_id'];
                $ctwa_data['adcopy_id'] = $raw_data['source_id'];
            }
                
                // Check dalam structure WhatsApp Business (struktur terbaru)
                // Path: mei.messages[0].message.extendedTextMessage.contextInfo
                if (isset($raw_data['mei']['messages'][0]['message']['extendedTextMessage']['contextInfo'])) {
                    $contextInfo = $raw_data['mei']['messages'][0]['message']['extendedTextMessage']['contextInfo'];
                    
                    // Check externalAdReply untuk CTWA data
                    if (isset($contextInfo['externalAdReply'])) {
                        $adReply = $contextInfo['externalAdReply'];
                        
                        if (isset($adReply['ctwaClid']) && !$ctwa_data['ctwa_clid']) {
                            $ctwa_data['ctwa_clid'] = $adReply['ctwaClid'];
                        }
                        
                        if (isset($adReply['sourceId']) && !$ctwa_data['source_id']) {
                            $ctwa_data['source_id'] = $adReply['sourceId'];
                            $ctwa_data['adcopy_id'] = $adReply['sourceId'];
                        }
                        
                        // Extract additional campaign data
                        if (isset($adReply['sourceType'])) {
                            $ctwa_data['campaign_data']['source_type'] = $adReply['sourceType'];
                        }
                        
                        if (isset($adReply['sourceUrl'])) {
                            $ctwa_data['campaign_data']['source_url'] = $adReply['sourceUrl'];
                        }
                        
                        if (isset($adReply['title'])) {
                            $ctwa_data['campaign_data']['title'] = $adReply['title'];
                        }
                        
                        if (isset($adReply['body'])) {
                            $ctwa_data['campaign_data']['body'] = $adReply['body'];
                        }
                    }
                    
                    // Check conversion data
                    if (isset($contextInfo['conversionSource'])) {
                        $ctwa_data['campaign_data']['conversion_source'] = $contextInfo['conversionSource'];
                    }
                    
                    if (isset($contextInfo['conversionData'])) {
                        $ctwa_data['campaign_data']['conversion_data'] = $contextInfo['conversionData'];
                    }
                    
                    if (isset($contextInfo['entryPointConversionSource'])) {
                        $ctwa_data['campaign_data']['entry_point_source'] = $contextInfo['entryPointConversionSource'];
                    }
                    
                    if (isset($contextInfo['entryPointConversionApp'])) {
                        $ctwa_data['campaign_data']['entry_point_app'] = $contextInfo['entryPointConversionApp'];
                    }
                }
                
                // Alternative path: Check messages array directly (fallback)
                if (!$ctwa_data['ctwa_clid'] && isset($raw_data['messages']) && is_array($raw_data['messages'])) {
                    foreach ($raw_data['messages'] as $message) {
                        if (isset($message['contextInfo']['externalAdReply']['ctwaClid'])) {
                            $ctwa_data['ctwa_clid'] = $message['contextInfo']['externalAdReply']['ctwaClid'];
                        }
                        if (isset($message['contextInfo']['externalAdReply']['sourceId'])) {
                            $ctwa_data['source_id'] = $message['contextInfo']['externalAdReply']['sourceId'];
                            $ctwa_data['adcopy_id'] = $message['contextInfo']['externalAdReply']['sourceId'];
                        }
                        if ($ctwa_data['ctwa_clid'] || $ctwa_data['source_id']) {
                            break;
                        }
                    }
                }
            }
        }
        
        // Check dalam message content (fallback untuk format lama)
        if (!$ctwa_data['ctwa_clid'] && preg_match('/ctwa_clid[=:]\s*([^&\s,]+)/i', $this->pesan, $matches)) {
            $ctwa_data['ctwa_clid'] = trim($matches[1]);
        }
        
        if (!$ctwa_data['source_id'] && preg_match('/source_id[=:]\s*([^&\s,]+)/i', $this->pesan, $matches)) {
            $ctwa_data['source_id'] = trim($matches[1]);
            $ctwa_data['adcopy_id'] = trim($matches[1]);
        }
        
        // Debug logging untuk hasil ekstraksi
        //error_log("Konekwa CTWA extraction result: " . json_encode($ctwa_data));
        
        return $ctwa_data;
    }
    
    /**
     * Extract CTWA data dari Halosis webhook
     */
    private function extractHalosisCTWA() {
        $ctwa_data = [
            'ctwa_clid' => null,
            'source_id' => null,
            'adcopy_id' => null,
            'campaign_data' => []
        ];
        
        // Check dalam data message
        if (isset($this->datax['data']['message_data'])) {
            $msg_data = $this->datax['data']['message_data'];
            
            if (isset($msg_data['ctwa_clid'])) {
                $ctwa_data['ctwa_clid'] = $msg_data['ctwa_clid'];
            }
            
            if (isset($msg_data['source_id'])) {
                $ctwa_data['source_id'] = $msg_data['source_id'];
                $ctwa_data['adcopy_id'] = $msg_data['source_id'];
            }
        }
        
        // Check dalam raw message content
        if (preg_match('/ctwa_clid[=:]\s*([^&\s,]+)/i', $this->input, $matches)) {
            $ctwa_data['ctwa_clid'] = trim($matches[1]);
        }
        
        if (preg_match('/source_id[=:]\s*([^&\s,]+)/i', $this->input, $matches)) {
            $ctwa_data['source_id'] = trim($matches[1]);
            $ctwa_data['adcopy_id'] = trim($matches[1]);
        }
        
        return $ctwa_data;
    }
    
    /**
     * Extract CTWA data secara generic dari platform lain
     */
    private function extractGenericCTWA() {
        $ctwa_data = [
            'ctwa_clid' => null,
            'source_id' => null,
            'adcopy_id' => null,
            'campaign_data' => []
        ];
        
        // Search dalam seluruh input JSON
        $input_string = $this->input;
        
        // Extract ctwa_clid dari berbagai format
        $patterns = [
            '/ctwa_clid[=:]\s*["\']?([^"\'&\s,}]+)["\']?/i',
            '/"ctwa_clid"\s*:\s*"([^"]+)"/i',
            '/ctwaClid[=:]\s*["\']?([^"\'&\s,}]+)["\']?/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input_string, $matches)) {
                $ctwa_data['ctwa_clid'] = trim($matches[1]);
                break;
            }
        }
        
        // Extract source_id dari berbagai format
        $patterns = [
            '/source_id[=:]\s*["\']?([^"\'&\s,}]+)["\']?/i',
            '/"source_id"\s*:\s*"([^"]+)"/i',
            '/sourceId[=:]\s*["\']?([^"\'&\s,}]+)["\']?/i',
            '/adcopy_id[=:]\s*["\']?([^"\'&\s,}]+)["\']?/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input_string, $matches)) {
                $ctwa_data['source_id'] = trim($matches[1]);
                $ctwa_data['adcopy_id'] = trim($matches[1]);
                break;
            }
        }
        
        // Check juga dalam message content
        if (preg_match('/ctwa_clid[=:]\s*([^&\s,]+)/i', $this->pesan, $matches)) {
            $ctwa_data['ctwa_clid'] = trim($matches[1]);
        }
        
        if (preg_match('/source_id[=:]\s*([^&\s,]+)/i', $this->pesan, $matches)) {
            $ctwa_data['source_id'] = trim($matches[1]);
            $ctwa_data['adcopy_id'] = trim($matches[1]);
        }
        
        return $ctwa_data;
    }
    
    /**
     * Validate dan process CTWA data
     */
    private function validateAndProcessCTWA($ctwa_data) {
        // Validate ctwa_clid format (biasanya UUID atau alphanumeric)
        if ($ctwa_data['ctwa_clid']) {
            $ctwa_data['ctwa_clid'] = preg_replace('/[^a-zA-Z0-9\-_]/', '', $ctwa_data['ctwa_clid']);
            if (strlen($ctwa_data['ctwa_clid']) < 5) {
                $ctwa_data['ctwa_clid'] = null;
            }
        }
        
        // Validate source_id (biasanya numeric atau alphanumeric)
        if ($ctwa_data['source_id']) {
            $ctwa_data['source_id'] = preg_replace('/[^a-zA-Z0-9\-_]/', '', $ctwa_data['source_id']);
            if (strlen($ctwa_data['source_id']) < 3) {
                $ctwa_data['source_id'] = null;
                $ctwa_data['adcopy_id'] = null;
            }
        }
        
        // Log CTWA data for debugging
        if ($ctwa_data['ctwa_clid'] || $ctwa_data['source_id']) {
            $log_data = [
                'timestamp' => date('Y-m-d H:i:s'),
                'phone' => $this->phone,
                'platform' => $this->detectPlatform(),
                'ctwa_clid' => $ctwa_data['ctwa_clid'],
                'source_id' => $ctwa_data['source_id'],
                'adcopy_id' => $ctwa_data['adcopy_id']
            ];
            
            // file_put_contents('log/ctwa-extraction-' . date('Y-m-d') . '.txt', 
            //     '[' . date('Y-m-d H:i:s') . '] ' . json_encode($log_data) . "\n", 
            //     FILE_APPEND
            // );
        }
        
        return $ctwa_data;
    }
    
    /**
     * Check if message contains CTWA data
     */
    public function hasCTWAData() {
        $ctwa_data = $this->extractCTWAData();
        return !empty($ctwa_data['ctwa_clid']) || !empty($ctwa_data['source_id']);
    }
    
    /**
     * Get extracted CTWA data untuk external use
     */
    public function getCTWAData() {
        return $this->extractCTWAData();
    }
    
    /**
     * Process CTWA data untuk visitor_ctwa table
     */
    private function processCTWAData($visitor) {
        try {
            // Skip if no db2 connection
            if (!$this->db2) {
                return;
            }
            
            $visitor_data = unserialize($visitor["data"] ?? '');
            
            // Extract CTWA data dari webhook input
            $ctwa_data = $this->extractCTWAData();
            
            // Extract adcopy_id dan ctwa_clid dari visitor data atau webhook
            $adcopy_id = $ctwa_data['adcopy_id'] ?? null;
            $ctwa_clid = $ctwa_data['ctwa_clid'] ?? null;
            
            // Fallback ke visitor data jika tidak ada di webhook
            if (!$adcopy_id && is_array($visitor_data)) {
                if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                    $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];
                }
            }
            
            if (!$ctwa_clid && is_array($visitor_data)) {
                if (isset($visitor_data["last_campaign"]["data"]["ctwaClid"])) {
                    $ctwa_clid = $visitor_data["last_campaign"]["data"]["ctwaClid"];
                } elseif (isset($visitor_data["last_campaign"]["data"]["ctwa_clid"])) {
                    $ctwa_clid = $visitor_data["last_campaign"]["data"]["ctwa_clid"];
                }
            }
            
            $data_payload = isset($visitor["data"]) ? $visitor["data"] : (is_array($visitor_data) ? serialize($visitor_data) : null);
            
            // Add extracted CTWA data to payload
            if ($ctwa_data['ctwa_clid'] || $ctwa_data['source_id']) {
                $enhanced_data = [
                    'original_data' => $visitor_data,
                    'ctwa_extraction' => $ctwa_data,
                    'extraction_timestamp' => date('Y-m-d H:i:s'),
                    'platform' => $this->detectPlatform()
                ];
                $data_payload = serialize($enhanced_data);
            }
                        // Process adcopy_id if found and integrate with Facebook insights
            if (isset($ctwa_data["adcopy_id"]) && $this->db2) {
                try {
                    $external_key = sha1($ctwa_data["adcopy_id"]);
                    // Check if rawQueryOne method exists, otherwise use where/getone
                    if (method_exists($this->db2, 'rawQueryOne') && method_exists($this->db2, 'escape')) {
                        $query = "SELECT * FROM report WHERE external_key = UNHEX('" . $this->db2->escape($external_key) . "') LIMIT 1";
                        $data_report = $this->db2->rawQueryOne($query);
                    } else {
                        $this->db2->where("external_key = UNHEX(?)", [$external_key]);
                        $data_report = $this->db2->getone("report");
                    }
                    $has_count = property_exists($this->db2, 'count');
                    if (($has_count && $this->db2->count > 0) || (!$has_count && $data_report)) {
                        if (isset($data_report["report_id"])) {
                            $ctwa_data["adcopy_id"] = $data_report["report_id"];
                        }
                    } else {
                        
                        // Fallback: grab insights from Facebook and create report structure, then save parent report_id
                        try {
                            // try all active facebook integrations (desc by id) until one returns data
                            if (method_exists($this->db2, 'rawQuery')) {
                                $query = "SELECT id, account_id, platform, type, data FROM integration WHERE status = 1 AND platform = 'facebook' ORDER BY id DESC";
                                $integrations = $this->db2->rawQuery($query);
                            } else {
                                $this->db2->where("status", 1);
                                $this->db2->where("platform", "facebook");
                                $this->db2->orderBy("id", "DESC");
                                $integrations = $this->db2->get("integration", null, "id,account_id,platform,type,data");
                            }
                            $ctwa_data["integrations"] = $integrations;
                            $resolved = false;
                            if ($integrations && is_array($integrations) && count($integrations) > 0) {
                                foreach ($integrations as $integration) {
                                    $access_token = null;
                                    $account_id_fb = null;
                                    if (isset($integration["data"])) {
                                        $tmpData = @unserialize($integration["data"]);
                                        if (is_array($tmpData) && isset($tmpData["access_token"])) {
                                            $access_token = $tmpData["access_token"];
                                        }
                                        $account_id_fb = $integration["account_id"];
                                    }
                                    if (!$access_token || !$account_id_fb) {
                                        continue;
                                    }
                                    $today = date('Y-m-d');
                                    $fields = "ad_id,ad_name,adset_id,adset_name,campaign_id,campaign_name";
                                    $url = "https://graph.facebook.com/v23.0/" . $account_id_fb . "/insights?level=ad&fields=" . $fields .
                                        "&filtering=[{'field':'ad.id','operator':'IN','value':['" . $ctwa_data["adcopy_id"] . "']}]" .
                                        "&access_token=" . $access_token;
    
                                    $ch = curl_init();
                                    curl_setopt($ch, CURLOPT_URL, $url);
                                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                                    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
                                    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                                        'Accept: application/json'
                                    ]);
                                    $response = curl_exec($ch);
                                    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                                    curl_close($ch);
    
                                    if ($response !== false && $http_code >= 200 && $http_code < 300) {
                                        $res = json_decode($response, true);
                                        if (isset($res["data"]) && is_array($res["data"]) && count($res["data"]) > 0) {
                                            $source = "meta";
                                            $r = new report();
                                            $campaigns = [];
                                            foreach ($res["data"] as $value) {
                                                $campaigns[$value["campaign_id"]]["name"] = $value["campaign_name"];
                                                $campaigns[$value["campaign_id"]]["adset"][$value["adset_id"]]["name"] = $value["adset_name"];
                                                $campaigns[$value["campaign_id"]]["adset"][$value["adset_id"]]["adcopy"][$value["ad_id"]]["name"] = $value["ad_name"];
                                            }
                                            foreach ($campaigns as $cid => $campaign) {
                                                $campaign_id = $r->add_report_kolom($source, $campaign["name"], "campaign", $cid, null, true)["data"];
                                                if ($campaign_id) {
                                                    foreach ($campaign["adset"] as $aid => $adset) {
                                                        $adset_id = $r->add_report_kolom($source, $adset["name"], "adset", $aid, $campaign_id, true)["data"];
                                                        if ($adset_id) {
                                                            foreach ($adset["adcopy"] as $adid => $adcopy) {
                                                                $r->add_report_kolom($source, $adcopy["name"], "adcopy", $adid, $adset_id, true);
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                            
                                            // after insert, get parent of this adcopy
                                            $external_key = sha1($ctwa_data["adcopy_id"]);
                                            if (method_exists($this->db2, 'rawQueryOne') && method_exists($this->db2, 'escape')) {
                                                $query = "SELECT * FROM report WHERE external_key = UNHEX('" . $this->db2->escape($external_key) . "') LIMIT 1";
                                                $adcopy_report = $this->db2->rawQueryOne($query);
                                            } else {
                                                $this->db2->where("external_key = UNHEX(?)", [$external_key]);
                                                $adcopy_report = $this->db2->getone("report");
                                            }
                                            $has_count = property_exists($this->db2, 'count');
                                            if (($has_count && $this->db2->count > 0) || (!$has_count && $adcopy_report)) {
                                                if (isset($adcopy_report["parent_id"])) {
                                                    $ctwa_data["adcopy_id"] = $adcopy_report["parent_id"];
                                                } else {
                                                    $ctwa_data["adcopy_id"] = $ctwa_data["adcopy_id"]; // fallback raw
                                                }
                                            } else {
                                                $ctwa_data["adcopy_id"] = $ctwa_data["adcopy_id"]; // fallback raw
                                            }
                                            $resolved = true;
                                            break;
                                        }
                                    } else {
                                        // Log error response
                                        $resolved = false;
                                    }
                                }
                            } else {
                                $resolved = false;
                            }
                            if ($resolved == false) {
                                // add ke table report flow tanpa integration
                                try {
                                    $ctwa_data["adset_id"] = $this->create_meta_unknown_report($ctwa_data["adcopy_id"]);                                
                                } catch (Exception $e) {
                                    // Log error but continue
                                    //error_log("Error creating report without integration: " . $e->getMessage());
                                }
                                $ctwa_data["adcopy_id"] = $ctwa_data["adcopy_id"]; // fallback if none matched
                            }
                            $ctwa_data["resolved"] = $resolved;
                        } catch (Exception $e) {
                            $ctwa_data["adcopy_id"] = $ctwa_data["adcopy_id"]; // fallback on exception
                        }
                    }
                } catch (Exception $e) {
                    // Log error but continue
                    //error_log("Error processing adcopy_id: " . $e->getMessage());
                    //file_put_contents('log/ctwa_error.txt', '[' . date('Y-m-d H:i:s') . "] Error processing adcopy_id: " . $e->getMessage() . "\n", FILE_APPEND);
                }
            }
            
            // If we found ctwa_clid or source_id, validate and process
            if ($ctwa_data['ctwa_clid'] || $ctwa_data['source_id']) {
                //error_log("CTWA data found: " . json_encode($ctwa_data));
                $ctwa_data = $this->validateAndProcessCTWA($ctwa_data);
            } else {
                //error_log("No CTWA data found in webhook");
            }
            if (!empty($this->phone) && $ctwa_data['ctwa_clid'] || $ctwa_data['adcopy_id']) {
                // Check if visitor_ctwa table exists and has required columns
                try {
                    $this->db2->where("phone", $this->phone);
                    $existing_ctwa = $this->db2->getOne("visitor_ctwa");
                    
                    // Only include fields that exist in visitor_ctwa table
                    $row_update = [
                        "data" => $data_payload,
                        "phone_cs" => $this->nope_cs,
                        "project_key" => $this->project_key,
                        "project_id" => $this->getProjectId()
                    ];
                    
                    // Add optional fields only if they exist in table structure
                    if ($adcopy_id !== null) {
                        $row_update["adcopy_id"] = $adcopy_id;
                    }
                    if ($ctwa_clid !== null) {
                        $row_update["ctwa_clid"] = $ctwa_clid;
                    }
                    
                    if ($this->db2->count > 0) {
                        $this->db2->where("phone", $this->phone);
                        $this->db2->update("visitor_ctwa", $row_update);
                    } else {
                        // Insert only if we have a valid visitor_id
                        $new_visitor_id = $visitor["visitor_id"] ?? null;
                        if (!empty($new_visitor_id)) {
                            $row_insert = $row_update;
                            $row_insert["visitor_id"] = $new_visitor_id;
                            $row_insert["phone"] = $this->phone;
                            $row_insert["status"] = 0;
                            // table uses `created` column, not created_at
                            $row_insert["created"] = date('Y-m-d H:i:s');
                            $this->db2->insert("visitor_ctwa", $row_insert);
                            if ($this->is_new_kontak==false) {
                                $this->trigger_kontak = true;
                                // Decrement today's Unknown 'wa' report since attribution is now known
                                try {
                                    $r = new report();
                                    $unknownId = $r->ensure_unknown_structure();
                                    $this->db2->where("report_id", $unknownId);
                                    $this->db2->where("date", date('Y-m-d'));
                                    $this->db2->where("report_key", "%wa%", "LIKE");
                                    $this->db2->update("report_data", [
                                        "report_value" => $this->db2->dec(1)
                                    ]);
                                    $key = $this->phone . ";lead";
                                    $hash = hash('sha256', $key);
                                    $this->db2->where("hash = UNHEX(?)", [$hash]);
                                    $this->db2->delete("report_hash");
                                    $this->t->kontak_unknown($this->is_new_kontak, $this->phone, $this->nope_cs, $this->trigger_kontak);
                                } catch (Exception $e) {
                                    // silent fail; do not block flow
                                }
                            }
                        }
                    }
                } catch (Exception $e) {
                    // Log error but don't fail the entire process
                    //error_log("CTWA processing error: " . $e->getMessage());
                    //file_put_contents('log/ctwa_error.txt', '[' . date('Y-m-d H:i:s') . "] Error processing CTWA: " . $e->getMessage() . "\n", FILE_APPEND);
                }
            }
        } catch (Exception $e) {
            // Log error but don't fail the entire process
            //error_log("CTWA processing error: " . $e->getMessage());
            //file_put_contents('log/ctwa_error.txt', '[' . date('Y-m-d H:i:s') . "] Error processing CTWA: " . $e->getMessage() . "\n", FILE_APPEND);
        }
    }
    
    /**
     * Get project ID from project key
     */
    private function getProjectId() {
        return $this->project_id;
        // $this->db->where("project_key = UNHEX(?)", [$this->project_key]);
        // $project = $this->db->getone("project");
        // return $project["project_id"] ?? null;
    }
    
    /**
     * Execute triggers
     */
    public function executeTriggers($visitor) {
        if ($this->trigger_kontak) {
            $this->t->lead($this->is_new_kontak, $visitor, $this->nope_cs, $this->phone, null, null, $this->trigger_kontak);
        }
        
        if ($this->trigger_mql) {
            $this->t->mql($this->nope_cs, $visitor, $this->phone);
        }
        
        if ($this->trigger_prospek) {
            $this->t->prospek($this->nope_cs, $visitor, $this->phone);
        }
        
        if ($this->trigger_purchase) {
            $this->t->purchase($this->nope_cs, $visitor, $this->phone, $this->value, null, $this->data_order, $this->pesan);
        }
    }
    
    public function process_old() {
        // Detect platform
        $platform = $this->detectPlatform();
        
        // Extract data
        $this->extractData($platform);
        
        // Validate data
        if (!$this->phone || !$this->pesan) {
            return false;
        }
        
        // Process new contact
        $this->processNewContact();
        
        if(file_exists("config/trigger_type/".$project_id.".txt") && file_get_contents("config/trigger_type/".$project_id.".txt") == "ai"){
            //$this->processAIContactTrigger();
        }else{
            // Process triggers
            $this->processContactTrigger();            
            $this->processProspectTrigger();
            $this->processPurchaseTrigger();
        }
        $this->processMQLTrigger();
        
        // Process visitor
        $visitor = $this->processVisitor();
        
        // Execute triggers
        $this->executeTriggers($visitor);
        
        // Save chat history if enabled
        $this->saveChatHistory();
        
        return true;
    }

    /**
     * Main processing method
     */
    public function process() {
        $process_result = [
            'status' => 'success',
            'steps' => [],
            'summary' => [
                'platform' => null,
                'phone' => null,
                'message_type' => null,
                'is_new_contact' => false,
                'triggers' => [],
                'visitor_id' => null,
                'ctwa_data' => null
            ]
        ];
        
        try {
            // Step 1: Detect platform
            $step_result = $this->process_step('platform_detection', function() {
                $platform = $this->detectPlatform();
                return [
                    'status' => 'success',
                    'platform' => $platform,
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            });
            $process_result['steps']['platform_detection'] = $step_result;
            $process_result['summary']['platform'] = $step_result['platform'];
            
            // Step 2: Extract data
            $step_result = $this->process_step('data_extraction', function() use ($process_result) {
                $platform = $process_result['steps']['platform_detection']['platform'];
                $extract_result = $this->extractData($platform);
                return [
                    'status' => $extract_result ? 'success' : 'failed',
                    'phone' => $this->phone,
                    'message' => $this->pesan,
                    'message_type' => $this->msg_type,
                    'message_length' => strlen($this->pesan ?? ''),
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            });
            $process_result['steps']['data_extraction'] = $step_result;
            $process_result['summary']['phone'] = $step_result['phone'];
            $process_result['summary']['message_type'] = $step_result['message_type'];
            
            // Step 3: Validate data
            $step_result = $this->process_step('data_validation', function() {
                if (!$this->phone || !$this->pesan) {
                    return [
                        'status' => 'failed',
                        'reason' => 'Missing phone or message',
                        'phone_exists' => !empty($this->phone),
                        'message_exists' => !empty($this->pesan),
                        'timestamp' => date('Y-m-d H:i:s')
                    ];
                }
                
                return [
                    'status' => 'success',
                    'phone_valid' => !empty($this->phone),
                    'message_valid' => !empty($this->pesan),
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            });
            $process_result['steps']['data_validation'] = $step_result;
            
            if ($step_result['status'] === 'failed') {
                $process_result['status'] = 'failed';
                $process_result['error'] = $step_result['reason'];
                return $process_result;
            }
            
            // Step 4: Process new contact
            $step_result = $this->process_step('new_contact_processing', function() {
                $this->processNewContact();
                return [
                    'status' => 'success',
                    'is_new_contact' => $this->is_new_kontak,
                    'phone' => $this->phone,
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            });
            $process_result['steps']['new_contact_processing'] = $step_result;
            $process_result['summary']['is_new_contact'] = $step_result['is_new_contact'];
            
            // Step 5: Process triggers
            $step_result = $this->process_step('trigger_processing', function() {
                $project_id = $this->getProjectId();
                
                if(file_exists("config/trigger_type/".$project_id.".txt") && file_get_contents("config/trigger_type/".$project_id.".txt") == "ai"){
                    return [
                        'status' => 'skipped',
                        'reason' => 'AI trigger type enabled',
                        'timestamp' => date('Y-m-d H:i:s')
                    ];
                } else {
                    // Process triggers
                    $this->processContactTrigger();            
                    $this->processProspectTrigger();
                    $this->processPurchaseTrigger();
                    
                    return [
                        'status' => 'success',
                        'contact_trigger' => $this->trigger_kontak,
                        'prospect_trigger' => $this->trigger_prospek,
                        'purchase_trigger' => $this->trigger_purchase,
                        'timestamp' => date('Y-m-d H:i:s')
                    ];
                }
            });
            $process_result['steps']['trigger_processing'] = $step_result;
            $process_result['summary']['triggers'] = [
                'contact' => $step_result['contact_trigger'] ?? false,
                'prospect' => $step_result['prospect_trigger'] ?? false,
                'purchase' => $step_result['purchase_trigger'] ?? false
            ];
            
            // Step 6: Process MQL trigger
            $step_result = $this->process_step('mql_processing', function() {
                $this->processMQLTrigger();
                return [
                    'status' => 'success',
                    'mql_trigger' => $this->trigger_mql,
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            });
            $process_result['steps']['mql_processing'] = $step_result;
            $process_result['summary']['triggers']['mql'] = $step_result['mql_trigger'];
            
            // Step 7: Process visitor
            $step_result = $this->process_step('visitor_processing', function() {
                try {
                    $visitor = $this->processVisitor();
                    return [
                        'status' => 'success',
                        'visitor_id' => $visitor['visitor_id'] ?? null,
                        'visitor_exists' => !empty($visitor),
                        'timestamp' => date('Y-m-d H:i:s')
                    ];
                } catch (Exception $e) {
                    // Log error but still return visitor data if available
                    //error_log("Visitor processing error: " . $e->getMessage());
                    //file_put_contents('log/visitor_error.txt', '[' . date('Y-m-d H:i:s') . "] Error processing visitor: " . $e->getMessage() . "\n", FILE_APPEND);
                    
                    // Try to get visitor data directly if processVisitor failed
                    $visitor = $this->t->get_visitor(null, $this->phone);
                    return [
                        'status' => 'partial_success',
                        'visitor_id' => $visitor['visitor_id'] ?? null,
                        'visitor_exists' => !empty($visitor),
                        'error' => $e->getMessage(),
                        'timestamp' => date('Y-m-d H:i:s')
                    ];
                }
            });
            $process_result['steps']['visitor_processing'] = $step_result;
            $process_result['summary']['visitor_id'] = $step_result['visitor_id'] ?? null;
            
            // Step 8: Execute triggers
            $step_result = $this->process_step('trigger_execution', function() use ($process_result) {
                try {
                    $visitor = $this->getVisitorFromStep($process_result, 'visitor_processing');
                    if ($visitor) {
                        $this->executeTriggers($visitor);
                    } else {
                        // Log warning if no visitor found
                        //error_log("No visitor found for trigger execution in step trigger_execution");
                    }
                    
                    return [
                        'status' => 'success',
                        'triggers_executed' => [
                            'contact' => $this->trigger_kontak,
                            'mql' => $this->trigger_mql,
                            'prospect' => $this->trigger_prospek,
                            'purchase' => $this->trigger_purchase
                        ],
                        'visitor_found' => !empty($visitor),
                        'timestamp' => date('Y-m-d H:i:s')
                    ];
                } catch (Exception $e) {
                    //error_log("Error in trigger execution: " . $e->getMessage());
                    return [
                        'status' => 'partial_success',
                        'triggers_executed' => [
                            'contact' => $this->trigger_kontak,
                            'mql' => $this->trigger_mql,
                            'prospect' => $this->trigger_prospek,
                            'purchase' => $this->trigger_purchase
                        ],
                        'visitor_found' => false,
                        'error' => $e->getMessage(),
                        'timestamp' => date('Y-m-d H:i:s')
                    ];
                }
            });
            $process_result['steps']['trigger_execution'] = $step_result;
            
            // Step 9: Save chat history
            $step_result = $this->process_step('chat_history', function() {
                $this->saveChatHistory();
                return [
                    'status' => 'success',
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            });
            $process_result['steps']['chat_history'] = $step_result;
            
            // Step 10: Extract CTWA data
            $step_result = $this->process_step('ctwa_extraction', function() {
                $ctwa_data = $this->extractCTWAData();
                return [
                    'status' => 'success',
                    'ctwa_data' => $ctwa_data,
                    'has_ctwa' => !empty($ctwa_data['ctwa_clid']) || !empty($ctwa_data['source_id']),
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            });
            $process_result['steps']['ctwa_extraction'] = $step_result;
            $process_result['summary']['ctwa_data'] = $step_result['ctwa_data'];
            
            // Overall process summary
            $process_result['summary']['total_steps'] = count($process_result['steps']);
            $process_result['summary']['success_rate'] = $this->calculateSuccessRate($process_result['steps']);
            $process_result['summary']['end_time'] = date('Y-m-d H:i:s');
            
        } catch (Exception $e) {
            $process_result['status'] = 'error';
            $process_result['error'] = [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
        
        // Store result in instance property
        $this->process_result = $process_result;
        
        return $process_result;
    }
    
    /**
     * Process individual step with error handling and timing
     */
    private function process_step($step_name, $callback) {
        $start_time = microtime(true);
        
        try {
            $result = $callback();
            $result['step_name'] = $step_name;
            $result['duration_ms'] = round((microtime(true) - $start_time) * 1000, 2);
            $result['timestamp'] = $result['timestamp'] ?? date('Y-m-d H:i:s');
            
            // Log step result
            $this->logStepResult($step_name, $result);
            
            return $result;
            
        } catch (Exception $e) {
            $result = [
                'step_name' => $step_name,
                'status' => 'error',
                'error' => $e->getMessage(),
                'duration_ms' => round((microtime(true) - $start_time) * 1000, 2),
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            // Log step error
            $this->logStepResult($step_name, $result);
            
            return $result;
        }
    }
    
    /**
     * Calculate success rate from steps
     */
    private function calculateSuccessRate($steps) {
        if (empty($steps)) return 0;
        
        $successful_steps = 0;
        $total_steps = count($steps);
        
        foreach ($steps as $step) {
            if (isset($step['status']) && in_array($step['status'], ['success', 'skipped', 'partial_success'])) {
                $successful_steps++;
            }
        }
        
        return round(($successful_steps / $total_steps) * 100, 2);
    }
    
    /**
     * Get visitor data from specific step
     */
    private function getVisitorFromStep($process_result, $step_name) {
        try {
            // Check if we have db2 connection
            if (!$this->db2) {
                //error_log("No db2 connection available in getVisitorFromStep");
                return null;
            }
            
            // Create a safe visitor lookup method
            $visitor = $this->getVisitorSafely($process_result, $step_name);
            return $visitor;
            
        } catch (Exception $e) {
            //error_log("Error in getVisitorFromStep: " . $e->getMessage());
            //file_put_contents('log/visitor_step_error.txt', '[' . date('Y-m-d H:i:s') . "] Error in getVisitorFromStep: " . $e->getMessage() . "\n", FILE_APPEND);
            return null;
        }
    }
    
    /**
     * Safe visitor lookup method that handles database connection issues
     */
    private function getVisitorSafely($process_result, $step_name) {
        try {
            // Validate db2 connection and methods
            if (!$this->db2) {
                //error_log("No db2 connection in getVisitorSafely");
                return null;
            }
            
            // Check if we have the methods we need
            $hasRawQuery = method_exists($this->db2, 'rawQueryOne');
            $hasWhere = method_exists($this->db2, 'where') && method_exists($this->db2, 'getone');
            
            if (!$hasRawQuery && !$hasWhere) {
                //error_log("No suitable database methods available in getVisitorSafely");
                return null;
            }
            
            // Use simple query approach to avoid parameter binding issues
            $visitor = null;
            
            // First try to get visitor by visitor_id from step
            if (isset($process_result['steps'][$step_name]['visitor_id'])) {
                $visitor_id = $process_result['steps'][$step_name]['visitor_id'];
                if ($visitor_id && !empty($visitor_id)) {
                    try {
                        if ($hasRawQuery) {
                            // Use raw query to avoid parameter binding issues
                            $query = "SELECT * FROM visitor WHERE visitor_id = '" . $this->db2->escape($visitor_id) . "' LIMIT 1";
                            $visitor = $this->db2->rawQueryOne($query);
                        } else {
                            // Fallback to where method
                            $this->db2->where("visitor_id", $visitor_id);
                            $visitor = $this->db2->getone("visitor");
                        }
                        if ($visitor) {
                            return $visitor;
                        }
                    } catch (Exception $e) {
                        //error_log("Error querying visitor by ID: " . $e->getMessage());
                    }
                }
            }
            
            // Fallback: try to get visitor by phone only
            if ($this->phone && !empty($this->phone)) {
                try {
                    if ($hasRawQuery) {
                        // Use raw query to avoid parameter binding issues
                        $query = "SELECT * FROM visitor WHERE phone = '" . $this->db2->escape($this->phone) . "' ORDER BY created_unix DESC LIMIT 1";
                        $visitor = $this->db2->rawQueryOne($query);
                    } else {
                        // Fallback to where method
                        $this->db2->where("phone", $this->phone);
                        $this->db2->orderBy("created_unix", "desc");
                        $visitor = $this->db2->getone("visitor");
                    }
                    if ($visitor) {
                        return $visitor;
                    }
                } catch (Exception $e) {
                    //error_log("Error querying visitor by phone: " . $e->getMessage());
                }
            }
            
        } catch (Exception $e) {
            //error_log("Error in getVisitorSafely: " . $e->getMessage());
            //file_put_contents('log/visitor_safe_error.txt', '[' . date('Y-m-d H:i:s') . "] Error in getVisitorSafe: " . $e->getMessage() . "\n", FILE_APPEND);
        }
        
        return null;
    }
    
    /**
     * Log step result for debugging
     */
    private function logStepResult($step_name, $result) {
        $log_data = [
            'timestamp' => date('Y-m-d H:i:s'),
            'step_name' => $step_name,
            'phone' => $this->phone ?? 'unknown',
            'project_key' => $this->project_key,
            'result' => $result
        ];
        
        $log_file = 'log/webhook_steps/' . date('Y-m-d') . '.log';
        $log_dir = dirname($log_file);
        
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0755, true);
        }
        
        file_put_contents($log_file, json_encode($log_data) . "\n", FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Save chat history
     */
    private function saveChatHistory() {
        $project_id = $this->getProjectId();
        if (!$project_id) return;
        
        $save_history = 1;
        if (file_exists('log/history/' . $project_id . '/setting.text')) {
            $save_history = file_get_contents('log/history/' . $project_id . '/setting.text');
        }
        
        if ($save_history == 1) {
            $chatHistory = new ChatHistory($project_id);
            if (!empty($this->phone) && !empty($this->pesan) && in_array($this->msg_type, ['message_in', 'message_out'])) {
                $chatHistory->save($this->phone, $this->msg_type, $this->pesan);
            }
        }
    }
    
    /**
     * Save CTWA log untuk audit trail
     */
    private function saveCTWALog($ctwa_data) {
        if (!($ctwa_data['ctwa_clid'] || $ctwa_data['source_id'])) return;
        
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'phone' => $this->phone,
            'nope_cs' => $this->nope_cs,
            'platform' => $this->detectPlatform(),
            'ctwa_clid' => $ctwa_data['ctwa_clid'],
            'source_id' => $ctwa_data['source_id'],
            'adcopy_id' => $ctwa_data['adcopy_id'],
            'campaign_data' => $ctwa_data['campaign_data'],
            'message_type' => $this->msg_type,
            'project_key' => $this->project_key
        ];
        
        // Save to daily log file
        $log_file = 'log/ctwa/ctwa-' . date('Y-m-d') . '.log';
        $log_dir = dirname($log_file);
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0755, true);
        }
        
        //file_put_contents($log_file, json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);
        
        // Save to database if table exists
        try {
            $this->db2->insert("ctwa_log", [
                "phone" => $this->phone,
                "phone_hash" => $this->db2->func("UNHEX(?)", [md5($this->phone)]),
                "nope_cs" => $this->nope_cs,
                "platform" => $this->detectPlatform(),
                "ctwa_clid" => $ctwa_data['ctwa_clid'],
                "source_id" => $ctwa_data['source_id'],
                "adcopy_id" => $ctwa_data['adcopy_id'],
                "campaign_data" => json_encode($ctwa_data['campaign_data']),
                "message_type" => $this->msg_type,
                "project_key" => $this->project_key,
                "project_id" => $this->getProjectId(),
                "raw_data" => $this->input,
                "created_at" => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            // Table might not exist, just continue
            //error_log("CTWA log table insert failed: " . $e->getMessage());
        }
    }
    
    /**
     * Enhanced CTWA processing with improved extraction
     */
    public function processEnhancedCTWA() {
        $ctwa_data = $this->extractCTWAData();
        
        if ($ctwa_data['ctwa_clid'] || $ctwa_data['source_id']) {
            // Save audit log
            $this->saveCTWALog($ctwa_data);
            
            // Process untuk tracking system
            $this->processAdvancedCTWATracking($ctwa_data);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Advanced CTWA tracking processing
     */
    private function processAdvancedCTWATracking($ctwa_data) {
        // Update visitor data dengan CTWA info
        if ($ctwa_data['source_id']) {
            // Look up adcopy report
            $this->db2->where("external_key = UNHEX(?)", [sha1($ctwa_data['source_id'])]);
            $report_data = $this->db2->getone("report");
            
            if ($report_data) {
                $visitor_ctwa_data = [
                    "last_campaign" => [
                        "source" => "meta",
                        "data" => [
                            "adcopy_id" => $report_data["report_id"],
                            "ctwa_clid" => $ctwa_data['ctwa_clid'],
                            "source_id" => $ctwa_data['source_id'],
                            "campaign_data" => $ctwa_data['campaign_data'],
                            "extraction_method" => "dynamic_webhook",
                            "extraction_timestamp" => date('Y-m-d H:i:s')
                        ]
                    ]
                ];
                
                // Update visitor record
                $this->db2->where("phone", $this->phone);
                $this->db2->update("visitor", ["data" => serialize($visitor_ctwa_data)]);
            }
        }
    }
    
    /**
     * Get CTWA summary untuk reporting
     */
    public function getCTWASummary() {
        $ctwa_data = $this->extractCTWAData();
        
        return [
            'has_ctwa' => !empty($ctwa_data['ctwa_clid']) || !empty($ctwa_data['source_id']),
            'ctwa_clid' => $ctwa_data['ctwa_clid'],
            'source_id' => $ctwa_data['source_id'],
            'platform' => $this->detectPlatform(),
            'extraction_method' => 'dynamic_webhook_v2'
        ];
    }
    
    /**
     * Get processed data for archiving
     */
    public function getArchiveData() {
        $archive_data = [
            'act' => 'archive_add',
            'pesan' => $this->pesan,
            'nope_cs' => $this->nope_cs,
            'phone' => $this->phone,
            'msg_type' => $this->msg_type,
        ];
        
        // Add CTWA data if available
        if ($this->hasCTWAData()) {
            $archive_data['ctwa_data'] = $this->getCTWAData();
        }
        
        return $archive_data;
    }
    
    /**
     * Get detailed step information
     */
    public function getStepDetails($step_name = null) {
        if ($step_name) {
            return $this->process_result['steps'][$step_name] ?? null;
        }
        return $this->process_result['steps'] ?? [];
    }
    
    /**
     * Get step summary
     */
    public function getStepSummary() {
        return $this->process_result['summary'] ?? [];
    }
    
    /**
     * Get step status
     */
    public function getStepStatus($step_name) {
        $step = $this->getStepDetails($step_name);
        return $step['status'] ?? 'unknown';
    }
    
    /**
     * Get step duration
     */
    public function getStepDuration($step_name) {
        $step = $this->getStepDetails($step_name);
        return $step['duration_ms'] ?? 0;
    }
    
    /**
     * Get all successful steps
     */
    public function getSuccessfulSteps() {
        $successful_steps = [];
        foreach ($this->process_result['steps'] ?? [] as $step_name => $step) {
            if (isset($step['status']) && in_array($step['status'], ['success', 'partial_success'])) {
                $successful_steps[$step_name] = $step;
            }
        }
        return $successful_steps;
    }
    
    /**
     * Get all failed steps
     */
    public function getFailedSteps() {
        $failed_steps = [];
        foreach ($this->process_result['steps'] ?? [] as $step_name => $step) {
            if (isset($step['status']) && in_array($step['status'], ['failed', 'error'])) {
                $failed_steps[$step_name] = $step;
            }
        }
        return $failed_steps;
    }
    
    /**
     * Get complete process result
     */
    public function getProcessResult() {
        return $this->process_result;
    }
    
    /**
     * Get step performance metrics
     */
    public function getStepPerformance() {
        $performance = [
            'total_steps' => 0,
            'successful_steps' => 0,
            'failed_steps' => 0,
            'skipped_steps' => 0,
            'total_duration_ms' => 0,
            'average_duration_ms' => 0,
            'slowest_step' => null,
            'fastest_step' => null
        ];
        
        $steps = $this->process_result['steps'] ?? [];
        $performance['total_steps'] = count($steps);
        
        $durations = [];
        foreach ($steps as $step_name => $step) {
            $status = $step['status'] ?? 'unknown';
            $duration = $step['duration_ms'] ?? 0;
            
            switch ($status) {
                case 'success':
                    $performance['successful_steps']++;
                    break;
                case 'failed':
                    $performance['failed_steps']++;
                    break;
                case 'skipped':
                    $performance['skipped_steps']++;
                    break;
            }
            
            if ($duration > 0) {
                $performance['total_duration_ms'] += $duration;
                $durations[$step_name] = $duration;
            }
        }
        
        if (!empty($durations)) {
            $performance['average_duration_ms'] = round($performance['total_duration_ms'] / count($durations), 2);
            $performance['slowest_step'] = array_search(max($durations), $durations);
            $performance['fastest_step'] = array_search(min($durations), $durations);
        }
        
        return $performance;
    }
}

