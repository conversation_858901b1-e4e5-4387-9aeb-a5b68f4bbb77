<?php


function db_get($param){
    $msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
		'limit'      => 'required',
		));
	$validated = $gump->run($param);
	if($validated === false) {
		$msg['code'] = 0 ;
		$msg['msg'] = $gump->get_readable_errors(true);	
	}else{
        $bt = new bt_api();
        $data = $bt->get_db($param['limit']);
        $msg['code'] = 1;
		$msg['msg'] = "Success get db";
        $msg['data'] = $data; 
    }
    return $msg;
}

function add_cron_performance($param){
    $msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
        'server'      => 'required',
		));
	$validated = $gump->run($param);
    $webhook = 'http://'.$param['server'].'/api.html?act=aapanel_get_performance&server='. $param['server'];
    $bt      = new bt_api();
    $msg = $bt->add_cron('cron_server_overload', 2, $webhook);
    return $msg;
}

function get_performance($param){
    $msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$bt = new bt_api();
	$data = $bt->get_performance();
	if(isset($data['cpu_usage']) && isset($param['server'])){
		if($data['cpu_usage'] > 90){
			$msg['code'] = 1;
			$msg['msg'] = "cpu_usage overload";
			$msg['data'] = $data; 

			$pos['act'] = 'wa_server_overload';
			$pos["server"] = $param['server'];
			$pos["data"] = json_encode($data);
			post_x_contents($pos, 'http://10.104.0.27/api.html');
		}else{
			$msg['code'] = 1;
			$msg['msg'] = "cpu_usage normal";
			$msg['data'] = $data; 
		}
	}else{
		$msg['code'] = 1;
		$msg['msg'] = "server down";
		$msg['data'] = $data; 
	}
    return $msg;
}

function db_add($param){
    $msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
		'db_name'      => 'required',
        'db_user'      => 'required',
        'db_pass'      => 'required',
		));
	$validated = $gump->run($param);
	if($validated === false) {
		$msg['code'] = 0 ;
		$msg['msg'] = $gump->get_readable_errors(true);	
	}else{
        $bt = new bt_api();
        $data = $bt->add_db($param['db_name'], $param['db_user'], $param['db_pass']);
        $msg['code'] = 1;
		$msg['msg'] = "Success add db";
        $msg['data'] = $data; 
    }
    return $msg;
}

function add_cron_bill($param){
    $msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
		'project_id'      => 'required',
        'server'      => 'required',
		));
	$validated = $gump->run($param);
    $webhook = 'http://'.$param['server'].'/api.html?act=bill_get&project_id='. $param['project_id'];
    $bt      = new bt_api();
    $msg = $bt->add_cron('bill_'.$param['project_id'], 1440, $webhook);
    return $msg;
}

function add_cron($param){
    $msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
		'url'      => 'required',
        'name'      => 'required',
		'menit'      => 'required',
		));
	$validated = $gump->run($param);
    $webhook = $param['url'];
    $bt      = new bt_api();
    $msg = $bt->add_cron($param['name'], $param['menit'], $webhook);
    return $msg;
}

function get_cron($param){
    $msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
    $bt      = new bt_api();
    $msg = $bt->get_cron();
    return $msg;
}

function delete_cron_name($param){
    $msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
		'name'      => 'required',
		));
	$validated = $gump->run($param);
    $bt      = new bt_api();
    $msg = $bt->delete_cron_name($param['name']);
    return $msg;
}

function delete_cron($param){
    $msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
		'id'      => 'required',
		));
	$validated = $gump->run($param);
    $bt      = new bt_api();
    $msg = $bt->delete_cron($param['id']);
    $msg['id'] = $param['id'];
    return $msg;
}

function add_site($param){
	$subdomain = $param['subdomain'];
	//$co=file_get_contents('config/c.gass.co.id.php');
	//file_put_contents('config/'.$subdomain.'.php', $co);
	//add_config('config/'.$subdomain.'.php', $co);
	add_site_json($subdomain);
	$bt      = new bt_api();
	$rex = $bt->add_site($subdomain); 
	$bt->update_site_rewrite($subdomain);
	return $rex;
}

function delete_site($param){
	$subdomain = $param['subdomain'];
	$id = $param['id'];
	$bt      = new bt_api();
	$rex = $bt->delete_site($id, $subdomain); 
	return $rex;
}

function add_site_json($subdomain){
	if(is_file('config/subdomain.json')) {  
        $jsonsubdomain = json_decode(file_get_contents('config/subdomain.json'), true);
        array_push($jsonsubdomain, $subdomain);
		file_put_contents('config/subdomain.json', json_encode($jsonsubdomain));
    }else{
        file_put_contents('config/subdomain.json', json_encode(array($subdomain)));
    } 
}
function add_config($file, $content){
    $handle = fopen($file, 'w');
    fwrite($handle, $content);
    fclose($handle);
}

function get_site($param){
	$domain = '';
	if(isset($param['domain'])){
		$domain = $param['domain'];
	}
	$bt      = new bt_api();
	$rex = $bt->get_site($domain); 
	return $rex;
}

function set_site_path($param){
	$bt      = new bt_api();
	$rex = $bt->set_site_path($param['site_id'], $param['path']); 
	return $rex;
}


function renew_ssl($param){
	$subdomain = $param['subdomain'];
	$id = $param['id'];
	$bt      = new bt_api();
	$rex = $bt->get_ssl($subdomain); 
	if($rex['status']==true){
		if($rex['index']==null){
			$rex = $bt->new_ssl($id, $subdomain); 
		}else{
			$rex = $bt->renew_ssl($rex['index']); 
		}
		
	}
	return $rex;
}

function get_all_subdomain($param){
	global $keya, $c_time, $app;
    $db = $app->db;
   
	

	$projects =  $db->get("project",null,"project_id,name");

	foreach($projects as $project){
		assign_child_db($project["project_id"]);
		$db2 = $app->db2;
		
		$subdomains =$db2->get("subdomain",null,"name");
		foreach($subdomains as $subdomain){
			if($subdomain["name"] != null){
				$xxx[] = $subdomain["name"];
			}
		}
	}
	sort($xxx);
	$xxx = json_encode($xxx);

	file_put_contents("subdomain.txt",$xxx);
	
	return $xxx;
	
}

function get_all_error_site($param){

	$prompt = 'You are a PHP Error Log Analyzer.

Your task is to read raw error log entries from PHP/NGINX (e.g., stderr, FastCGI, etc.) and generate a **short, clear, human-readable summary** of each unique error.

---

## INPUT
You will receive a single plain-text log with multiple entries. Entries may include:
- PHP Fatal errors
- mysqli_sql_exception
- File paths, line numbers
- Stack traces
- NGINX metadata (client IP, server name, etc.)
- **Timestamps/waktu kejadian error**

---

## OUTPUT FORMAT
Untuk setiap **error unik**, tampilkan dalam format teks biasa seperti ini:

---
**Jenis Error:** Class Not Found  
**Ringkasan:** Library GoogleAdsClientBuilder tidak ditemukan. Kemungkinan belum di-install atau autoload bermasalah.  
**File:** /www/wwwroot/c.gass.co.id/test.php:46  
**Rekomendasi:** Install library dengan perintah: `composer require googleads/google-ads-php`  
**Kategori:** missing_dependency  
**Jumlah Kemunculan:** 2x  
**Terakhir Terjadi:** 2024-06-07 14:23:01  

---
**Jenis Error:** SQL Data Too Long  
**Ringkasan:** Data yang dikirim ke kolom \'name\' terlalu panjang dan menyebabkan error.  
**File:** /www/wwwroot/c.gass.co.id/app/database.php:1567  
**Rekomendasi:** Batasi input menggunakan substr() atau ubah ukuran kolom \'name\' di database.  
**Kategori:** database_error  
**Jumlah Kemunculan:** 2x  
**Terakhir Terjadi:** 2024-06-07 13:55:10  

---

## RULES
- Gabungkan error yang identik (same file + same type + same description) menjadi satu, dan hitung jumlah kemunculannya.
- Fokus pada penjelasan dan rekomendasi yang bisa dipahami developer junior atau devops engineer.
- Jangan ulangi metadata seperti IP address, request method, atau upstream info — hanya fokus pada error PHP-nya.
- Gunakan bahasa Indonesia untuk deskripsi dan rekomendasi.
- Sertakan command praktis jika berkaitan dengan dependency seperti Composer.
- Jika bisa, bantu dengan _contoh tindakan konkret_ — bukan hanya saran abstrak.
- **Sertakan field "Terakhir Terjadi" (timestamp) untuk setiap error unik, ambil dari waktu kejadian error terakhir pada log. Jika tidak ada timestamp, tulis "Tidak diketahui".**

---

## HEURISTIC DETECTION TIPS
- "Class not found" → Cek apakah library belum di-install atau autoload gagal
- "Data too long" → Perlu validasi panjang input atau revisi ukuran kolom
- "Undefined function" → Kemungkinan fungsi belum di-include atau typo
- Beri penanda `kategori` seperti `missing_dependency`, `database_error`, `runtime_error`, `autoload_error`
- Jika error muncul lebih dari sekali, beri catatan urgensi jika berdampak pada trafik user
';


	$subdomains = file_get_contents("subdomain.txt");
	$subdomains = json_decode($subdomains,true);
	$res = [];
	$ada_error = false;
	foreach($subdomains as $subdomain){
		$bt      = new bt_api();
		$url = $bt->BT_PANEL.'/site?action=get_site_err_log';
    	$p_data = $bt->GetKeyData();
    	$p_data['siteName'] = $subdomain;
    	$result = $bt->HttpPostCookie($url,$p_data);
    	$data = json_decode($result,true);
		
		if($data["msg"] != "Log is empty"){
		$res["https://".$subdomain] = $data["msg"];
		}
		else{
			$ada_error = true;
		}


		$url = $bt->BT_PANEL.'/files?action=SaveFileBody';
		$p_data = $bt->GetKeyData();
		$p_data['data'] = '';
		$p_data['encoding'] = 'utf-8';
		$p_data['path'] = '/www/wwwlogs/'.$subdomain.'.error.log';
		$result = $bt->HttpPostCookie($url,$p_data);


		
	}

	if($ada_error){

		$res = json_encode($res);
		$res = aapanel_x_requestOpenAI($prompt, $res);
		$caption = $res["msg"];

		// Tambahkan IP server ke caption
		$server_ip = isset($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : gethostbyname(gethostname());
		$caption .= "\n[IP Server: $server_ip]";

		$post = [
			'act' => 'broadcast_add_bc_server_error',
			'pesan' => $caption,
		];
		$res = post_x_contents($post, 'http://**************/api.html');
		//$res = aapanel_x_send("628113608550", "6282139817939", $caption);
		//$res = aapanel_x_send("628113608550", "628985606632", $caption);

	}
	
    return $res;
}

function aapanel_x_requestOpenAI($instruction, $content, $model = "gpt-4o-mini"){
	/*
	return $this->requestGemini($instruction,$content);
		*/
	$messageContent = [
		"model" => $model,
		"messages" => [
			[
				"role" => "system",
				"content" => $instruction,
			],
			[
				"role" => "user",
				"content" => $content,
			],
		],
	];

	$curl = curl_init();
	curl_setopt_array($curl, [
		CURLOPT_URL => 'https://api.openai.com/v1/chat/completions',
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS => json_encode($messageContent),
		CURLOPT_HTTPHEADER => [
			'Authorization: Bearer ' . "********************************************************************************************************************************************************************",
			'Content-Type: application/json',
		],
	]);

	$response = curl_exec($curl);
	$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

	$responseData = json_decode($response, true);
	$jsonString = $responseData['choices'][0]['message']['content'] ?? null;

	if (!empty($jsonString)) {
		$cleanedString = preg_replace('/^```json\s*|\s*```$/m', '', $jsonString);
	} else {
		$cleanedString = $responseData['choices'][0]['message']['content'];
	}

	curl_close($curl);
	return [
		'status' => $httpCode === 200 ? 'success' : 'error',
		'msg' => $cleanedString,
	];
}
function aapanel_x_send($phone, $to, $caption){

		$data = [
			'act' => 'wa_send',
			'sender' => $phone,
			'message' =>  str_replace('\n', PHP_EOL, $caption),
			'type' => 'text',
			'to' => $to,
		];

	$result = aapanel_x_post_gateway($data, 'http://***************:8080/send-message');
	return json_decode($result, true);
}

function aapanel_x_post_gateway($fields, $url='http://***************:8080/send-message'){
	
	$fields = http_build_query($fields);
	$header[0] = "Accept-Language: en";
	$header[] = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
//        $header[] = "Pragma: no-cache";
//        $header[] = "Cache-Control: no-cache";
//        $header[] = "Accept-Encoding: gzip,deflate";
	$header[] = "Content-Type: application/x-www-form-urlencoded";

	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_POST, true);
	curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
	$data = curl_exec($ch);
	curl_close($ch);
	return $data;

}


function get_error_site($param){
	$subdomain = $param['subdomain'];
	$bt      = new bt_api();
	$url = $bt->BT_PANEL.'/site?action=get_site_err_log';
    $p_data = $bt->GetKeyData();
    $p_data['siteName'] = $subdomain;
    $result = $bt->HttpPostCookie($url,$p_data);
    $data = json_decode($result,true);
    return $data;
}

function clear_error_site($param){
	$subdomain = $param['subdomain'];
	$bt      = new bt_api();
	$url = $bt->BT_PANEL.'/files?action=SaveFileBody';
    $p_data = $bt->GetKeyData();
    $p_data['data'] = '';
	$p_data['encoding'] = 'utf-8';
	$p_data['path'] = '/www/wwwlogs/'.$subdomain.'.error.log';
    $result = $bt->HttpPostCookie($url,$p_data);
    $data = json_decode($result,true);
    return $data;
}