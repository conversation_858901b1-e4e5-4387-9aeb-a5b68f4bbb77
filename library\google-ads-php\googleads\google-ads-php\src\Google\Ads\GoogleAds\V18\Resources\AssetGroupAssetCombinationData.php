<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/resources/asset_group_top_combination_view.proto

namespace Google\Ads\GoogleAds\V18\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Asset group asset combination data
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.resources.AssetGroupAssetCombinationData</code>
 */
class AssetGroupAssetCombinationData extends \Google\Protobuf\Internal\Message
{
    /**
     * Output only. Served assets.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.AssetUsage asset_combination_served_assets = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    private $asset_combination_served_assets;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array<\Google\Ads\GoogleAds\V18\Common\AssetUsage>|\Google\Protobuf\Internal\RepeatedField $asset_combination_served_assets
     *           Output only. Served assets.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Resources\AssetGroupTopCombinationView::initOnce();
        parent::__construct($data);
    }

    /**
     * Output only. Served assets.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.AssetUsage asset_combination_served_assets = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getAssetCombinationServedAssets()
    {
        return $this->asset_combination_served_assets;
    }

    /**
     * Output only. Served assets.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.AssetUsage asset_combination_served_assets = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param array<\Google\Ads\GoogleAds\V18\Common\AssetUsage>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setAssetCombinationServedAssets($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Ads\GoogleAds\V18\Common\AssetUsage::class);
        $this->asset_combination_served_assets = $arr;

        return $this;
    }

}

