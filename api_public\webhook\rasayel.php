<?php 

$input = file_get_contents('php://input');
 
// file_put_contents('log/hooklog-rasayel.txt', '[' . date('Y-m-d H:i:s') . "]\n", FILE_APPEND);	
// file_put_contents('log/hooklog-rasayel.txt', $input, FILE_APPEND);	
// file_put_contents('log/hooklog-rasayel.txt', "\n\n", FILE_APPEND);

if(!isset($_GET["p"])){echo "invalid link";die();}
if(!isset($_GET["cs"])){echo "invalid link";die();}
if(!isset($input)){echo "invalid link";die();}


///////////////////////////////////////////////////////////////////////// setting //////////////////////
$trigger_kontak = false;
$trigger_mql = false;
$trigger_prospek = false;
$trigger_purchase = false;
$visitor_id = NULL;

if(!isset($_GET["p"])){
    return false;
}
if(!isset($_GET["cs"])){
    return false;
}
if(!isset($input)){
    return false;
}

$project_key = $_GET["p"];
$nope_cs = $_GET["cs"];
$datax = json_decode($input,true);

if($datax["event"] != 'message.created'){
    return false;
}

$pesan = $datax["data"]['body'];
if(isset($datax["data"]['conversation']['participants'][0]['user']['identifiers'])){
    $phone = $datax["data"]['conversation']['participants'][0]['user']['identifiers'][0]['sourceId'];
}elseif(isset($datax["data"]['conversation']['participants'][1]['user']['identifiers'])){
    $phone = $datax["data"]['conversation']['participants'][1]['user']['identifiers'][0]['sourceId'];
}

$msg = $input;


if($datax["data"]["direction"] == "INBOUND"){
    $msg_type = "message_in";
}else{
    $msg_type = "message_out";
   
}
if($pesan==null){
    $pesan = '';
}
$post_archive = [
    'act' => 'archive_add',
    'pesan' => $pesan,
    'nope_cs' =>  $nope_cs,
    'phone' => $phone,
    'msg_type' => $msg_type,
];

$forward_to =array("http://***********/api.html");
///////////////////////////////////////////////////////////////////////// end setting //////////////////////


global $app;
$db = $app->db;
$db->where("project_key = UNHEX(?)",[$project_key]);
$project = $db->getone("project");

if($project == NULL){echo "invalid link";die();}

assign_child_db($project["project_id"]);

$t = new track();
$m = new meta();
global $app;
$db2 = $app->db2;

$db2->where("cs_key = UNHEX(?)",[md5($nope_cs)]);
$cs = $db2->getone("cs");

if($cs == NULL){echo "invalid link";die();}

 ///////////////////  cek new kontak //////////////////////
 $is_new_kontak = false;

 $new_kontak["phone"] = $phone;
 $new_kontak["phone_hash"] = $db2->func("UNHEX(?)", [md5($phone) ]);
 $new_kontak["created"] = date("Y-m-d H:i:s");

 if ($db2->setQueryOption('IGNORE')->insert("new_kontak", $new_kontak))
 {
     $is_new_kontak = true;
 }
 ///////////////////  cek new kontak //////////////////////




 ///////////////////////////////////// kontak masuk
 $format_id = $m->get_meta("format_id");
 if($format_id["code"] == 0){
     preg_match("/ID \[(.*?)\]/s", $msg, $match);
     if (count($match) > 1)
     {
         $visitor_id = $match[1];
         $trigger_kontak = true;
     }else{
         if($is_new_kontak){
             $trigger_kontak = true;
         }
     }
 }else{
    
    $format_id =  clean_string($format_id["result"]["data"]);
    $format_id = preg_quote(trim($format_id));
    $format_id = "/(?<=".$format_id." )\S+\b/is";
    $format_id = preg_replace('/\s+/', ' ', $format_id);
    $msg = preg_replace('/\s+/', ' ', $msg);
    $msg = str_replace('\n'," ",$msg);
     preg_match($format_id, $msg, $match);
     if (count($match) > 0)
     {
         $visitor_id = trim($match[0]);
         $trigger_kontak = true;
     }else{

         preg_match("/ID \[(.*?)\]/s", $msg, $match);
         if (count($match) > 1)
         {
             $visitor_id = $match[1];
             $trigger_kontak = true;
            
         }else{
             if($is_new_kontak){
                 $trigger_kontak = true;
             }
         }
     }
    
 }
 ///////////////////////////////////// end kontak masuk /////////////////////////////////////



 if ($msg_type == "message_in")
    {

        

        //////////////////////////////////// mql //////////////////////////////////
        $data["count"] = 1;
        $data["phone"] = $phone;
        $data["created"] = date("Y-m-d H:i:s");
        $data["phone_hash"] = $db2->func("UNHEX(?)", [md5($phone) ]);
        $db2->setQueryOption(["IGNORE"])->insert("mql", $data);

       
        $res = $m->get_meta("mql");
        if ($res["code"] == 1)
        {
            $mql_limit = $res["result"]["data"] - 1;
        }

        $db2->where("phone_hash = UNHEX(?)", [md5($phone) ]);
        $mql_data = $db2->getone("mql");
        $inc = true;
        if ($mql_data != null)
        {
          
            if ($mql_data["count"] == $mql_limit)
            {
                $trigger_mql = true;
            }
            if ($mql_data["count"] > $mql_limit )
            {
                $inc = false;
            }
        }
        if ($inc)
        {
            $data = [];
            $data_insert = [];

            $data["phone_hash"] = $db2->func("UNHEX(?)", [md5($phone)]);
            $data["count"] = 1;
            $data_insert["count"] = $db2->inc(1);
            $db2->onDuplicate($data_insert);
            $db2->insert("mql", $data);
        }
    }
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    if ($msg_type == "message_out")
    {
        ///////////////////////// prospek
        $res = $m->get_meta("format_prospek");

        if ($res["code"] == 1)
        {
            $rgxFormatCheckout = clean_string($res["result"]["data"]);
            $rgxFormatCheckout = preg_quote($rgxFormatCheckout , '/');
            $strregex = str_replace('%ID%', '(\d+)', $rgxFormatCheckout);
            $rgx = '/' . $strregex . '/';
            $res = preg_match($rgx, $pesan, $matches);

            if ($res !== false && $res > 0)
            {

                $trigger_prospek = true;
            }
        }
        //////////////////////// end prospek

        ///////////////////////// purchase
        $value = 0;
        $meta_result = $m->get_meta("format_purchase");
        $meta_result2 = $m->get_meta("format_purchase_value");
        if ($meta_result["code"] == 1 && $meta_result2["code"] == 1)
        {

            $rgxFormatPurchase = clean_string($meta_result["result"]["data"]);
            $rgxFormatPurchase = preg_quote($rgxFormatPurchase, '/');
            $strregex = str_replace('%ID%', '(\d+)', $rgxFormatPurchase);
            $rgx = '/' . $strregex . '/';
            $res = preg_match($rgx, $pesan, $matches);

            if ($res !== false && $res > 0)
            {
                $rgxFormatValuePurchase = clean_string($meta_result2["result"]["data"]);
                $rgxFormatValuePurchase = preg_quote($rgxFormatValuePurchase, '/');
                $strregex = str_replace('%VALUE%', '([0-9,.]*)', $rgxFormatValuePurchase);
                $rgx = '/' . $strregex . '/';
                $res2 = preg_match($rgx, $pesan, $matches);

                if ($res2 !== false && $res2 > 0)
                {
                    $value = preg_replace('/[.,]/', '', $matches[1]);
                    $trigger_purchase = true;
                }
            }
        }
        //////////////////////// end purchase

    }


/////////////////////////////////////////////////////// trigger ////////////////////////
$x =false;
if($trigger_kontak || $trigger_mql || $trigger_prospek || $trigger_purchase)
    {
       
         $visitor = $t->get_visitor($visitor_id,$phone);
        
        
         if($visitor == false){
            if($phone != NULL){
                $visitor_id = $t->create_visitor($phone);
              
                $visitor_id = convBase($visitor_id, "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
                $visitor_id = str_split($visitor_id, 4);
                $visitor_id = implode(".", $visitor_id);

                $x = $t->fbwa_personal($phone,$datax);
                $visitor = $t->get_visitor($visitor_id,$phone);
            }
            
         }

        //////////////////////// cek ctwa ///////////////////////////////////////////
       
        if($x)
        {
            $data_tmp["is_new_kontak"] = $is_new_kontak;
            $data_tmp["visitor"] = $visitor;
            $data_tmp["nope_cs"] = $nope_cs;
            $data_tmp["phone"] = $phone;
            $data_tmp = serialize($data_tmp);
          //  @file_put_contents('log_ctwa.txt', '[' . date('Y-m-d H:i:s') . "]\n" . $data_tmp."\n\n", FILE_APPEND);
        }

         if($trigger_kontak){

            $t->lead($is_new_kontak,$visitor,$nope_cs,$phone);
         }

         if($trigger_mql){
            $t->mql($nope_cs,$visitor,$phone);
         }

         if($trigger_prospek){
            $t->prospek($nope_cs,$visitor,$phone);
         }

         if($trigger_purchase){
            $t->purchase($nope_cs,$visitor,$phone,$value);
         }
    }

    //$x = forwarder($post_archive,$forward_to);
    $save_history = 1;
    if(file_exists('log/history/'.$project["project_id"].'/setting.text')){
        $save_history = file_get_contents('log/history/'.$project["project_id"].'/setting.text');
    }
    if($save_history == 1){ 
        $chatHistory = new ChatHistory($project["project_id"]);   
        if (!empty($phone) && !empty($pesan) && in_array($msg_type, ['message_in', 'message_out'])) {
            $chatHistory->save($phone, $msg_type, $pesan);
        }
    }
    
?>