<?php 
class blacklist{
    function check_dom_blacklist($domain){
        global $app;
        $db = $app->db;
        $db->where("domain = ?",array($domain));
        $db->get("x_domain_blacklist");
        if($db->count>0){
            return true;
        }else{
            return false;
        }
    }

    function add_dom_blacklist($domain, $ket){
        global $app;
        $db = $app->db;
        $db->where("domain = ?",array($domain));
        $db->get("x_domain_blacklist");
        if($db->count>0){
            return 'domain already blacklist.';
        }else{
            $id = $db->insert("x_domain_blacklist", array('domain' => $domain, 'keterangan' => $ket));
            if($id){
                $this->block($domain);
                return 'success add blacklist.';
            }else{
                return 'error add blacklist.';
            }
        }
    }

    function block($domain){
        global $app;
        $db = $app->db;
        $db->where("domain = ?",array($domain));
        $site = $db->getone("x_site");
        if($db->count>0){
            $site_id = $site['id'];
            $sites = new sites();
            $sites->delete_sites($site_id);
            $db->where("site_id = ?",array($site_id));
            $campaign = $db->get("x_campaign");
            if( $db->count > 0){
                foreach($campaign as $k=>$v){
                    $sites->delete_campaign($v['id']);
                }
                $db->where("site_id = ?",array($site_id));
                $db->delete("x_campaign");
                $db->where("id",$site_id);
                $db->delete("x_site");
            }    
            return 'domain success block.';
        }else{
            return 'domain not found.';
        }
    }
}