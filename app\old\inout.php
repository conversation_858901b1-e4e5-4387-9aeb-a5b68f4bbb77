<?php
class inout 
{
	public function saldo()
    {
		global $keya, $c_time, $app;
		$db = $app->db;
		$db->orderBy("id", "desc");
        $db->where('status = ? and type = 1', array(1));
		$pendapatan = $db->getValue ("x_inout", "SUM(nominal)");
		$db->orderBy("id", "desc");
        $db->where('status = ? and type = 0', array(1));
		$pengeluaran = $db->getValue ("x_inout", "SUM(nominal)");
		return ($pendapatan - $pengeluaran);
        //$db->orderBy("id", "desc");
//        $db->where('status = ?', array(1));
//        $x = $db->getone("x_inout");
//        if ($db->count == 0) {
//            return 0;
//        } else {
//            return $x["saldo"];
//        }
    }
	
    public function add($tanggal, $keterangan, $nominal, $type, $status)
    {
		global $keya, $c_time, $app;
		$data["tanggal"] = $tanggal;
		$data["keterangan"] = $keterangan;
		$data["nominal"]     = $nominal;
		$data["type"]     = $type;
		$data["status"]     = $status;
		if($type ==1){
			$data["saldo"]      = $this->saldo() + $nominal;
		}else{
			$data["saldo"]      = $this->saldo() - $nominal;
		}  
		if($app->db->insert("x_inout", $data)){
			$ret["code"] = 1;
			$ret["saldo"] = $this->saldo();
			$ret["msg"] = "sukses tambah"; 
			return $ret;
		}else{
			$ret["code"] =0;  
			$ret["msg"] = "gagal tambah"; 
			return $ret;
		}		
        
    }   
	
	public function get($start, $end, $type=''){
		global $keya, $c_time, $app;
		$db = $app->db;
		if($type != ''){
			$db->where("type = ?", array($type));
		}
    	if($start !='' && $end != ''){
			$db->where("DATE(tanggal) BETWEEN ? AND ?", array($start, $end));  
		}	
		$db->orderBy("tanggal", "asc");
		$res = $db->get ("x_inout");  
		$pos = array();$i = 0;
		foreach($res as $r){
			$pos[$i]['id']= $r["id"];
            $pos[$i]['tanggal'] = $r['tanggal'];
            $pos[$i]['keterangan'] = $r['keterangan'];
            $pos[$i]['nominal'] = $r['nominal'];
            $pos[$i]['saldo'] = $r['saldo'];
			$pos[$i]['type'] = $r['type'];
			$pos[$i]['status'] = $r['status'];
			$i++;
		}
    	return $pos;
    }
	
	public function hapus($id){
		global $keya, $c_time, $app;
		$db = $app->db;
		$db->where("id = ?", array($id));
        if ($db->delete('x_inout')) {
            $res["code"] = 1;
			$res["msg"] = "success delete";
        } else {
            $res["code"] = 0;
			$res["msg"] = "error delete";
        }
		return $res;
	}
}
