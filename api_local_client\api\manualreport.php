<?php
function process($param){    
    extract($param);
    if (!is_array($data)) {
        $data = explode("\n", $data);
    }
    assign_child_db($project_id);
    $data_array = is_array($data) ? $data : explode("\n", $data);
    $mr = new ManualReport();
    if($event == "prospek"){
        $res_event = $mr->addProspek(implode("\n", $data));
    }else if($event == "lead"){
        $res_event = $mr->addLead(implode("\n", $data));
    }else if($event == "purchase"){
        $res_event = $mr->addPurchase(implode("\n", $data));
    }else{
        die();
    }
    $x_post["act"] = 'manualreport_update';
    $x_post["project_id"] = $project_id;
    $x_post["event"] = $event;
    $x_post["prosess"] = $batch;
    $x_post["data"] = is_array($data) ? count($data) : substr_count($data, "\n") + 1;
    $x_post["id"] = $r_id;  
    $rex = post_x_contents($x_post, 'http://143.198.91.203/api.html');
    $msg["code"] = 1;
    $msg["msg"] = "sukses";
    $msg["data"] = $res_event;
    return $msg;  
}

function get_setting($param)
{

    extract($param);
    assign_child_db($project_id);

    $mr = new ManualReport();
    $msg["code"] = 1;
    $msg["data"] = $mr->getFormat();
    $msg["msg"] = "sukses";

    return $msg;
}


function prosess_quee($param)
{
    $mr = new ManualReport();
    $msg = $mr->prosess_quee();
    return $msg;
}

function get_project_quee($param)
{
    $rules = [
        "project_id" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $mr = new ManualReport();
    $msg["code"] = 1;
    $msg["data"] = $mr->get_project_quee($project_id);
    $msg["msg"] = "sukses";

    return $msg;
}

function add_lead($param)
{
    $rules = [
        "data" => "required",
        "project_id" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $mr = new ManualReport();

   
   
    

    $msg["code"] = 1;
    //$msg["data"] = $mr->addLead($data);

    $msg["data"] =  $mr->addquee($project_id,$data,"lead");
    $msg["msg"] = "sukses";

    return $msg;
}

function add_prospek($param)
{
    $rules = [
        "data" => "required",
        "project_id" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $mr = new ManualReport();
    $msg["code"] = 1;
    //$msg["data"] = $mr->addProspek($data);
    $msg["data"] =  $mr->addquee($project_id,$data,"prospek");
    $msg["msg"] = "sukses";

    return $msg;
}

function add_purchase($param)
{
    $rules = [
        "data" => "required",
        "project_id" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $mr = new ManualReport();
    $msg["code"] = 1;
   // $msg["data"] = $mr->addPurchase($data);
    $msg["data"] =  $mr->addquee($project_id,$data,"purchase");
    $msg["msg"] = "sukses";

    return $msg;
}

function history($param)
{
    $rules = [
        "project_id" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    global $app;
    $db2 = $app->db2;

    $db2->orderBy("time","desc");
    $ret = $db2->get("manual_report_history",array(0,50),"input,event,time,result");
/*
        $result["event"] = "Prospek";
        $result["time"] = date("Y-m-d H:i:s");
        $result["result"] = "Add Prospek Success ID 101.xe";
        $ret[] = $result;

        $result["event"] = "Prospek";
        $result["time"] = date("Y-m-d H:i:s");
        $result["result"] = "Add Prospek Success ID 102.xe";
        $ret[] = $result;
*/
        return $ret;
}