<?php  
function post_flip($fields){
    $fields = http_build_query($fields);
    $header[0] = "Accept-Language: en";
    $header[] = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
    $header[] = "Content-Type: application/x-www-form-urlencoded";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://pay.gass.co.id/api.html');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
    $data = curl_exec($ch);
    curl_close($ch);
    return json_decode($data, true);
}

function isBot() {
    if(isset($_SERVER['HTTP_USER_AGENT']) && preg_match('/bot|crawl|slurp|spider|mediapartners/i', $_SERVER['HTTP_USER_AGENT'])){
        return true;
    }else{
        return false;
    }
}

function validDom($url) {
    if(strpos($url, 'http://') === false)
        $url = 'http://'.$url; 
    elseif(strpos($url, 'https://') === false)
        $url = 'https://'.$url; 
    $domain = parse_url($url,PHP_URL_HOST);
    if(filter_var($domain,FILTER_VALIDATE_DOMAIN)){
        return $domain;
    }else{
        return false;
    }
}

function get_icon_bank($kode){
	$bank = array("BCAVA" => 'https://tripay.co.id/images/payment-channel/ytBKvaleGy1605201833.png',
"MANDIRIVA" => 'https://tripay.co.id/images/payment-channel/T9Z012UE331583531536.png',
"BRIVA" => 'https://tripay.co.id/images/payment-channel/8WQ3APST5s1579461828.png',
"BNIVA" => 'https://tripay.co.id/images/payment-channel/n22Qsh8jMa1583433577.png',
"PERMATAVA" => 'https://tripay.co.id/images/payment-channel/szezRhAALB1583408731.png',
"MYBVA" => 'https://tripay.co.id/images/payment-channel/ZT91lrOEad1582929126.png',
"SMSVA" => 'https://tripay.co.id/images/payment-channel/KHcqcmqVFQ1607091889.png',
"MUAMALATVA" => 'https://tripay.co.id/images/payment-channel/GGwwcgdYaG1611929720.png',
"CIMBVA" => 'https://tripay.co.id/images/payment-channel/WtEJwfuphn1614003973.png',
"ALFAMART" => 'https://tripay.co.id/images/payment-channel/jiGZMKp2RD1583433506.png',
"QRISC" => 'https://tripay.co.id/images/payment-channel/BpE4BPVyIw1605597490.png');
	return $bank[$kode];
}
function post_tripay($fields, $url, $apiKey){
	$curl = curl_init();
	curl_setopt_array($curl, array(
	  CURLOPT_FRESH_CONNECT     => true,
	  CURLOPT_URL               => $url,
	  CURLOPT_RETURNTRANSFER    => true,
	  CURLOPT_HEADER            => false,
	  CURLOPT_HTTPHEADER        => array(
		"Authorization: Bearer ".$apiKey
	  ),
	  CURLOPT_FAILONERROR       => false,
	  CURLOPT_POST              => true,
	  CURLOPT_POSTFIELDS        => http_build_query($fields)
	));
	$response = curl_exec($curl);
	$err = curl_error($curl);
	curl_close($curl);
	return !empty($err) ? $err : $response;
}

function to_wa($site_id,$campaign_id = NULL,$method_pembagian = NULL,$campaign = NULL, $id = NULL){
    global $app;
    $db = $app->db;
    $db2 = $app->db2;
        $table_campaign = $site_id . "_campaign";
       // $db2->where("campaign_id",$campaign_id);
       // $campaign = $db2->getone($table_campaign);
        
    
        if ($method_pembagian == "closing_rate") {
            $cur_cs = get_cs_by_closing_rate($site_id,$campaign_id);
            $cs_id = $cur_cs["id"];
        }
        elseif ($method_pembagian == "omset_rate") {
            $cur_cs = get_cs_by_omset_rate($site_id,$campaign_id);
            $cs_id = $cur_cs["id"];
        }
        elseif ($method_pembagian == "random") {
            $cur_cs = get_cs_by_random($site_id,$campaign_id);
            $cs_id = $cur_cs["id"];
        }
        else{
            $cur_cs = get_cs_by_urut_kontak($site_id,$campaign_id);
            $cs_id = $cur_cs["id"];
        }
        $phone_plus = $cur_cs["nope"];
        $name= $cur_cs["name"];
        setcookie("cs", $cs_id, time()+60*60*24*100, "/");
        ///////// end get cs
        $msg = $campaign["sapaan"];
        /*
        if(isset($id)){
            $msg = "ID%20[".$id."]\n\n".$msg;
        }
        $msg = str_replace("\n", "%0a", $msg);
        $msg = str_replace(" ", "%20", $msg);
        
        $msg = trim(preg_replace('/\s+/', '', $msg));
        */
        $msg = "ID [".$id."]\n\n" . $msg;
        $msg = urlencode($msg);
        $phone = preg_replace('/\D/', '', $phone_plus);
        if(substr($phone, 0,2) == "62")
        {
          $phone = 0 . substr($phone, 2);
        }
        $phone = chunk_split($phone, 4, ' ');
        $ret["name"] = $name;
        $ret["phone"] = $phone;
        $ret["phone_plus"] = $phone_plus;
        $ret["msg"] = $msg;
        return $ret;
        //////////////////////////////////////
}
function get_browser_name($user_agent){
    $t = strtolower($user_agent);
    $t = " " . $t;
    if     (strpos($t, 'opera'     ) || strpos($t, 'opr/')     ) return 'Opera'            ;   
    elseif (strpos($t, 'edge'      )                           ) return 'Edge'             ;   
    elseif (strpos($t, 'chrome'    )                           ) return 'Chrome'           ;   
    elseif (strpos($t, 'safari'    )                           ) return 'Safari'           ;   
    elseif (strpos($t, 'firefox'   )                           ) return 'Firefox'          ;   
    elseif (strpos($t, 'msie'      ) || strpos($t, 'trident/7')) return 'Internet Explorer';
    return 'Unkown';
}
function isMobile()
{
    return preg_match(
        "/(android|avantgo|blackberry|bolt|boost|cricket|docomo|fone|hiptop|mini|mobi|palm|phone|pie|tablet|up\.browser|up\.link|webos|wos)/i",
        $_SERVER["HTTP_USER_AGENT"]
    );
}
function urut($limit)
{
    $file = "tmp1.txt";
    if (!is_file($file)) {
        $contents = "0"; // Some simple example content.
        file_put_contents($file, $contents); // Save our content to the file.
    }
    $x = file_get_contents($file);
    $file = fopen($file, "w+");
    // exclusive lock
    if (flock($file, LOCK_EX)) {
        $x++;
        if ($x >= $limit) {
            $x = 0;
        }
        fwrite($file, $x);
        // release lock
        flock($file, LOCK_UN);
    } else {
        //echo "Error locking file!";
    }
    fclose($file);
    return $x;
}
function getRandomWeightedElement(array $weightedValues)
{
    $rand = mt_rand(1, (int) array_sum($weightedValues));
    foreach ($weightedValues as $key => $value) {
        $rand -= $value;
        if ($rand <= 0) {
            return $key;
        }
    }
    die();
}

function get_cs_by_random($site_id,$campaign_id = null){
    global $app;
    $db = $app->db;
    $db2 = $app->db2;
    $table_name_cs = $site_id . "_cs";
    $table_cs = $site_id . "_cs";
    $table_cs_log = $site_id . "_cs_log";
    $table_campaign_cs = $site_id . "_campaign_cs";
   
    if($campaign_id == NULL)
    {
        $q = "SELECT {$table_cs}.`id`,{$table_cs}.`name`,{$table_cs}.`nope`,{$table_cs}.`status` FROM `{$table_campaign_cs}` join `{$table_cs}` on `{$table_campaign_cs}`.`cs_id` = `{$table_cs}`.`id`
where  `{$table_cs}`.`status` = 1 order by `{$table_cs}`.`last_contact` asc LIMIT 1;";
    }
    else{
        $q = "SELECT {$table_cs}.`id`,{$table_cs}.`name`,{$table_cs}.`nope`,{$table_cs}.`status` FROM `{$table_campaign_cs}` join `{$table_cs}` on `{$table_campaign_cs}`.`cs_id` = `{$table_cs}`.`id`
        where `{$table_campaign_cs}`.`campaign_id` = {$campaign_id} and `{$table_cs}`.`status` = 1 order by rand() asc LIMIT 1;";
    }
    

        $res = $db2->rawQuery( $q);
       
       if($res != NULL)
       {
        return $res[0];
       }
       else
       {
        return false;
       }
    
   
}

function get_cs_by_urut_kontak($site_id,$campaign_id = null){
    global $app;
    $db = $app->db;
    $db2 = $app->db2;
    $table_name_cs = $site_id . "_cs";
    $table_cs = $site_id . "_cs";
    $table_cs_log = $site_id . "_cs_log";
    $table_campaign_cs = $site_id . "_campaign_cs";
   
    if($campaign_id == NULL)
    {
        $q = "SELECT {$table_cs}.`id`,{$table_cs}.`name`,{$table_cs}.`nope`,{$table_cs}.`status` FROM `{$table_campaign_cs}` join `{$table_cs}` on `{$table_campaign_cs}`.`cs_id` = `{$table_cs}`.`id`
where  `{$table_cs}`.`status` = 1 order by `{$table_cs}`.`last_contact` asc LIMIT 1;";
    }
    else{
        $q = "SELECT {$table_cs}.`id`,{$table_cs}.`name`,{$table_cs}.`nope`,{$table_cs}.`status` FROM `{$table_campaign_cs}` join `{$table_cs}` on `{$table_campaign_cs}`.`cs_id` = `{$table_cs}`.`id`
        where `{$table_campaign_cs}`.`campaign_id` = {$campaign_id} and `{$table_cs}`.`status` = 1 order by `{$table_cs}`.`last_contact` asc LIMIT 1;";
    }
    

        $res = $db2->rawQuery( $q);
       
       if($res != NULL)
       {
        return $res[0];
       }
       else
       {
        return false;
       }
    
   
}

function get_cs_by_rata_kontak($site_id,$campaign_id = null){
    global $app;
    $db = $app->db;
    $db2 = $app->db2;
    $table_name_cs = $site_id . "_cs";
    $table_cs = $site_id . "_cs";
    $table_cs_log = $site_id . "_cs_log";
    $table_campaign_cs = $site_id . "_campaign_cs";
   


if($campaign_id == NULL)
{
    $q = "SELECT {$table_cs}.`id`,{$table_cs}.`name`,{$table_cs}.`nope`,{$table_cs}.`status`  FROM {$table_campaign_cs} 
    left join (select * from {$table_cs_log} where {$table_cs_log}.`tanggal` = CURRENT_DATE) as cs_log on `cs_log`.`cs_id` = {$table_campaign_cs}.`cs_id`
    join {$table_cs} on {$table_cs}.`id` = {$table_campaign_cs}.`cs_id`
    WHERE  {$table_cs}.`status` = 1  
    ORDER BY `cs_log`.`contact` ASC
    LIMIT 1;";
}
else{
    $q = "SELECT {$table_cs}.`id`,{$table_cs}.`name`,{$table_cs}.`nope`,{$table_cs}.`status`  FROM {$table_campaign_cs} 
    left join (select * from {$table_cs_log} where {$table_cs_log}.`tanggal` = CURRENT_DATE) as cs_log on `cs_log`.`cs_id` = {$table_campaign_cs}.`cs_id`
    join {$table_cs} on {$table_cs}.`id` = {$table_campaign_cs}.`cs_id`
    WHERE  {$table_campaign_cs}.`campaign_id`  = {$campaign_id} and {$table_cs}.`status` = 1  
    ORDER BY `cs_log`.`contact` ASC
    LIMIT 1;";
}

        $res = $db2->rawQuery( $q);
       
       if($res != NULL)
       {
        return $res[0];
       }
       else
       {
        return false;
       }
    
}

function get_cs_by_rata($site_id,$campaign_id = null){
    global $app;
    $db = $app->db;
    $db2 = $app->db2;
    $table_name_cs = $site_id . "_cs";
    $table_cs = $site_id . "_cs";
    $table_campaign_cs = $site_id . "_campaign_cs";
    if ($campaign_id == null) {
        $res = $db2->rawQuery( "select count(*) as count from " .$table_name_cs ." where status = 1");
        $count = $res[0]["count"];
        $key = urut($count);
        $q = "Select * from {$table_name_cs}  where status = 1";
        $cur_cs = $db2->rawQuery($q);
        $cur_cs = $cur_cs[$key];
    } else {
        $q = "SELECT count(*) as count FROM {$table_campaign_cs} as c join {$table_cs} as cs on c.cs_id = cs.id WHERE  c.campaign_id = ? and cs.status = 1";
        $res = $db2->rawQuery($q,array($campaign_id));
        if(isset($res[0]["count"])){
            $count = $res[0]["count"];
            if($count == 0)
            {
                echo "no cs available";
                die();
            }
        }        
        $key = urut($count);
        $q = "SELECT * FROM {$table_campaign_cs} as c join {$table_cs} as cs on c.cs_id = cs.id WHERE  c.campaign_id = ? and cs.status = 1";
        $cur_cs = $db2->rawQuery($q, [$campaign_id]);
        $cur_cs = $cur_cs[$key];
    }
    return $cur_cs;
}
function get_cs_by_closing_rate($site_id,$campaign_id = null, $day_data_range = 7)
{
    global $app;
    $db = $app->db;
    $db2 = $app->db2;
    $table_cs = $site_id . "_cs";
    $table_campaign_cs = $site_id . "_campaign_cs";
    $table_cs_log = $site_id . "_cs_log";
    if ($campaign_id != null) {
        $q =
            "SELECT {$table_cs}.id,{$table_cs}.name,{$table_cs}.nope,sum({$table_cs_log}.contact) as contact,sum({$table_cs_log}.purchase) as purchase , 
  sum({$table_cs_log}.purchase) / sum({$table_cs_log}.contact) * 100 as closing_rate  FROM `{$table_cs}` left join 
    (select * from {$table_cs_log} where  tanggal >= DATE_ADD(CURDATE(), INTERVAL - " .
            $day_data_range .
            " DAY))  as {$table_cs_log}
  on {$table_cs}.id = {$table_cs_log}.cs_id
  join {$table_campaign_cs} on {$table_cs}.id = {$table_campaign_cs}.cs_id
where {$table_cs}.status = 1  and {$table_campaign_cs}.campaign_id = ?
group by {$table_cs}.id
order by closing_rate desc
";
        $q2 =
            "select * from (SELECT {$table_cs}.id,{$table_cs}.name,{$table_cs}.nope,sum({$table_cs_log}.contact) as contact,sum({$table_cs_log}.purchase) as purchase , 
  sum({$table_cs_log}.purchase) / sum({$table_cs_log}.contact) * 100 as closing_rate  FROM `{$table_cs}` left join 
    (select * from {$table_cs_log} where  tanggal >= DATE_ADD(CURDATE(), INTERVAL - " .
            $day_data_range .
            " DAY))  as {$table_cs_log}
  on {$table_cs}.id = {$table_cs_log}.cs_id
  join {$table_campaign_cs} on {$table_cs}.id = {$table_campaign_cs}.cs_id
where {$table_cs}.status = 1  and {$table_campaign_cs}.campaign_id = ?
group by {$table_cs}.id
order by closing_rate asc)  as x where x.closing_rate > 0
";
        $res = $db2->rawQuery($q, [$campaign_id]);
        $res2 = $db2->rawQuery($q2, [$campaign_id]);
    } else {
        $q =
            "SELECT {$table_cs}.id,{$table_cs}.name,{$table_cs}.nope,sum({$table_cs_log}.contact) as contact,sum({$table_cs_log}.purchase) as purchase , 
  sum({$table_cs_log}.purchase) / sum({$table_cs_log}.contact) * 100 as closing_rate  FROM `{$table_cs}` left join 
    (select * from {$table_cs_log} where  tanggal >= DATE_ADD(CURDATE(), INTERVAL - " .
            $day_data_range .
            " DAY))  as {$table_cs_log}
  on {$table_cs}.id = {$table_cs_log}.cs_id
where {$table_cs}.status = 1 
group by {$table_cs}.id
order by closing_rate desc
";
        $q2 =
            "select * from (SELECT {$table_cs}.id,{$table_cs}.name,{$table_cs}.nope,sum({$table_cs_log}.contact) as contact,sum({$table_cs_log}.purchase) as purchase , 
  sum({$table_cs_log}.purchase) / sum({$table_cs_log}.contact) * 100 as closing_rate  FROM `{$table_cs}` left join 
    (select * from {$table_cs_log} where  tanggal >= DATE_ADD(CURDATE(), INTERVAL - " .
            $day_data_range .
            " DAY))  as {$table_cs_log}
  on {$table_cs}.id = {$table_cs_log}.cs_id
where {$table_cs}.status = 1 
group by {$table_cs}.id
order by closing_rate asc)  as x where x.closing_rate > 0
";
        $res = $db2->rawQuery($q, [$campaign_id]);
        $res2 = $db2->rawQuery($q2, [$campaign_id]);
    }
    if ($res[0]["closing_rate"] != null) {
        $max["closing_rate"] = $res[0]["closing_rate"];
        if (isset($res2[0]["closing_rate"])) {
            $default["closing_rate"] = $res2[0]["closing_rate"] / 2;
        } else {
            $default["closing_rate"] = 1;
        }
    } else {
        $max["closing_rate"] = 1;
        $default["closing_rate"] = 1;
    }
    if ($default["closing_rate"] == 0) {
        $default["closing_rate"] = 1;
    }
    $xa = [];
    foreach ($res as $key => $value) {
        $cs_id = $value["id"];
        if ($value["closing_rate"] < 1) {
            $val = $default["closing_rate"];
        } else {
            $val = $value["closing_rate"];
        }
        $xa[$cs_id] = (int) ceil($val);
    }
    $cur_cs = getRandomWeightedElement($xa);
    $db2->where("id", $cur_cs);
    $cur_cs = $db2->getone($table_cs);
    return $cur_cs;
}
function get_cs_by_omset_rate($site_id,$campaign_id = null, $day_data_range = 7)
{
    global $app;
    $db = $app->db;
    $db2 = $app->db2;
    $table_cs = $site_id . "_cs";
    $table_campaign_cs = $site_id . "_campaign_cs";
    $table_cs_log = $site_id . "_cs_log";
    if ($campaign_id != null) {
        $q =
            "SELECT {$table_cs}.id,{$table_cs}.name,{$table_cs}.nope,sum({$table_cs_log}.contact) as contact,sum({$table_cs_log}.purchase) as purchase , 
  sum({$table_cs_log}.value) / sum({$table_cs_log}.contact) * 100 as omset_rate  FROM `{$table_cs}` left join 
    (select * from {$table_cs_log} where  tanggal >= DATE_ADD(CURDATE(), INTERVAL - " .
            $day_data_range .
            " DAY))  as {$table_cs_log}
  on {$table_cs}.id = {$table_cs_log}.cs_id
   join {$table_campaign_cs} on {$table_cs}.id = {$table_campaign_cs}.cs_id
where {$table_cs}.status = 1 and {$table_campaign_cs}.campaign_id = ?
group by {$table_cs}.id
order by omset_rate desc
";
        $q2 =
            "select * from (SELECT {$table_cs}.status,{$table_cs}.id,{$table_cs}.name,{$table_cs}.nope,sum({$table_cs_log}.contact) as contact,sum({$table_cs_log}.purchase) as purchase , 
  sum({$table_cs_log}.value) / sum({$table_cs_log}.contact) * 100 as omset_rate  FROM `{$table_cs}` left join 
    (select * from {$table_cs_log} where  tanggal >= DATE_ADD(CURDATE(), INTERVAL - " .
            $day_data_range .
            " DAY))  as {$table_cs_log}
  on {$table_cs}.id = {$table_cs_log}.cs_id
   join {$table_campaign_cs} on {$table_cs}.id = {$table_campaign_cs}.cs_id
where {$table_cs}.status = 0 and {$table_campaign_cs}.campaign_id = ?
group by {$table_cs}.id
order by omset_rate desc) as x where x.omset_rate > 0 and status = 1
";
        $res = $db->rawQuery($q, [$campaign_id]);
        $res2 = $db->rawQuery($q2, [$campaign_id]);
    } else {
        $q =
            "SELECT {$table_cs}.id,{$table_cs}.name,{$table_cs}.nope,sum({$table_cs_log}.contact) as contact,sum({$table_cs_log}.purchase) as purchase , 
  sum({$table_cs_log}.value) / sum({$table_cs_log}.contact) * 100 as omset_rate  FROM `{$table_cs}` left join 
    (select * from {$table_cs_log} where  tanggal >= DATE_ADD(CURDATE(), INTERVAL - " .
            $day_data_range .
            " DAY))  as {$table_cs_log}
  on {$table_cs}.id = {$table_cs_log}.cs_id
where {$table_cs}.status = 1 
group by {$table_cs}.id
order by omset_rate desc
";
        $q2 =
            "select * from (SELECT {$table_cs}.status,{$table_cs}.id,{$table_cs}.name,{$table_cs}.nope,sum({$table_cs_log}.contact) as contact,sum({$table_cs_log}.purchase) as purchase , 
  sum({$table_cs_log}.value) / sum({$table_cs_log}.contact) * 100 as omset_rate  FROM `{$table_cs}` left join 
    (select * from {$table_cs_log} where  tanggal >= DATE_ADD(CURDATE(), INTERVAL - " .
            $day_data_range .
            " DAY))  as {$table_cs_log}
  on {$table_cs}.id = {$table_cs_log}.cs_id
where {$table_cs}.status = 1
group by {$table_cs}.id
order by omset_rate desc) as x where x.omset_rate > 0 and status = 1
";
        $res = $db->rawQuery($q);
        $res2 = $db->rawQuery($q2);
    }
    if ($res[0]["omset_rate"] != null) {
        $max["omset_rate"] = $res[0]["omset_rate"];
        if (isset($res2[0]["omset_rate"])) {
            $default["omset_rate"] = $res2[0]["omset_rate"] / 2;
        } else {
            $default["omset_rate"] = 1;
        }
    } else {
        $max["omset_rate"] = 1;
        $default["omset_rate"] = 1;
    }
    if ($default["omset_rate"] == 0) {
        $default["omset_rate"] = 1;
    }
    $xa = [];
    foreach ($res as $key => $value) {
        $cs_id = $value["id"];
        $omrate = (int) $value["omset_rate"];
        if ($omrate < 1) {
            $val = $default["omset_rate"];
        } else {
            $val = $value["omset_rate"];
        }
        $xa[$cs_id] = (int) ceil($val);
    }
    $cur_cs = getRandomWeightedElement($xa);
    $db2->where("id", $cur_cs);
    $cur_cs = $db2->getone($table_cs);
    return $cur_cs;
}
function post_gateway($fields, $url){
    
    $fields = http_build_query($fields);
    $header[0] = "Accept-Language: en";
    $header[] = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
    $header[] = "Content-Type: application/x-www-form-urlencoded";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
}
function get_unik($nominal){
	global $app;
	$db = $app->db;
	$db->where("status",2,"<");
	$db->where("total",$nominal,">");
	$res =$db->get("x_order2");
	$db->where("waktu >= current_date - interval 3 day");
	$db->where("total",$nominal,">");
	$res2 =$db->get("x_order2");
	$ii = 0 ;
	$unik = 0;
	$length = count($res);
	$unik = $nominal + 1;
	for($i=0;$i < 10000 ; $i++){
		$dup = false;
		foreach ($res as $key => $value) {
			$total = $value["total"];
			if($unik == $total){
				$dup = true;
				break;
			}
		}
		foreach ($res2 as $key2 => $value2) {
			$total = $value2["total"];
			if($unik == $total){
				$dup = true;
				break;
			}
		}
		if($dup == false){
			break;
		}
		$unik++;
	}
	$ret["angka_unik"] = $unik - $nominal;
	$ret["total"] = $unik;
	return $ret;
}
function hyphenate($str)
{
    return implode("-", str_split($str, 4));
}
function str_contains($string, $array, $caseSensitive = true)
{
    $stripedString = $caseSensitive ? str_replace($array, '', $string) : str_ireplace($array, '', $string);
    return strlen($stripedString) !== strlen($string);
}
function hp($nohp){
    $hp = "";
    if ($nohp[0] != '0' && $nohp[0] != '+' && $nohp[0] != '6') {
        $nohp = "0" . $nohp;
    }
    $nohp = str_replace(" ", "", $nohp);
    $nohp = str_replace("-", "", $nohp);
    $nohp = str_replace(" ", "", $nohp);
    $nohp = str_replace("(", "", $nohp);
    $nohp = str_replace(")", "", $nohp);
    $nohp = str_replace(".", "", $nohp);
    if (!preg_match('/[^+0-9]/', trim($nohp))) {
        if (substr(trim($nohp), 0, 3) == '+62') {
            $hp = trim($nohp);
        }
        if (substr(trim($nohp), 0, 2) == '62') {
            $hp = '+' . trim($nohp);
        }elseif (substr(trim($nohp), 0, 1) == '0') {
            $hp = '+62' . substr(trim($nohp), 1);
        }
    }
    $hp = str_replace("+", "", $hp);
    return $hp;
}
function hp_x($nohp){
    $hp = "";
    $nohp = str_replace(" ", "", $nohp);
    $nohp = str_replace("-", "", $nohp);
    $nohp = str_replace(" ", "", $nohp);
    $nohp = str_replace("(", "", $nohp);
    $nohp = str_replace(")", "", $nohp);
    $nohp = str_replace(".", "", $nohp);
    $hp = str_replace("+", "", $nohp);
    return $hp;
}
function get_contents($url)
{
    if (function_exists('curl_exec')) {
        $header[0] = "Accept-Language: en";
        $header[]  = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
        $header[]  = "Pragma: no-cache";
        $header[]  = "Cache-Control: no-cache";
        $header[]  = "Accept-Encoding: gzip,deflate";
        $header[]  = "Content-Encoding: gzip";
        $header[]  = "Content-Encoding: deflate";
        $ch        = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_ENCODING, 'gzip');
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        curl_setopt($ch, CURLOPT_REFERER, '');
        $data = curl_exec($ch);
        curl_close($ch);
    } else {
        $data = @file_get_contents($url);
    }
    return $data;
}
function replaceTXT($txt){
	$txt = str_replace("WAHANA","",strtolower($txt));
	$txt = str_replace("wahana","",strtolower($txt));
	$txt = str_replace("JNE","",strtolower($txt));
	$txt = str_replace("jne","",strtolower($txt));
	$txt = str_replace("JNT","",strtolower($txt));
	$txt = str_replace("jnt","",strtolower($txt));
	$txt = str_replace("agen wpl","",strtolower($txt));
	$txt = str_replace("aramex","",strtolower($txt));
	$txt = str_replace("TNT","",strtolower($txt));
	$txt = str_replace("tnt","",strtolower($txt));
	$txt = str_replace("dhl","",strtolower($txt));
	return ucfirst($txt);
}
function post_contents_api($fields)
{
    //$header[0] = "Accept-Language: en";
    // $header[]  = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
    // $header[]  = "Pragma: no-cache";
    //$header[]  = "Cache-Control: no-cache";
    //$header[]  = "Accept-Encoding: gzip,deflate";
    //$header[]  = "Content-Encoding: gzip";
    // $header[]  = "Content-Encoding: deflate";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://hiroexpress.com/apiv2");
    curl_setopt($ch, CURLOPT_POST, true);
    // curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    // curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
}
function post_contents($url, $post, $referer='', $host='') {
	$header[0] = "Accept-Language: en";
	$header[] = "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8";
	$header[] = "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:70.0) Gecko/20100101 Firefox/70.0";
	if($host) $header[] = "Host: ".$host;
	$header[] = "Pragma: no-cache";
	$header[] = "Accept-Encoding: gzip,deflate";
	$header[] = "Content-Type: text/html; charset=UTF-8";
	$ch = curl_init();
	curl_setopt ($ch, CURLOPT_URL, $url);
	if($referer) curl_setopt($ch, CURLOPT_REFERER, $referer);
	
	curl_setopt ($ch, CURLOPT_COOKIEJAR, 'test.dat');
	curl_setopt ($ch, CURLOPT_COOKIEFILE, 'test.dat');
	curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
	curl_setopt ($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
	curl_setopt($ch, CURLOPT_POST, true);
	curl_setopt($ch, CURLOPT_POSTFIELDS, is_array($post) ? http_build_query($post) : $post);
	$result = curl_exec ($ch);
	curl_close($ch);
	print_r($ch);
	return $result;
}
function post_x_contents($fields, $url){
	$header[0] = "Accept-Language: en";
	$header[] = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
	$header[] = "Pragma: no-cache";
	$header[] = "Cache-Control: no-cache";
	$header[] = "Accept-Encoding: gzip,deflate";
	$header[] = "Content-Encoding: gzip";
	$header[] = "Content-Encoding: deflate";
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_POST, true);
	curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
	$data = curl_exec($ch);
	curl_close($ch);
	return $data;
}
function send_klikwa($to, $caption, $auth = true){
	$data = array(
		"jid" => "6289682544347",
		"remotejid" => $to,
		"msgtext"=> $caption,
		);
	$data_string = json_encode($data);
	$header[0] = "User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:87.0) Gecko/20100101 Firefox/87.0";
	if($auth==true){
		$tkn = file_get_contents('.tokenklikwa.json');
		$header[] = "Authorization:Bearer ".$tkn;
	}	
	$header[] = "Host:apikwt1.klikwa.net";
	$header[] = "Referer:https://chat.klikwa.net/";
	$header[] = "Origin:https://chat.klikwa.net";
	$header[] = "Content-Type:application/json";
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, 'https://apikwt1.klikwa.net/v1/sendtextwa');
	curl_setopt($ch, CURLOPT_POST, true);
	curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
	$data = curl_exec($ch);
	curl_close($ch);
	return $data;
}
function login_klikwa(){
	$data = array(
		"email" => "<EMAIL>",
		"password" => "123456Aaxx",
		);
	$data_string = json_encode($data);
	$header[0] = "User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:87.0) Gecko/20100101 Firefox/87.0";
	$header[] = "Referer:https://chat.klikwa.net/";
	$header[] = "Origin:https://chat.klikwa.net";
	$header[] = "Content-Type:application/json";
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, 'https://provkwt.klikwa.net/v1/cslogin');
	curl_setopt($ch, CURLOPT_POST, true);
	curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
	$data = curl_exec($ch);
	curl_close($ch);
	return $data;
}
function validateEmail($email)
{
    return (preg_match("/(@.*@)|(\.\.)|(@\.)|(\.@)|(^\.)/", $email) || !preg_match("/^.+\@(\[?)[a-zA-Z0-9\-\.]+\.([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/", $email)) ? false : true;
}
function ceklogin($run)
{
    $xpath = $run['site']["type"];
    if ($run['site']["type"] == 'page') {
        $xpath = $run['tag']["title"];
    }
    if (isset($_COOKIE["token"]) || isset($_SESSION["token"])) {
        if ($xpath == 'login' || $xpath == 'register') {
            header('Location: /panel/dashboard.html');
        }
        if (!isset($run['profile']['uid'])) {
            $_SESSION['redirect'] = $run['config']["url"];
            header('Location: /panel/logout.html');
        }
	//	if (!isset($run['profile']['awb']) && $xpath != 'profile' && $run['profile']['admin'] == true) {
//			 header('Location: /profile.html');
//		}
    } else {
        if ($xpath !== 'login' && $xpath !== 'register' && $xpath !== 'home') {
            $_SESSION['redirect'] = $run['config']["url"];
            header('Location: /panel/login.html');
        }
    }
}
function cekloginadmin($xpath)
{
	global $app;
    if (isset($_COOKIE["token"]) || isset($_SESSION["token"])) {
        if ($xpath == 'login' || $xpath == 'register') {
            header('Location: /panel/admin-dashboard.html');
        }
        if (!isset($app->profile['token'])) {
            $_SESSION['redirect'] = $run['config']["url"];
            header('Location: /panel/admin-logout.html');
        }
		if ($app->profile['role'] != 99) {
			logout();
			sleep(2);
			header('Location: /panel/admin-login.html');
		}
    } else {
        if ($xpath !== 'login' && $xpath !== 'register' && $xpath !== 'home') {
            $_SESSION['redirect'] = $run['config']["url"];
            header('Location: /panel/admin-login.html');
        }
    }
}
function logout()
{
    session_destroy();
    if (isset($_SERVER['HTTP_COOKIE'])) {
        $cookies = explode(';', $_SERVER['HTTP_COOKIE']);
        foreach ($cookies as $cookie) {
            $parts = explode('=', $cookie);
            $name  = trim($parts[0]);
            setcookie($name, '', time() - 1000);
            setcookie($name, '', time() - 1000, '/');
        }
    }
}
function buatrp($angka)
{
    $jadi = "Rp " . number_format($angka, 2, ',', '.');
    return $jadi;
}
function debug($object)
{
    global $smarty;
    $smarty->assign("debug", nl2br(print_r($object, true)));
}
function trim_all($str, $what = null, $with = ' ')
{
    if ($what === null) {
        //  Character      Decimal      Use
        //  "\0"            0           Null Character
        //  "\t"            9           Tab
        //  "\n"           10           New line
        //  "\x0B"         11           Vertical Tab
        //  "\r"           13           New Line in Mac
        //  " "            32           Space
        $what = "\\x00-\\x20"; //all white-spaces and control chars
    }
    $x = trim(preg_replace("/[" . $what . "]+/", $with, $str));
    return $x;
}
function cekp($name)
{
    if (isset($_POST[$name])) {return true;} else {return false;}
}
function p($name)
{
    return $_POST[$name];
}
function cekg($name)
{
    if (isset($_GET[$name])) {return true;} else {return false;}
}
function g($name)
{
    return $_GET[$name];
}
function cekf($name)
{
    if (isset($_FILES[$name])) {return true;} else {return false;}
}
function f($name)
{
    return $_FILES[$name];
}
function calcCrow($lat1, $lon1, $lat2, $lon2){
        $R = 6371; // km
        $dLat = toRad($lat2-$lat1);
        $dLon = toRad($lon2-$lon1);
        $lat1 = toRad($lat1);
        $lat2 = toRad($lat2);
        $a = sin($dLat/2) * sin($dLat/2) +sin($dLon/2) * sin($dLon/2) * cos($lat1) * cos($lat2); 
        $c = 2 * atan2(sqrt($a), sqrt(1-$a)); 
        $d = $R * $c;
        return $d;
}
// Converts numeric degrees to radians
function toRad($Value) 
{
    return $Value * pi() / 180;
}
//echo distance(32.9697, -96.80322, 29.46786, -98.53506, "M") . " Miles<br>";
//echo distance(32.9697, -96.80322, 29.46786, -98.53506, "K") . " Kilometers<br>";
//echo distance(32.9697, -96.80322, 29.46786, -98.53506, "N") . " Nautical Miles<br>";
function distance($lat1, $lon1, $lat2, $lon2, $unit) {
  if (($lat1 == $lat2) && ($lon1 == $lon2)) {
    return 0;
  }
  else {
    $theta = $lon1 - $lon2;
    $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) +  cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
    $dist = acos($dist);
    $dist = rad2deg($dist);
    $miles = $dist * 60 * 1.1515;
    $unit = strtoupper($unit);
    if ($unit == "K") {
      return ($miles * 1.609344);
    } else if ($unit == "N") {
      return ($miles * 0.8684);
    } else {
      return $miles;
    }
  }
}