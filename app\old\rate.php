<?php
class rate
{  
	public function log_ongkir($country_id, $city_id)
    {
		global $keya, $c_time, $app;
        $today = date("Y-m-d"); 
        $app->db->where('negara_id = ? and regency_id = ? and DATE(tanggal) = ?', array($country_id, $city_id, $today));
        $x = $app->db->getone("v_log_cekongkir");
        if (count($x) == 0) {
			$data["tanggal"]     = $today;
			$data["negara_id"]   = $country_id;
			$data["regency_id"]  = $city_id;
			$data["count"]       = 1;
            $app->db->insert("v_log_cekongkir", $data);
        } else {
			
			$data["tanggal"]     = $today;
			$data["negara_id"]   = $country_id;
			$data["regency_id"]  = $city_id;
			$data["count"]       = $x['count'] + 1;
			$app->db->where('negara_id = ? and regency_id = ? and DATE(tanggal) = ?', array($country_id, $city_id, $today));
            $app->db->update('v_log_cekongkir', $data);
        }
    }
	
	public function get_log_ongkir($start, $end){
		global $keya, $c_time, $app;
		$app->db->join("c_regencies b", "b.id=l.regency_id", "LEFT");
		$app->db->join("v_country n", "l.negara_id=n.country_code", "LEFT");
		$app->db->where("DATE(l.tanggal) BETWEEN ? AND ?", array($start, $end));  
		$app->db->orderBy("l.tanggal", "desc");
		
		$res = $app->db->get ("v_log_cekongkir l", NULL, "l.*, n.name as negara, b.name as kota");
		return $res;
	}
	
	public function checkRate($city, $country, $berat){
		global $keya, $c_time, $app;
		$app->db->join("c_country c", "g.negara_id=c.id", "LEFT");
		$app->db->join("v_vendor v", "g.vendor_id=v.id", "LEFT");
		$app->db->join("c_city k", "v.city_id=k.id", "LEFT");	
		$app->db->where("g.berat = ceil(?) and c.name like ? and v.status = 1",array($berat, '%'.$country.'%'));
		//$app->db->where("g.berat = ceil(?) and g.negara_id = ? and g.type = ? and v.status = 1",array($berat, $country_id, $type));
		//$app->db->where("g.berat = 1 and negara_id = ? and type = ?",array($country_id, $type));
		$app->db->groupBy ("g.vendor_id");
		$app->db->orderBy("g.price", "asc");   
		$res = $app->db->get ("v_vendor_price g", NULL, "g.*, v.*, g.id as price_id, v.nama as vendor, c.name as negara, k.name as nama_kota");
		//echo $app->db->getLastQuery();
		return $res;
	}
	
	public function check($country_id, $city_id, $berat, $type, $custemer=true, $web=false){   
		global $keya, $c_time, $app;  
		//cek ongkir agen ke werehouse
		if($web==true){
			$this->log_ongkir($country_id, $city_id);
		}		
		$kotaagen = 'Semarang';
		$phoneagen = '6285778850060';
		$kotawerehouse = 'Semarang';
		$idagen = '4f22eaa842eb31f88468ad67e9e138af';
		$alamatagent = 'Jl. Taman Suryokusumo IV No.2, RT.07/RW.20, Muktiharjo Kidul, Kec. Pedurungan, Kota Semarang, 50197';
		$app->db->join("c_districts d", "g.district_id=d.id", "LEFT");
		$app->db->join("c_regencies r", "g.regency_id=r.id", "LEFT");
		$app->db->join("c_provinces p", "g.province_id=p.id", "LEFT");
		$app->db->join("x_user u", "g.user_id=u.user_id", "LEFT");
		$app->db->where("g.regency_id = ?",array($city_id));
		$agen = $app->db->getone("v_agent g", "g.*, p.name as nama_propinsi, r.name as nama_kota, d.name as nama_kecamatan, u.nama as user_name, u.phone as user_phone, u.email as user_email");
		$ongkiragen=0;$ongkirwerehouse=0;
		if($agen){  
			$idagen = bin2hex($agen['user_id']);
			$kotaagen = $agen['nama_kota'];
			$phoneagen = '6285778850060'; 
			$alamatagent = $agen['alamat'];
			$app->db->join("c_districts d", "g.district_id=d.id", "LEFT");
			$app->db->join("c_regencies r", "g.regency_id=r.id", "LEFT");
			$app->db->join("c_provinces p", "g.province_id=p.id", "LEFT");
			$app->db->join("x_user u", "g.user_id=u.user_id", "LEFT");
			$app->db->where("g.regency_id = ?",array($city_id));
			$gudang = $app->db->getone("v_gudang g", "g.*, p.name as nama_propinsi, r.name as nama_kota, d.name as nama_kecamatan, u.nama as user_name, u.phone as user_phone, u.email as user_email");
			if($gudang){   
				$kotawerehouse = $gudang['nama_kota'];
				// cek ongkir agen ke werehouse solo				
			}else{
				$ongkiragen = $this->ongkir($agen['nama_kota'], $kotaagen, $berat);
			}		
			
		}
		
		$app->db->join("v_country c", "g.negara_id=c.country_code", "LEFT");
		$app->db->join("v_vendor v", "g.vendor_id=v.id", "LEFT");
		$app->db->join("c_districts d", "v.district_id=d.id", "LEFT");
		$app->db->join("c_regencies r", "v.regency_id=r.id", "LEFT");
		$app->db->join("c_provinces p", "v.province_id=p.id", "LEFT");		
		$app->db->where("g.berat = ceil(?) and g.negara_id = ? and v.status = 1",array($berat, $country_id));
		//$app->db->where("g.berat = ceil(?) and g.negara_id = ? and g.type = ? and v.status = 1",array($berat, $country_id, $type));
		//$app->db->where("g.berat = 1 and negara_id = ? and type = ?",array($country_id, $type));
		$app->db->groupBy ("g.vendor_id");
		$app->db->orderBy("g.price", "asc");   
		$res = $app->db->get ("v_vendor_price g", NULL, "g.*, v.*, g.id as price_id, v.nama as vendor, c.name as negara, p.name as nama_propinsi, r.name as nama_kota, d.name as nama_kecamatan");
		
		//print_r($res);
		//echo $app->db->getLastQuery();
		
		
		$pos = array();$i = 0;$hargareg =0;$hargaexpress=0;$idreg;$idexpress;$totongkir=0;$vendorprice=0;
		     
		foreach($res as $r){
			if($custemer){
				$ongkirwerehouse = $this->ongkir($kotawerehouse, $r['nama_kota'], $berat);
				$totongkir = $ongkiragen + $ongkirwerehouse;
				//$hrg = ($r['price'] * $berat);				
				$hrg = $r['price'] ;  
				$hrg = $hrg + $totongkir + ($hrg/100*30);	      						
				$hrg =ceil($hrg);
				$hrg=round($hrg,-3); 			
				$vendorprice = $r['price'] ; 
			//	if($r['service']=='REG'){					
//					if($hrg > $hargareg){
//						$hargareg = $hrg;
//						$idreg = $r['price_id'];
//					}
//				}elseif($r['service']=='Express'){
//					if($hrg  > $hargaexpress){
//						$hargaexpress = $hrg;
//						$idexpress = $r['price_id'];
//					}
//				}
				$pos[$i]["id"] = $r['price_id'];
				$pos[$i]["harga"] = $hrg;
				$pos[$i]["service"] = $r['service'];
				$pos[$i]["ongkir"] = $totongkir;
				$pos[$i]["kota_agent"] = ucwords(strtolower($kotaagen));
				$pos[$i]["phone_agent"] = $phoneagen;
				$pos[$i]["alamat_agent"] = $alamatagent;
				if($web==false){
					$pos[$i]["vendor_price"] = $vendorprice;
					$pos[$i]["margin"] = $hrg - $vendorprice -$totongkir;
				}
				$i++; 
			}else{ 				
				$pos[$i]["agent_id"] = $idagen;
				$pos[$i]["id"] = $r['price_id'];
				$pos[$i]["vendor"] = $r['vendor'];
				$pos[$i]["negara"] = $r['negara'];
				$pos[$i]["vendor_id"] = $r['vendor_id'];
				$pos[$i]["negara_id"] = $r['negara_id'];
				$pos[$i]["type"] = $r['type'];
				$pos[$i]["berat"] = $r['berat'];
				$pos[$i]["price"] = $r['price'];   
				$pos[$i]["province_id"] = $r['province_id'];
				$pos[$i]["regency_id"] = $r['regency_id'];
				$pos[$i]["district_id"] = $r['district_id'];
				$pos[$i]["nama_propinsi"] = $r['nama_propinsi'];
				$pos[$i]["nama_kota"] = $r['nama_kota'];
				$pos[$i]["nama_kecamatan"] = $r['nama_kecamatan'];
				//$ongkirwerehouse = $this->ongkir('Surakarta', $r['nama_kota'], $berat);
				$pos[$i]["harga"] = $r['price']; //+ $ongkiragen + $ongkirwerehouse + ($r['price']/100*10);
				$pos[$i]["service"] = $r['service'];
				$pos[$i]["kota_agent"] = ucwords(strtolower($kotaagen));
				$pos[$i]["phone_agent"] = $phoneagen;
				$pos[$i]["alamat_agent"] = $alamatagent;
				$i++;
			} 
		}    
		if($custemer){
			$k=0;   
			if($hargareg !=0){ 
				$pos[$k]["id"] = $idreg;
				$pos[$k]["harga"] = $hargareg;
				$pos[$k]["service"] = 'REG';
				$pos[$k]["ongkir"] = $totongkir;
				$pos[$k]["kota_agent"] = ucwords(strtolower($kotaagen));
				$pos[$k]["phone_agent"] = $phoneagen;
				$pos[$k]["alamat_agent"] = $alamatagent;
				if($web==false){
					$pos[$k]["vendor_price"] = $vendorprice;
					$pos[$k]["margin"] = $hargareg - $vendorprice -$totongkir;
				}
				$k=$k+1; 
			}
			if($hargaexpress !=0){   
				$pos[$k]["id"] = $idexpress;  
				$pos[$k]["harga"] = $hargaexpress;   
				$pos[$k]["service"] = 'Express';
				$pos[$k]["ongkir"] = $totongkir; 
				$pos[$k]["kota_agent"] = ucwords(strtolower($kotaagen));
				$pos[$k]["phone_agent"] = $phoneagen;
				$pos[$k]["alamat_agent"] = $alamatagent;
			}			
		}
    	return $pos;  
	}
	
	function ongkir($dari, $ke, $berat, $kurir='jne'){   
		$dari = str_replace("KOTA ","",$dari); 
		$dari = str_replace("KABUPATEN ","",$dari);
		$ke = str_replace("KOTA ","",$ke); 
		$ke = str_replace("KABUPATEN ","",$ke);
		$web = new WebBrowser();
		$result = $web->Process('http://www.cek-ongkir.com/search?term='.$dari);
		$from  = json_decode($result['body'], true);
		$harga = 0;
		if(count($from)>0){
			$result = $web->Process('http://www.cek-ongkir.com/search?term='.$ke);
			$to  = json_decode($result['body'], true);
			if(count($to)>0){
				$options = array(
					"postvars" => array(
						"origin" => $from[0]['id'],
						"originType" => $from[0]['type'],
						"destination" => $to[0]['id'],
						"destinationType" => $to[0]['type'],    
						"weight" => $berat,
						"courier" => $kurir,
					)   
				);

				$htmloptions = TagFilter::GetHTMLOptions();
				$result = $web->Process('http://www.cek-ongkir.com/cost', $options);
				if ($result["success"] && $result["response"]["code"] == 200){
					$html = TagFilter::Explode($result["body"], $htmloptions);
					$rt = $html->Get();
					$prices = $rt->Find("h4");
					foreach ($prices as $price)
					{
						if(strpos($price->GetInnerHTML(), 'Rp.') !== false && $harga == 0){
							$harga = str_replace("Rp.","",$price->GetInnerHTML()); 
						}
					}
				}
			}			
		}
		return $harga;
	}
}
