<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/services/recommendation_service.proto

namespace Google\Ads\GoogleAds\V18\Services\ApplyRecommendationOperation;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Parameters to use when applying Target CPA recommendation.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.services.ApplyRecommendationOperation.TargetCpaOptInParameters</code>
 */
class TargetCpaOptInParameters extends \Google\Protobuf\Internal\Message
{
    /**
     * Average CPA to use for Target CPA bidding strategy. This is a required
     * field.
     *
     * Generated from protobuf field <code>optional int64 target_cpa_micros = 3;</code>
     */
    protected $target_cpa_micros = null;
    /**
     * Optional, budget amount to set for the campaign.
     *
     * Generated from protobuf field <code>optional int64 new_campaign_budget_amount_micros = 4;</code>
     */
    protected $new_campaign_budget_amount_micros = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $target_cpa_micros
     *           Average CPA to use for Target CPA bidding strategy. This is a required
     *           field.
     *     @type int|string $new_campaign_budget_amount_micros
     *           Optional, budget amount to set for the campaign.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Services\RecommendationService::initOnce();
        parent::__construct($data);
    }

    /**
     * Average CPA to use for Target CPA bidding strategy. This is a required
     * field.
     *
     * Generated from protobuf field <code>optional int64 target_cpa_micros = 3;</code>
     * @return int|string
     */
    public function getTargetCpaMicros()
    {
        return isset($this->target_cpa_micros) ? $this->target_cpa_micros : 0;
    }

    public function hasTargetCpaMicros()
    {
        return isset($this->target_cpa_micros);
    }

    public function clearTargetCpaMicros()
    {
        unset($this->target_cpa_micros);
    }

    /**
     * Average CPA to use for Target CPA bidding strategy. This is a required
     * field.
     *
     * Generated from protobuf field <code>optional int64 target_cpa_micros = 3;</code>
     * @param int|string $var
     * @return $this
     */
    public function setTargetCpaMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->target_cpa_micros = $var;

        return $this;
    }

    /**
     * Optional, budget amount to set for the campaign.
     *
     * Generated from protobuf field <code>optional int64 new_campaign_budget_amount_micros = 4;</code>
     * @return int|string
     */
    public function getNewCampaignBudgetAmountMicros()
    {
        return isset($this->new_campaign_budget_amount_micros) ? $this->new_campaign_budget_amount_micros : 0;
    }

    public function hasNewCampaignBudgetAmountMicros()
    {
        return isset($this->new_campaign_budget_amount_micros);
    }

    public function clearNewCampaignBudgetAmountMicros()
    {
        unset($this->new_campaign_budget_amount_micros);
    }

    /**
     * Optional, budget amount to set for the campaign.
     *
     * Generated from protobuf field <code>optional int64 new_campaign_budget_amount_micros = 4;</code>
     * @param int|string $var
     * @return $this
     */
    public function setNewCampaignBudgetAmountMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->new_campaign_budget_amount_micros = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(TargetCpaOptInParameters::class, \Google\Ads\GoogleAds\V18\Services\ApplyRecommendationOperation_TargetCpaOptInParameters::class);

