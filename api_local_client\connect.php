<?php

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
 global $clientId, $clientSecret, $developerToken, $refreshToken;
$clientId = '578522907736-o86tuu7pt0cgvplme2chrus2t6qrstvf.apps.googleusercontent.com';
$clientSecret = '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';
$developerToken = '-vapQDeqr2dKG0IZ5RCg7Q';
$refreshToken = "1//06yZeN4xwdtxgCgYIARAAGAYSNwF-L9IrLAZlOC2D96WUCJu5kqtOSStmSF14_AvixLURDwVSOqoW5ANpBVlKdFkMzrEFLCV5rNI";

use Google\Auth\CredentialsLoader;
use Google\Auth\OAuth2;
use Psr\Http\Message\ServerRequestInterface;
use React\EventLoop\Loop;
use React\Http\HttpServer;
use React\Http\Message\Response;
use React\Socket\SocketServer;

use GetOpt\GetOpt;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentNames;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentParser;
use Google\Ads\GoogleAds\Lib\OAuth2TokenBuilder;
use Google\Ads\GoogleAds\Lib\V14\GoogleAdsClient;
use Google\Ads\GoogleAds\Lib\V14\GoogleAdsClientBuilder;
use Google\Ads\GoogleAds\Lib\V14\GoogleAdsException;
use Google\Ads\GoogleAds\Lib\V14\GoogleAdsServerStreamDecorator;
use Google\Ads\GoogleAds\V14\Errors\GoogleAdsError;
use Google\Ads\GoogleAds\V14\Resources\CustomerClient;
use Google\Ads\GoogleAds\V14\Services\CustomerServiceClient;
use Google\Ads\GoogleAds\V14\Services\GoogleAdsRow;
use Google\ApiCore\ApiException;
use Google\AdsApi\AdWords\AdWordsServices;
use Google\AdsApi\AdWords\AdWordsSessionBuilder;
use Google\AdsApi\AdWords\v201809\cm\CampaignService;
use Google\AdsApi\AdManager\AdManagerSessionBuilder;


    $oAuth2Credential = (new OAuth2TokenBuilder())
      ->withClientId($clientId)
      ->withClientSecret($clientSecret)
      ->withRefreshToken($refreshToken)
      ->build();
      //print_r($oAuth2Credential);
    $googleAdsClient = (new GoogleAdsClientBuilder())
        ->withDeveloperToken($developerToken)
        ->withOAuth2Credential($oAuth2Credential)
        ->build();
    
    $accessibleCustomerIds = [];
            // Issues a request for listing all customers accessible by this authenticated Google
            // account.
    $customerServiceClient = $googleAdsClient->getCustomerServiceClient();
    //print_r( $customerServiceClient);
    $accessibleCustomers = $customerServiceClient->listAccessibleCustomers();
    foreach ($accessibleCustomers->getResourceNames() as $customerResourceName) {
        //print_r($customerResourceName);
        $customer = CustomerServiceClient::parseName($customerResourceName)['customer_id'];
        //print $customer . PHP_EOL;
        $accessibleCustomerIds[] = intval($customer);
    }
    print_r($accessibleCustomerIds);
    foreach ($accessibleCustomerIds as $rootCustomerId) {
        $customerClientToHierarchy = createCustomerClientToHierarchy($rootCustomerId, $rootCustomerId);
        //print_r($customerClientToHierarchy);
        if (is_null($customerClientToHierarchy)) {
            $accountsWithNoInfo[] = $rootCustomerId;
        } else {
            $allHierarchies += $customerClientToHierarchy;
        }
    }
    if (!empty($accountsWithNoInfo)) {
        print
            'Unable to retrieve information for the following accounts which are likely '
            . 'either test accounts or accounts with setup issues. Please check the logs for '
            . 'details:' . PHP_EOL;
        foreach ($accountsWithNoInfo as $accountId) {
            print $accountId . PHP_EOL;
        }
        print PHP_EOL;
    }
    
    // Prints the hierarchy information for all accounts for which there is hierarchy info
    // available.
    foreach ($allHierarchies as $rootCustomerId => $customerIdsToChildAccounts) {
        printf(
            "The hierarchy of customer ID %d is printed below:%s",
            $rootCustomerId,
            PHP_EOL
        );
        printAccountHierarchy(
            self::$rootCustomerClients[$rootCustomerId],
            $customerIdsToChildAccounts,
            0
        );
        print PHP_EOL;
    }
function printAccountHierarchy(
        CustomerClient $customerClient,
        array $customerIdsToChildAccounts,
        int $depth
    ) {
    if ($depth === 0) {
        print 'Customer ID (Descriptive Name, Currency Code, Time Zone)' . PHP_EOL;
    }
    $customerId = $customerClient->getId();
    print str_repeat('-', $depth * 2);
    printf(
        " %d ('%s', '%s', '%s')%s",
        $customerId,
        $customerClient->getDescriptiveName(),
        $customerClient->getCurrencyCode(),
        $customerClient->getTimeZone(),
        PHP_EOL
    );

    // Recursively call this function for all child accounts of $customerClient.
    if (array_key_exists($customerId, $customerIdsToChildAccounts)) {
        foreach ($customerIdsToChildAccounts[$customerId] as $childAccount) {
            printAccountHierarchy($childAccount, $customerIdsToChildAccounts, $depth + 1);
        }
    }
}
function createCustomerClientToHierarchy(
        ?int $loginCustomerId,
        int $rootCustomerId
    ): ?array {
        
    global $clientId, $clientSecret, $developerToken, $refreshToken;
    // Creates a GoogleAdsClient with the specified login customer ID. See
    // https://developers.google.com/google-ads/api/docs/concepts/call-structure#cid for more
    // information.
    // Generate a refreshable OAuth2 credential for authentication.
    $oAuth2Credential = (new OAuth2TokenBuilder())
      ->withClientId($clientId)
      ->withClientSecret($clientSecret)
      ->withRefreshToken($refreshToken)
      ->build();
    // Construct a Google Ads client configured from a properties file and the
    // OAuth2 credentials above.
    $googleAdsClient = (new GoogleAdsClientBuilder())->withDeveloperToken($developerToken)
        ->withOAuth2Credential($oAuth2Credential)
        ->withLoginCustomerId($rootCustomerId)
        ->build();

    // Creates the Google Ads Service client.
    $googleAdsServiceClient = $googleAdsClient->getGoogleAdsServiceClient();
    //print_r($googleAdsServiceClient);
    // Creates a query that retrieves all child accounts of the manager specified in search
    // calls below.
    $query = 'SELECT customer_client.client_customer, customer_client.level,'
        . ' customer_client.manager, customer_client.descriptive_name,'
        . ' customer_client.currency_code, customer_client.time_zone,'
        . ' customer_client.id FROM customer_client WHERE customer_client.level <= 1';

    $rootCustomerClient = null;
    // Adds the root customer ID to the list of IDs to be processed.
    $managerCustomerIdsToSearch = [$rootCustomerId];

    // Performs a breadth-first search algorithm to build an associative array mapping
    // managers to their child accounts ($customerIdsToChildAccounts).
    $customerIdsToChildAccounts = [];

    while (!empty($managerCustomerIdsToSearch)) {
        $customerIdToSearch = array_shift($managerCustomerIdsToSearch);
        // Issues a search request by specifying page size.
        /** @var GoogleAdsServerStreamDecorator $stream */
        $stream = $googleAdsServiceClient->searchStream(
            $customerIdToSearch,
            $query
        );

        // Iterates over all elements to get all customer clients under the specified customer's
        // hierarchy.
        foreach ($stream->iterateAllElements() as $googleAdsRow) {
            /** @var GoogleAdsRow $googleAdsRow */
            $customerClient = $googleAdsRow->getCustomerClient();

            // Gets the CustomerClient object for the root customer in the tree.
            if ($customerClient->getId() === $rootCustomerId) {
                $rootCustomerClient = $customerClient;
                $rootCustomerClients[$rootCustomerId] = $rootCustomerClient;
            }

            // The steps below map parent and children accounts. Continue here so that managers
            // accounts exclude themselves from the list of their children accounts.
            if ($customerClient->getId() === $customerIdToSearch) {
                continue;
            }

            // For all level-1 (direct child) accounts that are a manager account, the above
            // query will be run against them to create an associative array of managers to
            // their child accounts for printing the hierarchy afterwards.
            $customerIdsToChildAccounts[$customerIdToSearch][] = $customerClient;
            // Checks if the child account is a manager itself so that it can later be processed
            // and added to the map if it hasn't been already.
            if ($customerClient->getManager()) {
                // A customer can be managed by multiple managers, so to prevent visiting
                // the same customer multiple times, we need to check if it's already in the
                // map.
                $alreadyVisited = array_key_exists(
                    $customerClient->getId(),
                    $customerIdsToChildAccounts
                );
                if (!$alreadyVisited && $customerClient->getLevel() === 1) {
                    array_push($managerCustomerIdsToSearch, $customerClient->getId());
                }
            }
        }
    }

    return is_null($rootCustomerClient) ? null : [$rootCustomerClient->getId() => $customerIdsToChildAccounts];
}
//print_r($accessibleCustomers);

die();
// // Construct an API session configured from the OAuth2 credentials above.
// $session = (new AdWordsSessionBuilder())
//     ->withDeveloperToken("DEVELOPER_TOKEN")
//     ->withOAuth2Credential($oAuth2Credential)
//     ->withClientCustomerId("CLIENT_CUSTOMER_ID")
//     ->build();

// $oAuth2Credential = (new OAuth2TokenBuilder())
//     ->fromFile('/www/wwwroot/new.gass.co.id/adsapi_php.ini')
//     ->build();
// $googleAdsClient = (new GoogleAdsClientBuilder())
//     ->fromFile('/www/wwwroot/new.gass.co.id/adsapi_php.ini')
//     ->withOAuth2Credential($oAuth2Credential)
//     ->build();
// $conversionUploadServiceClient = $googleAdsClient->getConversionUploadServiceClient();
// /** @var UploadClickConversionsResponse $response */
// $response = $conversionUploadServiceClient->uploadClickConversions(
//     $customerId,
//     [$clickConversion],
//     true
// );

// print_r($response);
// die();
$oauth2 = new OAuth2([
    'authorizationUri' => 'https://accounts.google.com/o/oauth2/v2/auth',
    'tokenCredentialUri' => 'https://www.googleapis.com/oauth2/v4/token',
    'redirectUri' => 'https://n.gass.co.id/connect.html',
    'clientId' => $clientId,
    'clientSecret' => $clientSecret,
    'scope' => 'https://www.googleapis.com/auth/adwords',
]);
if (!isset($_GET['code'])) {
  // Create a 'state' token to prevent request forgery.
  // Store it in the session for later validation.
  $oauth2->setState(sha1(openssl_random_pseudo_bytes(1024)));
  $_SESSION['oauth2state'] = $oauth2->getState();

  // Redirect the user to the authorization URL.
  $config = [
    // Set to 'offline' if you require offline access.
    'access_type' => 'offline',
    'prompt' => 'consent'
  ];
  header('Location: ' . $oauth2->buildFullAuthorizationUri($config));
  exit;
// Check given state against previously stored one to mitigate CSRF attack.
} elseif (empty($_GET['state'])
    || ($_GET['state'] !== $_SESSION['oauth2state'])) {
  unset($_SESSION['oauth2state']);
  exit('Invalid state.');
} else {
  $oauth2->setCode($_GET['code']);
  $authToken = $oauth2->fetchAuthToken();
  //print_r($authToken);
  // Store the refresh token for your user in your local storage if you
  // requested offline access.
  $refreshToken = $authToken['refresh_token'];
  print_r($refreshToken);
//   $session = (new AdWordsSessionBuilder())
//     ->fromFile()
//     ->withOAuth2Credential($oauth2)
//     ->build();

//     $adWordsServices = new AdWordsServices();

//     $campaignService = $adWordsServices->get($session, CampaignService::class);
//     print_r($campaignService);


}
die();

if(isset($_GET['code'])){
    $post['code'] = $_GET['code'];
    $post['client_secret'] = $clientSecret;
    $post['client_id'] = $clientId;
    $post['redirect_uri'] = 'https://new.gass.co.id/connect.html';
    $post['grant_type'] = 'authorization_code';
    $res = post_x_contents($post, 'https://oauth2.googleapis.com/token');  
    print_r($res);
    if(isset($res['access_token'])){
        
        die();
        // Conversion data
        $customer_id = 'YOUR_CUSTOMER_ID';
        $conversionActionId = 'YOUR_CONVERSION_ACTION_ID'; // Replace with your specific conversion action ID
        $conversionValue = 10.0; // The conversion value you want to track

        // API endpoint
        $apiEndpoint = 'https://www.googleapis.com/googleads/v8/customers/'.$customer_id.'/conversions';

        // API request data
        $data = [
            'conversions' => [
                [
                    'conversion_action' => $conversionActionId,
                    'value' => $conversionValue,
                ]
            ],
        ];

        // Set cURL options
        $ch = curl_init($apiEndpoint);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $res['access_token'],
        ]);

        // Execute the cURL request
        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            echo 'Error: ' . curl_error($ch);
        }

        // Close the cURL session
        curl_close($ch);

        // Process the response
        if ($response) {
            $responseData = json_decode($response, true);
            // Handle the response data as needed
            var_dump($responseData);
        }

    }
}else{
    header("Location: https://accounts.google.com/o/oauth2/auth?client_id=".$clientId."&redirect_uri=https://new.gass.co.id/connect.html&scope=https://www.googleapis.com/auth/adwords&response_type=code&prompt=consent&access_type=offline");
}


die();