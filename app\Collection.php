<?php

class Collection implements IteratorAggregate {
    private array $data = [];

    public function rewind(): void {
        reset($this->data);
    }

    public function current(): mixed {
        return current($this->data);
    }

    public function key(): mixed {
        return key($this->data);
    }

    public function next(): void {
        next($this->data);
    }

    public function valid(): bool {
        return key($this->data) !== null;
    }

    public function __construct(array $data = []) {
        $this->data = $data;
    }

    public function __get(string $key): mixed {
        return $this->data($key);
    }

    public function __set(string $key, mixed $value): void {
        $this->data($key, $value);
    }

    public function __unset(string $key): void {
        unset($this->data[$key]);
    }

    public function __call(string $key, array $args): mixed {
        if (isset($this->data[$key]) && is_callable($this->data[$key])) {
            return call_user_func_array($this->data[$key], $args);
        } else {
            return isset($args[0]) ? $this->data($key, null, $args[0]) : $this->data($key);
        }
    }

    public function data(string|array|null $key = null, mixed $value = null, mixed $default = null): mixed {
        // ->data(array $key) append data ($key as new data array to append)
        if (is_array($key) && $value === null) {
            $this->data = array_merge($this->data, $key);
        }
        // ->data(array $key, bool true) overwrite data ($key as new data array to overwrite)
        elseif (is_array($key) && $value == true) {
            $this->data = $key;
        }
        // ->data($key, [null, [$default]]) get: return data[$key] or $default if $data[$key] is not set
        elseif ($key !== null && $value === null) {
            return $this->data[$key] ?? $default;
        }
        // ->data($key, $value) set: assign value to data[$key] as is
        elseif ($key !== null && $value !== null) {
            $this->data[$key] = $value;
        }
        // ->data() no args: get all. return all data as array
        elseif ($key === null && $value === null) {
            return $this->data;
        }

        // Default return if no conditions are met
        return null;
    }

    public function map(callable $callback): void {
        $this->data = array_map($callback, $this->data);
    }

    public function getIterator(): ArrayIterator {
        return new ArrayIterator($this->data);
    }
}
