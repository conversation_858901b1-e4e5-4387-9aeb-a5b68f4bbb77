<?php

class fb{
    private $access_token;

    function __construct($access_token){
        $this->access_token = $access_token;
    }
    
    function get_campaign($filter='', $status=''){
        global $app;
        $fb = new \Facebook\Facebook([
            'app_id' => $app->config->fb_app_id,
            'app_secret' => $app->config->fb_app_secret,
            'default_graph_version' => 'v16.0',
        ]);
        try {
            $response = $fb->get('/me/adaccounts?fields=campaigns{status,name,objective}', $this->access_token);
            //$res = $response->getDecodedBody();
            $graphEdge = $response->getGraphEdge();
            $campaigns = array();
            foreach ($graphEdge as $node) {
                $campaign = $node->getField('campaigns');
                foreach ($campaign as $data) {
                    $campaignData = $data->asArray();
                    if($filter ==''){                        
                        if($status !=''){
                            if (str_contains($campaignData['status'], $status)) {
                                array_push($campaigns, $campaignData);
                            }
                        }else{
                            array_push($campaigns, $campaignData);
                        }
                    }else{
                        if (str_contains($campaignData['name'], $filter)) {
                            if($status !=''){
                                if (str_contains($campaignData['status'], $status)) {
                                    array_push($campaigns, $campaignData);
                                }
                            }else{
                                array_push($campaigns, $campaignData);
                            }
                        }
                    }                    
                }
            }
            $result['code'] = 1;
            $result['msg'] = 'Success get data campaigns';
            $result['data'] = $campaigns;
        } catch (Facebook\Exceptions\FacebookResponseException $e) {
            $result['code'] = 0;
            $result['msg'] = 'Graph returned an error: ' . $e->getMessage();
        } catch (Facebook\Exceptions\FacebookSDKException $e) {
            $result['code'] = 0;
            $result['msg'] = 'Facebook SDK returned an error: ' . $e->getMessage();
        }
        return $result;
    }
    
    function start_campaign($campaign_id){
        global $app;
        $fb = new \Facebook\Facebook([
            'app_id' => $app->config->fb_app_id,
            'app_secret' => $app->config->fb_app_secret,
            'default_graph_version' => 'v16.0',
        ]);
        try {
            $response = $fb->post(
                "/$campaign_id",
                ['status' => 'ACTIVE'],
                $this->access_token
            );

            $graphNode = $response->getGraphNode();
            $result['code'] = 1;
            $result['msg'] = 'Success start campaign '.$graphNode['name'];
        } catch (Facebook\Exceptions\FacebookResponseException $e) {
            $result['code'] = 0;
            $result['msg'] = 'Graph returned an error: ' . $e->getMessage();
        } catch (Facebook\Exceptions\FacebookSDKException $e) {
            $result['code'] = 0;
            $result['msg'] = 'Facebook SDK returned an error: ' . $e->getMessage();
        }
        return $result;
    }
    
    function pause_campaign($campaign_id){
        global $app;
        $fb = new \Facebook\Facebook([
            'app_id' => $app->config->fb_app_id,
            'app_secret' => $app->config->fb_app_secret,
            'default_graph_version' => 'v16.0',
        ]);
        try {
            $response = $fb->post(
                "/$campaign_id",
                ['status' => 'PAUSED'],
                $this->access_token
            );

            $graphNode = $response->getGraphNode();
            $result['code'] = 1;
            $result['msg'] = 'Success pause campaign '.$graphNode['name'];
        } catch (Facebook\Exceptions\FacebookResponseException $e) {
            $result['code'] = 0;
            $result['msg'] = 'Graph returned an error: ' . $e->getMessage();
        } catch (Facebook\Exceptions\FacebookSDKException $e) {
            $result['code'] = 0;
            $result['msg'] = 'Facebook SDK returned an error: ' . $e->getMessage();
        }
        return $result;
    }
    
    function set_budget_campaign($campaign_id, $new_budget){
        global $app;
        $fb = new \Facebook\Facebook([
            'app_id' => $app->config->fb_app_id,
            'app_secret' => $app->config->fb_app_secret,
            'default_graph_version' => 'v16.0',
        ]);
        try {
            $response = $fb->post(
                "/$campaign_id",
                ['daily_budget' => $new_budget * 100, 'budget_remaining' => $new_budget * 100],
                $this->access_token
            );

            $graphNode = $response->getGraphNode();
            $result['code'] = 1;
            $result['msg'] = 'Success set budget campaign '.$graphNode['name'];
        } catch (Facebook\Exceptions\FacebookResponseException $e) {
            $result['code'] = 0;
            $result['msg'] = 'Graph returned an error: ' . $e->getMessage();
        } catch (Facebook\Exceptions\FacebookSDKException $e) {
            $result['code'] = 0;
            $result['msg'] = 'Facebook SDK returned an error: ' . $e->getMessage();
        }
        return $result;
    }
}