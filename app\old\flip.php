<?php

class flip{
    public $urlapi = 'https://bigflip.id/api/v2';
    public $secret_key = 'JDJ5JDEzJGJDemVlSktHMmFWMGFYTVJyb05aNWVSZ1hnUnpCd1o4S1JaUTEvd3I3dy94L0FZbEZ3MzBT';
    public $urlapi_test = 'https://business.flip.id/big_sandbox_api/v2';
    public $secret_key_test = "JDJ5JDEzJHkxZ1hiMTAuN0FjTWlCRzFlc2NTOU91OEpvNEVJTmNuNzlubFRrYVA4RHJrNjdLb0NPcHBl";
    public $modeTest = false;
    public function api($url, $metod='get', $payloads=array()){
        $ch = curl_init();
        if($this->modeTest==false){
            curl_setopt($ch, CURLOPT_USERPWD, $this->secret_key.":");
            curl_setopt($ch, CURLOPT_URL, $this->urlapi.$url);
        }else{
            curl_setopt($ch, CURLOPT_USERPWD, $this->secret_key_test.":");
            curl_setopt($ch, CURLOPT_URL, $this->urlapi_test.$url);
        }     
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_HEADER, FALSE);
        if($metod == 'post'){
            curl_setopt($ch, CURLOPT_POST, TRUE);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($payloads));
        }       
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/x-www-form-urlencoded"
        ));        
        $response = curl_exec($ch);
        curl_close($ch);
        return json_decode($response, true);
    }
    
    public function __construct($test=false){
        $this->modeTest = $test;
    }
    public function balance(){
        $res = $this->api('/general/balance');
        return $res['balance'];
    }
    
    public function create_bill($data){
        $res = $this->api('/pwf/bill', 'post', $data);
        return $res;
    }
}
