<?php
class trigger_tiktok{

	private $table_visitor;
	private $table_log;

	function __construct()
    {
  
    	$this->table_visitor = "visitor";
    	$this->table_log = "log_connector";
    	
    }
	
	function sent_pixel($connector_key,$value,$pixel_id,$access_token,$vid,$type,$custom=NULL)
	{
		global $app;
		$db2 = $app->db2;

		$hash["value"] = $value;
		$hash["pixel_id"] = $pixel_id;
		$hash["access_token"] = $access_token;
		$hash["source"] = "tiktok";
		$hash["vid"] = $vid;
		$hash["type"] = $type;
		$hash["custom"] = $custom;
		$hash["waktu"] = date("Y-m-d");
		$data_hash = json_encode($hash);
		//var_dump($hash2);
		$hash = hash("sha256", $data_hash);
		//echo $type;


		$table_visitor = $this->table_visitor;
		$table_log = $this->table_log;

		$waktu = time();

		$db2->where("visitor_id",$vid);
		$visitor = $db2->getone($this->table_visitor);

		$char_vid = convBase($vid, "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        $char_vid = str_split($char_vid, 4);
        $char_vid = implode(".", $char_vid);

			$ch = curl_init();
			$timestamp = date('Y-m-d\TH:i:s.Z\Z', time());
			$post["pixel_code"] = $pixel_id;
			$post["event"] = $type;
			$post["event_id"] = $type."-".$char_vid;
			$post["timestamp"] = $timestamp;

			if($visitor == NULL)
			{
				return;
			}
			if($visitor == "")
			{
				return;
			}

			if($visitor["data"] == NULL){return;}

			$visitor_data = unserialize($visitor["data"]);

			if(isset($visitor_data['ip']))
			{
				$tmp = explode(",",$visitor_data['ip']);
				if(count($tmp) > 1)
				{
					$visitor_data["ip"] = $tmp[0];
				}
			}


			if(isset($visitor_data["useragent"])){if($visitor_data["useragent"] != ""){
					$post["context"]["user_agent"] = $visitor_data['useragent'];
			}}
			if(isset($visitor_data["ip"])){if($visitor_data["ip"] != ""){
					$post["context"]["ip"] = $visitor_data['ip'];
			}}
			if(isset($visitor_data["tiktok"]["ttclid"])){if($visitor_data["tiktok"]["ttclid"] != ""){
					$post["context"]["ad"]["callback"] = $visitor_data["tiktok"]["ttclid"];
			}}
			if(isset($visitor_data["tiktok"]["ttclid2"])){if($visitor_data["tiktok"]["ttclid2"] != ""){
					$post["context"]["ad"]["callback"] = $visitor_data["tiktok"]["ttclid2"];
			}}

			if(isset($visitor_data["tiktok"]["ttp"])){if($visitor_data["tiktok"]["ttp"] != ""){
					$post["context"]["user"]["ttp"] = $visitor_data["tiktok"]["ttp"];
			}}

			if(isset($visitor_data["phone"])){if($visitor_data["phone"] != ""){
					$post["context"]["user"]["phone_number"] = hash('sha256', "+" . $visitor_data["phone"]);
			}}

			if(isset($visitor["phone"] ) && $visitor["phone"] != "" && $visitor["phone"] != "0" ){
			$visitor["phone"] = preg_replace('/[^0-9.]+/', '', $visitor["phone"]);
			$post["context"]["user"]["phone_number"] = hash('sha256', "+" . $visitor["phone"]);
			}

			if(isset($visitor_data["current_url"])){if($visitor_data["current_url"] != ""){
					$post["context"]["page"]["url"] = $visitor_data["current_url"];
			}}

			if($value != 0)
			{
				$m = new meta();
				$cur = $m->get_meta("currency");
				if(isset($cur["result"]["data"])){
					$post["properties"]["currency"] = $cur["result"]["data"];
				}else{
					$post["properties"]["currency"] = "IDR";
				}
				
				$post["properties"]["value"] = $value;
			}
			$post["context"]["user"]["external_id"] = md5($vid);
			

			curl_setopt($ch, CURLOPT_URL, 'https://business-api.tiktok.com/open_api/v1.2/pixel/track/');
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-type: multipart/form-data"));
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post));
			$headers = array();
			$headers[] = 'Access-Token: ' . $access_token;
			$headers[] = 'Content-Type: application/json';
			curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
			$result = curl_exec($ch);

			$log["pixel_id"] = $pixel_id;
			$log["access_token"] = $access_token;

		
			$log["type"] = $type;
			$log["custom"] = $custom;
			$data_insertx = array();
			$data_insertx["waktu"] = date("Y-m-d H:i:s");

			$data_insertx["connector_key"] = $db2->func("UNHEX(?)",[$connector_key]);
			$data_insertx["vid"] = $vid;
			$data_insertx["event"] = $type;

			if (curl_errno($ch)) {
				$return = curl_error($ch);

				//$log["msg"]['result'] = $result;
				//$log["msg"]['param'] = $post;

			  	$log["msg"] = $return;
				$data_insertx["error"] = 1;
				$ddd["input"] = $data_hash;
				$ddd["output"] = $log;
				$data_insertx["result"] = json_encode($ddd);
				$db2->insert($table_log,$data_insertx);
				$result = $return;

			}else
			{

				$log["msg"]['result'] = $result;
				$log["msg"]['param'] = $post;
				$data_insertx["error"] = 0;
				$data_insertx["result"] = json_encode($log);



		$db2->insert($table_log,$data_insertx);

				
			}
			curl_close($ch);
		
		return $result;
	}



}