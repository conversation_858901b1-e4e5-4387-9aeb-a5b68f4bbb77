<?php

namespace DynamicWebhook\Abstracts;

use DynamicWebhook\Interfaces\DataExtractorInterface;
use DynamicWebhook\Interfaces\MessageNormalizerInterface;

/**
 * Abstract base class for data extractors
 */
abstract class AbstractDataExtractor implements DataExtractorInterface
{
    protected MessageNormalizerInterface $normalizer;
    protected array $platformConfigs;

    public function __construct(MessageNormalizerInterface $normalizer, array $platformConfigs = [])
    {
        $this->normalizer = $normalizer;
        $this->platformConfigs = $platformConfigs;
    }

    /**
     * Extract data from webhook payload
     */
    public function extract(array $data, string $platform): array
    {
        $config = $this->getPlatformConfig($platform);
        
        if (!$config) {
            return $this->getEmptyResult();
        }

        return $this->extractPlatformSpecificData($data, $config, $platform);
    }

    /**
     * Validate extracted data
     */
    public function validate(array $extractedData): bool
    {
        return !empty($extractedData['phone']) && !empty($extractedData['message']);
    }

    /**
     * Get platform configuration
     */
    protected function getPlatformConfig(string $platform): ?array
    {
        return $this->platformConfigs[$platform] ?? null;
    }

    /**
     * Get empty result structure
     */
    protected function getEmptyResult(): array
    {
        return [
            'phone' => null,
            'message' => '',
            'message_type' => 'message_in',
            'visitor_id' => null,
            'cs_phone' => null,
            'raw_data' => []
        ];
    }

    /**
     * Extract platform-specific data - to be implemented by concrete classes
     */
    abstract protected function extractPlatformSpecificData(array $data, array $config, string $platform): array;

    /**
     * Determine message type based on platform configuration
     */
    protected function determineMessageType($typeValue, array $config, string $platform = null): string
    {
        // Handle special cases for specific platforms
        if ($platform === 'waba' && $config['incoming_condition'] === 'from_exists') {
            return isset($data['entry'][0]['changes'][0]['value']['messages'][0]['from']) ? 'message_in' : 'message_out';
        }

        if ($config['incoming_condition'] === 'exists') {
            return isset($typeValue) ? 'message_in' : 'message_out';
        } elseif ($config['incoming_condition'] === 'not_exists') {
            return !isset($typeValue) ? 'message_in' : 'message_out';
        } elseif ($typeValue === $config['incoming_condition']) {
            return 'message_in';
        } elseif ($typeValue === $config['outgoing_condition']) {
            return 'message_out';
        }

        return 'message_in'; // Default to incoming
    }
}
