<?php

function add($param)
{
    $rules = [
        "cs_key" => "required",
        "day" => "required",
        "project_id" => "required",
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $d = new cs_jadwal();
    $msg = $d->add_jadwal($cs_key,$day,$project_id);

    return $msg;
}

function delete($param)
{
	$rules = [
        "cs_key" => "required",
        "project_id" => "required",
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $d = new cs_jadwal();
    $msg = $d->delete_jadwal($cs_key,$project_id);

    return $msg;
}

function get($param)
{

    $rules = [
        "cs_key" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $d = new cs_jadwal();
    $msg = $d->get_jadwal($cs_key);

    return $msg;
}