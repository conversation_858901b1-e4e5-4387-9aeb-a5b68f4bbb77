<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/resources/lead_form_submission_data.proto

namespace Google\Ads\GoogleAds\V18\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Fields in the submitted custom question
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.resources.CustomLeadFormSubmissionField</code>
 */
class CustomLeadFormSubmissionField extends \Google\Protobuf\Internal\Message
{
    /**
     * Output only. Question text for custom question, maximum number of
     * characters is 300.
     *
     * Generated from protobuf field <code>string question_text = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $question_text = '';
    /**
     * Output only. Field value for custom question response, maximum number of
     * characters is 70.
     *
     * Generated from protobuf field <code>string field_value = 2 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $field_value = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $question_text
     *           Output only. Question text for custom question, maximum number of
     *           characters is 300.
     *     @type string $field_value
     *           Output only. Field value for custom question response, maximum number of
     *           characters is 70.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Resources\LeadFormSubmissionData::initOnce();
        parent::__construct($data);
    }

    /**
     * Output only. Question text for custom question, maximum number of
     * characters is 300.
     *
     * Generated from protobuf field <code>string question_text = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return string
     */
    public function getQuestionText()
    {
        return $this->question_text;
    }

    /**
     * Output only. Question text for custom question, maximum number of
     * characters is 300.
     *
     * Generated from protobuf field <code>string question_text = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param string $var
     * @return $this
     */
    public function setQuestionText($var)
    {
        GPBUtil::checkString($var, True);
        $this->question_text = $var;

        return $this;
    }

    /**
     * Output only. Field value for custom question response, maximum number of
     * characters is 70.
     *
     * Generated from protobuf field <code>string field_value = 2 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return string
     */
    public function getFieldValue()
    {
        return $this->field_value;
    }

    /**
     * Output only. Field value for custom question response, maximum number of
     * characters is 70.
     *
     * Generated from protobuf field <code>string field_value = 2 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param string $var
     * @return $this
     */
    public function setFieldValue($var)
    {
        GPBUtil::checkString($var, True);
        $this->field_value = $var;

        return $this;
    }

}

