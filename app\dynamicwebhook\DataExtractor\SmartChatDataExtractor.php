<?php

namespace DynamicWebhook\DataExtractor;

use DynamicWebhook\Abstracts\AbstractDataExtractor;

/**
 * SmartChat data extractor with form-data support
 */
class SmartChatDataExtractor extends AbstractDataExtractor
{
    /**
     * Get supported platforms
     */
    public function getSupportedPlatforms(): array
    {
        return ['smartchat'];
    }

    /**
     * Extract platform-specific data
     */
    protected function extractPlatformSpecificData(array $data, array $config, string $platform): array
    {
        $result = $this->getEmptyResult();

        // Try $_POST first (form-data)
        if (isset($_POST['message'])) {
            $result['message'] = $_POST['message'];
            $result['phone'] = $this->normalizer->cleanPhoneNumber($_POST['no_customer'] ?? '');
            $result['message_type'] = ($_POST['message_type'] ?? '') === 'inbound' ? 'message_in' : 'message_out';
            
            $result['message'] = $this->normalizer->normalizeMessage($result['message']);
            $result['raw_data'] = $_POST;
            
            return $result;
        }
        
        // Try customData if exists
        if (isset($data['customData'])) {
            $customData = $data['customData'];
            $result['message'] = $customData['message'] ?? '';
            $result['phone'] = $this->normalizer->cleanPhoneNumber($customData['no_customer'] ?? '');
            $result['message_type'] = ($customData['message_type'] ?? '') === 'inbound' ? 'message_in' : 'message_out';
            
            $result['message'] = $this->normalizer->normalizeMessage($result['message']);
            $result['raw_data'] = $data;
            
            return $result;
        }
        
        // Fallback to normal extraction
        $result['phone'] = $this->normalizer->extractNestedValue($data, $config['phone_field']);
        $result['message'] = $this->normalizer->extractNestedValue($data, $config['message_field']);
        $typeValue = $this->normalizer->extractNestedValue($data, $config['type_field']);
        $result['message_type'] = $this->determineMessageType($typeValue, $config);
        
        // Clean and normalize
        $result['phone'] = $this->normalizer->cleanPhoneNumber($result['phone']);
        $result['message'] = $this->normalizer->normalizeMessage($result['message']);
        $result['raw_data'] = $data;

        return $result;
    }
}
