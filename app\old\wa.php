<?php

class wa{
	
    function post_gateway($fields){
	
        $fields = http_build_query($fields);
        $header[0] = "Accept-Language: en";
        $header[] = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
//        $header[] = "Pragma: no-cache";
//        $header[] = "Cache-Control: no-cache";
//        $header[] = "Accept-Encoding: gzip,deflate";
        $header[] = "Content-Type: application/x-www-form-urlencoded";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://convertion.id/panel/api.html');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;

    }
    
	public function post_gatewayx($data){
		global $run;
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $run['site']['gateway']);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
		$result = curl_exec($ch);
		curl_close($ch);
		return $result;
	}
	
	public function send($type, $phone, $to, $caption){
		$data =array();
		if($type == "text"){
			$data = [
				'act' => 'wa_send',
				'sender' => $phone,
				'message' =>  str_replace('\n', PHP_EOL, $caption),
				'type' => 'text',
				'to' => $to,
			];
		}elseif($type == "file"){
			$data = [
				'act' => 'send_message',
				'phone' => $phone,
				'caption' => base64_encode($caption),
				'type' => 'file',
				'to' => $to ];
		}  
		$result = $this->post_gateway($data);
		//if($result == false)
//		{
//			$result["code"] = "500";
//			$result["data_terkirim"] = $data;
//		}
		return json_decode($result, true);
	}
    
	public function sendx($type, $phone, $to, $caption){
		$data =array();
		if($type == "text"){
			$data = [
				'act' => 'send_message',
				'phone' => $phone,
				'caption' => base64_encode($caption),
				'type' => 'text',
				'to' => $to,
			];
		}elseif($type == "file"){
			$data = [
				'act' => 'send_message',
				'phone' => $phone,
				'caption' => base64_encode($caption),
				'type' => 'file',
				'to' => $to ];
		}  
		$result = $this->post_gateway($data);
		if($result == false)
		{
			$result["code"] = "500";
			$result["data_terkirim"] = $data;
		}
		return $result;
	}
	public function check_phone($phone, $target){

		$data = [
			'act' => 'filter_number',
			'phone' => $phone,
			'target' => $target,
		];
		$result = $this->post_gateway($data);
		return $result;
	}
	
	function check_status($token, $phone){
		global $db;
		$u = new user();
		$user_id = $u->convert_token($token);
		$db->where("user_id =UNHEX(?)",array($user_id));
		$user = $db->getone("x_user");
		if(count($user) <1)
		{
			$ret["code"] = 0;
			$ret["msg"] = "user not found"; 
			return $ret;
		}
		//////////////////////// end cek token
		$data = [
			'act' => 'check_status',
			'phone' => $phone
		];
		$result = $this->post_gateway($data);
		$result = json_decode($result,true);
		$db->where("user_id = UNHEX(?)",array($user_id));
		$db->where("phone",$phone);
		if($result["code"] == "200"){
			if($result["msg"]=="phone connected"){
				$db->update("x_wa_account",array("status" => $result["msg"]));
				//return "phone connected";
			}elseif($result["msg"] == "scan QR"){
				$db->update("x_wa_account",array("status" => $result["msg"]));
			}elseif($result["msg"] == "phone not connected" ){
				$db->update("x_wa_account",array("status" => $result["msg"]));
				//return "phone not connected";
			}
		}else{
			$db->update("x_wa_account",array("status" => $result["msg"]));
		}
		return $result;
	}
}