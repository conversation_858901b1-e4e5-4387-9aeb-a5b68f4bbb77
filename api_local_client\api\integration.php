<?php
use Google\Auth\CredentialsLoader;
use Google\Auth\OAuth2;
use Psr\Http\Message\ServerRequestInterface;
use React\EventLoop\Loop;
use React\Http\HttpServer;
use React\Http\Message\Response;
use React\Socket\SocketServer;

use GetOpt\GetOpt;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentNames;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentParser;
use Google\Ads\GoogleAds\Lib\OAuth2TokenBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClient;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClientBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsException;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsServerStreamDecorator;
use Google\Ads\GoogleAds\V18\Errors\GoogleAdsError;
use Google\Ads\GoogleAds\V18\Resources\CustomerClient;
use Google\Ads\GoogleAds\V18\Services\CustomerServiceClient;
use Google\Ads\GoogleAds\V18\Services\GoogleAdsRow;
use Google\ApiCore\ApiException;
use Google\AdsApi\AdWords\AdWordsServices;
use Google\AdsApi\AdWords\AdWordsSessionBuilder;
use Google\AdsApi\AdWords\v201809\cm\CampaignService;
use Google\AdsApi\AdManager\AdManagerSessionBuilder;
use Google\Ads\GoogleAds\V18\Services\ListAccessibleCustomersRequest;
use Google\Ads\GoogleAds\V18\Services\GoogleAdsServiceClient;
use Google\Ads\GoogleAds\V18\Services\SearchGoogleAdsRequest;



use Google\Ads\GoogleAds\Examples\Utils\Helper;
use Google\Ads\GoogleAds\V18\Enums\ConversionActionCategoryEnum\ConversionActionCategory;
use Google\Ads\GoogleAds\V18\Enums\ConversionActionStatusEnum\ConversionActionStatus;
use Google\Ads\GoogleAds\V18\Enums\ConversionActionTypeEnum\ConversionActionType;
use Google\Ads\GoogleAds\V18\Resources\ConversionAction;
use Google\Ads\GoogleAds\V18\Resources\ConversionAction\ValueSettings;
use Google\Ads\GoogleAds\V18\Services\ConversionActionOperation;
use Google\Ads\GoogleAds\V18\Services\MutateConversionActionsRequest;

function add($param){
    global $app;
    $rules = array(
        'token'      => 'required', 
        'access_token'      => 'required',
        'type'      => 'required',
        'platform'      => 'required',
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $acc = new account($project_id);
    if($param["platform"]=='facebook'){
        $fb = new \Facebook\Facebook([
            'app_id' => $app->config->fb_app_id,
            'app_secret' => $app->config->fb_app_secret,
            'default_graph_version' => 'v20.0',
        ]);
        if($param["type"]=='report'){
            /*
            $fb = new \Facebook\Facebook([
                'app_id' => $app->config->fb_app_id,
                'app_secret' => $app->config->fb_app_secret,
                'default_graph_version' => 'v20.0',
            ]);*/
            if(isset($account_id) && isset($account_name)){
                $data = file_get_contents('https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token&client_id='.$app->config->fb_app_id.'&client_secret='.$app->config->fb_app_secret.'&fb_exchange_token='.$access_token);
                $data = json_decode($data, true);
                //var_dump('https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token&client_id='.$app->config->fb_app_id.'&client_secret='.$app->config->fb_app_secret.'&fb_exchange_token='.$access_token);
                //var_dump($data);
                //$msg = $acc->add($user_id, $res['id'], $res['name'], $data);
                $msg = $acc->add($user_id, $account_id, $account_name, $data, 'facebook', $type);
                if(isset($msg['id'])){
                    $post['id'] = 'project_'.$project_id.'_'.$msg['id'];
                    $post['webhook'] = 'http://'.$server.'/api.html?act=integration_grab&integration_id='.$msg['id'].'&project_id='.$project_id;
                    $post['time'] = 5;
                    //post_x_contents($post, 'http://localhost:3000/add-cron');
                    //$bt = new bt_api();
                    //$bt->add_cron($post['id'], 5, $post['webhook']);
                    unset($msg['id']);
                }
            }else{
                $msg['code'] = 0;
                $msg["result"]['msg'] = 'Error add acoount integration';
            }
        }else{
            /*
            $fb = new \Facebook\Facebook([
                'app_id' => $app->config->fb_app_id_ctwa,
                'app_secret' => $app->config->fb_app_secret_ctwa,
                'default_graph_version' => 'v20.0',
            ]);*/

            if(isset($account_id) && isset($account_name)){
                $data = file_get_contents('https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token&client_id='.$app->config->fb_app_id_ctwa.'&client_secret='.$app->config->fb_app_secret_ctwa.'&fb_exchange_token='.$access_token);
                $data = json_decode($data, true);
                //$msg = $acc->add($user_id, $res['id'], $res['name'], $data);
                $msg = $acc->add($user_id, $account_id, $account_name, $data, 'facebook', $type);
                if(isset($msg['id'])){
                    $post['id'] = 'project_'.$project_id.'_'.$msg['id'];
                    $post['webhook'] = 'http://'.$server.'/api.html?act=integration_grab&integration_id='.$msg['id'].'&project_id='.$project_id;
                    $post['time'] = 5;
                    //post_x_contents($post, 'http://localhost:3000/add-cron');
                    //$bt = new bt_api();
                    //$bt->add_cron($post['id'], 5, $post['webhook']);
                    unset($msg['id']);
                }
            }else{
                $msg['code'] = 0;
                $msg["result"]['msg'] = 'Error add acoount integration';
            }
        }
        //$response = $fb->get('/me', $access_token);
        //$res = $response->getDecodedBody();
        

    }
    if($param["platform"]=='google'){
        if($param["type"]=='report'){
            if(isset($account_id) && isset($account_name)){
                
                $data['access_token'] = $access_token;
                $data['refresh_token'] = $refresh_token;
                $msg = $acc->add($user_id, $account_id, $account_name, $data, 'google', $type);
                if(isset($msg['id'])){
                    $post['id'] = 'project_'.$project_id.'_'.$msg['id'];
                    $post['webhook'] = 'http://'.$server.'/api.html?act=integration_grab&integration_id='.$msg['id'].'&project_id='.$project_id;
                    $post['time'] = 5;
                    //post_x_contents($post, 'http://localhost:3000/add-cron');
                    //$bt = new bt_api();
                    //$bt->add_cron($post['id'], 5, $post['webhook']);
                    unset($msg['id']);
                }
            }else{
                $msg['code'] = 0;
                $msg["result"]['msg'] = 'Error add acoount integration';
            }
        }
//        $userDataUrl = 'https://www.googleapis.com/oauth2/v20userinfo';
//        $ch = curl_init($userDataUrl);
//        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
//        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Authorization: Bearer ' . $access_token]);
//        $userData = curl_exec($ch);
//        curl_close($ch);  
//        print_r($userData);
//        $res = json_decode($userData, true);
//        $res['access_token'] = $access_token;
//        $res['refresh_token'] = $refresh_token;
//        $msg = $acc->add($user_id, $res['id'], $res['name'], $res, 'google');
    }
    return $msg;
}
function get_adaccount($param){
    global $app;
    $rules = array(
        'token'      => 'required', 
        'access_token'      => 'required',
        'platform'      => 'required',
        'type'      => 'required',
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    if($param["platform"]=='facebook'){
        $fb = new \Facebook\Facebook([
            'app_id' => $app->config->fb_app_id,
            'app_secret' => $app->config->fb_app_secret,
            'default_graph_version' => 'v20.0',
        ]);
        
        /*
        if($param["type"]=='report'){
            $fb = new \Facebook\Facebook([
                'app_id' => $app->config->fb_app_id,
                'app_secret' => $app->config->fb_app_secret,
                'default_graph_version' => 'v20.0',
            ]);
        }else{
            $fb = new \Facebook\Facebook([
                'app_id' => $app->config->fb_app_id_ctwa,
                'app_secret' => $app->config->fb_app_secret_ctwa,
                'default_graph_version' => 'v20.0',
            ]);
        }
        */
        try {
            $response = $fb->get('/me/adaccounts?fields=amount_spent,account_status,name,account_id,end_advertiser_name', $access_token);
            $res = $response->getDecodedBody();
            if(isset($res['data'])){
                $dd = array();
                foreach($res['data'] as $k=>$v){
                    if($v['account_status']==1){
                        array_push($dd, $v);
                    }
                }
                $msg['code'] = 1;
                $msg["result"]['msg'] = 'Success get AD acoount integration';
                $msg["result"]['data'] = $dd;
            }else{
                $msg['code'] = 0;
                $msg["result"]['msg'] = 'Error get AD acoount integration';
            }
        } catch (\Facebook\Exception\ResponseException $e) {
            file_put_contents('log-error-connect.txt', '[' . date('Y-m-d H:i:s') . "]\nResponseException: " . json_encode($e) . "\n\n", FILE_APPEND);
            $msg['code'] = 0;
            $msg["result"]['msg'] = 'Error get AD acoount integration';
            $msg["result"]['data'] = $e;
        } catch (\Facebook\Exception\SDKException $e) {
            file_put_contents('log-error-connect.txt', '[' . date('Y-m-d H:i:s') . "]\SDKException: " . json_encode($e) . "\n\n", FILE_APPEND);
            $msg['code'] = 0;
            $msg["result"]['msg'] = 'Error get AD acoount integration';
            $msg["result"]['data'] = $e;
        }     
        
        
    }
    
    if($param["platform"]=='google'){
        $clientId = '539839321760-jpvf55v4ukvo6516rq2hg94r26dd45sm.apps.googleusercontent.com';
        $clientSecret = '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';
        $developerToken = 'PN0opBN-69S0Cfz6JFGn6A';
        $oAuth2Credential = (new OAuth2TokenBuilder())
          ->withClientId($clientId)
          ->withClientSecret($clientSecret)
          ->withRefreshToken($refresh_token)
          ->build();
          //print_r($oAuth2Credential);
        $googleAdsClient = (new GoogleAdsClientBuilder())
            ->withDeveloperToken($developerToken)
            ->withOAuth2Credential($oAuth2Credential)
            ->build();
        
        $customerIds = []; $accounts = []; $ids=0;
        try {
            $request = new ListAccessibleCustomersRequest();
            $accessibleCustomers = $googleAdsClient->getCustomerServiceClient()->listAccessibleCustomers($request);
            foreach ($accessibleCustomers->getResourceNames() as $customerResourceName) {
                $customerIds[] = str_replace('customers/', '', $customerResourceName) ;
            }
//             foreach ($customerIds as $customerId) {
// //                if($customerId != '**********'){
// //                    continue;
// //                }
//                 $query = "SELECT customer.id, customer.descriptive_name FROM customer";
//                 $request = new SearchGoogleAdsRequest([
//                     'customer_id' => $customerId,
//                     'query' => $query,
//                 ]);

//                 // Fetch the data
//                 $googleAdsServiceClient = $googleAdsClient->getGoogleAdsServiceClient();
//                 $response = $googleAdsServiceClient->search($request);

//                 foreach ($response->getIterator() as $googleAdsRow) {
//                     $accounts[$ids]['id'] = $googleAdsRow->getCustomer()->getId();
//                     $accounts[$ids]['name'] = $googleAdsRow->getCustomer()->getDescriptiveName();
//                     $ids++;
//                 }
//             }
            foreach ($customerIds as $customerId) {
                try {
                    $query = "SELECT customer.id, customer.descriptive_name FROM customer";
                    $request = new SearchGoogleAdsRequest([
                        'customer_id' => $customerId,
                        'query' => $query,
                    ]);

                    $googleAdsServiceClient = $googleAdsClient->getGoogleAdsServiceClient();
                    $response = $googleAdsServiceClient->search($request);

                    foreach ($response->getIterator() as $googleAdsRow) {
                        $accounts[$ids]['id'] = $googleAdsRow->getCustomer()->getId();
                        $accounts[$ids]['name'] = $googleAdsRow->getCustomer()->getDescriptiveName();
                        $ids++;
                    }
                } catch (Exception $e) {
                    // skip customer yang error
                    continue;
                }
            }

            $msg['code'] = 1;
            $msg["result"]['msg'] = 'Success get AD acoount integration';
            $msg["result"]['data'] = $accounts;
        } catch (Exception $e) {
            $msg['code'] = 0;
            $msg["result"]['msg'] = 'Error get AD acoount integration';
            $msg["result"]['data'] = $e->getMessage();
            file_put_contents('log/log-error-connect-google.txt', '[' . date('Y-m-d H:i:s') . "]\n".json_encode($param).$e->getMessage() . "\n\n", FILE_APPEND);
        }
    }
    return $msg;
}


function get($param){
    global $app;
    $rules = array(
        'token'      => 'required', 
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $platform = '';
    if(isset($param['platform'])){
        $platform = $param['platform'];
    }    
    $acc = new account($project_id);
    $msg["result"]['data'] = $acc->get_list($platform);
    $msg['code'] = 1;
    $msg["result"]["msg"] = 'Success get list integration account';
    
    return $msg;
}

function delete($param){
    global $app;
    $rules = array(
        'token'      => 'required', 
        'account_id'      => 'required', 
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $acc = new account($project_id);
    $msg = $acc->reset_account($account_id);
    return $msg;
}

function check_status($param){
    global $app;
    $rules = array(
        'token'      => 'required', 
        'account_id'  => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $acc = new account($project_id);
    
    $res = $acc->get_detail_id_by_account_id($account_id);

    if($res["platform"] == "facebook" || $res["type"] == "ctwa"){
        $access_token = $res['data']['access_token'];
        
        // Check Facebook token status
        $token_status = check_facebook_token_status($access_token);
        
        if($token_status['is_valid']){
            $msg['code'] = 1;
            $msg['result']['msg'] = 'Token valid dan aktif';
            $msg['result']['data'] = $token_status;
        } else {
            $msg['code'] = 0;
            $msg['result']['msg'] = $token_status['error_message'];
            $msg['result']['data'] = $token_status;
        }
        
        return $msg;
    }
    
    $msg['code'] = 0;
    $msg['result']['msg'] = 'Platform tidak didukung untuk pengecekan token';
    return $msg;
}

function check_facebook_token_status($access_token){
    global $app;
    
    // Gunakan app access token untuk debug token
    $app_access_token = $app->config->fb_app_id . '|' . $app->config->fb_app_secret;
    
    // URL untuk debug token
    $debug_url = 'https://graph.facebook.com/v21.0/debug_token';
    $params = array(
        'input_token' => $access_token,
        'access_token' => $app_access_token
    );
    
    // Build URL dengan parameter
    $url = $debug_url . '?' . http_build_query($params);
    
    // Get response dari Facebook Graph API
    $response = file_get_contents($url);
    $data = json_decode($response, true);
    
    if($data && isset($data['data'])){
        $token_data = $data['data'];
        
        // Cek apakah ada error
        if(isset($token_data['error'])){
            return array(
                'is_valid' => false,
                'error_message' => $token_data['error']['message'],
                'error_code' => $token_data['error']['code'],
                'error_subcode' => isset($token_data['error']['subcode']) ? $token_data['error']['subcode'] : null,
                //'token_info' => $token_data
            );
        }
        
        // Cek apakah token valid
        if(isset($token_data['is_valid']) && $token_data['is_valid'] === true){
            return array(
                'is_valid' => true,
                'error_message' => null,
                'error_code' => null,
                'error_subcode' => null,
                //'token_info' => $token_data
            );
        } else {
            return array(
                'is_valid' => false,
                'error_message' => 'Token tidak valid atau telah kadaluarsa',
                'error_code' => null,
                'error_subcode' => null,
                //'token_info' => $token_data
            );
        }
    }
    
    // Jika response tidak valid
    return array(
        'is_valid' => false,
        'error_message' => 'Gagal mendapatkan informasi token dari Facebook',
        'error_code' => null,
        'error_subcode' => null,
        'token_info' => null
    );
}

function grab($param){
    global $app;
    $rules = array(
        'integration_id'      => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $acc = new account($project_id);
    
    $res = $acc->get_detail_id($integration_id);

    if($res["platform"] == "facebook" || $res["type"] == "ctwa"){

        $res =$acc->grab_fb($res);
    }
    return $res;
   // var_dump($res);
    if($res != NULL){
       // $res['data'] = unserialize($res['data']);

        if($res['platform']=='facebook'){
            $fb = new \Facebook\Facebook([
                'app_id' => $app->config->fb_app_id,
                'app_secret' => $app->config->fb_app_secret,
                'default_graph_version' => 'v20.0',
            ]); 
            $response = $fb->get($data_grab, $res['data']['access_token']);
            
          
            $res = $response->getDecodedBody();
              
        }
        $msg["result"]['data'] = $res;
        $msg['code'] = 1;
    }else{
        $msg["result"]['msg'] = 'not found';
        $msg['code'] = 0;
    }
    return $msg;
}

function message($param){
    $message = $param['data'];
    $rules = array(
        'project_id' => 'required',
        'account_id' => 'required',
     );
    validate_param($rules, $param);
    extract($param);     
    assign_child_db($project_id,"localhost");
    global $keya, $c_time, $app;
    $db = $app->db;
    $db2 = $app->db2;
    $db2->where("account_id = ?", [$account_id]);
    $db2->update("account", ["message" => $message]);
    $msg["code"] = 1;
    $msg["result"]["msg"] = "success update integration account";
    return $msg;
}