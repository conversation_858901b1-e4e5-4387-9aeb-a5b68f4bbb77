<?php
namespace Aws\CodeGuruProfiler;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon CodeGuru Profiler** service.
 * @method \Aws\Result addNotificationChannels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise addNotificationChannelsAsync(array $args = [])
 * @method \Aws\Result batchGetFrameMetricData(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchGetFrameMetricDataAsync(array $args = [])
 * @method \Aws\Result configureAgent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise configureAgentAsync(array $args = [])
 * @method \Aws\Result createProfilingGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createProfilingGroupAsync(array $args = [])
 * @method \Aws\Result deleteProfilingGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteProfilingGroupAsync(array $args = [])
 * @method \Aws\Result describeProfilingGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeProfilingGroupAsync(array $args = [])
 * @method \Aws\Result getFindingsReportAccountSummary(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getFindingsReportAccountSummaryAsync(array $args = [])
 * @method \Aws\Result getNotificationConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getNotificationConfigurationAsync(array $args = [])
 * @method \Aws\Result getPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPolicyAsync(array $args = [])
 * @method \Aws\Result getProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getProfileAsync(array $args = [])
 * @method \Aws\Result getRecommendations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getRecommendationsAsync(array $args = [])
 * @method \Aws\Result listFindingsReports(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFindingsReportsAsync(array $args = [])
 * @method \Aws\Result listProfileTimes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listProfileTimesAsync(array $args = [])
 * @method \Aws\Result listProfilingGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listProfilingGroupsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result postAgentProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise postAgentProfileAsync(array $args = [])
 * @method \Aws\Result putPermission(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putPermissionAsync(array $args = [])
 * @method \Aws\Result removeNotificationChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise removeNotificationChannelAsync(array $args = [])
 * @method \Aws\Result removePermission(array $args = [])
 * @method \GuzzleHttp\Promise\Promise removePermissionAsync(array $args = [])
 * @method \Aws\Result submitFeedback(array $args = [])
 * @method \GuzzleHttp\Promise\Promise submitFeedbackAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateProfilingGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateProfilingGroupAsync(array $args = [])
 */
class CodeGuruProfilerClient extends AwsClient {}
