<?php
/* 
function add($param){
    global $keya, $c_time, $app, $project_id;
    $db = $app->db;
    $rules = array( 
        'project_key' => 'required',
    );
    if (isset($param["vid"])) {
        $param["visitor_id"] = $param["vid"];
    }
    if (isset($param["visitor_id"])) {
        $param["visitor_id"] = $param["visitor_id"];
    }
    validate_param($rules, $param);
    extract($param);
    $memcached = new Memcached();
    $project_key = $param["project_key"];
    $memcached->addServer('127.0.0.1', 11211);

    if (extension_loaded('memcached')) {
        $mem_key = 'GetProjectByKey_' . $project_key;
        $project = $memcached->get($mem_key);
    }
    if (empty($project)) {
        $db->where("project_key = UNHEX(?)", [$project_key]);
        $project = $db->getone("project");
        $expiration = 3600 * 24 * 30;
        if (extension_loaded('memcached')) {
            $memcached->set($mem_key, $project, $expiration);
        }
    }


    if ($project != null) {
        $project_id = $project["project_id"];
        assign_child_db($project_id);
        $db2 = $app->db2;
        $data = array();
        if (isset($param["name"])) {
            $data["name"] = $param["name"];
        }
        if (isset($param["email"])) {
            $data["email"] = $param["email"];
        }
        if (isset($param["msg"])) {
            $data["msg"] = $param["msg"];
        }

        foreach ($data as $key => $value) {
            $pattern = "/[^a-zA-Z0-9\?\.,!@\n \x{1F600}-\x{1F64F}\x{1F300}-\x{1F5FF}]/u";

            // Use preg_replace to remove unwanted characters
            $cleanedString = preg_replace($pattern, '', $value);

            $data[$key] = $cleanedString;
        }
        $set_divisi = "";
        if(isset($divisi)){
            $set_divisi = $divisi;
        }
        $visitor_id = str_replace(".", "", $visitor_id);
        $visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");

        $form = new form();
        $res = $form->add($visitor_id, $phone, $data,$set_divisi);
        if ($res["code"] == 1) {
            $divisi_slug = $divisi;
            if (extension_loaded('memcached')) {
                $mem_key = $project["project_id"] . '_GetDivisiByName_' . $divisi_slug;
                $divisi = $memcached->get($mem_key);
            }
            if (empty($divisi)) {
                $db2->where("divisi_key = UNHEX(?)", [md5($divisi_slug)]);
                $divisi = $db2->getone("divisi");
                $expiration = 3600;
                if (extension_loaded('memcached')) {
                    $memcached->set($mem_key, $divisi, $expiration);
                }
            }
            $pembagian = new cs_pembagian();
            if ($divisi != null) {
                $cs = $pembagian->get_cs($divisi["divisi_id"]);
            } else {
                $all_cs = true;
            }
            if ($all_cs) {
                $cs = $pembagian->random();
            }
            $cs_key = md5($cs);
            $db2->where("form_id", $res["id"]);
            $db2->update("form", array("cs_key" => $db2->func("UNHEX(?)",[$cs_key])));
            unset($res["id"]);
            $res["result"]["visitor_id"] = $visitor_id;
            $res["result"]["msg"] = 'Success add visitor';
            $res["result"]['phone_cs'] = $cs;
        }
        return $res;
    }else{
        $return["code"] = 0;
        $return["result"]["msg"] = "Project not found";
        return $return;
    }

} */

function update($param)
{
    $log_data = [
        'timestamp' => date("Y-m-d H:i:s"),
        'params' => $param
    ];
    $log_message = json_encode($log_data) . "\n";
    file_put_contents("log/form_update.log", $log_message, FILE_APPEND);


    global $keya, $c_time, $app, $project_id;
    $db = $app->db;
    $rules = array(
        'project_key' => 'required',
        'visitor_id' => 'required',
    );
    validate_param($rules, $param);
    extract($param);



    $memcached = new Memcached();
    $project_key = $param["project_key"];
    $memcached->addServer('127.0.0.1', 11211);

    $vid = raw_to_vid($visitor_id);

    if (extension_loaded('memcached')) {
        $mem_key = 'GetProjectByKey_' . $project_key;
        $project = $memcached->get($mem_key);
    }
    if (empty($project)) {
        $db->where("project_key = UNHEX(?)", [$project_key]);
        $project = $db->getone("project");
        $expiration = 3600 * 24 * 30;
        if (extension_loaded('memcached')) {
            $memcached->set($mem_key, $project, $expiration);
        }
    }

    if ($project != null) {
        $project_id = $project["project_id"];
        assign_child_db($project_id);
        $db2 = $app->db2;
        $data = array();

        if (isset($param["s"])) {
            if ($param["s"] == "samabe") {
                if (isset($param["gass_email"])) {
                    $email = $param["gass_email"];
                }
            }
        }

        try {
            if (isset($phone)) {
                $db2->where("visitor_id", $vid);
                if (!$db2->update("visitor", ["phone" => $phone])) {
                    throw new Exception("Failed to update phone for visitor_id");
                }
            }

            $db2->where("visitor_id", $vid);
            $visitor = $db2->getone("visitor");

            $visitor_data = unserialize($visitor["data"]);



            if (isset($name)) {
                $data["data"]["name"] = $name;
            }
            if (isset($email)) {
                $data["email"] = strtolower(preg_replace('/\s+/', '', $email));
                $visitor_data["email"] = $data["email"];
                $db2->where("visitor_id", $vid);
                $db2->update("visitor", ["data" => serialize($visitor_data)]);
            }

            if (isset($data)) {
                $data["data"] = serialize($data["data"]);
            }
            if (isset($data)) {
                $data["visitor_id"] = $vid;
                $db2->where("visitor_id", $vid);
                $db2->orwhere("email", $data["email"]);
                $db2->orderBy("created", "DESC");
                $existingVisitor = $db2->getOne("visitor_custom");

                if (empty($existingVisitor)) {
                    if (!$db2->insert("visitor_custom", $data)) {
                        throw new Exception("Failed to insert");
                    }
                } else {
                    $db2->where("visitor_id", $existingVisitor["visitor_id"]);
                    if (!$db2->update("visitor_custom", $data)) {
                        throw new Exception("Failed to update");
                    }
                }


            }



        } catch (Exception $e) {
            return ["code" => 0, "msg" => $e->getMessage()];
        }

        $return["code"] = 1;
        $return["result"]["msg"] = "Success";
        return $return;

    } else {
        $return["code"] = 0;
        $return["result"]["msg"] = "Project not found";
        return $return;
    }

}


function trigger_custom($param)
{


    $log_data = [
        'timestamp' => date("Y-m-d H:i:s"),
        'params' => $param
    ];
    $log_message = json_encode($log_data) . "\n";
    file_put_contents("log/trigger_custom.log", $log_message, FILE_APPEND);


    global $keya, $c_time, $app;
    $db = $app->db;
    $rules = array(
        'project_key' => 'required',
    );
    validate_param($rules, $param);
    extract($param);
    if (!isset($event)) {
        return ["status" => "error", "message" => "event is required"];
    }
    $db->where("project_key = UNHEX(?)", [$project_key]);
    $project = $db->getone("project");





    if ($db->count > 0) {
        $project_id = $project["project_id"];
        assign_child_db($project_id);
        $db2 = $app->db2;

        if (isset($param["visitor_id"])) {
            $visitor_id = $param["visitor_id"];
            $visitor_id = str_replace(".", "", $visitor_id);
            $visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");
            $db2->where("visitor_id", $visitor_id);
            $visitor = $db2->getone("visitor");
        }
        if ($visitor == NULL) {
            if (isset($email)) {
                $email = strtolower(preg_replace('/\s+/', '', $email));
                $db2->where("email", $email);
                $db2->orderBy("visitor.created", "DESC");
                $visitor = $db2->join("visitor", "visitor.visitor_id = visitor_custom.visitor_id")
                    ->getone("visitor_custom");


            }
        }
        if ($visitor == NULL) {
            if (isset($phone)) {
                $db2->where("phone", $phone);
                $db2->orderBy("visitor.created", "DESC");
                $visitor = $db2->getone("visitor");


            }
        }



        if ($visitor != null) {
            $t = new track();
            $message_id = NULL;
            $data_order = null;
            $pesan = null;
            $visitor_id = $visitor["visitor_id"];
            $phone = isset($visitor["phone"]) ? $visitor["phone"] : null;

            if (isset($email)) {
                $visitor_data = isset($visitor["data"]) && !empty($visitor["data"]) ? unserialize($visitor["data"]) : [];
                $visitor_data["email"] = $email;
                $db2->where("visitor_id", $visitor_id);
                $db2->update("visitor", ["data" => serialize($visitor_data)]);

            }

            /*
           if (empty($phone)) {
               return ["status" => "error", "message" => "data not found"];
           } */

            if ($event == "addtocart") {
                $cta_visitor_id = convBase($visitor_id, "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
                $t->cta($cta_visitor_id);
                return ["status" => "success", "message" => "true"];
            }



            if ($event == "mql") {
                $x = $t->mql($nope_cs, $visitor, $phone, $message_id);
                return ["status" => "success", "message" => $x];
            }

            if ($event == "prospek") {
                $x = $t->prospek($nope_cs, $visitor, $phone, $message_id);
                return ["status" => "success", "message" => $x];
            }

            if ($event == "purchase") {
                if (isset($value)) {
                    $x = $t->purchase($nope_cs, $visitor, $phone, $value, $message_id, $data_order, $pesan);
                    return ["status" => "success", "message" => $x];
                } else {
                    return ["status" => "error", "message" => "empty value"];
                }
            }
        } else {
            return ["status" => "error", "message" => "visitor not found"];
        }
    } else {
        return ["status" => "error", "message" => "Project Not Found"];
    }
}