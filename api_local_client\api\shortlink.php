<?php

use FacebookAds\Logger\NullLogger;

function add($param)
{
    $rules = [
        "project_id" => "required",
        "subdomain" => "required",
        "name" => "required",
        "divisi_key" => "required",
        "msg" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $s = new shortlink($project_id);
    $data_connector = null;
    if(isset($connector)){
        $data_connector = $connector;
    }
    $msg = $s->add_shortlink($subdomain,$name,$divisi_key,$msg,$data_connector);

    return $msg;
}

function delete($param)
{
    $rules = [
        "project_id" => "required",
        "shortlink_key" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $d = new shortlink($project_id);
    $msg = $d->delete_shortlink($shortlink_key);

    return $msg;
}

function get($param)
{
    $rules = [
        "project_id" => "required",
        "page" => "required|numeric"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $current_page = $page ?? 0;
    $d = new shortlink($project_id);
    $msg = $d->get_shortlink($page);

    return $msg;
}

function update($param){
    $rules = [
        "project_id" => "required",
        "subdomain" => "required",
        "name" => "required",
        "divisi_key" => "required",
        "msg" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $s = new shortlink($project_id);
    $data_connector = null;
    if(isset($connector)){
        $data_connector = $connector;
    }
    $msg = $s->update_shortlink($subdomain,$name,$divisi_key,$msg,$data_connector);

    return $msg;
}