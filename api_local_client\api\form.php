<?php

function send_message($param){
    global $app;
    $rules = array(
        'visitor_id'      => 'required',
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $form = new form();
    
    $db2 = $app->db2;
    $data = $form->send_message($visitor_id);
    return $data;
}

function get($param)
{
    $rules = array(
        'start'      => 'required',
        'end'      => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $form = new form();
    global $app;
    $db2 = $app->db2;
    $data = $form->get($start,$end);
    return $data;
}

function set_setting($param)
{
    extract($param);

    assign_child_db($project_id);

   
   $form = new form();
   global $app;
   $db2 = $app->db2;

  

   $data = $form->set_setting($param);


    return $data;
}

function get_setting($param)
{
    extract($param);

    assign_child_db($project_id);

   
   $form = new form();
   global $app;
   $db2 = $app->db2;

  

   $data = $form->get_setting();


    return $data;
}