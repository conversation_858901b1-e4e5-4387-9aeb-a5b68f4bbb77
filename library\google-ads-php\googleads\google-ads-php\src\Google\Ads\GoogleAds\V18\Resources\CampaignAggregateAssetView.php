<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/resources/campaign_aggregate_asset_view.proto

namespace Google\Ads\GoogleAds\V18\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A campaign-level aggregate asset view that shows where the asset is linked,
 * performamce of the asset and stats.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.resources.CampaignAggregateAssetView</code>
 */
class CampaignAggregateAssetView extends \Google\Protobuf\Internal\Message
{
    /**
     * Output only. The resource name of the campaign aggregate asset view.
     * Campaign aggregate asset view resource names have the form:
     * `customers/{customer_id}/campaignAggregateAssetViews/{Campaign.campaign_id}~{Asset.asset_id}~{AssetLinkSource.asset_link_source}~{AssetFieldType.field_type}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     */
    protected $resource_name = '';
    /**
     * Output only. Campaign in which the asset served.
     *
     * Generated from protobuf field <code>optional string campaign = 2 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     */
    protected $campaign = null;
    /**
     * Output only. The ID of the asset.
     *
     * Generated from protobuf field <code>optional string asset = 3 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     */
    protected $asset = null;
    /**
     * Output only. Source of the asset link.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AssetSourceEnum.AssetSource asset_source = 4 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $asset_source = null;
    /**
     * Output only. FieldType of the asset.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AssetFieldTypeEnum.AssetFieldType field_type = 5 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $field_type = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $resource_name
     *           Output only. The resource name of the campaign aggregate asset view.
     *           Campaign aggregate asset view resource names have the form:
     *           `customers/{customer_id}/campaignAggregateAssetViews/{Campaign.campaign_id}~{Asset.asset_id}~{AssetLinkSource.asset_link_source}~{AssetFieldType.field_type}`
     *     @type string $campaign
     *           Output only. Campaign in which the asset served.
     *     @type string $asset
     *           Output only. The ID of the asset.
     *     @type int $asset_source
     *           Output only. Source of the asset link.
     *     @type int $field_type
     *           Output only. FieldType of the asset.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Resources\CampaignAggregateAssetView::initOnce();
        parent::__construct($data);
    }

    /**
     * Output only. The resource name of the campaign aggregate asset view.
     * Campaign aggregate asset view resource names have the form:
     * `customers/{customer_id}/campaignAggregateAssetViews/{Campaign.campaign_id}~{Asset.asset_id}~{AssetLinkSource.asset_link_source}~{AssetFieldType.field_type}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getResourceName()
    {
        return $this->resource_name;
    }

    /**
     * Output only. The resource name of the campaign aggregate asset view.
     * Campaign aggregate asset view resource names have the form:
     * `customers/{customer_id}/campaignAggregateAssetViews/{Campaign.campaign_id}~{Asset.asset_id}~{AssetLinkSource.asset_link_source}~{AssetFieldType.field_type}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setResourceName($var)
    {
        GPBUtil::checkString($var, True);
        $this->resource_name = $var;

        return $this;
    }

    /**
     * Output only. Campaign in which the asset served.
     *
     * Generated from protobuf field <code>optional string campaign = 2 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getCampaign()
    {
        return isset($this->campaign) ? $this->campaign : '';
    }

    public function hasCampaign()
    {
        return isset($this->campaign);
    }

    public function clearCampaign()
    {
        unset($this->campaign);
    }

    /**
     * Output only. Campaign in which the asset served.
     *
     * Generated from protobuf field <code>optional string campaign = 2 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setCampaign($var)
    {
        GPBUtil::checkString($var, True);
        $this->campaign = $var;

        return $this;
    }

    /**
     * Output only. The ID of the asset.
     *
     * Generated from protobuf field <code>optional string asset = 3 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getAsset()
    {
        return isset($this->asset) ? $this->asset : '';
    }

    public function hasAsset()
    {
        return isset($this->asset);
    }

    public function clearAsset()
    {
        unset($this->asset);
    }

    /**
     * Output only. The ID of the asset.
     *
     * Generated from protobuf field <code>optional string asset = 3 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setAsset($var)
    {
        GPBUtil::checkString($var, True);
        $this->asset = $var;

        return $this;
    }

    /**
     * Output only. Source of the asset link.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AssetSourceEnum.AssetSource asset_source = 4 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getAssetSource()
    {
        return isset($this->asset_source) ? $this->asset_source : 0;
    }

    public function hasAssetSource()
    {
        return isset($this->asset_source);
    }

    public function clearAssetSource()
    {
        unset($this->asset_source);
    }

    /**
     * Output only. Source of the asset link.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AssetSourceEnum.AssetSource asset_source = 4 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setAssetSource($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\AssetSourceEnum\AssetSource::class);
        $this->asset_source = $var;

        return $this;
    }

    /**
     * Output only. FieldType of the asset.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AssetFieldTypeEnum.AssetFieldType field_type = 5 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getFieldType()
    {
        return isset($this->field_type) ? $this->field_type : 0;
    }

    public function hasFieldType()
    {
        return isset($this->field_type);
    }

    public function clearFieldType()
    {
        unset($this->field_type);
    }

    /**
     * Output only. FieldType of the asset.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AssetFieldTypeEnum.AssetFieldType field_type = 5 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setFieldType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\AssetFieldTypeEnum\AssetFieldType::class);
        $this->field_type = $var;

        return $this;
    }

}

