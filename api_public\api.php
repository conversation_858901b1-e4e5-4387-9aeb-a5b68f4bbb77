<?php
global $app;  
$db = $app->db;
header('Access-Control-Allow-Origin: *'); 
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');
//header("Access-Control-Allow-Headers: X-Requested-With");
set_time_limit(0);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
//file_put_contents('server.txt', json_encode($_SERVER));
if (count($_GET) > 0) {
    $_POST = array_merge($_POST,$_GET);
} else {    
    
}
// $data = @file_get_contents("post_log.txt");
// $content = "\n\n\n".date("Y-m-d H:i:s") . "\n\n". @json_encode($_POST);
// $data .= $content;
// @file_put_contents("post_log.txt", $data);

foreach ($_POST as $key => $value) {

    if(!is_array($value))
    {
        if($value == "null" || $value == "undefined")
        {
            unset($_POST[$key]);
        }
    }else{
        foreach ($value as $key2 => $value2) {
            if(!is_array($value2))
            {
                if($value2 == "null" || $value2 == "undefined")
                {
                    unset($_POST[$key][$key2]);
                }
            }
            else{
                foreach ($value2 as $key3 => $value3) {
                    if(!is_array($value3))
                    {
                        if($value3 == "null" || $value3 == "undefined")
                        {
                            unset($_POST[$key][$key2][$key3]);
                        }
                    }
                }
            }

        }
    }
}

header('Content-Type: application/json');
if (cekp("act")) {
    $act = p("act"); 
    $tmp = explode("_", $act); 
    if (count($tmp) >= 2) {
        $file = "api_public/api/".$tmp[0].".php";
        $file_tmp = $tmp[0];
        if (file_exists($file)) {
            include "api/".$tmp[0].".php";
            
        }else{
            $res["code"] = 0;
            $res["msg"] = "act API not found";
            echo json_encode($res);
            die();
        }
        unset($tmp[0]);
        $func = implode("_", $tmp);
        if (!function_exists($func)) {;die();}

        //try {
            if(isset($_FILES)){           
                $res = @$func($_POST, $_FILES);
            }else{
                $res = @$func($_POST);
            }        
            echo json_encode($res);
//        }
//        //catch exception
//        catch(Exception $e) {
//            $res["code"] = 0;
//            $res["msg"] = $e->getMessage();
//            echo json_encode($res);
//        }
        die();
    }
}
die();