<?php

class OpenRouter {
    private $apiKey;
    private $apiUrl = 'https://openrouter.ai/api/v1';
    private $model = 'openai/gpt-5-nano';
    private $curlOptions = [];
    
    /**
     * Constructor to initialize the OpenRouter client
     * 
     * @param string $apiKey Your OpenRouter API key
     * @param array $options Additional options (model, apiUrl, curlOptions)
     */
    public function __construct($apiKey, $options = []) {
        if (empty($apiKey)) {
            throw new InvalidArgumentException('API key is required');
        }
        
        $this->apiKey = $apiKey;
        
        if (isset($options['model'])) {
            $this->model = $options['model'];
        }
        
        if (isset($options['apiUrl'])) {
            $this->apiUrl = $options['apiUrl'];
        }
        
        if (isset($options['curlOptions'])) {
            $this->curlOptions = $options['curlOptions'];
        }
    }
    
    /**
     * Send a chat completion request
     * 
     * @param array $messages Array of message objects
     * @param array $options Additional options for the request
     * @return array Response from the API
     */
    public function chatCompletion($messages, $options = []) {
        $data = [
            'model' => $options['model'] ?? $this->model,
            'messages' => $messages
        ];
        
        // Merge additional options
        $data = array_merge($data, $options);
        
        return $this->sendRequest('/chat/completions', $data);
    }
    
    /**
     * Send a request to the OpenRouter API
     * 
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @return array Decoded JSON response
     */
    private function sendRequest($endpoint, $data) {
        $url = $this->apiUrl . $endpoint;
        
        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json',
            'HTTP-Referer: ' . $this->getCurrentUrl(),
            'X-Title: PHP OpenRouter Client'
        ];
        
        $ch = curl_init();
        
        $curlOptions = [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30
        ];
        
        // Merge custom curl options
        $curlOptions = $curlOptions + $this->curlOptions;
        
        curl_setopt_array($ch, $curlOptions);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            throw new Exception('cURL Error: ' . $error);
        }
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 400) {
            $errorMessage = 'API Error';
            if (isset($decodedResponse['error'])) {
                $errorMessage = $decodedResponse['error']['message'] ?? $errorMessage;
            }
            throw new Exception('HTTP Error ' . $httpCode . ': ' . $errorMessage, $httpCode);
        }
        
        return $decodedResponse;
    }
    
    /**
     * Get current URL for referer header
     * 
     * @return string Current URL or placeholder
     */
    private function getCurrentUrl() {
        if (isset($_SERVER['HTTP_HOST']) && isset($_SERVER['REQUEST_URI'])) {
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            return $protocol . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        }
        return 'https://example.com';
    }
    
    /**
     * Set the default model
     * 
     * @param string $model Model name
     */
    public function setModel($model) {
        $this->model = $model;
    }
    
    /**
     * Get the default model
     * 
     * @return string Model name
     */
    public function getModel() {
        return $this->model;
    }
}
