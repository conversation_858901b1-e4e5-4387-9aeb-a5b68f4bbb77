<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/resources/ad_group_criterion.proto

namespace Google\Ads\GoogleAds\V18\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * An ad group criterion.
 * The ad_group_criterion report only returns criteria that were explicitly
 * added to the ad group.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.resources.AdGroupCriterion</code>
 */
class AdGroupCriterion extends \Google\Protobuf\Internal\Message
{
    /**
     * Immutable. The resource name of the ad group criterion.
     * Ad group criterion resource names have the form:
     * `customers/{customer_id}/adGroupCriteria/{ad_group_id}~{criterion_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     */
    protected $resource_name = '';
    /**
     * Output only. The ID of the criterion.
     * This field is ignored for mutates.
     *
     * Generated from protobuf field <code>optional int64 criterion_id = 56 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $criterion_id = null;
    /**
     * Output only. The display name of the criterion.
     * This field is ignored for mutates.
     *
     * Generated from protobuf field <code>string display_name = 77 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $display_name = '';
    /**
     * The status of the criterion.
     * This is the status of the ad group criterion entity, set by the client.
     * Note: UI reports may incorporate additional information that affects
     * whether a criterion is eligible to run. In some cases a criterion that's
     * REMOVED in the API can still show as enabled in the UI.
     * For example, campaigns by default show to users of all age ranges unless
     * excluded. The UI will show each age range as "enabled", since they're
     * eligible to see the ads; but AdGroupCriterion.status will show "removed",
     * since no positive criterion was added.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupCriterionStatusEnum.AdGroupCriterionStatus status = 3;</code>
     */
    protected $status = 0;
    /**
     * Output only. Information regarding the quality of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.resources.AdGroupCriterion.QualityInfo quality_info = 4 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $quality_info = null;
    /**
     * Immutable. The ad group to which the criterion belongs.
     *
     * Generated from protobuf field <code>optional string ad_group = 57 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     */
    protected $ad_group = null;
    /**
     * Output only. The type of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.CriterionTypeEnum.CriterionType type = 25 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $type = 0;
    /**
     * Immutable. Whether to target (`false`) or exclude (`true`) the criterion.
     * This field is immutable. To switch a criterion from positive to negative,
     * remove then re-add it.
     *
     * Generated from protobuf field <code>optional bool negative = 58 [(.google.api.field_behavior) = IMMUTABLE];</code>
     */
    protected $negative = null;
    /**
     * Output only. Serving status of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.CriterionSystemServingStatusEnum.CriterionSystemServingStatus system_serving_status = 52 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $system_serving_status = 0;
    /**
     * Output only. Approval status of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupCriterionApprovalStatusEnum.AdGroupCriterionApprovalStatus approval_status = 53 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $approval_status = 0;
    /**
     * Output only. List of disapproval reasons of the criterion.
     * The different reasons for disapproving a criterion can be found here:
     * https://support.google.com/adspolicy/answer/6008942
     * This field is read-only.
     *
     * Generated from protobuf field <code>repeated string disapproval_reasons = 59 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    private $disapproval_reasons;
    /**
     * Output only. The resource names of labels attached to this ad group
     * criterion.
     *
     * Generated from protobuf field <code>repeated string labels = 60 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     */
    private $labels;
    /**
     * The modifier for the bid when the criterion matches. The modifier must be
     * in the range: 0.1 - 10.0. Most targetable criteria types support modifiers.
     *
     * Generated from protobuf field <code>optional double bid_modifier = 61;</code>
     */
    protected $bid_modifier = null;
    /**
     * The CPC (cost-per-click) bid.
     *
     * Generated from protobuf field <code>optional int64 cpc_bid_micros = 62;</code>
     */
    protected $cpc_bid_micros = null;
    /**
     * The CPM (cost-per-thousand viewable impressions) bid.
     *
     * Generated from protobuf field <code>optional int64 cpm_bid_micros = 63;</code>
     */
    protected $cpm_bid_micros = null;
    /**
     * The CPV (cost-per-view) bid.
     *
     * Generated from protobuf field <code>optional int64 cpv_bid_micros = 64;</code>
     */
    protected $cpv_bid_micros = null;
    /**
     * The CPC bid amount, expressed as a fraction of the advertised price
     * for some good or service. The valid range for the fraction is [0,1) and the
     * value stored here is 1,000,000 * [fraction].
     *
     * Generated from protobuf field <code>optional int64 percent_cpc_bid_micros = 65;</code>
     */
    protected $percent_cpc_bid_micros = null;
    /**
     * Output only. The effective CPC (cost-per-click) bid.
     *
     * Generated from protobuf field <code>optional int64 effective_cpc_bid_micros = 66 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $effective_cpc_bid_micros = null;
    /**
     * Output only. The effective CPM (cost-per-thousand viewable impressions)
     * bid.
     *
     * Generated from protobuf field <code>optional int64 effective_cpm_bid_micros = 67 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $effective_cpm_bid_micros = null;
    /**
     * Output only. The effective CPV (cost-per-view) bid.
     *
     * Generated from protobuf field <code>optional int64 effective_cpv_bid_micros = 68 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $effective_cpv_bid_micros = null;
    /**
     * Output only. The effective Percent CPC bid amount.
     *
     * Generated from protobuf field <code>optional int64 effective_percent_cpc_bid_micros = 69 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $effective_percent_cpc_bid_micros = null;
    /**
     * Output only. Source of the effective CPC bid.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_cpc_bid_source = 21 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $effective_cpc_bid_source = 0;
    /**
     * Output only. Source of the effective CPM bid.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_cpm_bid_source = 22 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $effective_cpm_bid_source = 0;
    /**
     * Output only. Source of the effective CPV bid.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_cpv_bid_source = 23 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $effective_cpv_bid_source = 0;
    /**
     * Output only. Source of the effective Percent CPC bid.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_percent_cpc_bid_source = 35 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $effective_percent_cpc_bid_source = 0;
    /**
     * Output only. Estimates for criterion bids at various positions.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.resources.AdGroupCriterion.PositionEstimates position_estimates = 10 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $position_estimates = null;
    /**
     * The list of possible final URLs after all cross-domain redirects for the
     * ad.
     *
     * Generated from protobuf field <code>repeated string final_urls = 70;</code>
     */
    private $final_urls;
    /**
     * The list of possible final mobile URLs after all cross-domain redirects.
     *
     * Generated from protobuf field <code>repeated string final_mobile_urls = 71;</code>
     */
    private $final_mobile_urls;
    /**
     * URL template for appending params to final URL.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 72;</code>
     */
    protected $final_url_suffix = null;
    /**
     * The URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 73;</code>
     */
    protected $tracking_url_template = null;
    /**
     * The list of mappings used to substitute custom parameter tags in a
     * `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.CustomParameter url_custom_parameters = 14;</code>
     */
    private $url_custom_parameters;
    /**
     * Output only. The primary status for the ad group criterion.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AdGroupCriterionPrimaryStatusEnum.AdGroupCriterionPrimaryStatus primary_status = 85 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $primary_status = null;
    /**
     * Output only. The primary status reasons for the ad group criterion.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.enums.AdGroupCriterionPrimaryStatusReasonEnum.AdGroupCriterionPrimaryStatusReason primary_status_reasons = 86 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    private $primary_status_reasons;
    protected $criterion;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $resource_name
     *           Immutable. The resource name of the ad group criterion.
     *           Ad group criterion resource names have the form:
     *           `customers/{customer_id}/adGroupCriteria/{ad_group_id}~{criterion_id}`
     *     @type int|string $criterion_id
     *           Output only. The ID of the criterion.
     *           This field is ignored for mutates.
     *     @type string $display_name
     *           Output only. The display name of the criterion.
     *           This field is ignored for mutates.
     *     @type int $status
     *           The status of the criterion.
     *           This is the status of the ad group criterion entity, set by the client.
     *           Note: UI reports may incorporate additional information that affects
     *           whether a criterion is eligible to run. In some cases a criterion that's
     *           REMOVED in the API can still show as enabled in the UI.
     *           For example, campaigns by default show to users of all age ranges unless
     *           excluded. The UI will show each age range as "enabled", since they're
     *           eligible to see the ads; but AdGroupCriterion.status will show "removed",
     *           since no positive criterion was added.
     *     @type \Google\Ads\GoogleAds\V18\Resources\AdGroupCriterion\QualityInfo $quality_info
     *           Output only. Information regarding the quality of the criterion.
     *     @type string $ad_group
     *           Immutable. The ad group to which the criterion belongs.
     *     @type int $type
     *           Output only. The type of the criterion.
     *     @type bool $negative
     *           Immutable. Whether to target (`false`) or exclude (`true`) the criterion.
     *           This field is immutable. To switch a criterion from positive to negative,
     *           remove then re-add it.
     *     @type int $system_serving_status
     *           Output only. Serving status of the criterion.
     *     @type int $approval_status
     *           Output only. Approval status of the criterion.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $disapproval_reasons
     *           Output only. List of disapproval reasons of the criterion.
     *           The different reasons for disapproving a criterion can be found here:
     *           https://support.google.com/adspolicy/answer/6008942
     *           This field is read-only.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $labels
     *           Output only. The resource names of labels attached to this ad group
     *           criterion.
     *     @type float $bid_modifier
     *           The modifier for the bid when the criterion matches. The modifier must be
     *           in the range: 0.1 - 10.0. Most targetable criteria types support modifiers.
     *     @type int|string $cpc_bid_micros
     *           The CPC (cost-per-click) bid.
     *     @type int|string $cpm_bid_micros
     *           The CPM (cost-per-thousand viewable impressions) bid.
     *     @type int|string $cpv_bid_micros
     *           The CPV (cost-per-view) bid.
     *     @type int|string $percent_cpc_bid_micros
     *           The CPC bid amount, expressed as a fraction of the advertised price
     *           for some good or service. The valid range for the fraction is [0,1) and the
     *           value stored here is 1,000,000 * [fraction].
     *     @type int|string $effective_cpc_bid_micros
     *           Output only. The effective CPC (cost-per-click) bid.
     *     @type int|string $effective_cpm_bid_micros
     *           Output only. The effective CPM (cost-per-thousand viewable impressions)
     *           bid.
     *     @type int|string $effective_cpv_bid_micros
     *           Output only. The effective CPV (cost-per-view) bid.
     *     @type int|string $effective_percent_cpc_bid_micros
     *           Output only. The effective Percent CPC bid amount.
     *     @type int $effective_cpc_bid_source
     *           Output only. Source of the effective CPC bid.
     *     @type int $effective_cpm_bid_source
     *           Output only. Source of the effective CPM bid.
     *     @type int $effective_cpv_bid_source
     *           Output only. Source of the effective CPV bid.
     *     @type int $effective_percent_cpc_bid_source
     *           Output only. Source of the effective Percent CPC bid.
     *     @type \Google\Ads\GoogleAds\V18\Resources\AdGroupCriterion\PositionEstimates $position_estimates
     *           Output only. Estimates for criterion bids at various positions.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $final_urls
     *           The list of possible final URLs after all cross-domain redirects for the
     *           ad.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $final_mobile_urls
     *           The list of possible final mobile URLs after all cross-domain redirects.
     *     @type string $final_url_suffix
     *           URL template for appending params to final URL.
     *     @type string $tracking_url_template
     *           The URL template for constructing a tracking URL.
     *     @type array<\Google\Ads\GoogleAds\V18\Common\CustomParameter>|\Google\Protobuf\Internal\RepeatedField $url_custom_parameters
     *           The list of mappings used to substitute custom parameter tags in a
     *           `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     *     @type int $primary_status
     *           Output only. The primary status for the ad group criterion.
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $primary_status_reasons
     *           Output only. The primary status reasons for the ad group criterion.
     *     @type \Google\Ads\GoogleAds\V18\Common\KeywordInfo $keyword
     *           Immutable. Keyword.
     *     @type \Google\Ads\GoogleAds\V18\Common\PlacementInfo $placement
     *           Immutable. Placement.
     *     @type \Google\Ads\GoogleAds\V18\Common\MobileAppCategoryInfo $mobile_app_category
     *           Immutable. Mobile app category.
     *     @type \Google\Ads\GoogleAds\V18\Common\MobileApplicationInfo $mobile_application
     *           Immutable. Mobile application.
     *     @type \Google\Ads\GoogleAds\V18\Common\ListingGroupInfo $listing_group
     *           Immutable. Listing group.
     *     @type \Google\Ads\GoogleAds\V18\Common\AgeRangeInfo $age_range
     *           Immutable. Age range.
     *     @type \Google\Ads\GoogleAds\V18\Common\GenderInfo $gender
     *           Immutable. Gender.
     *     @type \Google\Ads\GoogleAds\V18\Common\IncomeRangeInfo $income_range
     *           Immutable. Income range.
     *     @type \Google\Ads\GoogleAds\V18\Common\ParentalStatusInfo $parental_status
     *           Immutable. Parental status.
     *     @type \Google\Ads\GoogleAds\V18\Common\UserListInfo $user_list
     *           Immutable. User List.
     *     @type \Google\Ads\GoogleAds\V18\Common\YouTubeVideoInfo $youtube_video
     *           Immutable. YouTube Video.
     *     @type \Google\Ads\GoogleAds\V18\Common\YouTubeChannelInfo $youtube_channel
     *           Immutable. YouTube Channel.
     *     @type \Google\Ads\GoogleAds\V18\Common\TopicInfo $topic
     *           Immutable. Topic.
     *     @type \Google\Ads\GoogleAds\V18\Common\UserInterestInfo $user_interest
     *           Immutable. User Interest.
     *     @type \Google\Ads\GoogleAds\V18\Common\WebpageInfo $webpage
     *           Immutable. Webpage
     *     @type \Google\Ads\GoogleAds\V18\Common\AppPaymentModelInfo $app_payment_model
     *           Immutable. App Payment Model.
     *     @type \Google\Ads\GoogleAds\V18\Common\CustomAffinityInfo $custom_affinity
     *           Immutable. Custom Affinity.
     *     @type \Google\Ads\GoogleAds\V18\Common\CustomIntentInfo $custom_intent
     *           Immutable. Custom Intent.
     *     @type \Google\Ads\GoogleAds\V18\Common\CustomAudienceInfo $custom_audience
     *           Immutable. Custom Audience.
     *     @type \Google\Ads\GoogleAds\V18\Common\CombinedAudienceInfo $combined_audience
     *           Immutable. Combined Audience.
     *     @type \Google\Ads\GoogleAds\V18\Common\AudienceInfo $audience
     *           Immutable. Audience.
     *     @type \Google\Ads\GoogleAds\V18\Common\LocationInfo $location
     *           Immutable. Location.
     *     @type \Google\Ads\GoogleAds\V18\Common\LanguageInfo $language
     *           Immutable. Language.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Resources\AdGroupCriterion::initOnce();
        parent::__construct($data);
    }

    /**
     * Immutable. The resource name of the ad group criterion.
     * Ad group criterion resource names have the form:
     * `customers/{customer_id}/adGroupCriteria/{ad_group_id}~{criterion_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getResourceName()
    {
        return $this->resource_name;
    }

    /**
     * Immutable. The resource name of the ad group criterion.
     * Ad group criterion resource names have the form:
     * `customers/{customer_id}/adGroupCriteria/{ad_group_id}~{criterion_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setResourceName($var)
    {
        GPBUtil::checkString($var, True);
        $this->resource_name = $var;

        return $this;
    }

    /**
     * Output only. The ID of the criterion.
     * This field is ignored for mutates.
     *
     * Generated from protobuf field <code>optional int64 criterion_id = 56 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int|string
     */
    public function getCriterionId()
    {
        return isset($this->criterion_id) ? $this->criterion_id : 0;
    }

    public function hasCriterionId()
    {
        return isset($this->criterion_id);
    }

    public function clearCriterionId()
    {
        unset($this->criterion_id);
    }

    /**
     * Output only. The ID of the criterion.
     * This field is ignored for mutates.
     *
     * Generated from protobuf field <code>optional int64 criterion_id = 56 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int|string $var
     * @return $this
     */
    public function setCriterionId($var)
    {
        GPBUtil::checkInt64($var);
        $this->criterion_id = $var;

        return $this;
    }

    /**
     * Output only. The display name of the criterion.
     * This field is ignored for mutates.
     *
     * Generated from protobuf field <code>string display_name = 77 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return string
     */
    public function getDisplayName()
    {
        return $this->display_name;
    }

    /**
     * Output only. The display name of the criterion.
     * This field is ignored for mutates.
     *
     * Generated from protobuf field <code>string display_name = 77 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param string $var
     * @return $this
     */
    public function setDisplayName($var)
    {
        GPBUtil::checkString($var, True);
        $this->display_name = $var;

        return $this;
    }

    /**
     * The status of the criterion.
     * This is the status of the ad group criterion entity, set by the client.
     * Note: UI reports may incorporate additional information that affects
     * whether a criterion is eligible to run. In some cases a criterion that's
     * REMOVED in the API can still show as enabled in the UI.
     * For example, campaigns by default show to users of all age ranges unless
     * excluded. The UI will show each age range as "enabled", since they're
     * eligible to see the ads; but AdGroupCriterion.status will show "removed",
     * since no positive criterion was added.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupCriterionStatusEnum.AdGroupCriterionStatus status = 3;</code>
     * @return int
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * The status of the criterion.
     * This is the status of the ad group criterion entity, set by the client.
     * Note: UI reports may incorporate additional information that affects
     * whether a criterion is eligible to run. In some cases a criterion that's
     * REMOVED in the API can still show as enabled in the UI.
     * For example, campaigns by default show to users of all age ranges unless
     * excluded. The UI will show each age range as "enabled", since they're
     * eligible to see the ads; but AdGroupCriterion.status will show "removed",
     * since no positive criterion was added.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupCriterionStatusEnum.AdGroupCriterionStatus status = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setStatus($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\AdGroupCriterionStatusEnum\AdGroupCriterionStatus::class);
        $this->status = $var;

        return $this;
    }

    /**
     * Output only. Information regarding the quality of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.resources.AdGroupCriterion.QualityInfo quality_info = 4 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V18\Resources\AdGroupCriterion\QualityInfo|null
     */
    public function getQualityInfo()
    {
        return $this->quality_info;
    }

    public function hasQualityInfo()
    {
        return isset($this->quality_info);
    }

    public function clearQualityInfo()
    {
        unset($this->quality_info);
    }

    /**
     * Output only. Information regarding the quality of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.resources.AdGroupCriterion.QualityInfo quality_info = 4 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V18\Resources\AdGroupCriterion\QualityInfo $var
     * @return $this
     */
    public function setQualityInfo($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Resources\AdGroupCriterion\QualityInfo::class);
        $this->quality_info = $var;

        return $this;
    }

    /**
     * Immutable. The ad group to which the criterion belongs.
     *
     * Generated from protobuf field <code>optional string ad_group = 57 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getAdGroup()
    {
        return isset($this->ad_group) ? $this->ad_group : '';
    }

    public function hasAdGroup()
    {
        return isset($this->ad_group);
    }

    public function clearAdGroup()
    {
        unset($this->ad_group);
    }

    /**
     * Immutable. The ad group to which the criterion belongs.
     *
     * Generated from protobuf field <code>optional string ad_group = 57 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setAdGroup($var)
    {
        GPBUtil::checkString($var, True);
        $this->ad_group = $var;

        return $this;
    }

    /**
     * Output only. The type of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.CriterionTypeEnum.CriterionType type = 25 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Output only. The type of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.CriterionTypeEnum.CriterionType type = 25 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\CriterionTypeEnum\CriterionType::class);
        $this->type = $var;

        return $this;
    }

    /**
     * Immutable. Whether to target (`false`) or exclude (`true`) the criterion.
     * This field is immutable. To switch a criterion from positive to negative,
     * remove then re-add it.
     *
     * Generated from protobuf field <code>optional bool negative = 58 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return bool
     */
    public function getNegative()
    {
        return isset($this->negative) ? $this->negative : false;
    }

    public function hasNegative()
    {
        return isset($this->negative);
    }

    public function clearNegative()
    {
        unset($this->negative);
    }

    /**
     * Immutable. Whether to target (`false`) or exclude (`true`) the criterion.
     * This field is immutable. To switch a criterion from positive to negative,
     * remove then re-add it.
     *
     * Generated from protobuf field <code>optional bool negative = 58 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param bool $var
     * @return $this
     */
    public function setNegative($var)
    {
        GPBUtil::checkBool($var);
        $this->negative = $var;

        return $this;
    }

    /**
     * Output only. Serving status of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.CriterionSystemServingStatusEnum.CriterionSystemServingStatus system_serving_status = 52 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getSystemServingStatus()
    {
        return $this->system_serving_status;
    }

    /**
     * Output only. Serving status of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.CriterionSystemServingStatusEnum.CriterionSystemServingStatus system_serving_status = 52 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setSystemServingStatus($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\CriterionSystemServingStatusEnum\CriterionSystemServingStatus::class);
        $this->system_serving_status = $var;

        return $this;
    }

    /**
     * Output only. Approval status of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupCriterionApprovalStatusEnum.AdGroupCriterionApprovalStatus approval_status = 53 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getApprovalStatus()
    {
        return $this->approval_status;
    }

    /**
     * Output only. Approval status of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AdGroupCriterionApprovalStatusEnum.AdGroupCriterionApprovalStatus approval_status = 53 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setApprovalStatus($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\AdGroupCriterionApprovalStatusEnum\AdGroupCriterionApprovalStatus::class);
        $this->approval_status = $var;

        return $this;
    }

    /**
     * Output only. List of disapproval reasons of the criterion.
     * The different reasons for disapproving a criterion can be found here:
     * https://support.google.com/adspolicy/answer/6008942
     * This field is read-only.
     *
     * Generated from protobuf field <code>repeated string disapproval_reasons = 59 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getDisapprovalReasons()
    {
        return $this->disapproval_reasons;
    }

    /**
     * Output only. List of disapproval reasons of the criterion.
     * The different reasons for disapproving a criterion can be found here:
     * https://support.google.com/adspolicy/answer/6008942
     * This field is read-only.
     *
     * Generated from protobuf field <code>repeated string disapproval_reasons = 59 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setDisapprovalReasons($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->disapproval_reasons = $arr;

        return $this;
    }

    /**
     * Output only. The resource names of labels attached to this ad group
     * criterion.
     *
     * Generated from protobuf field <code>repeated string labels = 60 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getLabels()
    {
        return $this->labels;
    }

    /**
     * Output only. The resource names of labels attached to this ad group
     * criterion.
     *
     * Generated from protobuf field <code>repeated string labels = 60 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setLabels($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->labels = $arr;

        return $this;
    }

    /**
     * The modifier for the bid when the criterion matches. The modifier must be
     * in the range: 0.1 - 10.0. Most targetable criteria types support modifiers.
     *
     * Generated from protobuf field <code>optional double bid_modifier = 61;</code>
     * @return float
     */
    public function getBidModifier()
    {
        return isset($this->bid_modifier) ? $this->bid_modifier : 0.0;
    }

    public function hasBidModifier()
    {
        return isset($this->bid_modifier);
    }

    public function clearBidModifier()
    {
        unset($this->bid_modifier);
    }

    /**
     * The modifier for the bid when the criterion matches. The modifier must be
     * in the range: 0.1 - 10.0. Most targetable criteria types support modifiers.
     *
     * Generated from protobuf field <code>optional double bid_modifier = 61;</code>
     * @param float $var
     * @return $this
     */
    public function setBidModifier($var)
    {
        GPBUtil::checkDouble($var);
        $this->bid_modifier = $var;

        return $this;
    }

    /**
     * The CPC (cost-per-click) bid.
     *
     * Generated from protobuf field <code>optional int64 cpc_bid_micros = 62;</code>
     * @return int|string
     */
    public function getCpcBidMicros()
    {
        return isset($this->cpc_bid_micros) ? $this->cpc_bid_micros : 0;
    }

    public function hasCpcBidMicros()
    {
        return isset($this->cpc_bid_micros);
    }

    public function clearCpcBidMicros()
    {
        unset($this->cpc_bid_micros);
    }

    /**
     * The CPC (cost-per-click) bid.
     *
     * Generated from protobuf field <code>optional int64 cpc_bid_micros = 62;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCpcBidMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->cpc_bid_micros = $var;

        return $this;
    }

    /**
     * The CPM (cost-per-thousand viewable impressions) bid.
     *
     * Generated from protobuf field <code>optional int64 cpm_bid_micros = 63;</code>
     * @return int|string
     */
    public function getCpmBidMicros()
    {
        return isset($this->cpm_bid_micros) ? $this->cpm_bid_micros : 0;
    }

    public function hasCpmBidMicros()
    {
        return isset($this->cpm_bid_micros);
    }

    public function clearCpmBidMicros()
    {
        unset($this->cpm_bid_micros);
    }

    /**
     * The CPM (cost-per-thousand viewable impressions) bid.
     *
     * Generated from protobuf field <code>optional int64 cpm_bid_micros = 63;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCpmBidMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->cpm_bid_micros = $var;

        return $this;
    }

    /**
     * The CPV (cost-per-view) bid.
     *
     * Generated from protobuf field <code>optional int64 cpv_bid_micros = 64;</code>
     * @return int|string
     */
    public function getCpvBidMicros()
    {
        return isset($this->cpv_bid_micros) ? $this->cpv_bid_micros : 0;
    }

    public function hasCpvBidMicros()
    {
        return isset($this->cpv_bid_micros);
    }

    public function clearCpvBidMicros()
    {
        unset($this->cpv_bid_micros);
    }

    /**
     * The CPV (cost-per-view) bid.
     *
     * Generated from protobuf field <code>optional int64 cpv_bid_micros = 64;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCpvBidMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->cpv_bid_micros = $var;

        return $this;
    }

    /**
     * The CPC bid amount, expressed as a fraction of the advertised price
     * for some good or service. The valid range for the fraction is [0,1) and the
     * value stored here is 1,000,000 * [fraction].
     *
     * Generated from protobuf field <code>optional int64 percent_cpc_bid_micros = 65;</code>
     * @return int|string
     */
    public function getPercentCpcBidMicros()
    {
        return isset($this->percent_cpc_bid_micros) ? $this->percent_cpc_bid_micros : 0;
    }

    public function hasPercentCpcBidMicros()
    {
        return isset($this->percent_cpc_bid_micros);
    }

    public function clearPercentCpcBidMicros()
    {
        unset($this->percent_cpc_bid_micros);
    }

    /**
     * The CPC bid amount, expressed as a fraction of the advertised price
     * for some good or service. The valid range for the fraction is [0,1) and the
     * value stored here is 1,000,000 * [fraction].
     *
     * Generated from protobuf field <code>optional int64 percent_cpc_bid_micros = 65;</code>
     * @param int|string $var
     * @return $this
     */
    public function setPercentCpcBidMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->percent_cpc_bid_micros = $var;

        return $this;
    }

    /**
     * Output only. The effective CPC (cost-per-click) bid.
     *
     * Generated from protobuf field <code>optional int64 effective_cpc_bid_micros = 66 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int|string
     */
    public function getEffectiveCpcBidMicros()
    {
        return isset($this->effective_cpc_bid_micros) ? $this->effective_cpc_bid_micros : 0;
    }

    public function hasEffectiveCpcBidMicros()
    {
        return isset($this->effective_cpc_bid_micros);
    }

    public function clearEffectiveCpcBidMicros()
    {
        unset($this->effective_cpc_bid_micros);
    }

    /**
     * Output only. The effective CPC (cost-per-click) bid.
     *
     * Generated from protobuf field <code>optional int64 effective_cpc_bid_micros = 66 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int|string $var
     * @return $this
     */
    public function setEffectiveCpcBidMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->effective_cpc_bid_micros = $var;

        return $this;
    }

    /**
     * Output only. The effective CPM (cost-per-thousand viewable impressions)
     * bid.
     *
     * Generated from protobuf field <code>optional int64 effective_cpm_bid_micros = 67 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int|string
     */
    public function getEffectiveCpmBidMicros()
    {
        return isset($this->effective_cpm_bid_micros) ? $this->effective_cpm_bid_micros : 0;
    }

    public function hasEffectiveCpmBidMicros()
    {
        return isset($this->effective_cpm_bid_micros);
    }

    public function clearEffectiveCpmBidMicros()
    {
        unset($this->effective_cpm_bid_micros);
    }

    /**
     * Output only. The effective CPM (cost-per-thousand viewable impressions)
     * bid.
     *
     * Generated from protobuf field <code>optional int64 effective_cpm_bid_micros = 67 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int|string $var
     * @return $this
     */
    public function setEffectiveCpmBidMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->effective_cpm_bid_micros = $var;

        return $this;
    }

    /**
     * Output only. The effective CPV (cost-per-view) bid.
     *
     * Generated from protobuf field <code>optional int64 effective_cpv_bid_micros = 68 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int|string
     */
    public function getEffectiveCpvBidMicros()
    {
        return isset($this->effective_cpv_bid_micros) ? $this->effective_cpv_bid_micros : 0;
    }

    public function hasEffectiveCpvBidMicros()
    {
        return isset($this->effective_cpv_bid_micros);
    }

    public function clearEffectiveCpvBidMicros()
    {
        unset($this->effective_cpv_bid_micros);
    }

    /**
     * Output only. The effective CPV (cost-per-view) bid.
     *
     * Generated from protobuf field <code>optional int64 effective_cpv_bid_micros = 68 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int|string $var
     * @return $this
     */
    public function setEffectiveCpvBidMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->effective_cpv_bid_micros = $var;

        return $this;
    }

    /**
     * Output only. The effective Percent CPC bid amount.
     *
     * Generated from protobuf field <code>optional int64 effective_percent_cpc_bid_micros = 69 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int|string
     */
    public function getEffectivePercentCpcBidMicros()
    {
        return isset($this->effective_percent_cpc_bid_micros) ? $this->effective_percent_cpc_bid_micros : 0;
    }

    public function hasEffectivePercentCpcBidMicros()
    {
        return isset($this->effective_percent_cpc_bid_micros);
    }

    public function clearEffectivePercentCpcBidMicros()
    {
        unset($this->effective_percent_cpc_bid_micros);
    }

    /**
     * Output only. The effective Percent CPC bid amount.
     *
     * Generated from protobuf field <code>optional int64 effective_percent_cpc_bid_micros = 69 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int|string $var
     * @return $this
     */
    public function setEffectivePercentCpcBidMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->effective_percent_cpc_bid_micros = $var;

        return $this;
    }

    /**
     * Output only. Source of the effective CPC bid.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_cpc_bid_source = 21 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getEffectiveCpcBidSource()
    {
        return $this->effective_cpc_bid_source;
    }

    /**
     * Output only. Source of the effective CPC bid.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_cpc_bid_source = 21 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setEffectiveCpcBidSource($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\BiddingSourceEnum\BiddingSource::class);
        $this->effective_cpc_bid_source = $var;

        return $this;
    }

    /**
     * Output only. Source of the effective CPM bid.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_cpm_bid_source = 22 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getEffectiveCpmBidSource()
    {
        return $this->effective_cpm_bid_source;
    }

    /**
     * Output only. Source of the effective CPM bid.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_cpm_bid_source = 22 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setEffectiveCpmBidSource($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\BiddingSourceEnum\BiddingSource::class);
        $this->effective_cpm_bid_source = $var;

        return $this;
    }

    /**
     * Output only. Source of the effective CPV bid.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_cpv_bid_source = 23 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getEffectiveCpvBidSource()
    {
        return $this->effective_cpv_bid_source;
    }

    /**
     * Output only. Source of the effective CPV bid.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_cpv_bid_source = 23 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setEffectiveCpvBidSource($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\BiddingSourceEnum\BiddingSource::class);
        $this->effective_cpv_bid_source = $var;

        return $this;
    }

    /**
     * Output only. Source of the effective Percent CPC bid.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_percent_cpc_bid_source = 35 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getEffectivePercentCpcBidSource()
    {
        return $this->effective_percent_cpc_bid_source;
    }

    /**
     * Output only. Source of the effective Percent CPC bid.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.BiddingSourceEnum.BiddingSource effective_percent_cpc_bid_source = 35 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setEffectivePercentCpcBidSource($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\BiddingSourceEnum\BiddingSource::class);
        $this->effective_percent_cpc_bid_source = $var;

        return $this;
    }

    /**
     * Output only. Estimates for criterion bids at various positions.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.resources.AdGroupCriterion.PositionEstimates position_estimates = 10 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V18\Resources\AdGroupCriterion\PositionEstimates|null
     */
    public function getPositionEstimates()
    {
        return $this->position_estimates;
    }

    public function hasPositionEstimates()
    {
        return isset($this->position_estimates);
    }

    public function clearPositionEstimates()
    {
        unset($this->position_estimates);
    }

    /**
     * Output only. Estimates for criterion bids at various positions.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.resources.AdGroupCriterion.PositionEstimates position_estimates = 10 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V18\Resources\AdGroupCriterion\PositionEstimates $var
     * @return $this
     */
    public function setPositionEstimates($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Resources\AdGroupCriterion\PositionEstimates::class);
        $this->position_estimates = $var;

        return $this;
    }

    /**
     * The list of possible final URLs after all cross-domain redirects for the
     * ad.
     *
     * Generated from protobuf field <code>repeated string final_urls = 70;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getFinalUrls()
    {
        return $this->final_urls;
    }

    /**
     * The list of possible final URLs after all cross-domain redirects for the
     * ad.
     *
     * Generated from protobuf field <code>repeated string final_urls = 70;</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFinalUrls($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->final_urls = $arr;

        return $this;
    }

    /**
     * The list of possible final mobile URLs after all cross-domain redirects.
     *
     * Generated from protobuf field <code>repeated string final_mobile_urls = 71;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getFinalMobileUrls()
    {
        return $this->final_mobile_urls;
    }

    /**
     * The list of possible final mobile URLs after all cross-domain redirects.
     *
     * Generated from protobuf field <code>repeated string final_mobile_urls = 71;</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFinalMobileUrls($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->final_mobile_urls = $arr;

        return $this;
    }

    /**
     * URL template for appending params to final URL.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 72;</code>
     * @return string
     */
    public function getFinalUrlSuffix()
    {
        return isset($this->final_url_suffix) ? $this->final_url_suffix : '';
    }

    public function hasFinalUrlSuffix()
    {
        return isset($this->final_url_suffix);
    }

    public function clearFinalUrlSuffix()
    {
        unset($this->final_url_suffix);
    }

    /**
     * URL template for appending params to final URL.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 72;</code>
     * @param string $var
     * @return $this
     */
    public function setFinalUrlSuffix($var)
    {
        GPBUtil::checkString($var, True);
        $this->final_url_suffix = $var;

        return $this;
    }

    /**
     * The URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 73;</code>
     * @return string
     */
    public function getTrackingUrlTemplate()
    {
        return isset($this->tracking_url_template) ? $this->tracking_url_template : '';
    }

    public function hasTrackingUrlTemplate()
    {
        return isset($this->tracking_url_template);
    }

    public function clearTrackingUrlTemplate()
    {
        unset($this->tracking_url_template);
    }

    /**
     * The URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 73;</code>
     * @param string $var
     * @return $this
     */
    public function setTrackingUrlTemplate($var)
    {
        GPBUtil::checkString($var, True);
        $this->tracking_url_template = $var;

        return $this;
    }

    /**
     * The list of mappings used to substitute custom parameter tags in a
     * `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.CustomParameter url_custom_parameters = 14;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getUrlCustomParameters()
    {
        return $this->url_custom_parameters;
    }

    /**
     * The list of mappings used to substitute custom parameter tags in a
     * `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.CustomParameter url_custom_parameters = 14;</code>
     * @param array<\Google\Ads\GoogleAds\V18\Common\CustomParameter>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUrlCustomParameters($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Ads\GoogleAds\V18\Common\CustomParameter::class);
        $this->url_custom_parameters = $arr;

        return $this;
    }

    /**
     * Output only. The primary status for the ad group criterion.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AdGroupCriterionPrimaryStatusEnum.AdGroupCriterionPrimaryStatus primary_status = 85 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getPrimaryStatus()
    {
        return isset($this->primary_status) ? $this->primary_status : 0;
    }

    public function hasPrimaryStatus()
    {
        return isset($this->primary_status);
    }

    public function clearPrimaryStatus()
    {
        unset($this->primary_status);
    }

    /**
     * Output only. The primary status for the ad group criterion.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.AdGroupCriterionPrimaryStatusEnum.AdGroupCriterionPrimaryStatus primary_status = 85 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setPrimaryStatus($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\AdGroupCriterionPrimaryStatusEnum\AdGroupCriterionPrimaryStatus::class);
        $this->primary_status = $var;

        return $this;
    }

    /**
     * Output only. The primary status reasons for the ad group criterion.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.enums.AdGroupCriterionPrimaryStatusReasonEnum.AdGroupCriterionPrimaryStatusReason primary_status_reasons = 86 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getPrimaryStatusReasons()
    {
        return $this->primary_status_reasons;
    }

    /**
     * Output only. The primary status reasons for the ad group criterion.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.enums.AdGroupCriterionPrimaryStatusReasonEnum.AdGroupCriterionPrimaryStatusReason primary_status_reasons = 86 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setPrimaryStatusReasons($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::ENUM, \Google\Ads\GoogleAds\V18\Enums\AdGroupCriterionPrimaryStatusReasonEnum\AdGroupCriterionPrimaryStatusReason::class);
        $this->primary_status_reasons = $arr;

        return $this;
    }

    /**
     * Immutable. Keyword.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.KeywordInfo keyword = 27 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\KeywordInfo|null
     */
    public function getKeyword()
    {
        return $this->readOneof(27);
    }

    public function hasKeyword()
    {
        return $this->hasOneof(27);
    }

    /**
     * Immutable. Keyword.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.KeywordInfo keyword = 27 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\KeywordInfo $var
     * @return $this
     */
    public function setKeyword($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\KeywordInfo::class);
        $this->writeOneof(27, $var);

        return $this;
    }

    /**
     * Immutable. Placement.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.PlacementInfo placement = 28 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\PlacementInfo|null
     */
    public function getPlacement()
    {
        return $this->readOneof(28);
    }

    public function hasPlacement()
    {
        return $this->hasOneof(28);
    }

    /**
     * Immutable. Placement.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.PlacementInfo placement = 28 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\PlacementInfo $var
     * @return $this
     */
    public function setPlacement($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\PlacementInfo::class);
        $this->writeOneof(28, $var);

        return $this;
    }

    /**
     * Immutable. Mobile app category.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.MobileAppCategoryInfo mobile_app_category = 29 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\MobileAppCategoryInfo|null
     */
    public function getMobileAppCategory()
    {
        return $this->readOneof(29);
    }

    public function hasMobileAppCategory()
    {
        return $this->hasOneof(29);
    }

    /**
     * Immutable. Mobile app category.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.MobileAppCategoryInfo mobile_app_category = 29 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\MobileAppCategoryInfo $var
     * @return $this
     */
    public function setMobileAppCategory($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\MobileAppCategoryInfo::class);
        $this->writeOneof(29, $var);

        return $this;
    }

    /**
     * Immutable. Mobile application.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.MobileApplicationInfo mobile_application = 30 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\MobileApplicationInfo|null
     */
    public function getMobileApplication()
    {
        return $this->readOneof(30);
    }

    public function hasMobileApplication()
    {
        return $this->hasOneof(30);
    }

    /**
     * Immutable. Mobile application.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.MobileApplicationInfo mobile_application = 30 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\MobileApplicationInfo $var
     * @return $this
     */
    public function setMobileApplication($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\MobileApplicationInfo::class);
        $this->writeOneof(30, $var);

        return $this;
    }

    /**
     * Immutable. Listing group.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ListingGroupInfo listing_group = 32 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ListingGroupInfo|null
     */
    public function getListingGroup()
    {
        return $this->readOneof(32);
    }

    public function hasListingGroup()
    {
        return $this->hasOneof(32);
    }

    /**
     * Immutable. Listing group.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ListingGroupInfo listing_group = 32 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ListingGroupInfo $var
     * @return $this
     */
    public function setListingGroup($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ListingGroupInfo::class);
        $this->writeOneof(32, $var);

        return $this;
    }

    /**
     * Immutable. Age range.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AgeRangeInfo age_range = 36 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\AgeRangeInfo|null
     */
    public function getAgeRange()
    {
        return $this->readOneof(36);
    }

    public function hasAgeRange()
    {
        return $this->hasOneof(36);
    }

    /**
     * Immutable. Age range.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AgeRangeInfo age_range = 36 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\AgeRangeInfo $var
     * @return $this
     */
    public function setAgeRange($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\AgeRangeInfo::class);
        $this->writeOneof(36, $var);

        return $this;
    }

    /**
     * Immutable. Gender.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.GenderInfo gender = 37 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\GenderInfo|null
     */
    public function getGender()
    {
        return $this->readOneof(37);
    }

    public function hasGender()
    {
        return $this->hasOneof(37);
    }

    /**
     * Immutable. Gender.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.GenderInfo gender = 37 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\GenderInfo $var
     * @return $this
     */
    public function setGender($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\GenderInfo::class);
        $this->writeOneof(37, $var);

        return $this;
    }

    /**
     * Immutable. Income range.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.IncomeRangeInfo income_range = 38 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\IncomeRangeInfo|null
     */
    public function getIncomeRange()
    {
        return $this->readOneof(38);
    }

    public function hasIncomeRange()
    {
        return $this->hasOneof(38);
    }

    /**
     * Immutable. Income range.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.IncomeRangeInfo income_range = 38 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\IncomeRangeInfo $var
     * @return $this
     */
    public function setIncomeRange($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\IncomeRangeInfo::class);
        $this->writeOneof(38, $var);

        return $this;
    }

    /**
     * Immutable. Parental status.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ParentalStatusInfo parental_status = 39 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ParentalStatusInfo|null
     */
    public function getParentalStatus()
    {
        return $this->readOneof(39);
    }

    public function hasParentalStatus()
    {
        return $this->hasOneof(39);
    }

    /**
     * Immutable. Parental status.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ParentalStatusInfo parental_status = 39 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ParentalStatusInfo $var
     * @return $this
     */
    public function setParentalStatus($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ParentalStatusInfo::class);
        $this->writeOneof(39, $var);

        return $this;
    }

    /**
     * Immutable. User List.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.UserListInfo user_list = 42 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\UserListInfo|null
     */
    public function getUserList()
    {
        return $this->readOneof(42);
    }

    public function hasUserList()
    {
        return $this->hasOneof(42);
    }

    /**
     * Immutable. User List.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.UserListInfo user_list = 42 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\UserListInfo $var
     * @return $this
     */
    public function setUserList($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\UserListInfo::class);
        $this->writeOneof(42, $var);

        return $this;
    }

    /**
     * Immutable. YouTube Video.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.YouTubeVideoInfo youtube_video = 40 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\YouTubeVideoInfo|null
     */
    public function getYoutubeVideo()
    {
        return $this->readOneof(40);
    }

    public function hasYoutubeVideo()
    {
        return $this->hasOneof(40);
    }

    /**
     * Immutable. YouTube Video.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.YouTubeVideoInfo youtube_video = 40 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\YouTubeVideoInfo $var
     * @return $this
     */
    public function setYoutubeVideo($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\YouTubeVideoInfo::class);
        $this->writeOneof(40, $var);

        return $this;
    }

    /**
     * Immutable. YouTube Channel.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.YouTubeChannelInfo youtube_channel = 41 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\YouTubeChannelInfo|null
     */
    public function getYoutubeChannel()
    {
        return $this->readOneof(41);
    }

    public function hasYoutubeChannel()
    {
        return $this->hasOneof(41);
    }

    /**
     * Immutable. YouTube Channel.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.YouTubeChannelInfo youtube_channel = 41 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\YouTubeChannelInfo $var
     * @return $this
     */
    public function setYoutubeChannel($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\YouTubeChannelInfo::class);
        $this->writeOneof(41, $var);

        return $this;
    }

    /**
     * Immutable. Topic.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.TopicInfo topic = 43 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\TopicInfo|null
     */
    public function getTopic()
    {
        return $this->readOneof(43);
    }

    public function hasTopic()
    {
        return $this->hasOneof(43);
    }

    /**
     * Immutable. Topic.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.TopicInfo topic = 43 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\TopicInfo $var
     * @return $this
     */
    public function setTopic($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\TopicInfo::class);
        $this->writeOneof(43, $var);

        return $this;
    }

    /**
     * Immutable. User Interest.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.UserInterestInfo user_interest = 45 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\UserInterestInfo|null
     */
    public function getUserInterest()
    {
        return $this->readOneof(45);
    }

    public function hasUserInterest()
    {
        return $this->hasOneof(45);
    }

    /**
     * Immutable. User Interest.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.UserInterestInfo user_interest = 45 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\UserInterestInfo $var
     * @return $this
     */
    public function setUserInterest($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\UserInterestInfo::class);
        $this->writeOneof(45, $var);

        return $this;
    }

    /**
     * Immutable. Webpage
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.WebpageInfo webpage = 46 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\WebpageInfo|null
     */
    public function getWebpage()
    {
        return $this->readOneof(46);
    }

    public function hasWebpage()
    {
        return $this->hasOneof(46);
    }

    /**
     * Immutable. Webpage
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.WebpageInfo webpage = 46 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\WebpageInfo $var
     * @return $this
     */
    public function setWebpage($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\WebpageInfo::class);
        $this->writeOneof(46, $var);

        return $this;
    }

    /**
     * Immutable. App Payment Model.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AppPaymentModelInfo app_payment_model = 47 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\AppPaymentModelInfo|null
     */
    public function getAppPaymentModel()
    {
        return $this->readOneof(47);
    }

    public function hasAppPaymentModel()
    {
        return $this->hasOneof(47);
    }

    /**
     * Immutable. App Payment Model.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AppPaymentModelInfo app_payment_model = 47 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\AppPaymentModelInfo $var
     * @return $this
     */
    public function setAppPaymentModel($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\AppPaymentModelInfo::class);
        $this->writeOneof(47, $var);

        return $this;
    }

    /**
     * Immutable. Custom Affinity.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CustomAffinityInfo custom_affinity = 48 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\CustomAffinityInfo|null
     */
    public function getCustomAffinity()
    {
        return $this->readOneof(48);
    }

    public function hasCustomAffinity()
    {
        return $this->hasOneof(48);
    }

    /**
     * Immutable. Custom Affinity.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CustomAffinityInfo custom_affinity = 48 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\CustomAffinityInfo $var
     * @return $this
     */
    public function setCustomAffinity($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\CustomAffinityInfo::class);
        $this->writeOneof(48, $var);

        return $this;
    }

    /**
     * Immutable. Custom Intent.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CustomIntentInfo custom_intent = 49 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\CustomIntentInfo|null
     */
    public function getCustomIntent()
    {
        return $this->readOneof(49);
    }

    public function hasCustomIntent()
    {
        return $this->hasOneof(49);
    }

    /**
     * Immutable. Custom Intent.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CustomIntentInfo custom_intent = 49 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\CustomIntentInfo $var
     * @return $this
     */
    public function setCustomIntent($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\CustomIntentInfo::class);
        $this->writeOneof(49, $var);

        return $this;
    }

    /**
     * Immutable. Custom Audience.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CustomAudienceInfo custom_audience = 74 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\CustomAudienceInfo|null
     */
    public function getCustomAudience()
    {
        return $this->readOneof(74);
    }

    public function hasCustomAudience()
    {
        return $this->hasOneof(74);
    }

    /**
     * Immutable. Custom Audience.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CustomAudienceInfo custom_audience = 74 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\CustomAudienceInfo $var
     * @return $this
     */
    public function setCustomAudience($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\CustomAudienceInfo::class);
        $this->writeOneof(74, $var);

        return $this;
    }

    /**
     * Immutable. Combined Audience.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CombinedAudienceInfo combined_audience = 75 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\CombinedAudienceInfo|null
     */
    public function getCombinedAudience()
    {
        return $this->readOneof(75);
    }

    public function hasCombinedAudience()
    {
        return $this->hasOneof(75);
    }

    /**
     * Immutable. Combined Audience.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CombinedAudienceInfo combined_audience = 75 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\CombinedAudienceInfo $var
     * @return $this
     */
    public function setCombinedAudience($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\CombinedAudienceInfo::class);
        $this->writeOneof(75, $var);

        return $this;
    }

    /**
     * Immutable. Audience.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AudienceInfo audience = 79 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\AudienceInfo|null
     */
    public function getAudience()
    {
        return $this->readOneof(79);
    }

    public function hasAudience()
    {
        return $this->hasOneof(79);
    }

    /**
     * Immutable. Audience.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AudienceInfo audience = 79 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\AudienceInfo $var
     * @return $this
     */
    public function setAudience($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\AudienceInfo::class);
        $this->writeOneof(79, $var);

        return $this;
    }

    /**
     * Immutable. Location.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LocationInfo location = 82 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\LocationInfo|null
     */
    public function getLocation()
    {
        return $this->readOneof(82);
    }

    public function hasLocation()
    {
        return $this->hasOneof(82);
    }

    /**
     * Immutable. Location.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LocationInfo location = 82 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\LocationInfo $var
     * @return $this
     */
    public function setLocation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\LocationInfo::class);
        $this->writeOneof(82, $var);

        return $this;
    }

    /**
     * Immutable. Language.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LanguageInfo language = 83 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\LanguageInfo|null
     */
    public function getLanguage()
    {
        return $this->readOneof(83);
    }

    public function hasLanguage()
    {
        return $this->hasOneof(83);
    }

    /**
     * Immutable. Language.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LanguageInfo language = 83 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\LanguageInfo $var
     * @return $this
     */
    public function setLanguage($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\LanguageInfo::class);
        $this->writeOneof(83, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getCriterion()
    {
        return $this->whichOneof("criterion");
    }

}

