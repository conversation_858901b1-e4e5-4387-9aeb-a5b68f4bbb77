<?php

function contact($param){
    global $keya, $c_time, $app;
    $db = $app->db;
    $db2 = $app->db2;
    $site_id = $param["site_id"];
    $db->where("id",$site_id);
    $site = $db->getone("x_site");
    if($db->count==0){
        die();
    }

    $log["site_id"] = $site_id;
    $log["param"] = @json_encode($param);
    @$db->insert("x_log_contact2",$log);

    
    print_r($param);
    $param["pixel"] = $site["fb_pixel"];
    $param["apikey"] = $site["fb_token"];
    $param["adw_global_tag_id"] = $site["adw_tag"];
    $param["adw_conv_id"] = $site["adw_conv_id"];
    $param["gtm"] = $site["gtm"];
    $param["tiktok"] = $site["tiktok_pixel"];
    $param["vid"] =  preg_replace('/[^0-9]/', '', $param["vid"]);
    if(isset($param["phone"])){
        $custom["phone"] = $param["phone"];
    }
    if(isset($param["email"])){
        $custom["email"] = $param["email"];
    }
    if(isset($param["debug"])){
        $custom["debug"] = 1;
    }
    $et = new eztrack($site_id);
    if(isset($custom)){
        $res = $et->contact($site,$param,$custom);
    }else{
        $res = $et->contact($site,$param);
    }
    echo 1;
    die();
}

function checkout($param){
    global $keya, $c_time, $app;
    $db = $app->db;
    $db2 = $app->db2;
    $site_id = $param["site_id"];
    $db->where("id",$site_id);
    $site = $db->getone("x_site");
    if($db->count==0){
        die();
    }
    print_r($param);
    if($param["vid"] == "" || $param["vid"] == "undefined"){
        $table_visitor = $site_id . '_visitor';
        $db2->where("phone",$param["phone"]);
        $tmp = $db2->getone($table_visitor);
        $param["vid"] = $tmp["vid"];
    }
    $param["pixel"] = $site["fb_pixel"];
    $param["apikey"] = $site["fb_token"];
    $param["adw_global_tag_id"] = $site["adw_tag"];
    $param["adw_conv_id"] = $site["adw_conv_id"];
    $param["gtm"] = $site["gtm"];
    $param["tiktok"] = $site["tiktok_pixel"];
    $param["vid"] =  preg_replace('/[^0-9]/', '', $param["vid"]);
    if(isset($param["phone"])){
        $custom["phone"] = $param["phone"];
    }
    if(isset($param["email"])){
        $custom["email"] = $param["email"];
    }
    if(isset($param["debug"])){
        $custom["debug"] = 1;
    }
    if(isset($param["pesan"])){
        $custom["pesan"] = $param["pesan"];
    }
    $et = new eztrack($site_id);
    if(isset($custom)){
        $res = $et->checkout($site,$param,$custom);
    }else{
        $res = $et->checkout($site,$param);
    }
    echo 1;
    die();
}

function purchase($param){
    global $keya, $c_time, $app;
    $db = $app->db;
    $db2 = $app->db2;
    $site_id = $param["site_id"];
    $db->where("id",$site_id);
    $site  = $db->getone("x_site");
    if($db->count==0){
        die();
    }
    
    $log["site_id"] = $site_id;
    $log["param"] = @json_encode($param);
    @$db->insert("x_log_purchase",$log);

    if($param["vid"] == "" || $param["vid"] == "undefined"){
        $table_visitor = $site_id . '_visitor';
        $db2->where("phone",$param["phone"]);
        $tmp = $db2->getone($table_visitor);
        $param["vid"] = $tmp["vid"];
        //echo $db2->getLastQuery();
    }
    $param["pixel"] = $site["fb_pixel"];
    $param["apikey"] = $site["fb_token"];
    $param["adw_global_tag_id"] = $site["adw_tag"];
    $param["adw_conv_id"] = $site["adw_conv_id"];
    $param["gtm"] = $site["gtm"];
    $param["tiktok"] = $site["tiktok_pixel"];
    $param["vid"] =  preg_replace('/[^0-9]/', '', $param["vid"]);
    if(isset($param["phone"])){
        $custom["phone"] = $param["phone"];
    }
    if(isset($param["email"])){
        $custom["email"] = $param["email"];
    }
    if(isset($param["currency"])){
        $custom["currency"] = $param["currency"];
    }
    if(isset($param["value"])){
        $custom["value"] = $param["value"];
    }
    if(isset($param["pesan"])){
        $custom["pesan"] = $param["pesan"];
    }
    $et = new eztrack($site_id);
    if(isset($custom)){
        $res = $et->purchase($site, $param, $custom);
    }else{
        $res = $et->purchase($site,$param);
    }
    echo $res;
    die();
}