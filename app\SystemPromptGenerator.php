<?php
class SystemPromptGenerator {
    private $aiRouter;
    private $metaPrompt;
    
    /**
     * Constructor to initialize the SystemPromptGenerator
     * 
     * @param AIRouter $aiRouter AIRouter instance
     * @param string $metaPromptPath Path to meta prompt file
     */
    public function __construct(AIRouter $aiRouter, $metaPromptPath = 'prompt/meta_prompt.md') {
        $this->aiRouter = $aiRouter;
        $this->metaPrompt = file_get_contents($metaPromptPath);
        
        if ($this->metaPrompt === false) {
            throw new Exception('Failed to read meta prompt file: ' . $metaPromptPath);
        }
    }
    
    /**
     * Generate system prompt using AI Router
     * 
     * @param string $businessDescription Business description
     * @param string|null $additionalRules Optional additional rules
     * @param array $options Additional options for the AI request
     * @return array Generated system prompt as JSON
     */
    public function generateSystemPrompt($businessDescription, $additionalRules = null, $options = []) {
        // Build the prompt for AI
        $prompt = $this->buildPrompt($businessDescription, $additionalRules);
        
        // Create messages for AI Router
        $messages = [
            ['role' => 'system', 'content' => $this->metaPrompt],
            ['role' => 'user', 'content' => $prompt]
        ];
        
        // Send request to AI Router
        $response = $this->aiRouter->chatCompletion($messages, $options);
        
        // Extract and parse the response
        if (isset($response['choices'][0]['message']['content'])) {
            $content = $response['choices'][0]['message']['content'];
            
            // Try to extract JSON from the response
            $jsonContent = $this->extractJsonFromResponse($content);
            
            if ($jsonContent) {
                return json_decode($jsonContent, true);
            }
            
            // If no JSON found, return the raw response
            return ['response' => $content];
        }
        
        throw new Exception('Failed to generate system prompt: No response content');
    }
    
    /**
     * Build the prompt for AI generation
     * 
     * @param string $businessDescription Business description
     * @param string|null $additionalRules Optional additional rules
     * @return string Formatted prompt
     */
    private function buildPrompt($businessDescription, $additionalRules = null) {
        $prompt = "Create a System Prompt in JSON format based on the provided template.\n\n";
        $prompt .= "Business Description: " . $businessDescription . "\n";
        
        if ($additionalRules) {
            $prompt .= "Additional Rules: " . $additionalRules . "\n";
        }
        
        $prompt .= "\nPlease generate a System Prompt customized with the business description above in valid JSON format.";
        
        return $prompt;
    }
    
    /**
     * Extract JSON content from AI response
     * 
     * @param string $content Response content
     * @return string|null Extracted JSON string
     */
    private function extractJsonFromResponse($content) {
        // Look for JSON content between ```json and ``` markers
        if (preg_match('/```json\s*(.*?)\s*```/s', $content, $matches)) {
            return $matches[1];
        }
        
        // Look for JSON content between ``` and ``` markers
        if (preg_match('/```\s*(\{.*?\})\s*```/s', $content, $matches)) {
            return $matches[1];
        }
        
        // Look for JSON object directly
        if (preg_match('/(\{.*\})/s', $content, $matches)) {
            return $matches[1];
        }
        
        return null;
    }
}
