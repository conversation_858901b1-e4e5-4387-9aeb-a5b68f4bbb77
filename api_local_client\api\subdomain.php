<?php

function add_migrasi($param)
{
    $rules = [
        "project_id" => "required",
        "subdomain" => "required"
    ];
    validate_param($rules, $param);
    set_config_to_file($param);
    extract($param);
    assign_child_db($project_id);

    $subdomain = strtolower($subdomain);
    $subdomain = str_replace("https://", "", $subdomain);
    $subdomain = str_replace("http://", "", $subdomain);
    $tmp = explode("/", $subdomain);
    if(count($tmp) > 1){
        $subdomain = $tmp[0];
    }
    
    //$d = new subdomain();
    //$msg = $d->add_subdomain($subdomain);
    //if($msg['code']==1){
        //$subdomain_id = $msg["result"]["data"];
        //$co=file_get_contents('config/c.gass.co.id.php');
        //file_put_contents('config/'.$subdomain.'.php', $co);
        //add_config('config/'.$subdomain.'.php', $co);
        $webhook = 'http://'. $server.'/api.html?act=subdomain_cron&project_id='.$project_id.'&subdomain_id='.$subdomain_id;
		$bt      = new bt_api();
        $rex = $bt->add_site($subdomain); 
        $bt->update_site_rewrite($subdomain);
		$msg = $bt->add_cron('subdomain_'.$project_id.'_'.$subdomain_id, 5, $webhook);
    //}
    return $msg;
}

function add_config($file, $content){
    $handle = fopen($file, 'w');
    fwrite($handle, $content);
    fclose($handle);
}

function set_config_to_file($param){
    global $app;
    $rules = [
        "project_id" => "required",
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $db2 = $app->db2;
    //$db2->where("status = ?", array(1));
    $res = $db2->get("subdomain");
    if(is_file('config/subdomain.json')) {  
        $subdomain = json_decode(file_get_contents('config/subdomain.json'), true);
    }else{
        $subdomain = array();
    }
    foreach($res as $k => $v){
        $ketemu = false;
        foreach($subdomain as $j =>$d){
            if($d ==$v['name']){
                $ketemu = true;
            }
        }
        if ($ketemu == false) {
            array_push($subdomain, $v['name']);
        }  
        if(is_file('config/'.$v['name'].'.php')) {
            unlink('config/'.$v['name'].'.php');
        }        
    }
    file_put_contents('config/subdomain.json', json_encode($subdomain));
    print_r(json_encode($subdomain));
}

function add($param)
{
    $rules = [
        "project_id" => "required",
        "subdomain" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $subdomain = strtolower($subdomain);
    $subdomain = str_replace("https://", "", $subdomain);
    $subdomain = str_replace("http://", "", $subdomain);
    $tmp = explode("/", $subdomain);
    if(count($tmp) > 1){
        $subdomain = $tmp[0];
    }
    
    $d = new subdomain();
    $msg = $d->add_subdomain($subdomain);
   
    if($msg['code']==1){
        $subdomain_id = $msg["result"]["data"];
        //$co=file_get_contents('config/c.gass.co.id.php');
        //file_put_contents('config/'.$subdomain.'.php', $co);
        //add_config('config/'.$subdomain.'.php', $co);
        $webhook = 'http://'. $server.'/api.html?act=subdomain_cron&project_id='.$project_id.'&subdomain_id='.$subdomain_id;
		$bt      = new bt_api();
        // $rex = $bt->add_site($subdomain); 
        // $bt->update_site_rewrite($subdomain);
		$bt->add_cron('subdomain_'.$project_id.'_'.$subdomain_id, 5, $webhook);
    }
    return $msg;
}

function edit($param)
{
    $rules = [
        "project_id" => "required",
        "subdomain" => "required",
        "status" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $subdomain = strtolower($subdomain);
    $subdomain = str_replace("https://", "", $subdomain);
    $subdomain = str_replace("http://", "", $subdomain);
    $tmp = explode("/", $subdomain);
    if(count($tmp) > 1){
        $subdomain = $tmp[0];
    }
    
    $d = new subdomain();
    $msg = $d->edit_subdomain($subdomain, array('status'=>$param['status'], 'expired_ssl' => $param['expired_ssl']));
    return $msg;
}
function edit_data($param)
{
    $rules = [
        "project_id" => "required",
        "subdomain" => "required",
        "data" => "required"
    ];
    validate_param($rules, $param);
    //extract($param);
    assign_child_db($param['project_id']);

    $subdomain = strtolower($param['subdomain']);
    $subdomain = str_replace("https://", "", $subdomain);
    $subdomain = str_replace("http://", "", $subdomain);
    $tmp = explode("/", $subdomain);
    if(count($tmp) > 1){
        $subdomain = $tmp[0];
    }
    $dd = json_decode($param['data'], true);
    $d = new subdomain();
    $msg = $d->edit_subdomain($subdomain, $dd);
    return $msg;
}

function delete($param)
{
    $rules = [
        "project_id" => "required",
        "subdomain_key" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $d = new subdomain();
    $msg = $d->delete_subdomain($subdomain_key);

    return $msg;
}

function get($param)
{
	$rules = [
        "project_id" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $d = new subdomain();
    $msg = $d->get_subdomain($subdomain);

    return $msg;
}

function renew_ssl($param)
{
    $rules = [
        "subdomain" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $subdomain = strtolower($subdomain);
    $subdomain = str_replace("https://", "", $subdomain);
    $subdomain = str_replace("http://", "", $subdomain);
    $tmp = explode("/", $subdomain);
    if(count($tmp) > 1){
        $subdomain = $tmp[0];
    }
    
    $d = new subdomain();
    $msg = $d->renew_ssl($subdomain);
    return $msg;
}

function add_cron_site($param){
    global $app;
	$rules = [
        "project_id" => "required",
        "subdomain_id" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $db2 = $app->db2;
    $db2->where("subdomain_id = ?", [$subdomain_id]);
    $res = $db2->getone("subdomain","subdomain_id,HEX(subdomain_key) as subdomain_key,name,status");
    if($db2->count >0){
        $bt = new bt_api();
        $rex = $bt->add_site($res['name']); 
        $bt->update_site_rewrite($res['name']);
        $db2->where("subdomain_id = ?", [$res['subdomain_id']]);
        $db2->update("subdomain", array('status'=>1));
        echo "sukses add ".$res['name'] . "\n";
    }
}
function cron($param){
    global $app;
	$rules = [
        "project_id" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);
    $d = new subdomain();
    $db2 = $app->db2;
    $dt = new DateTime("now", new DateTimeZone('Asia/Jakarta'));
    $timestamp = $dt->format('Y-m-d H:i:s');
    if(isset($subdomain_id)){
        $db2->where("status = ? and subdomain_id = ?", [0, $subdomain_id]);
        $res = $db2->getone("subdomain","subdomain_id,HEX(subdomain_key) as subdomain_key,name,status, expired_ssl, siteId, DATEDIFF(NOW(), expired_ssl) AS endtime, DATEDIFF(expired_ssl, NOW()) AS days_left, last_cron, error_cron");
        if($db2->count >0){    
            if($res['last_cron'] != '' and $res['error_cron'] > 2016){
                $db2->where("subdomain_id = ?", [$res['subdomain_id']]);
                $db2->update("subdomain", array('status'=> -1));
                // delete cron
                echo 'delete cron';
                $bt      = new bt_api();
                echo $bt->delete_cron_name('subdomain_'.$project_id.'_'.$subdomain_id);
            }
            $add_sub = cek_site($res['name']);
            if($add_sub==true){
                $bt = new bt_api();
                if($res['siteId'] ==''){
                    $rex = $bt->add_site($res['name']); 
                    $bt->update_site_rewrite($res['name']);
                    
                    $dd['expired_ssl'] = date('Y-m-d', strtotime($timestamp.' +90 days'));
                    $dd['status'] = 1;
                    $dd['siteId'] = $rex['siteId'];
                    $db2->where("subdomain_id = ?", [$res['subdomain_id']]);
                    $db2->update("subdomain", $dd);
                    $pos["act"] = 'site_add_data';
                    $pos["siteId"] = $rex['siteId'];
                    $pos["subdomain_key"] = $res['subdomain_key'];
                    $pos["project_id"] = $project_id;
                    $pos["subdomain"] = $res['name'];
                    $pos["subdomain_id"] = $res['subdomain_id'];
                    post_x_contents($pos, 'http://***********/api.html');
                }else{
                    if($res['endtime'] > -10){
                        $msg = $d->renew_ssl($res['name']);
                        if(isset($msg['status']) && $msg['status']){
                            $timestamp = $dt->format('Y-m-d H:i:s');
                            $new_date = date('Y-m-d', strtotime($timestamp. ' +90 days'));
                            $db2->where("subdomain_id  = ?", array($res['subdomain_id']));
                            $db2->update("subdomain", array('expired_ssl'=> $new_date, 'status'=> 1));
                        }
                    }else{  
                        if($res['days_left'] > 20){
                            $db2->where("subdomain_id  = ?", array($res['subdomain_id']));
                            $db2->update("subdomain", array('status'=> 1));
                        }
                    }
                }                
                echo "sukses add ".$res['name'] . "\n";
            }else{
                if($res['endtime'] > 10){
                    $db2->where("subdomain_id  = ?", array($res['subdomain_id']));
                    $db2->update("subdomain", array('status'=> -1));
                }else{
                    $db2->where("subdomain_id  = ?", array($res['subdomain_id']));
                    $db2->update("subdomain", array('error_cron'=> $res['error_cron']+1));
                }
                echo "gagal add ".$res['name'] . "\n";
            }   
            if($res['last_cron'] == '' || $res['last_cron'] < $timestamp){
                $db2->where("subdomain_id = ?", [$res['subdomain_id']]);
                $db2->update("subdomain", array('last_cron'=> $timestamp));
            }
        }else{
            // delete cron
            echo 'delete cron';
            $bt      = new bt_api();
		    echo $bt->delete_cron_name('subdomain_'.$project_id.'_'.$subdomain_id);
        }
    }else{
        $db2->where("status = ?", [0]);
        $res = $db2->get("subdomain",NULL,"subdomain_id,HEX(subdomain_key) as subdomain_key,name,status");
        if($db2->count >0){
            foreach($res as $r){
                $add_sub = cek_site($r['name']);
                if($add_sub==true){
                    echo 1;
                    $bt = new bt_api();
                    $rex = $bt->add_site($r['name']); 
                    $bt->update_site_rewrite($r['name']);
                    $db2->where("subdomain_id = ?", [$r['subdomain_id']]);
                    $db2->update("subdomain", array('status'=>1, 'siteId'=>$rex['siteId']));
                    $pos["act"] = 'site_add_data';
                    $pos["siteId"] = $rex['siteId'];
                    $pos["subdomain_key"] = $r['subdomain_key'];
                    $pos["project_id"] = $project_id;
                    $pos["subdomain"] = $r['name'];
                    $pos["subdomain_id"] = $r['subdomain_id'];
                    post_x_contents($pos, 'http://***********/api.html');
                    echo "sukses add ".$r['name'] . "\n";
                }else{
                    echo "gagal add ".$r['name'] . "\n";
                }   
            }
        }else{
            /// delete cron
            echo 'delete cron';
            $bt      = new bt_api();
		    $bt->delete_cron_name('subdomain_'.$project_id);
        }

    }
}

function cek_site($name){
    $add_sub = false;
    // Check via local API first
    $url_checkin = 'http://localhost:3434/check-domain?domain=' . urlencode($name);
    $context = stream_context_create([
        'http' => [
            'timeout' => 15
        ]
    ]);
    $response = @file_get_contents($url_checkin, false, $context);
    if ($response !== false) {
        $data = json_decode($response, true);
        if (isset($data['code']) && $data['code'] == 1) {
            return true;
        }
    }
    // Fallback to old methods
    //return false;
    $url = 'http://' . $name;
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    $response = curl_exec($ch);
    curl_close($ch);
    if (strpos($response, 'meta') !== false) {
        $doc = new DOMDocument();
        libxml_use_internal_errors(true);
        $doc->loadHTML($response);
        libxml_clear_errors();
        $xpath = new DOMXPath($doc);
        $nodes = $xpath->query('//head/meta[@name]');
        $tags = [];
        foreach($nodes as $node) {
            $tags[$node->getAttribute('name')] = $node->getAttribute('content');
        }
        if(count($tags) > 0){
            $timeSlot = floor(time() / 5);
            $gass_verification = sha1(crc32($name . $timeSlot));
            if($gass_verification == $tags["gass_verification"]){
                $add_sub = true;
            }else{
                echo "gagal add ".$name. "\n";
            }
        }else{
            echo "gagal add ".$name. "\n";
        }
    }else{
        $response = cloudflare_render_url($url);
        //$response = @file_get_contents($url);
        if (strpos($response, 'meta') !== false) {
            $doc = new DOMDocument();
            libxml_use_internal_errors(true);
            $doc->loadHTML($response);
            libxml_clear_errors();
            $xpath = new DOMXPath($doc);
            $nodes = $xpath->query('//head/meta[@name]');
            $tags = [];
            foreach($nodes as $node) {
                $tags[$node->getAttribute('name')] = $node->getAttribute('content');
            }
            if(count($tags) > 0){
                $timeSlot = floor(time() / 5);
                $gass_verification = sha1(crc32($name . $timeSlot));
                if($gass_verification == $tags["gass_verification"]){
                    $add_sub = true;
                }else{
                    echo "gagal add ".$name. "\n";
                }
            }else{
                echo "gagal add ".$name. "\n";
            }
        }else{
            $context = stream_context_create([
                'http' => [
                    'timeout' => 15
                ],
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false
                ]
            ]);
            $response = @file_get_contents('https://grab.gass.co.id/?url='.$url, false, $context);
            if (strpos($response, 'meta') !== false) {
                $doc = new DOMDocument();
                libxml_use_internal_errors(true);
                $doc->loadHTML($response);
                libxml_clear_errors();
                $xpath = new DOMXPath($doc);
                $nodes = $xpath->query('//head/meta[@name]');
                $tags = [];
                foreach($nodes as $node) {
                    $tags[$node->getAttribute('name')] = $node->getAttribute('content');
                }
                if(count($tags) > 0){
                    $timeSlot = floor(time() / 5);
                    $gass_verification = sha1(crc32($name . $timeSlot));
                    if($gass_verification == $tags["gass_verification"]){
                        $add_sub = true;
                    }else{
                        echo "gagal add ".$name. "\n";
                    }
                }else{
                    echo "gagal add ".$name. "\n";
                }
            }
        }
    }
    return $add_sub;
}

function cloudflare_render_url($url) {
    $api_url = 'https://api.cloudflare.com/client/v4/accounts/a664197d92717007b1b273365770c296/browser-rendering/content';
    $token = 'pyRHzO7pe33_A9SqXjBkln9rYmO7PXrRm1Z1y-w1';

    $data = json_encode(['url' => $url]);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json',
        'Content-Length: ' . strlen($data)
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code == 200) {
        $result = json_decode($response, true);
        if (isset($result['result'])) {
            return $result['result'];
        }
    }

    return false;
}

function test($param)
{
    $rules = [
        "project_id" => "required",
        "subdomain" => "required"
    ];
    validate_param($rules, $param);
    extract($param);
    assign_child_db($project_id);

    $subdomain = strtolower($subdomain);
    $subdomain = str_replace("https://", "", $subdomain);
    $subdomain = str_replace("http://", "", $subdomain);
    $tmp = explode("/", $subdomain);
    if(count($tmp) > 1){
        $subdomain = $tmp[0];
    }
    
    $co=file_get_contents('config/c.gass.co.id.php');
    file_put_contents('config/'.$subdomain.'.php', $co);
    print_r($co);
    return $msg;
}

function check_ssl($param)
{
    $rules = [
        "subdomain" => "required",
    ];
    validate_param($rules, $param);
    extract($param);
    $d = new subdomain();
    $msg = $d->checkSSLCertificate($subdomain);

    return $msg;
}