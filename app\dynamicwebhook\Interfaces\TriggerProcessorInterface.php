<?php

namespace DynamicWebhook\Interfaces;

/**
 * Interface for trigger processing
 */
interface TriggerProcessorInterface
{
    /**
     * Process trigger based on extracted data
     *
     * @param array $data Extracted webhook data
     * @param array $context Processing context
     * @return bool Whether trigger was activated
     */
    public function process(array $data, array $context): bool;

    /**
     * Get trigger type name
     *
     * @return string
     */
    public function getTriggerType(): string;

    /**
     * Check if trigger should be processed for given message type
     *
     * @param string $messageType
     * @return bool
     */
    public function shouldProcess(string $messageType): bool;
}
