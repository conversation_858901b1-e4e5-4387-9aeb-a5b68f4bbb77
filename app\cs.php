<?php 

class cs{


	function add_cs($project_id,$name,$phone,$autodc,$without_scan,$divisi)
	{

		global $app,$keya;
		$db2 = $app->db2;
		$db = $app->db;
		$phone = preg_replace('/[^0-9]/', '', $phone);
		$cs_key = md5($phone);
		/*
		$db2->where("cs_key = UNHEX(?)",[$cs_key]);
		$tmp = $db2->getone("cs");
		if($tmp != NULL){
			$return["code"] = 0;
			$return["result"]["msg"] = "cs already exist";
			return $return;
		}
		*/
		$data2 = array();
		$data2["cs_key"] = $db2->func("UNHEX(?)",[$cs_key]);
		$data2["phone"] = $phone;
		$data2["name"] = $name;
		$data2["autodc"] = $autodc;
		$data2["without_scan"] = $without_scan;
		$data2["status"] = 1;

		$db->where("cs_key = UNHEX(?)",[$cs_key]);
		$db->delete("cs");

		$data_mastercs["cs_key"] = $db2->func("UNHEX(?)",[$cs_key]);
		$data_mastercs["name"] = $name;
		$data_mastercs["phone"] = $phone;
		$data_mastercs["project_id"] = $project_id;
		$db->insert("cs",$data_mastercs);

		try {
			if(isset($divisi))
				{
					$d = new divisi();
					if(is_array($divisi))
					{
						foreach ($divisi as $key => $divisi_key) {
							if($divisi_key != NULL){
								
							$d->add_cs($phone,$divisi_key);
							}
						}
					}
				}
			$db2->where("cs_key = UNHEX(?)",[$cs_key]);
			$db2->delete("cs");
			if($cs_id = $db2->insert("cs",$data2)){
				$return["code"] = 1;
				/*
				$return["result"]["data"]["name"] = $name;
				$return["result"]["data"]["cs_key"] = $cs_key;
				$return["result"]["data"]["phone"] = $phone;
				*/
				$return["result"]["data"] = $this->get_cs($cs_key)["result"]["data"];

				$return["result"]["msg"] = "sukses add cs";
			}else{

				$return["code"] = 0;
				$return["result"]["msg"] = "error, failed to insert";
			}
		}catch(Exception $e) {			
  			$return["code"] = 0;
				$return["result"]["msg"] = "error, failed to insert ";
		}
		return $return;	
	}

    function status($phone){
		global $app,$keya;
		$db2 = $app->db2;
		$phone = preg_replace('/[^0-9]/', '', $phone);
		$cs_key = md5($phone);
		$db2->where("cs_key = UNHEX(?)",[$cs_key]);
		$tmp = $db2->getone("cs");
		if($db2->count > 0){
			$return["code"] = 1;
            $return["result"]["data"]["cs_key"] = $cs_key;
            $return["result"]["data"]["phone"] = $phone;
			$return["result"]["msg"] = "cs found";
		}else{
            $return["code"] = 0;
			$return["result"]["msg"] = "cs not found";
        }		
		return $return;	
	}
	public static function edit_cs_status($phone,$status)
	{
		global $app,$keya;
		$db2 = $app->db2;

		$phone = preg_replace('/[^0-9]/', '', $phone);
		$cs_key = md5($phone);

		$update["status"] = $status;

		$db2->where("cs_key = UNHEX(?)",[$cs_key]);
		try {
			if($cs_id = $db2->update("cs",$update))
			{
				$return["code"] = 1;
				$return["result"]["msg"] = "sukses update";
			}
			else
			{
				$return["code"] = 0;
				$return["result"]["msg"] = "error, failed to insert";
			}
		}
		catch(Exception $e) {
  			$return["code"] = 0;
				$return["result"]["msg"] = "error, failed to insert";
		}
		return $return;	
		
	}
    
	function edit_cs($phone,$update,$divisi)
	{
		global $app,$keya;
		$db2 = $app->db2;

		$phone = preg_replace('/[^0-9]/', '', $phone);
		$cs_key = md5($phone);

		

		
		$d = new divisi();
		$d->remove_cs($cs_key);

		if($divisi != NULL)
		{
			if(is_array($divisi))
			{
				foreach ($divisi as $key => $divisi_key) {
					$d->add_cs($phone,$divisi_key);
				}
			}
		}


		$db2->where("cs_key = UNHEX(?)",[$cs_key]);
		try {
			if($cs_id = $db2->update("cs",$update))
			{
				$return["code"] = 1;
				$return["result"]["msg"] = "sukses update";
			}
			else
			{
				$return["code"] = 0;
				$return["result"]["msg"] = "error, failed to insert";
			}
		}
		catch(Exception $e) {
  			$return["code"] = 0;
				$return["result"]["msg"] = "error, failed to insert";
		}
		return $return;	
		
	}

	function get_cs($cs_key = NULL, $divisi_key = NULL)
	{
		global $app,$keya;
		$db2 = $app->db2;
		$db2->join("cs_divisi","cs_divisi.cs_key = cs.cs_key","LEFT");
		$db2->join("divisi","divisi.divisi_id = cs_divisi.divisi_id","LEFT");
		$db2->join("(SELECT cs_key, MAX(tanggal) as last_message FROM cs_log GROUP BY cs_key) as cs_log_latest","cs_log_latest.cs_key = cs.cs_key","LEFT");
		if($cs_key != NULL){
			$db2->where("cs.cs_key = UNHEX(?)",[$cs_key]);
		}
		if($divisi_key != NULL){
			$db2->where("divisi.divisi_key = UNHEX(?)",[$divisi_key]);
		}
		$db2->where("status",0,">=");
		$data = $db2->get("cs",NULL,"HEX(cs.cs_key) as cs_key,cs.name,cs.autodc,cs.created,cs.phone,cs.status,HEX(divisi.divisi_key) as divisi_key,divisi.name as divisi_name,cs_log_latest.last_message");
		if($data != NULL){
			$cs = array();
			foreach ($data as $key => $value) {
				$phone = $value["phone"];
				$cs[$phone]["cs_key"] = $value["cs_key"];
				$cs[$phone]["name"] = $value["name"];
				$cs[$phone]["autodc"] = $value["autodc"];
				$cs[$phone]["created"] = $value["created"];
				$cs[$phone]["phone"] = $value["phone"];
				$cs[$phone]["status"] = $value["status"];
				$cs[$phone]["last_message"] = $value["last_message"];

				if(!isset($cs[$phone]["divisi"] )){
					$cs[$phone]["divisi"] = array();	
				}

				if($value["divisi_key"] != NULL or $value["divisi_name"] != NULL){
					$divisi["divisi_key"] = $value["divisi_key"];
					$divisi["divisi_name"] = $value["divisi_name"];
					array_push($cs[$phone]["divisi"], $divisi);
				}
			}	
			$tmp = array();
			foreach ($cs as $key => $value) {
				array_push($tmp, $value);
			}
			$return["code"] = 1;
			$return["result"]["data"] = $tmp;
			$return["result"]["msg"] = "sukses get cs";
		}else{
			$return["code"] = 0;
			$return["result"]["msg"] = "no data cs";
		}
		return $return;
	}
	
	function delete_cs($phone){
		global $app;
		$db = $app->db;
		$db2 = $app->db2;

		$phone = preg_replace('/[^0-9]/', '', $phone);
		$phone_key = md5($phone);

		$db2->where("cs_key = UNHEX(?)",array($phone_key));
		$db2->update("cs",["status"=>-1]);

		$db2->where("cs_key = UNHEX(?)",array($phone_key));
		$db2->delete("cs_divisi");

//		$db2->where("cs_key = UNHEX(?)",array($phone_key));
//		$db2->delete("cs_log");

		$db->where("cs_key = UNHEX(?)",array($phone_key));
		$db->delete("cs_jadwal");

		$db2->where("cs_key = UNHEX(?)",array($phone_key));
		$db2->delete("cs_jadwal");

		$return["code"] = 1;
		$return["result"]["msg"] = "sukses";

		return $return;
	}

}
