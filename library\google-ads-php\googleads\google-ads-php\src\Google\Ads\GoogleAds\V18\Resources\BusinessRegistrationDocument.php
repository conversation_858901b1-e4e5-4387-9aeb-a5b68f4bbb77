<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/resources/local_services_verification_artifact.proto

namespace Google\Ads\GoogleAds\V18\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A proto holding information specific to a local services business
 * registration document.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.resources.BusinessRegistrationDocument</code>
 */
class BusinessRegistrationDocument extends \Google\Protobuf\Internal\Message
{
    /**
     * Output only. The readonly field containing the information for an uploaded
     * business registration document.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.common.LocalServicesDocumentReadOnly document_readonly = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $document_readonly = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Ads\GoogleAds\V18\Common\LocalServicesDocumentReadOnly $document_readonly
     *           Output only. The readonly field containing the information for an uploaded
     *           business registration document.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Resources\LocalServicesVerificationArtifact::initOnce();
        parent::__construct($data);
    }

    /**
     * Output only. The readonly field containing the information for an uploaded
     * business registration document.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.common.LocalServicesDocumentReadOnly document_readonly = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\LocalServicesDocumentReadOnly|null
     */
    public function getDocumentReadonly()
    {
        return $this->document_readonly;
    }

    public function hasDocumentReadonly()
    {
        return isset($this->document_readonly);
    }

    public function clearDocumentReadonly()
    {
        unset($this->document_readonly);
    }

    /**
     * Output only. The readonly field containing the information for an uploaded
     * business registration document.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.common.LocalServicesDocumentReadOnly document_readonly = 1 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\LocalServicesDocumentReadOnly $var
     * @return $this
     */
    public function setDocumentReadonly($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\LocalServicesDocumentReadOnly::class);
        $this->document_readonly = $var;

        return $this;
    }

}

