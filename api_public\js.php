<?php header("Content-Type: application/javascript");header("Cache-Control: max-age=604800, public");?>  
!function(e){var t={};function n(i){if(t[i])return t[i].exports;var o=t[i]={i:i,l:!1,exports:{}};return e[i].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(i,o,function(t){return e[t]}.bind(null,o));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){e.exports=n(1)},function(e,t){function n(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var i=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.options={overlayBackgroundColor:"#666666",overlayOpacity:.6,spinnerIcon:"ball-circus",spinnerColor:"#000",spinnerSize:"3x",overlayIDName:"overlay",spinnerIDName:"spinner",offsetY:0,offsetX:0,lockScroll:!1,containerID:null},this.stylesheetBaseURL="https://cdn.jsdelivr.net/npm/load-awesome@1.1.0/css/",this.spinner=null,this.spinnerStylesheetURL=null,this.numberOfEmptyDivForSpinner={"ball-8bits":16,"ball-atom":4,"ball-beat":3,"ball-circus":5,"ball-climbing-dot":1,"ball-clip-rotate":1,"ball-clip-rotate-multiple":2,"ball-clip-rotate-pulse":2,"ball-elastic-dots":5,"ball-fall":3,"ball-fussion":4,"ball-grid-beat":9,"ball-grid-pulse":9,"ball-newton-cradle":4,"ball-pulse":3,"ball-pulse-rise":5,"ball-pulse-sync":3,"ball-rotate":1,"ball-running-dots":5,"ball-scale":1,"ball-scale-multiple":3,"ball-scale-pulse":2,"ball-scale-ripple":1,"ball-scale-ripple-multiple":3,"ball-spin":8,"ball-spin-clockwise":8,"ball-spin-clockwise-fade":8,"ball-spin-clockwise-fade-rotating":8,"ball-spin-fade":8,"ball-spin-fade-rotating":8,"ball-spin-rotate":2,"ball-square-clockwise-spin":8,"ball-square-spin":8,"ball-triangle-path":3,"ball-zig-zag":2,"ball-zig-zag-deflect":2,cog:1,"cube-transition":2,fire:3,"line-scale":5,"line-scale-party":5,"line-scale-pulse-out":5,"line-scale-pulse-out-rapid":5,"line-spin-clockwise-fade":8,"line-spin-clockwise-fade-rotating":8,"line-spin-fade":8,"line-spin-fade-rotating":8,pacman:6,"square-jelly-box":2,"square-loader":1,"square-spin":1,timer:1,"triangle-skew-spin":1},this.originalBodyPosition="",this.originalBodyTop="",this.originalBodywidth=""}var t,i,o;return t=e,(i=[{key:"show",value:function(e){this.setOptions(e),this.addSpinnerStylesheet(),this.generateSpinnerElement(),this.options.lockScroll&&(document.body.style.overflow="hidden",document.documentElement.style.overflow="hidden"),this.generateAndAddOverlayElement()}},{key:"hide",value:function(){this.options.lockScroll&&(document.body.style.overflow="",document.documentElement.style.overflow="");var e=document.getElementById("loading-overlay-stylesheet");e&&(e.disabled=!0,e.parentNode.removeChild(e),document.getElementById(this.options.overlayIDName).remove(),document.getElementById(this.options.spinnerIDName).remove())}},{key:"setOptions",value:function(e){if(void 0!==e)for(var t in e)this.options[t]=e[t]}},{key:"generateAndAddOverlayElement",value:function(){var e="50%";0!==this.options.offsetX&&(e="calc(50% + "+this.options.offsetX+")");var t="50%";if(0!==this.options.offsetY&&(t="calc(50% + "+this.options.offsetY+")"),this.options.containerID&&document.body.contains(document.getElementById(this.options.containerID))){var n='<div id="'.concat(this.options.overlayIDName,'" style="display: block !important; position: absolute; top: 0; left: 0; overflow: auto; opacity: ').concat(this.options.overlayOpacity,"; background: ").concat(this.options.overlayBackgroundColor,'; z-index: 50; width: 100%; height: 100%;"></div><div id="').concat(this.options.spinnerIDName,'" style="display: block !important; position: absolute; top: ').concat(t,"; left: ").concat(e,'; -webkit-transform: translate(-50%); -ms-transform: translate(-50%); transform: translate(-50%); z-index: 9999;">').concat(this.spinner,"</div>"),i=document.getElementById(this.options.containerID);return i.style.position="relative",void i.insertAdjacentHTML("beforeend",n)}var o='<div id="'.concat(this.options.overlayIDName,'" style="display: block !important; position: fixed; top: 0; left: 0; overflow: auto; opacity: ').concat(this.options.overlayOpacity,"; background: ").concat(this.options.overlayBackgroundColor,'; z-index: 50; width: 100%; height: 100%;"></div><div id="').concat(this.options.spinnerIDName,'" style="display: block !important; position: fixed; top: ').concat(t,"; left: ").concat(e,'; -webkit-transform: translate(-50%); -ms-transform: translate(-50%); transform: translate(-50%); z-index: 9999;">').concat(this.spinner,"</div>");document.body.insertAdjacentHTML("beforeend",o)}},{key:"generateSpinnerElement",value:function(){var e=this,t=Object.keys(this.numberOfEmptyDivForSpinner).find((function(t){return t===e.options.spinnerIcon})),n=this.generateEmptyDivElement(this.numberOfEmptyDivForSpinner[t]);this.spinner='<div style="color: '.concat(this.options.spinnerColor,'" class="la-').concat(this.options.spinnerIcon," la-").concat(this.options.spinnerSize,'">').concat(n,"</div>")}},{key:"addSpinnerStylesheet",value:function(){this.setSpinnerStylesheetURL();var e=document.createElement("link");e.setAttribute("id","loading-overlay-stylesheet"),e.setAttribute("rel","stylesheet"),e.setAttribute("type","text/css"),e.setAttribute("href",this.spinnerStylesheetURL),document.getElementsByTagName("head")[0].appendChild(e)}},{key:"setSpinnerStylesheetURL",value:function(){this.spinnerStylesheetURL=this.stylesheetBaseURL+this.options.spinnerIcon+".min.css"}},{key:"generateEmptyDivElement",value:function(e){for(var t="",n=1;n<=e;n++)t+="<div></div>";return t}}])&&n(t.prototype,i),o&&n(t,o),e}();window.JsLoadingOverlay=new i,e.exports=JsLoadingOverlay}]);

!function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');

!function (w, d, t) {
w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++
)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var i="https://analytics.tiktok.com/i18n/pixel/events.js";ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=i,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};n=document.createElement("script");n.type="text/javascript",n.async=!0,n.src=i+"?sdkid="+e+"&lib="+t;e=document.getElementsByTagName("script")[0];e.parentNode.insertBefore(n,e)};
}(window, document, 'ttq');

"function"!=typeof Array.prototype.indexOf&&(Array.prototype.indexOf=function(e){for(var t=0;t<this.length;t++)if(this[t]===e)return t;return-1}),window.addEventListener("DOMContentLoaded",(function(){for(var e=document.querySelectorAll("a"),t=0;t<e.length;t++)e[t].href.indexOf("/cta")}),!0),window.gass=function(){function e(e){for(var t=0;t<e.length;t++)this[t]=e[t];this.length=e.length}e.prototype.forEach=function(e){return this.map(e),this},e.prototype.map=function(e){for(var t=[],n=0;n<this.length;n++)t.push(e.call(this,this[n],n));return t},e.prototype.mapOne=function(e){var t=this.map(e);return t.length>1?t:t[0]},e.prototype.text=function(e){return void 0!==e?this.forEach((function(t){t.innerText=e})):this.mapOne((function(e){return e.innerText}))},e.prototype.html=function(e){return void 0!==e?this.forEach((function(t){t.innerHTML=e})):this.mapOne((function(e){return e.innerHTML}))},e.prototype.addClass=function(e){var t="";if("string"!=typeof e)for(var n=0;n<e.length;n++)t+=" "+e[n];else t=" "+e;return this.forEach((function(e){e.className+=t}))},e.prototype.removeClass=function(e){return this.forEach((function(t){for(var n,o=t.className.split(" ");(n=o.indexOf(e))>-1;)o=o.slice(0,n).concat(o.slice(++n));t.className=o.join(" ")}))},e.prototype.attr=function(e,t){return void 0!==t?this.forEach((function(n){n.setAttribute(e,t)})):this.mapOne((function(t){return t.getAttribute(e)}))},e.prototype.append=function(e){return this.forEach((function(t,n){e.forEach((function(e){t.appendChild(n>0?e.cloneNode(!0):e)}))}))},e.prototype.prepend=function(e){return this.forEach((function(t,n){for(var o=e.length-1;o>-1;o--)t.insertBefore(n>0?e[o].cloneNode(!0):e[o],t.firstChild)}))},e.prototype.remove=function(){return this.forEach((function(e){return e.parentNode.removeChild(e)}))},e.prototype.on=document.addEventListener?function(e,t){return this.forEach((function(n){n.addEventListener(e,t,!1)}))}:document.attachEvent?function(e,t){return this.forEach((function(n){n.attachEvent("on"+e,t)}))}:function(e,t){return this.forEach((function(n){n["on"+e]=t}))},e.prototype.off=document.removeEventListener?function(e,t){return this.forEach((function(n){n.removeEventListener(e,t,!1)}))}:document.detachEvent?function(e,t){return this.forEach((function(n){n.detachEvent("on"+e,t)}))}:function(e,t){return this.forEach((function(t){t["on"+e]=null}))},e.prototype.updatelink=function(e){if(t.countpost<2)if(null==t.getCookie("visitor_id")&&0==t.countpost){try{t.post_visit(t.campaign_id,"v_visit",(function(){t.post_visit(t.campaign_id,"v_cron",(function(){}))}))}catch(e){(n=new FormData).append("message",e.message),n.append("stack",e.stack),n.append("url",window.location.href),n.append("project_key",t.pkey),t.request_post("https://"+t.subdomain+"/api.html?act=bug_js_error",n,(function(e){}))}null!=e&&e(t)}else{try{t.visitor_id=t.getCookie("visitor_id"),t.updateCta(),t.post_visit(t.campaign_id,"v_visit",(function(){t.post_visit(t.campaign_id,"v_cron",(function(){}))}))}catch(e){var n;(n=new FormData).append("message",e.message),n.append("stack",e.stack),n.append("url",window.location.href),n.append("project_key",t.pkey),t.request_post("https://"+t.subdomain+"/api.html?act=bug_js_error",n,(function(e){}))}null!=e&&e(t)}};var t={version:"0.0.1",interval:2e3,cta_hidden:0,use_form_ig:0,use_form_fb:0,use_form:0,send_cta_js:0,back_view:0,timer:null,timer1:null,timer2:null,timer3:null,param_get:{},connector:[],cctors:[],CTAeventName:null,pkey:null,page_url:null,ip:null,browser_agent:null,domain:null,subdomain:null,id:null,visitor_id:null,fbp:null,fbc:null,ref:null,fbclid:null,gclid:null,ttclid:null,adw_tag:null,_ttp:null,countpost:0,domload:0,divisi:null,form:[],getCookie:function(e){const t=`; ${document.cookie}`.split(`; ${e}=`);if(2===t.length)return t.pop().split(";").shift()},run:function(t,n){var o=this;if(t="object"==typeof t&&t||{},this.connector=t.connector||this.connector,this.pkey=t.pkey||this.pkey,this.interval=t.interval||this.interval,this.cta_hidden=t.cta_hidden||this.cta_hidden,this.use_form_ig=t.use_form_ig||this.use_form_ig,this.use_form_fb=t.use_form_fb||this.use_form_fb,this.use_form=t.use_form||this.use_form,this.send_cta_js=t.send_cta_js||this.send_cta_js,this.back_view=t.back_view||this.back_view,this.subdomain=t.subdomain,null!=t.adw_tag&&(this.adw_tag=t.adw_tag),1==o.cta_hidden)for(var i=document.querySelectorAll("a"),r=0;r<i.length;r++)-1!=i[r].href.indexOf("/cta")&&(i[r].style.display="none");window.addEventListener("load",(function(){o.domload=1}));var a=window.location.href;a=(a=a.replace("https://www.","")).replace("http://www.","");try{if(this.domain=a.replace("http://","").replace("https://","").split(/[/?#]/)[0],-1!==window.location.href.indexOf("&")){var s=window.location.href.split("?")[1].split("&"),d={};for(r=0;r<s.length;r++){var c=s[r].split("=");d[decodeURIComponent(c[0])]=decodeURIComponent(c[1])}this.param_get=d}void 0!==document.referrer&&(this.ref=document.referrer),window.clearInterval(this.timer),window.clearInterval(this.timer1),window.clearInterval(this.timer2);var l=new e(this.get("a"));o.request_get("https://ip.gass.co.id/",(function(e){if(1==e.code){o.ip=e.msg;var t=e.msg;let n=new Date;n.setTime(n.getTime()+864e5);const i="expires="+n.toUTCString();document.cookie="ip_gass="+t+"; "+i+"; path=/"}})),l.updatelink((function(e){void 0!==n&&n(e)})),o.timer=window.setInterval((function(){o.fbp=o.getCookie("_fbp"),void 0!==o.fbp&&(o.post_visit(o.campaign_id,"v_update",(function(){})),window.clearInterval(o.timer))}),2e3),o.timer1=window.setInterval((function(){o.fbc=o.getCookie("_fbc"),void 0!==o.fbc&&(o.post_visit(o.campaign_id,"v_update",(function(){})),window.clearInterval(o.timer1))}),2e3),o.timer2=window.setInterval((function(){void 0!==o.getCookie("client_id")&&(o.post_visit(o.campaign_id,"v_update",(function(){})),window.clearInterval(o.timer2))}),2e3),o.timer3=window.setInterval((function(){var e=o.getCookie("_ttp");void 0!==e&&(o._ttp=e,o.post_visit(o.campaign_id,"v_update",(function(){})),window.clearInterval(o.timer3))}),2e3),o.timer_link(o),setTimeout((function(){1!=o.use_form_ig&&1!=o.use_form_fb&&1!=o.use_form||o.enableForm()}),1e3)}catch(e){var p=new FormData;p.append("message",e.message),p.append("stack",e.stack),p.append("url",a),p.append("project_key",o.pkey),o.request_post("https://"+o.subdomain+"/api.html?act=bug_js_error",p,(function(e){}))}},timer_link:function(e){setInterval((function(){null!=e&&e.updateCta()}),2e3),1==e.back_view&&(history.pushState({page:1},"",""),window.addEventListener("popstate",(function(t){var n=e.get("a");e.getCookie("visitor_id");n.forEach((function(t){void 0===e.visitor_id||-1==t.href.indexOf("https://"+e.subdomain+"/cta")&&-1==t.href.indexOf("http://"+e.subdomain+"/cta")||e.post_click(t.href)}))})),setTimeout((function(){if(null!=e){var t=e.get("a");e.getCookie("visitor_id");t.forEach((function(t){void 0===e.visitor_id||-1==t.href.indexOf("https://"+e.subdomain+"/cta")&&-1==t.href.indexOf("http://"+e.subdomain+"/cta")||e.post_click(t.href)}))}}),1e4))},append_googletag:function(e,t,n){var o=e.getElementsByTagName(t)[0],i=e.createElement(t);i.async=!0,i.src="https://www.googletagmanager.com/gtag/js?id="+n,o.parentNode.insertBefore(i,o)},gtag:function(){window.dataLayer=window.dataLayer||[],dataLayer.push(arguments)},check_src:function(e){for(var t=document.getElementsByTagName("script"),n=0;n<t.length;n++)if(t[n].getAttribute("src")===e)return!0;return!1},post_visit:function(t,n,o){var i=this;i.countpost++,i.ip=i.getCookie("ip_gass"),i.fbp=i.getCookie("_fbp"),i.fbc=i.getCookie("_fbc");var r=new FormData;r=i.appendFormdata(r),i.request_post("https://"+i.subdomain+"/api.html?act="+n,r,(function(t){if(null!=t.visitor_id){i.visitor_id=t.visitor_id;var n=t.visitor_id;let o=new Date;o.setTime(o.getTime()+2592e6);const r="expires="+o.toUTCString();document.cookie="visitor_id="+n+"; "+r+"; path=/; SameSite=None; Secure",null!=t.connector&&(i.cctors=t.connector,Object.keys(t.connector).forEach((e=>{if("googleads"==t.connector[e].type){var n=t.connector[e].data.global_tag;null!=n&&0==i.check_src("https://www.googletagmanager.com/gtag/js?id="+n)&&(i.append_googletag(document,"script",n),i.gtag("js",new Date),i.gtag("config",n),i.gtag("event","page_view",{send_to:n,user_id:t.connector[e].data.account_id}))}else if("google analytic"==t.connector[e].type){var o=t.connector[e].data.measurement_id;0==i.check_src("https://www.googletagmanager.com/gtag/js?id="+o)&&(i.append_googletag(document,"script",o),i.gtag("js",new Date),i.gtag("config",o),i.gtag("get",o,"client_id",(function(e){i.param_get.clientId=e,document.cookie="client_id="+e+"; "+r+"; path=/"})))}else"facebook"==t.connector[e].type?(i.CTAeventName=t.connector[e].data.event&&t.connector[e].data.event.cta?t.connector[e].data.event.cta:"AddToCart",i.fbq=fbq,i.fbq("init",t.connector[e].data.pixel_id,{external_id:i.visitor_id,eventID:"ViewContent-"+i.visitor_id}),i.fbq("track","ViewContent")):"tiktok"==t.connector[e].type&&(i.ttq=ttq,i.ttq.load(t.connector[e].data.pixel_id),i.ttq.page(),i.ttq.track("ViewContent",{user:[{external_id:t.connector[e].data.visitor_id_hash}],event_id:"ViewContent-"+i.visitor_id}))}))),i.updateCta();new e(i.get("a"))}null!=o&&o(i)}))},post_click:function(e){for(var t=this,n=e.substr(1).split("&"),o={},i=0;i<n.length;i++){var r=n[i].split("=");o[decodeURIComponent(r[0])]=decodeURIComponent(r[1])}t.countpost++,t.fbp=t.getCookie("_fbp"),t.fbc=t.getCookie("_fbc"),t.visitor_id=t.getCookie("visitor_id");var a=new FormData;if(void 0!==typeof t.visitor_id&&null!==t.visitor_id&&a.append("visitor_id",t.visitor_id),void 0!==o.p&&a.append("project_key",o.p),void 0!==o.divisi&&null!==o.divisi&&a.append("divisi",o.divisi),void 0!==o.d&&null!==o.d&&a.append("divisi",o.d),void 0!==o.msg){var s=o.msg;s=s.replace(/\+/g," "),a.append("msg",s)}t.isMobileDevice()?a.append("use_deeplink",1):a.append("use_deeplink",0),a=t.appendFormdata(a),t.request_post("https://"+t.subdomain+"/api.html?act=v_cta",a,(function(e){t.isInstagramBrowser()?1==t.use_form_ig||(window.location=e.wa_url):t.isFacebookBrowser()?1==t.use_form_fb||(window.location=e.wa_url):void 0!==o.form||(window.location=e.wa_url)}))},isMobileDevice:function(e){return void 0!==window.orientation||-1!==navigator.userAgent.indexOf("IEMobile")},request_post:function(e,t,n){var o=new XMLHttpRequest;o.open("POST",e,!0),o.onreadystatechange=function(){if(4===o.readyState)if(o.status>=200&&o.status<400){var e=o.responseText;if(e)try{n(JSON.parse(e))}catch(t){n(e)}}else{n({code:0,msg:"Request failed"})}},o.onerror=function(){n({code:0,msg:"Request Error"})},o.send(t)},request_get:function(e,t){var n=new XMLHttpRequest;n.open("GET",e,!0),n.onreadystatechange=function(){if(n.readyState===XMLHttpRequest.DONE)if(n.status>=200&&n.status<400){var e=n.responseText;if(e)t({code:1,msg:e})}else{t({code:0,msg:"Request failed"})}},n.onerror=function(){t({code:0,msg:"Request Error"})},n.send()},appendFormdata:function(e){var t=this;t.ip=t.getCookie("ip_gass"),e.append("domain",t.domain),e.append("page_url",window.location),void 0!==t.ip&&e.append("ip",t.ip),void 0!==t.visitor_id&&null!==t.visitor_id?e.append("visitor_id",t.visitor_id):(t.visitor_id=t.getCookie("visitor_id"),void 0!==typeof t.visitor_id&&null!==t.visitor_id&&e.append("visitor_id",t.visitor_id)),void 0!==t.pkey&&null!==t.pkey&&e.append("project_key",t.pkey),void 0!==t.divisi&&null!==t.divisi&&e.append("divisi",t.divisi),void 0!==t.fbc&&void 0!==t.fbc&&null!==t.fbc&&e.append("fbc",t.fbc),void 0!==t.fbp&&void 0!==t.fbp&&null!==t.fbp&&e.append("fbp",t.fbp),void 0!==t._ttp&&void 0!==t._ttp&&null!==t._ttp&&e.append("_ttp",t._ttp),null!==t.ref&&e.append("ref",t.ref);var n=t.getCookie("client_id");void 0!==n&&e.append("clientId",n),t.connector.forEach((function(t){e.append("connector[]",t)}));const o=window.location.search;return new URLSearchParams(o).forEach(((n,o)=>{""!==o&&null!=n&&e.append(o,t.safeDecode(n))})),Object.keys(t.param_get).forEach((n=>{""!==n&&void 0!==t.param_get[n]&&null!==t.param_get[n]&&e.append(n,t.safeDecode(t.param_get[n]))})),e},safeDecode:function(e){try{return decodeURIComponent(e)}catch(t){return e}},enableForm:function(){var e=this;const t=document.createElement("div");t.id="popupForm",t.style.display="none",t.style.position="fixed",t.style.top="0",t.style.left="0",t.style.width="100%",t.style.height="100%",t.style.backgroundColor="rgba(0, 0, 0, 0.5)",t.style.justifyContent="center",t.style.alignItems="center",t.style.zIndex="99";const n=document.createElement("div");n.style.backgroundColor="#fff",n.style.padding="20px",n.style.borderRadius="5px",n.style.width="300px",n.style.boxShadow="0 4px 8px rgba(0, 0, 0, 0.2)",n.style.textAlign="center";const o=document.createElement("span");o.innerHTML="&times;",o.style.float="right",o.style.fontSize="24px",o.style.cursor="pointer",o.style.color="#333",o.onclick=function(){t.style.display="none"};const i=document.createElement("h2");i.innerText="Contact";const r=document.createElement("form");r.id="ContactForm";const a=document.createElement("input");a.type="text",a.id="firstname",a.name="firstname",a.placeholder="First Name";const s=document.createElement("input");s.type="tel",s.id="phone",s.name="phone",s.required=!0,s.placeholder="phone";const d=document.createElement("textarea");d.id="msg",d.name="msg",d.placeholder="Message";const c=document.createElement("button");c.type="submit",c.innerText="Submit",c.style.borderStyle="none",c.style.padding="0",c.style.display="inline-block",c.style.fontWeight="400",c.style.color="#c36",c.style.textAlign="center",c.style.whiteSpace="nowrap",c.style.userSelect="none",c.style.backgroundColor="transparent",c.style.border="1px solid #c36",c.style.padding=".5rem 1rem",c.style.fontSize="1rem",c.style.borderRadius="3px",c.style.transition="all .3s",c.style.cursor="pointer",r.appendChild(document.createElement("br")),r.appendChild(a),r.appendChild(document.createElement("br")),r.appendChild(document.createElement("br")),r.appendChild(s),r.appendChild(document.createElement("br")),r.appendChild(document.createElement("br")),r.appendChild(d),r.appendChild(document.createElement("br")),r.appendChild(document.createElement("br")),r.appendChild(c),n.appendChild(o),n.appendChild(i),n.appendChild(r),t.appendChild(n),e.form=r,document.body.appendChild(t),r.addEventListener("submit",(function(n){n.preventDefault();var o=new FormData(r);void 0!==typeof e.visitor_id&&null!==e.visitor_id&&o.append("visitor_id",e.visitor_id),o=e.appendFormdata(o),e.request_post("https://"+e.subdomain+"/api.html?act=v_form",o,(function(n){t.style.display="none",1==n.code?(e.showAlertForm(n.result.msg,0),setTimeout((function(){}),2e3)):e.showAlertForm(n.result.msg,1)}))}));document.querySelectorAll("input, textarea").forEach((e=>{e.style.width="90%",e.style.border="1px solid #666",e.style.borderRadius="3px",e.style.padding=".5rem 1rem",e.style.transition="all .3s",e.style.overflow="visible",e.style.fontFamily="inherit",e.style.fontSize="1rem",e.style.lineHeight="1.5",e.style.margin="0"})),window.onclick=function(e){e.target===t&&(t.style.display="none")}},showFormPopup:function(e){document.getElementById("popupForm")||this.enableForm();for(var t=e.substr(1).split("&"),n={},o=0;o<t.length;o++){var i=t[o].split("=");n[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}void 0!==n.divisi&&(this.divisi=n.divisi);const r=document.getElementById("popupForm");r&&(r.style.display="flex");const a=document.getElementById("msg");if(a&&void 0!==n.msg){var s=n.msg;s=(s=s.replace(/\+/g," ")).replaceAll("%break%","\n"),a.value=s}},showAlertForm:function(e,t){const n=document.createElement("div");n.style.position="fixed",n.style.top="0",n.style.left="0",n.style.width="100%",n.style.height="100%",n.style.backgroundColor="rgba(0, 0, 0, 0.5)",n.style.display="flex",n.style.alignItems="center",n.style.justifyContent="center",n.style.zIndex="99";const o=document.createElement("div");o.style.width="300px",o.style.padding="20px",o.style.backgroundColor="#fff",o.style.borderRadius="5px",o.style.boxShadow="0 4px 8px rgba(0, 0, 0, 0.2)",o.style.textAlign="center",o.style.position="relative";const i=document.createElement("div");i.innerHTML=e,i.style.margin="0",i.style.fontSize="1rem",i.style.color="#333",i.style.userSelect="text";const r=document.createElement("button");r.innerText="Close",r.style.marginTop="20px",r.style.padding="10px 20px",r.style.fontSize="1rem",r.style.color="#fff",r.style.backgroundColor="#c36",r.style.border="none",r.style.borderRadius="3px",r.style.cursor="pointer",r.onclick=function(){if(document.body.removeChild(n),t){var e=document.getElementById("popupForm");e&&(e.style.display="flex")}},o.appendChild(i),o.appendChild(r),n.appendChild(o),document.body.appendChild(n)},isInstagramBrowser:function(){const e=navigator.userAgent||navigator.vendor||window.opera;return/Instagram/i.test(e)},isFacebookBrowser:function(){const e=navigator.userAgent||navigator.vendor||window.opera;return/FBAN|FBAV/i.test(e)},updateCta:function(){var e=this;if(0!=e.domload){var t=this.get("a");e.getCookie("visitor_id");t.forEach((function(t){if(void 0!==e.visitor_id&&(-1!=t.href.indexOf("https://"+e.subdomain+"/cta")||-1!=t.href.indexOf("http://"+e.subdomain+"/cta"))){for(var n=t.href.substr(1).split("&"),o={},i=0;i<n.length;i++){var r=n[i].split("=");o[decodeURIComponent(r[0])]=decodeURIComponent(r[1])}if(void 0!==o.v)var a=o.v;if(null!=a);else if(null==a&&null!=e.visitor_id){var s=t.cloneNode(!0);-1==t.href.indexOf("p=")?s.href=t.href+"&cta=1&v="+e.visitor_id+"&p="+e.pkey:s.href=t.href+"&cta=1&v="+e.visitor_id,s.addEventListener("click",(t=>{if(t.preventDefault(),1==e.send_cta_js&&null!=e.CTAeventName&&""!=e.CTAeventName){try{var n=e.CTAeventName;e.fbq=fbq,e.fbq("track",n,{},{eventID:n+"-"+e.visitor_id})}catch(t){}setTimeout((()=>{e.post_click(s.href)}),1e3)}else e.post_click(s.href);var i=s.href;e.isInstagramBrowser()?1!=e.use_form_ig&&1!=e.use_form||e.showFormPopup(i):e.isFacebookBrowser()?1!=e.use_form_fb&&1!=e.use_form||e.showFormPopup(i):1!=e.use_form_ig&&1!=e.use_form_fb&&1!=e.use_form&&void 0===o.form||e.showFormPopup(i)})),s.style.display="",t.replaceWith(s)}e.get(t).attr("target","_self")}}))}},get:function(t){return new e("string"==typeof t?document.querySelectorAll(t):t.length?t:[t])},create:function(t,n){var o=new e([document.createElement(t)]);if(n)for(var i in n.className&&(o.addClass(n.className),delete n.className),n.text&&(o.text(n.text),delete n.text),n)n.hasOwnProperty(i)&&o.attr(i,n[i]);return o}};return t}();