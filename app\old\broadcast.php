<?php
class broadcast{
	
	function add($phone, $nama, $pesan){
		global $keya, $c_time, $app;
		$data["phone"] = $phone;
		$data["nama"] = $nama;
		$data["pesan"]     = $pesan;
		$id = $app->db->insert("x_broadcast", $data);
		if($id){
			$ret["code"] = 1;
			$ret["id"] = $id;
			$ret["msg"] = "sukses tambah broadcast"; 
			return $ret;
		}else{
			$ret["code"] =0;  
			$ret["msg"] = "gagal tambah broadcast"; 
			return $ret;
		}	
	}
}
