<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/services/recommendation_service.proto

namespace Google\Ads\GoogleAds\V18\Services\ApplyRecommendationOperation;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Parameters to use when applying a raise target CPA bid too low
 * recommendation. The apply is asynchronous and can take minutes depending on
 * the number of ad groups there is in the related campaign..
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.services.ApplyRecommendationOperation.RaiseTargetCpaBidTooLowParameters</code>
 */
class RaiseTargetCpaBidTooLowParameters extends \Google\Protobuf\Internal\Message
{
    /**
     * Required. A number greater than 1.0 indicating the factor by which to
     * increase the target CPA. This is a required field.
     *
     * Generated from protobuf field <code>double target_multiplier = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    protected $target_multiplier = 0.0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type float $target_multiplier
     *           Required. A number greater than 1.0 indicating the factor by which to
     *           increase the target CPA. This is a required field.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Services\RecommendationService::initOnce();
        parent::__construct($data);
    }

    /**
     * Required. A number greater than 1.0 indicating the factor by which to
     * increase the target CPA. This is a required field.
     *
     * Generated from protobuf field <code>double target_multiplier = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return float
     */
    public function getTargetMultiplier()
    {
        return $this->target_multiplier;
    }

    /**
     * Required. A number greater than 1.0 indicating the factor by which to
     * increase the target CPA. This is a required field.
     *
     * Generated from protobuf field <code>double target_multiplier = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param float $var
     * @return $this
     */
    public function setTargetMultiplier($var)
    {
        GPBUtil::checkDouble($var);
        $this->target_multiplier = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(RaiseTargetCpaBidTooLowParameters::class, \Google\Ads\GoogleAds\V18\Services\ApplyRecommendationOperation_RaiseTargetCpaBidTooLowParameters::class);

