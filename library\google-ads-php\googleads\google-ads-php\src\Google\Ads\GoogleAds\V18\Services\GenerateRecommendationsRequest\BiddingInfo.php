<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/services/recommendation_service.proto

namespace Google\Ads\GoogleAds\V18\Services\GenerateRecommendationsRequest;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Current bidding information of the campaign. Provides a wrapper for
 * bidding-related signals that inform recommendations.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.services.GenerateRecommendationsRequest.BiddingInfo</code>
 */
class BiddingInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * Current bidding strategy.
     * This field is necessary for the following recommendation_types:
     * CAMPAIGN_BUDGET, MAXIMIZE_CLICKS_OPT_IN, MAXIMIZE_CONVERSIONS_OPT_IN,
     * MAXIMIZE_CONVERSION_VALUE_OPT_IN, SET_TARGET_CPA, SET_TARGET_ROAS,
     * TARGET_CPA_OPT_IN, TARGET_ROAS_OPT_IN
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.BiddingStrategyTypeEnum.BiddingStrategyType bidding_strategy_type = 1;</code>
     */
    protected $bidding_strategy_type = null;
    protected $bidding_strategy_target_info;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $bidding_strategy_type
     *           Current bidding strategy.
     *           This field is necessary for the following recommendation_types:
     *           CAMPAIGN_BUDGET, MAXIMIZE_CLICKS_OPT_IN, MAXIMIZE_CONVERSIONS_OPT_IN,
     *           MAXIMIZE_CONVERSION_VALUE_OPT_IN, SET_TARGET_CPA, SET_TARGET_ROAS,
     *           TARGET_CPA_OPT_IN, TARGET_ROAS_OPT_IN
     *     @type int|string $target_cpa_micros
     *           Current target_cpa in micros.
     *           This can be populated for campaigns with a bidding strategy type of
     *           TARGET_CPA or MAXIMIZE_CONVERSIONS.
     *     @type float $target_roas
     *           Current target_roas.
     *           This can be populated for campaigns with a bidding strategy type of
     *           TARGET_ROAS or MAXIMIZE_CONVERSION_VALUE.
     *     @type \Google\Ads\GoogleAds\V18\Services\GenerateRecommendationsRequest\TargetImpressionShareInfo $target_impression_share_info
     *           Optional. Current Target Impression Share information of the campaign.
     *           This field is necessary for the following recommendation_types:
     *           CAMPAIGN_BUDGET
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Services\RecommendationService::initOnce();
        parent::__construct($data);
    }

    /**
     * Current bidding strategy.
     * This field is necessary for the following recommendation_types:
     * CAMPAIGN_BUDGET, MAXIMIZE_CLICKS_OPT_IN, MAXIMIZE_CONVERSIONS_OPT_IN,
     * MAXIMIZE_CONVERSION_VALUE_OPT_IN, SET_TARGET_CPA, SET_TARGET_ROAS,
     * TARGET_CPA_OPT_IN, TARGET_ROAS_OPT_IN
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.BiddingStrategyTypeEnum.BiddingStrategyType bidding_strategy_type = 1;</code>
     * @return int
     */
    public function getBiddingStrategyType()
    {
        return isset($this->bidding_strategy_type) ? $this->bidding_strategy_type : 0;
    }

    public function hasBiddingStrategyType()
    {
        return isset($this->bidding_strategy_type);
    }

    public function clearBiddingStrategyType()
    {
        unset($this->bidding_strategy_type);
    }

    /**
     * Current bidding strategy.
     * This field is necessary for the following recommendation_types:
     * CAMPAIGN_BUDGET, MAXIMIZE_CLICKS_OPT_IN, MAXIMIZE_CONVERSIONS_OPT_IN,
     * MAXIMIZE_CONVERSION_VALUE_OPT_IN, SET_TARGET_CPA, SET_TARGET_ROAS,
     * TARGET_CPA_OPT_IN, TARGET_ROAS_OPT_IN
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v18.enums.BiddingStrategyTypeEnum.BiddingStrategyType bidding_strategy_type = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setBiddingStrategyType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\BiddingStrategyTypeEnum\BiddingStrategyType::class);
        $this->bidding_strategy_type = $var;

        return $this;
    }

    /**
     * Current target_cpa in micros.
     * This can be populated for campaigns with a bidding strategy type of
     * TARGET_CPA or MAXIMIZE_CONVERSIONS.
     *
     * Generated from protobuf field <code>int64 target_cpa_micros = 2;</code>
     * @return int|string
     */
    public function getTargetCpaMicros()
    {
        return $this->readOneof(2);
    }

    public function hasTargetCpaMicros()
    {
        return $this->hasOneof(2);
    }

    /**
     * Current target_cpa in micros.
     * This can be populated for campaigns with a bidding strategy type of
     * TARGET_CPA or MAXIMIZE_CONVERSIONS.
     *
     * Generated from protobuf field <code>int64 target_cpa_micros = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setTargetCpaMicros($var)
    {
        GPBUtil::checkInt64($var);
        $this->writeOneof(2, $var);

        return $this;
    }

    /**
     * Current target_roas.
     * This can be populated for campaigns with a bidding strategy type of
     * TARGET_ROAS or MAXIMIZE_CONVERSION_VALUE.
     *
     * Generated from protobuf field <code>double target_roas = 3;</code>
     * @return float
     */
    public function getTargetRoas()
    {
        return $this->readOneof(3);
    }

    public function hasTargetRoas()
    {
        return $this->hasOneof(3);
    }

    /**
     * Current target_roas.
     * This can be populated for campaigns with a bidding strategy type of
     * TARGET_ROAS or MAXIMIZE_CONVERSION_VALUE.
     *
     * Generated from protobuf field <code>double target_roas = 3;</code>
     * @param float $var
     * @return $this
     */
    public function setTargetRoas($var)
    {
        GPBUtil::checkDouble($var);
        $this->writeOneof(3, $var);

        return $this;
    }

    /**
     * Optional. Current Target Impression Share information of the campaign.
     * This field is necessary for the following recommendation_types:
     * CAMPAIGN_BUDGET
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.services.GenerateRecommendationsRequest.TargetImpressionShareInfo target_impression_share_info = 4 [(.google.api.field_behavior) = OPTIONAL];</code>
     * @return \Google\Ads\GoogleAds\V18\Services\GenerateRecommendationsRequest\TargetImpressionShareInfo|null
     */
    public function getTargetImpressionShareInfo()
    {
        return $this->readOneof(4);
    }

    public function hasTargetImpressionShareInfo()
    {
        return $this->hasOneof(4);
    }

    /**
     * Optional. Current Target Impression Share information of the campaign.
     * This field is necessary for the following recommendation_types:
     * CAMPAIGN_BUDGET
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.services.GenerateRecommendationsRequest.TargetImpressionShareInfo target_impression_share_info = 4 [(.google.api.field_behavior) = OPTIONAL];</code>
     * @param \Google\Ads\GoogleAds\V18\Services\GenerateRecommendationsRequest\TargetImpressionShareInfo $var
     * @return $this
     */
    public function setTargetImpressionShareInfo($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Services\GenerateRecommendationsRequest\TargetImpressionShareInfo::class);
        $this->writeOneof(4, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getBiddingStrategyTargetInfo()
    {
        return $this->whichOneof("bidding_strategy_target_info");
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(BiddingInfo::class, \Google\Ads\GoogleAds\V18\Services\GenerateRecommendationsRequest_BiddingInfo::class);

