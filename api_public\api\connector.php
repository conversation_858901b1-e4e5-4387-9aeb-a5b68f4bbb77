<?php

function trigger($param)
{
    $rules = array(
        'project_key' => 'required',
     );
    validate_param($rules, $param);
    extract($param);

    global $app;
    $db = $app->db;
    $db->where("project_key = UNHEX(?)", [$project_key]);
    $project = $db->getone("project");

    if ($project != null) {
        $project_id = $project["project_id"];
        assign_child_db($project_id);
    }
    extract($param);
    assign_child_db($project_id, "localhost");

    global $app;
    $db2 = $app->db2;

    $con = new connector();

    $quee = $db2->get("quee_trigger_view");
   
    foreach ($quee as $key => $value) {
        try {
            $res = $con->trigger($value["visitor_id"], $value["type"]);
            $db2->where("visitor_id",$value["visitor_id"]);
            $db2->delete("quee_trigger_view");
            
            return ["code" => 1, "result" => "empty"];
        } catch (\Throwable $th) {
            return ["code" => 0, "result" => "empty"];
        }

        
    }

    return ["code" => 0, "result" => "empty"];

}
