<?php 
use Google\Auth\CredentialsLoader;
use Google\Auth\OAuth2;
use Psr\Http\Message\ServerRequestInterface;
use React\EventLoop\Loop;
use React\Http\HttpServer;
use React\Http\Message\Response;
use React\Socket\SocketServer;

use GetOpt\GetOpt;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentNames;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentParser;
use Google\Ads\GoogleAds\Lib\OAuth2TokenBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClient;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClientBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsException;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsServerStreamDecorator;
use Google\Ads\GoogleAds\V18\Errors\GoogleAdsError;
use Google\Ads\GoogleAds\V18\Resources\CustomerClient;
use Google\Ads\GoogleAds\V18\Services\CustomerServiceClient;
use Google\Ads\GoogleAds\V18\Services\GoogleAdsRow;
use Google\ApiCore\ApiException;
use Google\AdsApi\AdWords\AdWordsServices;
use Google\AdsApi\AdWords\AdWordsSessionBuilder;
use Google\AdsApi\AdWords\v201809\cm\CampaignService;
use Google\AdsApi\AdManager\AdManagerSessionBuilder;
use Google\Ads\GoogleAds\V18\Services\ListAccessibleCustomersRequest;
use Google\Ads\GoogleAds\V18\Services\GoogleAdsServiceClient;
use Google\Ads\GoogleAds\V18\Services\SearchGoogleAdsRequest;
use Google\Ads\GoogleAds\V18\Services\SearchGoogleAdsStreamRequest;


use Google\Ads\GoogleAds\Examples\Utils\Helper;
use Google\Ads\GoogleAds\V18\Enums\ConversionActionCategoryEnum\ConversionActionCategory;
use Google\Ads\GoogleAds\V18\Enums\ConversionActionStatusEnum\ConversionActionStatus;
use Google\Ads\GoogleAds\V18\Enums\ConversionActionTypeEnum\ConversionActionType;
use Google\Ads\GoogleAds\V18\Resources\ConversionAction;
use Google\Ads\GoogleAds\V18\Resources\ConversionAction\ValueSettings;
use Google\Ads\GoogleAds\V18\Services\ConversionActionOperation;
use Google\Ads\GoogleAds\V18\Services\MutateConversionActionsRequest;

function create_conversion_action($param) {
    $rules = array(
        'account_id' => 'required',
        'name' => 'required',
        'category' => 'required',
        'type' => 'required'
    );
    validate_param($rules, $param);
    extract($param);
    
    assign_child_db($project_id, "localhost");
    global $app;
    $db2 = $app->db2;
    
    $db2->where("project_id = ? and account_hash = UNHEX(?) and status = 1", [$project_id, $account_id]);
    $integration = $db2->getone("integration");
    
    if ($db2->count > 0) {
        $integration["data"] = unserialize($integration["data"]);
        
        // Google Ads API configuration
        $clientId = $app->config->google_clientId;
        $clientSecret = $app->config->google_clientSecret;
        $developerToken = $app->config->google_developerToken;
        $refresh_token = $integration["data"]['refresh_token'];
        
        // Build OAuth2 credentials
        $oAuth2Credential = (new OAuth2TokenBuilder())
            ->withClientId($clientId)
            ->withClientSecret($clientSecret)
            ->withRefreshToken($refresh_token)
            ->build();
            
        // Initialize Google Ads client
        $googleAdsClient = (new GoogleAdsClientBuilder())
            ->withDeveloperToken($developerToken)
            ->withOAuth2Credential($oAuth2Credential)
            ->build();
            
        try {
            $type = 'UPLOAD_CLICKS';
            // Create conversion action
            $conversionAction = new ConversionAction([
                'name' => $name,
                'category' => ConversionActionCategory::value($category),
                'type' => ConversionActionType::value($type),
                'status' => ConversionActionStatus::ENABLED,
                'value_settings' => new ValueSettings([
                    'default_value' => 0.0,
                    'always_use_default_value' => true
                ])
            ]);
            
            // Create operation
            $operation = new ConversionActionOperation();
            $operation->setCreate($conversionAction);
            
            // Build the request
            $request = new MutateConversionActionsRequest([
                'customer_id' => $integration['account_id'],
                'operations' => [$operation]
            ]);
            
            // Execute request
            $response = $googleAdsClient->getConversionActionServiceClient()
                ->mutateConversionActions($request);
                
            $result = $response->getResults()[0];
            
            $msg["code"] = 1;
            $msg["result"]["msg"] = "Conversion action created successfully";
            $msg["result"]["conversion_id"] = $result->getResourceName();
            
        } catch (GoogleAdsException $e) {
            $msg["code"] = 0;
            $msg["result"]["msg"] = $e->getMessage();
        }
    } else {
        $msg["code"] = 0;
        $msg["result"]["msg"] = "Account ID not found";
    }
    
    return $msg;
}


function get_convertion($param){
    $rules = array(
        'account_id' => 'required',
     );
    validate_param($rules, $param);
    extract($param);     
    assign_child_db($project_id,"localhost");
    global $keya, $c_time, $app;
    $db = $app->db;
    $db2 = $app->db2;
    $db2->where("project_id = ? and account_hash = UNHEX(?) and status = 1", [$project_id, $account_id]);
    $x = $db2->getone("integration");
    $convertion = []; $y=0;
    if($db2->count >0){
        $x["data"] = unserialize($x["data"]);
        $clientId = $app->config->google_clientId;
        $clientSecret = $app->config->google_clientSecret;
        $developerToken = $app->config->google_developerToken;
        $refresh_token = $x["data"]['refresh_token'];
        $oAuth2Credential = (new OAuth2TokenBuilder())
          ->withClientId($clientId)
          ->withClientSecret($clientSecret)
          ->withRefreshToken($refresh_token)
          ->build();
          //print_r($oAuth2Credential);
        $googleAdsClient = (new GoogleAdsClientBuilder())
            ->withDeveloperToken($developerToken)
            ->withOAuth2Credential($oAuth2Credential)
            ->build();
        
        try {
            $query = "
        SELECT
            conversion_action.id,
            conversion_action.name,
            conversion_action.type,
            conversion_action.primary_for_goal,
            conversion_action.category,
            conversion_action.status
        FROM conversion_action WHERE conversion_action.status = 'ENABLED'
        ORDER BY conversion_action.id";
            // Execute the query.
            $request = new SearchGoogleAdsRequest([
                'customer_id' => $x['account_id'], // Replace with a valid Customer ID
                'query' => $query,
            ]);
            $response = $googleAdsClient->getGoogleAdsServiceClient()->search($request);

            // Display conversion action details.
            
            foreach ($response->iterateAllElements() as $row) {
                $conversionAction = $row->getConversionAction();
                $convertion[$y]['conversionActionId'] = $conversionAction->getId();
                $convertion[$y]['name'] = $conversionAction->getName();
                $y++;
            }

        } catch (ApiException $e) {
            
        }
        $msg["code"] = 1;
        $msg["result"]["msg"] = "Success get convertion";
        $msg["result"]["data"] = $convertion;
    }else{
        $msg["code"] = 1;
        $msg["result"]["msg"] = "account_id not found";
    }
    return $msg;
}

function get_campaign($param){
    $rules = array(
        'account_id' => 'required',
     );
    validate_param($rules, $param);
    extract($param);     
    assign_child_db($project_id,"localhost");
    global $keya, $c_time, $app;
    $db = $app->db;
    $db2 = $app->db2;
    $db2->where("project_id = ? and account_hash = UNHEX(?) and status = 1", [$project_id, $account_id]);
    $x = $db2->getone("integration");
    $results = []; $y=0;
    if($db2->count >0){
        $x["data"] = unserialize($x["data"]);
        $clientId = $app->config->google_clientId;
        $clientSecret = $app->config->google_clientSecret;
        $developerToken = $app->config->google_developerToken;
        $refresh_token = $x["data"]['refresh_token'];
        $oAuth2Credential = (new OAuth2TokenBuilder())
          ->withClientId($clientId)
          ->withClientSecret($clientSecret)
          ->withRefreshToken($refresh_token)
          ->build();
          //print_r($oAuth2Credential);
        $googleAdsClient = (new GoogleAdsClientBuilder())
            ->withDeveloperToken($developerToken)
            ->withOAuth2Credential($oAuth2Credential)
            ->build();
        
        try {
            $query = "
                    SELECT 
                    campaign.id,
                    campaign.name,
                    ad_group.id,
                    ad_group.name,
                    ad_group_ad.ad.id,
                    ad_group_ad.ad.name
                FROM ad_group_ad";
            // Execute the query.
            $request = new SearchGoogleAdsRequest([
                'customer_id' => $x['account_id'], // Replace with a valid Customer ID
                'query' => $query,
            ]);
            $response = $googleAdsClient->getGoogleAdsServiceClient()->search($request);

            // Process results
            foreach ($response->iterateAllElements() as $row) {
                $results[] = [
                    'campaign_id' => $row->getCampaign()?->getId() ?? null,
                    'campaign_name' => $row->getCampaign()?->getName() ?? 'Unknown',
                    'adset_id' => $row->getAdGroup()?->getId() ?? null,
                    'adset_name' => $row->getAdGroup()?->getName() ?? 'Unknown',
                    'ad_id' => $row->getAdGroupAd()?->getAd()?->getId() ?? null,
                    'ad_name' => $row->getAdGroupAd()?->getAd()?->getName() ?? 'Unknown',
                ];
            }

        } catch (ApiException $e) {
            
        }
        $msg["code"] = 1;
        $msg["result"]["msg"] = "Success get campaign";
        $msg["result"]["data"] = $results;
    }else{
        $msg["code"] = 1;
        $msg["result"]["msg"] = "account_id not found";
    }
    return $msg;
}

function get_type($param)
{
    //$rules = array('project_id'      => 'required');
    //validate_param($rules, $param);
    extract($param);     
    assign_child_db($project_id,"localhost");

    $d = new connector();
    $msg = $d->get_type($project_id);
    
    return $msg;
}


function add($param)
{
    $rules = array(
        'name' => 'required',
        'type' => 'required',
        'data' => 'required'
     );
    validate_param($rules, $param);
    
  
        
        extract($param);
        assign_child_db($project_id,"localhost");

        $d = new connector();
        $msg = $d->add($type,$name,$data);

  
    return $msg;
}

function delete($param)
{
    $rules = array(
        'connector_key' => 'required'
     );
    validate_param($rules, $param);
    
  
        
    extract($param);
    assign_child_db($project_id,"localhost");

    $d = new connector();
    $msg = $d->delete($connector_key);

  
    return $msg;
}

function update($param)
{
    $rules = array(
        'name' => 'required',
        'type' => 'required',
        'data' => 'required',
        'connector_key' => 'required'
     );
    validate_param($rules, $param);
    
    extract($param);  
    assign_child_db($project_id,"localhost");
    $d = new connector();
    $msg = $d->update($connector_key,$type,$name,$data);

    return $msg;
}

function get($param)
{
    $rules = array(
        'project_id'      => 'required'
     );
    validate_param($rules, $param);
    extract($param);     
    assign_child_db($project_id,"localhost");

    $d = new connector();
    $msg = $d->get();
    return $msg;
}
function get_by_key($param)
{
     $rules = array(
        'project_id'      => 'required',
        'connector_key' => 'required'
     );
    validate_param($rules, $param);
    extract($param);     
    assign_child_db($project_id,"localhost");

    $d = new connector();
    $msg = $d->get_detail_key($connector_key);
    return $msg;
}

function get_log_type($param)
{
     $rules = array(
        'project_id'      => 'required',
        'connector_key' => 'required'
     );
    validate_param($rules, $param);
    extract($param);     
    assign_child_db($project_id,"localhost");

    $d = new connector();
    $msg = $d->get_log_type($connector_key);
    return $msg;
}


function get_log($param)
{
    $rules = array(
        'project_id'      => 'required',
        'connector_key' => 'required'
     );
    validate_param($rules, $param);
    extract($param);     
    assign_child_db($project_id,"localhost");

    $param_type = NULL;
    if(isset($type)){$param_type = $type;}

    $param_start = NULL;
    if(isset($start)){$param_start = $start;}

    $d = new connector();
   
    $msg = $d->get_log($connector_key,$param_start,$param_type);
    return $msg;
}