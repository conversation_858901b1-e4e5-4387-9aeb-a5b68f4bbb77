<?php

function get_detail($param)
{
    $rules = [
        "project_key" => "required",
        "id" => "required",
    ];

    validate_param($rules, $param);

    extract($param);

    global $app;
    $db = $app->db;
   

    $db->where("project_key = UNHEX(?)",[$project_key]);
    $project = $db->getone("project");

    if($project == NULL){
        $msg["code"] = 0;
        $msg["message"] = "project key not found";
        return $msg;
    }
    assign_child_db($project['project_id']);
    $db2 = $app->db2;

    $db2->where("report_group_id",$id);
    $res = $db2->getone("report_group","report_group_name,logo_url");

    if($res == NULL)
    {
        $msg['code'] = 0;
        $msg["result"]['msg'] = 'Error report not found';
    }
    else{
        $msg['code'] = 1;
        $msg["result"]['msg'] = 'Sukses';
        $msg["result"]['data'] = $res;
    }

    return $msg;
}

function get_custom($param)
{
    $rules = [
        "project_key" => "required",
        "id" => "required",
        'column'      => 'required',
        'start'      => 'required',
        'end'      => 'required',
        'type' => 'required',
    ];
    validate_param($rules, $param);

    extract($param);

    global $app;
    $db = $app->db;

    $db->where("project_key = UNHEX(?)",[$project_key]);
    $project = $db->getone("project");

    if($project == NULL){
        $msg["code"] = 0;
        $msg["message"] = "project key not found";
        return $msg;
    }
    assign_child_db($project['project_id']);

    $column = explode(",",trim($column));
    $column = array_filter($column);


    $r = new report();
    if(!isset($parent_id))
    {
        $parent_id = 0;
    }
    if(!isset($source))
    {
        $source = "";
    }
    if(isset($platform))
    {
        $source = $platform;
    }

    if(isset($convertion_type))
    {
        $param_convertion_type = $convertion_type;
    }else{
        $param_convertion_type = "realtime";
    }

    if($type != "group" || $type != "custom")
    {
        $parent_id = $id;
        $id = "";
    }
   
    $sort_by =NULL;
    $sort_type= NULL;
    $platform = NULL;
    if(isset($param['sort_type'])){$sort_type = $param['sort_type'];}
    if(isset($param['sort_by'])){$sort_by = $param['sort_by'];}

    $data = $r->get_overview_custom($id,$param_convertion_type,$column,$start,$end,$type,$source,$parent_id,$sort_by,$sort_type);

    if($data)
    {
        $msg['code'] = 1;
        $msg["result"]['msg'] = 'Sukses';
        $msg["result"]['data'] = $data;
    }
    else{
        $msg['code'] = 0;
        $msg["result"]['msg'] = 'Error';
    }
    
    return $msg;
}