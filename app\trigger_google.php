<?php
use Google\Auth\CredentialsLoader;
use Google\Auth\OAuth2;
use Psr\Http\Message\ServerRequestInterface;
use React\EventLoop\Loop;
use React\Http\HttpServer;
use React\Http\Message\Response;
use React\Socket\SocketServer;

use GetOpt\GetOpt;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentNames;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentParser;
use Google\Ads\GoogleAds\Lib\OAuth2TokenBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClient;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClientBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsException;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsServerStreamDecorator;
use Google\Ads\GoogleAds\V18\Errors\GoogleAdsError;
use Google\Ads\GoogleAds\V18\Resources\CustomerClient;
use Google\Ads\GoogleAds\V18\Services\CustomerServiceClient;
use Google\Ads\GoogleAds\V18\Services\GoogleAdsRow;
use Google\ApiCore\ApiException;
use Google\AdsApi\AdWords\AdWordsServices;
use Google\AdsApi\AdWords\AdWordsSessionBuilder;
use Google\AdsApi\AdWords\v201809\cm\CampaignService;
use Google\AdsApi\AdManager\AdManagerSessionBuilder;
use Google\Ads\GoogleAds\V18\Services\ListAccessibleCustomersRequest;
use Google\Ads\GoogleAds\V18\Services\GoogleAdsServiceClient;
use Google\Ads\GoogleAds\V18\Services\SearchGoogleAdsRequest;
use Google\Ads\GoogleAds\V18\Services\SearchGoogleAdsStreamRequest;


use Google\Ads\GoogleAds\Examples\Utils\Helper;
use Google\Ads\GoogleAds\V18\Enums\ConversionActionCategoryEnum\ConversionActionCategory;
use Google\Ads\GoogleAds\V18\Enums\ConversionActionStatusEnum\ConversionActionStatus;
use Google\Ads\GoogleAds\V18\Enums\ConversionActionTypeEnum\ConversionActionType;
use Google\Ads\GoogleAds\V18\Resources\ConversionAction;
use Google\Ads\GoogleAds\V18\Resources\ConversionAction\ValueSettings;
use Google\Ads\GoogleAds\V18\Services\ConversionActionOperation;
use Google\Ads\GoogleAds\V18\Services\MutateConversionActionsRequest;

use Google\Ads\GoogleAds\V18\Services\ClickConversion;
use Google\Ads\GoogleAds\V18\Services\UploadClickConversionsRequest;
use Google\Ads\GoogleAds\V18\Services\ConversionUploadServiceClient;


class trigger_gads {
    private $table_visitor;
	private $table_log;

	function __construct()
    {
  
    	$this->table_visitor = "visitor";
    	$this->table_log = "log_connector";
    	
    }
    public function sent_pixel($connector_key, $event_value, $vid, $event) {
        global $keya, $c_time, $app;
        $db = $app->db;
        $db2 = $app->db2;
        $db2->where("connector_key = UNHEX(?)", [$connector_key]);
        $x = $db2->getone("connector");
        if($db2->count >0){
            $data = json_decode($x['data'], true);
            $conversion_id = $data['event'][$event];
        }else{
            $log["result"] = json_encode([
                "code" => 0,
                "msg" => "connector not found"
            ]);
            return $log['result'];
        }
        $log["connector_key"] = $db2->func("UNHEX(?)", array($connector_key));
        $log["vid"] = $vid;
        $log["event"] = $event;
        $log["waktu"] = date("Y-m-d H:i:s");
        $gclid = $this->get_gclid($vid);
        if($gclid==null){
            $log["error"] = 1;
            $log["result"] = json_encode([
                "code" => 0,
                "msg" => "gclid not found"
            ]);
            $db2->insert("log_connector", $log);
            return $log['result'];
        }
        if($conversion_id==null || $conversion_id==''){
            $log["error"] = 1;
            $log["result"] = json_encode([
                "code" => 0,
                "msg" => "conversion_id not found"
            ]);
            return $log['result'];
        }
        try {
            // Google Ads API implementation here
            $conversion_data = [
                'customer_id' => $data['account_id'],
                'conversion_action_id' => $conversion_id,
                'value' => $event_value,
                'refresh_token' => $data['refresh_token'],
                //'gclid' => '**********',
                'gclid' => $gclid
            ];

            // Make API call to Google Ads
            $response = $this->send_conversion($conversion_data);
            if($response['code']==0){
                if($response['msg']=='Failed to get access token'){
                    $db2->where("account_id = ?", [$data['account_id']]);
                    $db2->update("account", ["message" => $response['msg']]);
                }
                $log["error"] = 1;
                $log["result"] = json_encode([
                    "code" => 0,
                    "msg" => "Failed to send conversion to Google Ads ".$response['msg'],
                    "response" => $response
                ]);
            }else{
                $log["error"] = 0;
                $log["result"] = json_encode([
                    "code" => 1,
                    "msg" => "Success sending conversion to Google Ads",
                    "response" => $response
                ]);
            }
        } catch (Exception $e) {
            $log["error"] = 1; 
            $log["result"] = json_encode([
                "code" => 0,
                "msg" => $e->getMessage()
            ]);
        }
        $db2->insert("log_connector", $log);
        return $log["result"];
    }

    private function get_gclid($vid) {
        global $app;
        $db2 = $app->db2;
        
        $db2->where("visitor_id", $vid);
        $visitor = $db2->getOne("visitor");
        
        if ($visitor) {
            $data = unserialize($visitor["data"]);
            return isset($data['google_ads']["gclid"]) ? $data['google_ads']["gclid"] : null;
        }
        return null;
    }

    public function send_conversion($data) {
        global $app;
        $db2 = $app->db2;
        // Validate required data
        if (empty($data['gclid']) || strlen($data['gclid']) < 10) {
            return ['code' => 0, 'msg' => 'Invalid GCLID'];
        }
    
        // OAuth2 token endpoint
        $token_url = 'https://oauth2.googleapis.com/token';
        
        // Get fresh access token using refresh token
        $token_data = [
            'client_id' => $app->config->google_clientId,
            'client_secret' => $app->config->google_clientSecret,
            'refresh_token' => $data['refresh_token'],
            'grant_type' => 'refresh_token'
        ];
    
        $ch = curl_init($token_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($token_data));
        $token_response = json_decode(curl_exec($ch), true);
        curl_close($ch);
    
        if (!isset($token_response['access_token'])) {            
            return ['code' => 0, 'msg' => 'Failed to get access token'];
        }
    
        // Prepare conversion data
        $conversion_endpoint = "https://googleads.googleapis.com/v18/customers/{$data['customer_id']}:uploadClickConversions";
        
        $dt = new DateTime('now', new DateTimeZone('Asia/Jakarta'));
        $conversion_payload = [
            'conversions' => [[
                'conversion_action' => "customers/{$data['customer_id']}/conversionActions/{$data['conversion_action_id']}",
                'conversion_date_time' => $dt->format('Y-m-d H:i:sP'),
                'conversion_value' => (float) $data['value'],
                'currency_code' => 'IDR',
                'gclid' => (string) $data['gclid']
            ]],
            'partial_failure' => true
        ];
    
        // Send conversion request
        $ch = curl_init($conversion_endpoint);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($conversion_payload),
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $token_response['access_token'],
                'developer-token: ' . $app->config->google_developerToken,
                'Content-Type: application/json'
            ]
        ]);
    
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        // Parse the response
        $response_data = json_decode($response, true);
        
        // Check for partial failures
        if (isset($response_data['partialFailureError'])) {
            return [
                'code' => 0,
                'msg' => 'Failed to send conversion: ' . $response_data['partialFailureError']['message'],
                'response' => $response
            ];
        }
    
        if ($http_code === 200) {
            return [
                'code' => 1, 
                'msg' => 'Success sending conversion to Google Ads',
                'response' => $response
            ];
        }
    
        return [
            'code' => 0,
            'msg' => 'Failed to send conversion',
            'response' => $response
        ];
    }
    
    
    private function send_conversion_lib($data) {
        global $app;
        $clientId = $app->config->google_clientId;
        $clientSecret = $app->config->google_clientSecret;
        $developerToken = $app->config->google_developerToken;
        $refresh_token = $data['refresh_token'];

        // Validasi GCLID sebelum mengirim
        if (empty($data['gclid']) || strlen($data['gclid']) < 10) {
            return ['code' => 0, 'msg' => 'Invalid GCLID. It must be a valid Google Click ID.'];
        }

        // Build OAuth2 credentials
        $oAuth2Credential = (new OAuth2TokenBuilder())
            ->withClientId($clientId)
            ->withClientSecret($clientSecret)
            ->withRefreshToken($refresh_token)
            ->build();

        // Initialize Google Ads client
        $googleAdsClient = (new GoogleAdsClientBuilder())
            ->withDeveloperToken($developerToken)
            ->withOAuth2Credential($oAuth2Credential)
            ->build();

        try {
            // Format conversion_action sesuai Google Ads API
            $conversionActionResourceName = "customers/{$data['customer_id']}/conversionActions/{$data['conversion_action_id']}";

            // Format waktu sesuai RFC 3339
            $dt = new DateTime('now', new DateTimeZone('Asia/Jakarta'));
            $conversionDateTime = $dt->format('Y-m-d H:i:sP');

            // Convert data into ClickConversion object
            $conversionAction = new ClickConversion([
                'conversion_action' => $conversionActionResourceName,
                'conversion_date_time' => $conversionDateTime,
                'conversion_value' => (float) $data['value'],
                'currency_code' => 'IDR',
                'gclid' => (string) $data['gclid']
            ]);

            // Create UploadClickConversionsRequest
            $request = new UploadClickConversionsRequest([
                'customer_id' => $data['customer_id'],
                'conversions' => [$conversionAction],
                'partial_failure' => true
            ]);

            // Send conversion to Google Ads
            $response = $googleAdsClient->getConversionUploadServiceClient()
                ->uploadClickConversions($request);

            // Check for partial failure
            $partialFailure = $response->getPartialFailureError();
            if ($partialFailure) {
                return [
                    'code' => 0,
                    'msg' => 'Partial failure',
                    'error_details' => $partialFailure->getMessage()
                ];
            }

            return ['code' => 1, 'msg' => 'Success sending conversion to Google Ads', 'response' => $response->serializeToJsonString()];

        } catch (GoogleAdsException $e) {
            return ['code' => 0, 'msg' => 'Google Ads error: ' . $e->getMessage()];
        } catch (ApiException $e) {
            return ['code' => 0, 'msg' => 'API error: ' . $e->getMessage()];
        }
    }
    

}
