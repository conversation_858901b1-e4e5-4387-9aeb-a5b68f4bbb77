<?php

class Collection implements Iterator {
	private $data = [];

	function rewind() {
		return reset($this->data);
	}
	function current() {
		return current($this->data);
	}
	function key() {
		return key($this->data);
	}
	function next() {
		return next($this->data);
	}
	function valid() {
		return key($this->data) !== null;
	}
	function __construct(array $data = []) {
		$this->data = $data;
	}
	function __get($key) {
		return $this->data($key);
	}
	function __set($key, $value) {
		$this->data($key, $value);
	}
	function __unset($key) {
		unset($this->data[$key]);
	}
	function __call($key, $args) {
		if(isset($this->data[$key]) && is_callable($this->data[$key]))
			return call_user_func_array($this->data[$key], $args);
		else
			return isset($args[0]) ? $this->data($key, null, $args[0]) : $this->data($key);
	}
	function data($key = null, $value = null, $default = null) {
		// ->data(array $key) append data ($key as new data array to append)
		if(is_array($key) && $value === null) $this->data = array_merge($this->data, $key);
		// ->data(array $key, bool true) overwrite data ($key as new data array to overwrite)
		elseif(is_array($key) && $value == true) $this->data = $key;
		// ->data($key, [null, [$default]]) get: return data[$key] or $default if $data[$key] is not set
		elseif($key !== null && $value === null) return isset($this->data[$key]) ? $this->data[$key] : $default;
		// ->data($key, $value) set: assign value to data[$key] as is
		elseif($key !== null && $value !== null) $this->data[$key] = $value;
		// ->data() no args: get all. return all data as array
		elseif($key === null && $value === null) return $this->data;
	}
	function map($callback) {
		$this->data = array_map($callback, $this->data);
	}
}