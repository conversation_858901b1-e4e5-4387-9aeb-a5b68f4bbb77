<?php

namespace DynamicWebhook;

use DynamicWebhook\Interfaces\PlatformDetectorInterface;

/**
 * Platform detector for webhook sources
 */
class PlatformDetector implements PlatformDetectorInterface
{
    private array $supportedPlatforms = [
        'konekwa', 'waba', 'halosis', 'sleekflow', 'barantum', 'chatdaddy',
        'fonnte', 'freshchat', 'smartchat', 'botsailor', 'kommo', 'otoklix',
        'pancake', 'qiscus-omnichannel', 'rasayel', 'respondio', 'wabahalosis',
        'hs', 'qontak', 'default'
    ];

    /**
     * Detect platform based on webhook data
     */
    public function detect(array $data, ?string $provider = null): string
    {
        // If provider is explicitly specified, use it
        if ($provider && $this->isSupported($provider)) {
            return $provider;
        }

        // Auto-detection based on data structure
        return $this->autoDetectPlatform($data);
    }

    /**
     * Check if platform is supported
     */
    public function isSupported(string $platform): bool
    {
        return in_array($platform, $this->supportedPlatforms);
    }

    /**
     * Get list of supported platforms
     */
    public function getSupportedPlatforms(): array
    {
        return $this->supportedPlatforms;
    }

    /**
     * Auto-detect platform based on data structure
     */
    private function autoDetectPlatform(array $data): string
    {
        // Konekwa - has 'raw' field
        if (isset($data['raw'])) {
            return 'konekwa';
        }

        // WABA - Meta WhatsApp Business API structure
        if (isset($data['entry'][0]['changes'][0]['value']['messages']) || 
            isset($data['entry'][0]['changes'][0]['value']['message_echoes'])) {
            return 'waba';
        }

        // Halosis - has type message.received/sent
        if (isset($data['type']) && in_array($data['type'], ['message.received', 'message.sent'])) {
            return 'halosis';
        }

        // Sleekflow - has messageContent or nested message structure
        if (isset($data['messageContent']) || isset($data['message']['message_content'])) {
            return 'sleekflow';
        }

        // Barantum - has message_users_id
        if (isset($data['message_users_id'])) {
            return 'barantum';
        }

        // ChatDaddy - has event message-insert
        if (isset($data['event']) && $data['event'] == 'message-insert') {
            return 'chatdaddy';
        }

        // Fonnte - has 'quick' field
        if (isset($data['quick'])) {
            return 'fonnte';
        }

        // Freshchat - nested data.message.message_parts structure
        if (isset($data['data']['message']['message_parts'])) {
            return 'freshchat';
        }

        // SmartChat - has specific fields for form-data or customData
        if (isset($data['no_customer']) && isset($data['message_type'])) {
            return 'smartchat';
        }
        if (isset($data['customData']['no_customer'])) {
            return 'smartchat';
        }

        // Qontak - structure with room and sender_type
        if (isset($data['room']['account_uniq_id']) && isset($data['sender_type'])) {
            return 'qontak';
        }
        if (isset($data['text']) && isset($data['room']['channel_integration_id'])) {
            return 'qontak';
        }

        // Pancake - similar to WABA but with different status field patterns
        if (isset($data['entry'][0]['changes'][0]['value'])) {
            $value = $data['entry'][0]['changes'][0]['value'];
            if (isset($value['statuses']) && !isset($value['messages'])) {
                return 'pancake'; // Status update from Pancake
            }
            if (isset($value['messages']) && isset($value['contacts'])) {
                // Could be either WABA or Pancake, assume Pancake if we can't determine otherwise
                return 'pancake';
            }
        }

        return 'default';
    }
}
