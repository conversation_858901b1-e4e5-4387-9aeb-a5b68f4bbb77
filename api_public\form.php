<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL ^ E_DEPRECATED);
function isInstagramInAppBrowser() {
  // Get the user agent from the request
  $userAgent = $_SERVER['HTTP_USER_AGENT'];

  // Check if Instagram's in-app browser is being used and if the device is iOS (iPhone, iPad, or iPod)
  $isInstagram = strpos($userAgent, 'Instagram') !== false;
  $isIOS = preg_match('/iPhone|iPad|iPod|android/i', $userAgent);

  // Return true if both Instagram and iOS are detected
  return $isInstagram && $isIOS;
}

function isInstagramInAppBrowserOnIOS() {
  // Get the user agent from the request
  $userAgent = $_SERVER['HTTP_USER_AGENT'];

  // Check if Instagram's in-app browser is being used and if the device is iOS (iPhone, iPad, or iPod)
  $isInstagram = strpos($userAgent, 'Instagram') !== false;
  $isIOS = preg_match('/iPhone|iPad|iPod/i', $userAgent);

  // Return true if both Instagram and iOS are detected
  return $isInstagram && $isIOS;
}

function isInstagramInAppBrowserOnAndroid() {
  // Get the user agent from the request
  $userAgent = $_SERVER['HTTP_USER_AGENT'];

  // Check if Instagram's in-app browser is being used and if the device is iOS (iPhone, iPad, or iPod)
  $isInstagram = strpos($userAgent, 'Instagram') !== false;
  $isIOS = preg_match('/android/i', $userAgent);

  // Return true if both Instagram and iOS are detected
  return $isInstagram && $isIOS;
}

global $app;
$db = $app->db;
$form = false;
if (isset($_GET["divisi"])) {
    $_GET["d"] = $_GET["divisi"];
}
if (isset($_GET["form"]) && $_GET["form"] == 1) {
    $form = true;
}

if(isInstagramInAppBrowser()){
    $form = true;
}

if (!isset($_GET["p"])) {
    echo 'Invalid Gass.co.id JavaScript Code';
    die();
} else {
    $db->where("project_key = UNHEX(?)", [$_GET["p"]]);
    $project = $db->getone("project");

    if ($project == null) {
        echo 'Invalid Gass.co.id JavaScript Code';
        die();
    } else {

        assign_child_db($project["project_id"]);
        global $app;
        $db2 = $app->db2;
    }
}

if (isInstagramInAppBrowserOnIOS()){
    $wa_url = "https://api.whatsapp.com/send";
    $form = true;
}else if (isInstagramInAppBrowserOnAndroid()) {
    $wa_url = "https://api.whatsapp.com/send";
    $form = true;
}else if (isMobile()) {
    $wa_url = "whatsapp://send";
} else {
    $wa_url = "https://api.whatsapp.com/send";
}

//var_dump($_GET);die();

if (isset($_GET["msg"])) {
    $msg = urlencode($_GET["msg"]);
    $msg = str_replace('%25break%25', '%0A', $msg);
} else { $msg = "Halo";}

$divisi = null;
$all_cs = false;
$pembagian = new cs_pembagian();
if (isset($_GET["d"])) {
    $divisi = $_GET["d"];

    $db2->where("divisi_key = UNHEX(?)", [md5($divisi)]);
    $divisi = $db2->getone("divisi");

    if ($divisi != null) {

        $cs = $pembagian->get_cs($divisi["divisi_id"]);

    } else {
        $all_cs = true;
    }
} else {
    $all_cs = true;
}

if ($all_cs) {
    $cs = $pembagian->random();
}

$phone = $cs;

if (isset($_GET["v"])) {
    $visitor_id = $_GET["v"];
}

if (isset($visitor_id)) {

    if (strpos($msg, '_gid_') !== false) {
        $msg = str_replace("_gid_", " " . $visitor_id . " ", $msg);
    } else {
        $msg = "ID+%5B" . " " . $visitor_id . " " . "%5D%0A%0A" . $msg;
    }

    $visitor_id = str_replace(".", "", $visitor_id);
    $visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");

    $db2->where("visitor_id", $visitor_id);
    $visitor = $db2->getone("visitor");

    ////////////////////////// add to report //////////////
    if ($visitor != null) {

        $visitor_data = unserialize($visitor["data"]);

        if (isset($visitor_data["last_campaign"])) {
            if ($visitor_data["last_campaign"]["source"] == "meta") {
                if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                    $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];

                    $campaign = new report();
                    ////////////////////////////// unique by adcopy id
                    if (!isset($_COOKIES["cta-meta-" . $adcopy_id])) {
                        $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                    }
                    $campaign->add_report_data($visitor, $adcopy_id, "cta");
                    setcookie("cta-meta-" . $adcopy_id, 1, time() + (86400 * 360), "/");
                }
            }

            if ($visitor_data["last_campaign"]["source"] == "tiktok") {
                if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                    $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];

                    $campaign = new report();
                    ////////////////////////////// unique by adcopy id
                    if (!isset($_COOKIES["cta-tiktok-" . $adcopy_id])) {
                        $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                    }
                    $campaign->add_report_data($visitor, $adcopy_id, "cta");
                    setcookie("cta-tiktok-" . $adcopy_id, 1, time() + (86400 * 360), "/");
                }
            }

            if ($visitor_data["last_campaign"]["source"] == "snack") {
                if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                    $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];

                    $campaign = new report();
                    ////////////////////////////// unique by adcopy id
                    if (!isset($_COOKIES["cta-snack-" . $adcopy_id])) {
                        $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                    }
                    $campaign->add_report_data($visitor, $adcopy_id, "cta");
                    setcookie("cta-snack-" . $adcopy_id, 1, time() + (86400 * 360), "/");
                }
            }

            if ($visitor_data["last_campaign"]["source"] == "google") {
                if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                    $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];

                    $campaign = new report();
                    ////////////////////////////// unique by adcopy id
                    if (!isset($_COOKIES["cta-google-" . $adcopy_id])) {
                        $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                    }
                    $campaign->add_report_data($visitor, $adcopy_id, "cta");
                    setcookie("cta-google-" . $adcopy_id, 1, time() + (86400 * 360), "/");
                }
            }

            if ($visitor_data["last_campaign"]["source"] == "organic") {
                if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                    $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];

                    $campaign = new report();
                    ////////////////////////////// unique by adcopy id
                    if (!isset($_COOKIES["cta-organic-" . $adcopy_id])) {
                        $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                    }
                    $campaign->add_report_data($visitor, $adcopy_id, "cta");
                    setcookie("cta-organic-" . $adcopy_id, 1, time() + (86400 * 360), "/");
                }
            }

        }
        //@file_put_contents("log_cta.txt", "[" . date("Y-m-d H:i:s") . "]\n" . $visitor_id . "\n\n\n", FILE_APPEND);
        $con = new connector();
        $con->trigger($visitor_id, "cta");

        //setcookie("cta_lifetime", 1, time() + (86400 * 360), "/");

        $visitor_event["waktu"] = date("Y-m-d H:i:s");
        $visitor_event["visitor_id"] = $visitor_id;
        $visitor_event["event"] = "cta";
        $db2->insert("visitor_event", $visitor_event);
        //  echo $db2->getLastQuery();die();
    }

    ////////////////////////////////////////////////////

    ////////////////////////// trigger connector //////////////////////////

    //////////////////////////////////////////
}else{
    $visitor_id = '';
}

ob_start("minifier");

//var_dump($msg);die();
function minifier($code)
{

    $search = array(

        // Remove whitespaces after tags
        '/\>[^\S ]+/s',

        // Remove whitespaces before tags
        '/[^\S ]+\</s',

        // Remove multiple whitespace sequences
        '/(\s)+/s',

        // Removes comments
        '/<!--(.|\s)*?-->/',
    );
    $replace = array('>', '<', '\\1');
    $code = preg_replace($search, $replace, $code);
    return $code;
}
header("Content-Type: text/html; charset=UTF-8");
?>


<?php
 if($form==false){
     
?>
<!doctype html>
<html ⚡>

<head>
  <meta charset="utf-8">
  <meta name="robots" content="noindex"/>
  <script async src="https://cdn.ampproject.org/v0.js"></script>
  <script async custom-element="amp-iframe" src="https://cdn.ampproject.org/v0/amp-iframe-0.1.js"></script>
  <script async custom-element="amp-analytics" src="https://cdn.ampproject.org/v0/amp-analytics-0.1.js"></script>
  <!--AMP HTML files require a canonical link pointing to the regular HTML. If no HTML version exists, it should point to itself.-->
  <link rel="canonical" href="contact.php">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/solid.css" integrity="sha384-ioUrHig76ITq4aEJ67dHzTvqjsAP/7IzgwE7lgJcg2r7BRNGYSK0LwSmROzYtgzs" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/fontawesome.css" integrity="sha384-sri+NftO+0hcisDKgr287Y/1LVnInHJ1l+XC7+FOabmTTIK0HnE2ID+xxvJ21c5J" crossorigin="anonymous">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:100,100i,300,300i,400,400i,500,500i,700,700i,900,900i">
  <link type="text/css" href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" />
  <meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1,maximum-scale=1,user-scalable=no">
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black" />

  <meta http-equiv="refresh" content="1;url=<?=$wa_url?>?phone=<?=$phone?>&text=<?=$msg?>" />

  <style amp-custom>
    .acor{font-size:16px;text-align:center;margin-left:20px;margin-right:20px}.faq{font-family:Marcellus,Georgia,Times,'Century Schoolbook L',serif;font-size:18px;text-align:center}.faq h2{margin-top:10px}.faq p{font-family:Arial;font-size:14px;text-align:center;margin:20px}.judul{margin-top:50px;margin-bottom:30px}body{font-family:Roboto,sans-serif;font-size:14px;background-color:#fff}header{background-color:#1f1f1f;border-bottom:solid 1px rgba(0,0,0,.1)}nav{background:#2e2e2e;background:-moz-linear-gradient(top,rgba(46,46,46,1) 0,rgba(31,31,31,1) 49%,rgba(0,0,0,1) 50%,rgba(0,0,0,1) 100%);background:-webkit-gradient(linear,left top,left bottom,color-stop(0,rgba(46,46,46,1)),color-stop(49%,rgba(31,31,31,1)),color-stop(50%,rgba(0,0,0,1)),color-stop(100%,rgba(0,0,0,1)));background:-webkit-linear-gradient(top,rgba(46,46,46,1) 0,rgba(31,31,31,1) 49%,rgba(0,0,0,1) 50%,rgba(0,0,0,1) 100%);background:-o-linear-gradient(top,rgba(46,46,46,1) 0,rgba(31,31,31,1) 49%,rgba(0,0,0,1) 50%,rgba(0,0,0,1) 100%);background:-ms-linear-gradient(top,rgba(46,46,46,1) 0,rgba(31,31,31,1) 49%,rgba(0,0,0,1) 50%,rgba(0,0,0,1) 100%);background:linear-gradient(to bottom,rgba(46,46,46,1) 0,rgba(31,31,31,1) 49%,rgba(0,0,0,1) 50%,rgba(0,0,0,1) 100%)}nav a{color:#fff}.header-icon-1,.header-icon-2{color:#fff}.menu-item em{color:#000;font-weight:500;font-size:12px;font-style:normal}.menu-item strong{color:#fd4c29;font-weight:500;font-size:14px}.header-clear{height:30px;display:block}.bg-teal-light{background-color:#1abc9c;color:#fff}.bg-teal-dark{background-color:#16a085;color:#fff}.border-teal-light{border:solid 1px #1abc9c}.border-teal-dark{border:solid 1px #16a085}.color-teal-light{color:#1abc9c}.color-teal-dark{color:#16a085}.bg-green-light{background-color:#2ecc71;color:#fff}.bg-green-dark{background-color:#2abb67;color:#fff}.border-green-light{border:solid 1px #2ecc71}.border-green-dark{border:solid 1px #2abb67}.color-green-light{color:#2ecc71}.color-green-dark{color:#2abb67}.bg-blue-light{background-color:#3498db;color:#fff}.bg-blue-dark{background-color:#2980b9;color:#fff}.border-blue-light{border:solid 1px #3498db}.border-blue-dark{border:solid 1px #2980b9}.color-blue-light{color:#3498db}.color-blue-dark{color:#2980b9}.bg-magenta-light{background-color:#9b59b6;color:#fff}.bg-magenta-dark{background-color:#8e44ad;color:#fff}.border-magenta-light{border:solid 1px #9b59b6}.border-magenta-dark{border:solid 1px #8e44ad}.color-magenta-light{color:#9b59b6}.color-magenta-dark{color:#8e44ad}.bg-night-light{background-color:#34495e;color:#fff}.bg-night-dark{background-color:#2c3e50;color:#fff}.border-night-light{border:solid 1px #34495e}.border-night-dark{border:solid 1px #2c3e50}.color-night-light{color:#34495e}.color-night-dark{color:#2c3e50}.bg-yellow-light{background-color:#e67e22;color:#fff}.bg-yellow-dark{background-color:#e86f2a;color:#fff}.border-yellow-light{border:solid 1px #e67e22}.border-yellow-dark{border:solid 1px #f27935}.color-yellow-light{color:#f1c40f}.color-yellow-dark{color:#f39c12}.bg-orange-light{background-color:#f9690e;color:#fff}.bg-orange-dark{background-color:#d35400;color:#fff}.border-orange-light{border:solid 1px #f9690e}.border-orange-dark{border:solid 1px #d35400}.color-orange-light{color:#e67e22}.color-orange-dark{color:#d35400}.bg-red-light{background-color:#e74c3c;color:#fff}.bg-red-dark{background-color:#c0392b;color:#fff}.border-red-light{border:solid 1px #e74c3c}.border-red-dark{border:solid 1px #c0392b}.color-red-light{color:#e74c3c}.color-red-dark{color:#c0392b}.bg-pink-light{background-color:#fa6a8e;color:#fff}.bg-pink-dark{background-color:#fb3365;color:#fff}.border-pink-light{border:solid 1px #fa6a8e}.border-pink-dark{border:solid 1px #fb3365}.color-pink-light{color:#fa6a8e}.color-pink-dark{color:#fb3365}.bg-gray-light{background-color:#bdc3c7;color:#fff}.bg-gray-dark{background-color:#95a5a6;color:#fff}.border-gray-light{border:solid 1px #bdc3c7}.border-gray-dark{border:solid 1px #95a5a6}.color-gray-light{color:#bdc3c7}.color-gray-dark{color:#95a5a6}.bg-white{background-color:#fff}.color-white{color:#fff}.border-white{border:solid 1px #fff}.bg-black{background-color:#000}.color-black{color:#000}.border-black{border:solid 1px #000}.color-heading{color:#676767}.facebook-bg{background-color:#3b5998;color:#fff}.linkedin-bg{background-color:#0077b5;color:#fff}.twitter-bg{background-color:#4099ff;color:#fff}.google-bg{background-color:#d34836;color:#fff}.whatsapp-bg{background-color:#34af23;color:#fff}.pinterest-bg{background-color:#c92228;color:#fff}.sms-bg{background-color:#27ae60;color:#fff}.mail-bg{background-color:#3498db;color:#fff}.dribbble-bg{background-color:#ea4c89;color:#fff}.tumblr-bg{background-color:#2c3d52;color:#fff}.reddit-bg{background-color:#369;color:#fff}.youtube-bg{background-color:#d12827;color:#fff}.phone-bg{background-color:#27ae60;color:#fff}.skype-bg{background-color:#12a5f4;color:#fff}.facebook-color{color:#3b5998}.linkedin-color{color:#0077b5}.twitter-color{color:#4099ff}.google-color{color:#d34836}.whatsapp-color{color:#34af23}.pinterest-color{color:#c92228}.sms-color{color:#27ae60}.mail-color{color:#3498db}.dribbble-color{color:#ea4c89}.tumblr-color{color:#2c3d52}.reddit-color{color:#369}.youtube-color{color:#d12827}.phone-color{color:#27ae60}.skype-color{color:#12a5f4}.bg-1{background-image:url(images/pictures/1.jpg)}.bg-2{background-image:url(images/pictures/2.jpg)}.bg-3{background-image:url(images/pictures/3.jpg)}.bg-4{background-image:url(images/pictures/4.jpg)}.bg-5{background-image:url(images/pictures/5.jpg)}.bg-6{background-image:url(images/pictures/6.jpg)}.bg-7{background-image:url(images/pictures/7.jpg)}.bg-8{background-image:url(images/pictures/8.jpg)}.bg-9{background-image:url(images/pictures/9.jpg)}.bg-body-1{background-image:url(images/pictures_vertical/bg1.jpg)}.bg-body-2{background-image:url(images/pictures_vertical/bg0.jpg)}.overlay{background-color:rgba(0,0,0,.8);position:absolute;top:0;right:0;bottom:0;left:0}h1{font-size:24px;line-height:34px;font-weight:500}h2{font-size:22px;line-height:32px;font-weight:500}h3{font-size:20px;line-height:30px;font-weight:500}h4{font-size:18px;line-height:28px;font-weight:500}h5{font-size:16px;line-height:26px;font-weight:500}h6{font-size:14px;line-height:22px;font-weight:800}.ultrathin{font-weight:200}.thin{font-weight:300}.thiner{font-weight:400}.boder{font-weight:600}.bold{font-weight:700}.ultrabold{font-weight:800}.capitalize{text-transform:capitalize}.italic{font-style:italic}.small-text{font-size:12px;display:block}.center-text{text-align:center;display:block}.right-text{text-align:right}.uppercase{text-transform:uppercase}.boxed-text{width:74%;margin:0 auto 30px auto}.round-image{border-radius:500px}p a{display:inline}.content{padding:0 20px 0 20px}.container{margin-bottom:30px}.full-bottom{margin-bottom:25px}.no-bottom{margin-bottom:0}.negative-bottom{margin-bottom:-10px}.full-top{margin-top:25px}.half-bottom{margin-bottom:15px}.half-top{margin-top:15px}.quarter-bottom{margin-bottom:15px}.hidden{display:none}.left-column{width:45%;margin-right:5%;float:left}.right-column{width:45%;margin-left:5%;float:left}.one-third-left{float:left;width:29%;margin-right:1%}.one-third-center{float:left;width:29%;margin-left:5%;margin-right:5%}.one-third-right{float:left;width:29%;margin-left:1%}.clear{clear:both}*{margin:0;padding:0;border:0;font-size:100%;vertical-align:baseline;outline:0;font-size-adjust:none;-webkit-text-size-adjust:none;-moz-text-size-adjust:none;-ms-text-size-adjust:none;-webkit-tap-highlight-color:transparent;-webkit-font-smoothing:antialiased;-webkit-transform:translate3d(1,1,1);transform:translate3d(1,1,1);text-rendering:auto;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}a,blockquote,code,div,fieldset,form,iframe,img,input,label,p,pre,textarea{display:block;position:relative}p{line-height:30px;font-weight:400;color:#666;font-size:14px;margin-bottom:30px}a{text-decoration:none;color:#3498db}.icon-list{list-style:none;font-size:14px;line-height:35px;color:#666}.icon-list i{width:30px;font-size:15px}.center-icon{width:80px;height:80px;border-radius:80px;border:solid 1px rgba(0,0,0,.5);text-align:center;line-height:80px;font-size:24px;margin:0 auto 30px auto;display:block}.decoration,.decoration-no-bottom{height:1px;background-color:rgba(0,0,0,.1)}.deco{height:1px;margin-bottom:30px}.deco-box .deco{width:10%;float:left;height:5px}.decoration{margin-bottom:30px}.decoration-margins{margin:0 20px 30px 20px}.image-border{padding:10px;border:solid 6px #fff;-webkit-box-shadow:0 0 0 1px rgba(0,0,0,.2);box-shadow:0 0 0 1px rgba(0,0,0,.2);border-radius:5px}::-webkit-scrollbar{width:0}.menu *{user-select:none;-moz-user-select:none;-webkit-user-select:none}header{top:0;height:55px;width:100%;z-index:9999;transition:all 250ms ease}.footer-logo{background-image:url(images/logo_dark.png);margin:0 auto 10px auto;background-repeat:no-repeat;background-position:center center;background-size:130px 40px;width:130px;height:40px;display:block;overflow:hidden}.header-icon-1,.header-icon-2{position:absolute;line-height:60px;text-align:center;width:60px;display:block;background-color:transparent}.header-icon-2{right:0;top:0}.header-icon-1{left:0;top:0}.header-logo{background-image:url(images/logo_light.png);margin:0 auto;background-repeat:no-repeat;background-position:center center;background-size:100px 29px;width:105px;height:60px;display:block;overflow:hidden}nav{z-index:9999999;height:60px;position:fixed;bottom:0;left:0;right:0}.copyright{margin-bottom:90px;margin-top:30px}.footer-7-icons a{width:14.285714%}.footer-6-icons a{width:16.6667%}.footer-5-icons a{width:20%}.footer-4-icons a{width:25%}.footer-3-icons a{width:33.333%}nav a{box-sizing:border-box;float:left;text-align:center;font-size:15px;height:60px;border-right:solid 1px rgba(255,255,255,.05);border-left:solid 1px rgba(0,0,0,1)}nav a i{margin-top:13px;display:block;height:23px;line-height:23px}nav a em{display:block;font-size:10px;opacity:.8;text-transform:uppercase;font-style:normal}.active-item{background-color:rgba(255,255,255,.08)}.footer{margin-bottom:80px}.icon-12{font-size:12px}.icon-13{font-size:13px}.icon-14{font-size:14px}.icon-15{font-size:15px}.icon-16{font-size:16px}.icon-17{font-size:17px}.icon-18{font-size:18px}.icon-19{font-size:19px}.icon-20{font-size:20px}.footer-socials a{width:40px;height:40px;line-height:40px;margin-left:2px;margin-right:2px;text-align:center;float:left;border-radius:5px}.footer-socials{width:265px;margin:0 auto 30px auto}.news-slider .caption{background-color:rgba(0,0,0,.8)}.caption{position:absolute;bottom:0;left:0;right:0;height:65px;padding-left:20px;padding-right:20px;background-color:rgba(0,0,0,.8)}.caption h4{font-size:14px;color:#fff;line-height:20px;margin-top:12px}.caption h3{color:#fff;margin-bottom:5px;font-size:17px;padding-top:23px;line-height:0}.caption p{font-size:12px;color:rgba(255,255,255,.5)}.call-to-action{width:245px;margin:0 auto}.call-to-action a{margin:0 5px;font-size:11px}.social-icons{width:150px;margin:0 auto}.social-round a{border-radius:50px}.social-icons-small{width:95px;margin:0 auto}.social-icons a{line-height:35px;width:35px;height:35px;margin-left:10px;margin-right:5px;float:left;font-size:12px}.social-icons-small a{line-height:35px;width:35px;height:35px;margin-left:5px;margin-right:5px;float:left}.large-link{font-size:13px;height:50px;line-height:50px;color:#000;border-bottom:solid 1px rgba(0,0,0,.1)}.large-link .fa-caret-right{font-size:10px;position:absolute;right:-25px;line-height:50px}.large-link .fa-angle-right{position:absolute;right:0;height:50px;line-height:50px;width:10px}.large-link-deco{height:1px;margin-bottom:3px;background-color:rgba(0,0,0,.1)}.large-link i:last-child{width:20px;margin-right:20px;text-align:center}.heading-box{padding:20px 20px 20px 20px;margin-bottom:30px}.heading-box h3{margin-bottom:-5px;font-size:17px;position:relative;z-index:10}.heading-box p{position:relative;z-index:10;margin-bottom:0;color:rgba(255,255,255,.4)}.heading-box i{padding:0 5px 0 5px}.heading-block{padding:30px 20px;margin-bottom:30px}.heading-block h4{font-size:20px;position:relative;z-index:10;color:#fff}.heading-block p{position:relative;z-index:10;color:rgba(255,255,255,.5);margin-bottom:0}.heading-block a{z-index:10;width:100px;height:10px;line-height:10px;color:#fff;text-align:center;font-size:12px;margin:20px auto 0 auto;border:solid 1px rgba(255,255,255,.5);border-radius:5px;display:block;text-transform:uppercase;font-weight:800}.icon-heading h4{padding-left:80px;font-size:15px}.icon-heading p{line-height:24px;padding-left:80px}.icon-heading i{border-radius:10px;position:absolute;width:70px;height:70px;line-height:70px;margin-top:5px;text-align:center;font-size:24px}.icon-heading amp-img{width:55px;height:55px;margin-left:0;margin-top:10px;float:left}.quote-style h4,.quote-style h5{font-weight:300;margin-left:25px;margin-right:25px;text-align:center;line-height:40px}.rating{width:80px;margin:20px auto 20px auto;display:block}.half-column-left .half-left-img{position:absolute;border-radius:150px;margin-left:-50px;left:0}.half-column-left{padding-left:70px;padding-right:20px;min-height:150px;overflow:hidden}.half-column-right .half-right-img{position:absolute;border-radius:150px;margin-right:-50px;right:0}.half-column-right{padding-right:70px;padding-left:20px;min-height:150px;overflow:hidden}.gallery-thumb2{width:46%;float:left;margin-bottom:3%;box-sizing:border-box}.gallery-thumb2 p{margin-bottom:10px;line-height:20px;padding-top:5px;text-align:center;font-size:13px}.gallery-round .gallery-thumb2{border-radius:100px}.gallery-thumb2:nth-child(2n-1){margin-left:4%;margin-right:4%}.gallery-thumb3{width:90%;float:left;margin-bottom:3%;box-sizing:border-box}.gallery-thumb3 p{margin-bottom:10px;line-height:20px;padding-top:5px;text-align:center;font-size:13px}.gallery-round .gallery-thumb3{border-radius:100px}.gallery-thumb3:nth-child(2n-1){margin-left:4%;margin-right:4%}.gallery-thumb{width:31%;float:left;margin-bottom:3%;box-sizing:border-box}.gallery-thumb p{margin-bottom:10px;line-height:20px;padding-top:5px;text-align:center;font-size:13px}.gallery-round .gallery-thumb{border-radius:100px}.gallery-wide .gallery-thumb-wide{margin-bottom:5px}.gallery-wide h4{position:absolute;background-color:rgba(0,0,0,.8);color:#fff;z-index:99;height:50px;line-height:50px;margin-top:-55px;width:100%;padding-left:20px;font-weight:300;font-size:14px;pointer-events:none}.gallery-thumb:nth-child(3n-1){margin-left:3%;margin-right:3%}.gallery-text{font-size:12px;color:#939393;line-height:24px}.gallery-text i{font-size:11px;padding:0 10px 0 0}.gallery-text em{padding:0 15px 0 0;font-style:normal}.splash-content .splash-logo{background-image:url(images/amp-logo.png);background-size:80px 80px;width:80px;height:80px;margin:-30px auto 20px auto}.splash-content{position:fixed;width:240px;height:350px;left:50%;top:50%;margin-top:-140px;margin-left:-120px}.splash-button{width:130px;margin:0 auto;text-align:center;height:40px;line-height:40px;font-size:12px}.landing-logo{background-image:url(images/logo_light.png);background-size:160px 47px;margin:0 auto;height:47px;width:160px;margin-top:20px}.landing-content{width:300px;margin:40px auto 30px auto}.landing-content a{width:70px;height:70px;float:left;margin:0 15px 60px 15px;border-radius:70px;line-height:70px;font-size:21px;text-align:center}.landing-content a em{position:absolute;font-size:14px;width:70px;text-align:center;bottom:-60px;left:0;right:0;font-style:normal}.body-bg{background-image:url(images/pictures_vertical/bg2.jpg);position:fixed;top:0;left:0;right:0;bottom:0}.accordion h4{background-color:transparent;border:none}.accordion h4{font-size:16px;line-height:40px}.accordion h4 i{height:40px;line-height:40px;position:absolute;right:0;font-size:12px}.nested-accordion h4{font-size:14px}section[expanded] .fa-plus{transform:rotate(45deg)}section[expanded] .fa-angle-down{transform:rotate(180deg)}section[expanded] .fa-chevron-down{transform:rotate(180deg)}.demo-icons a{color:#fff;width:20%;height:50px;float:left}.demo-icons a i{color:#1f1f1f;font-size:21px;width:50px;height:50px;float:left;text-align:center;overflow:hidden}.user-notification{text-align:left;padding-top:5px;padding-left:10px;padding-right:10px;background-color:#27ae60;height:50px;color:#fff;font-size:12px;line-height:24px;width:70%;float:left}.user-notification button{background-color:#27ae60;color:#fff;height:55px;position:fixed;right:0;bottom:60px;width:25%}.text-input{height:45px;line-height:45px;text-indent:10px;border:solid 1px rgba(0,0,0,.1);display:block;width:100%;font-size:12px}.input-icon-field{height:45px;line-height:45px;text-indent:50px;border:solid 1px rgba(0,0,0,.1);display:block;width:100%;font-size:12px}.input-icon i{position:absolute;z-index:9;height:45px;line-height:45px;text-align:center;width:45px;color:#666}.select-style{border:1px solid rgba(0,0,0,.1);width:100%;height:45px;display:block;border-radius:3px;overflow:hidden;background:#fff url(data:image/png;base64,R0lGODlhDwAUAIABAAAAAP///yH5BAEAAAEALAAAAAAPABQAAAIXjI+py+0Po5wH2HsXzmw//lHiSJZmUAAAOw==) no-repeat 95% 50%}.select-style select{font-size:12px;line-height:35px;padding:5px 15px;width:100%;border:none;box-shadow:none;background-color:rgba(255,255,255,0);background-image:none;-webkit-appearance:none}.select-style select:focus{outline:0}.dropcaps-1:first-letter{float:left;font-size:57px;padding:14px 15px 0 0;font-weight:800;color:#1f1f1f}.dropcaps-2:first-letter{font-family:'Times New Roman',sans-serif;float:left;font-size:42px;padding:15px 15px 0 0;font-weight:800;color:#1f1f1f}.dropcaps-3:first-letter{background-color:#1f1f1f;padding:10px 15px 10px 15px;margin:5px 12px 0 0;float:left;font-size:24px;font-weight:800;color:#fff}.dropcaps-4:first-letter{font-family:'Times New Roman',sans-serif;font-weight:800;background-color:#1f1f1f;padding:8px 17px 8px 17px;margin:5px 12px 0 0;float:left;font-size:20px;font-weight:400;color:#fff}.highlight{margin-bottom:10px}.highlight span{padding:3px 5px 3px 5px;margin-right:2px}ol ul{padding-left:5px}ol,ul{line-height:24px;margin-left:20px}.icon-list{list-style:none;margin-left:0;padding-left:0}.icon-list i{font-size:10px}.icon-list ul{list-style:none;padding-left:10px}.icon-list ul ul{padding-left:10px}.blockquote-1{border-left:solid 3px #1f1f1f;padding:10px 0 10px 20px}.blockquote-1 a{text-align:right;margin-top:-20px;font-size:12px}.blockquote-2 .blockquote-image{position:absolute;border-radius:50px}.blockquote-2 h5{padding-left:60px}.blockquote-2 .first-icon{padding-left:60px}.blockquote-2 a{text-align:right;margin-top:-20px;font-size:12px}.blockquote-3 .blockquote-image{width:150px;border-radius:150px;margin:0 auto;display:block}.blockquote-3 h5{margin:10px 0 10px 0}.blockquote-3 .ratings{width:100px;margin:10px auto 10px auto}.blockquote-3 .ratings i{font-size:18px}.blockquote-4 i{font-size:24px;position:absolute;margin-top:10px}.blockquote-4 p{padding-left:50px}.blockquote-5 amp-img{width:50px;height:50px;margin:0 auto}.blockquote-5 h4{font-size:24px;font-weight:200}.blockquote-5 p{font-style:italic;max-width:80%;font-weight:300;margin:0 auto;color:#747474;font-size:16px;line-height:35px}.button{display:inline-block;padding:13px 20px;margin:0 0 10px 0;font-size:12px;transition:all 250ms ease}.button-3d{display:inline-block;padding:15px 20px;margin:0 0 10px 0;font-size:12px;transition:all 250ms ease;border-right:none;border-left:none;border-top:none;border-width:3px}.button-round{border-radius:30px}.button-full{display:block;text-align:center}.button-center{width:140px;margin-left:auto;margin-right:auto;display:block;text-align:center;margin-bottom:30px}.button:hover{opacity:.9;transition:all 250ms ease}.icon-round,.icon-square{width:40px;height:40px;line-height:40px;text-align:center;display:inline-block;margin-left:6px;margin-right:6px;margin-bottom:10px;font-size:14px}.icon-round:hover,.icon-square:hover{opacity:.9}.icon-round{border-radius:45px}.page-404 h1{font-size:60px;line-height:70px;margin-top:70px}.page-404 a{margin-bottom:100px}.page-soon h1{font-size:60px;line-height:70px;margin-top:70px}.page-soon h6{font-size:24px}.page-soon .social-icons{margin-bottom:100px}.profile-gradient{background:-moz-linear-gradient(top,rgba(255,255,255,0) 0,rgba(255,255,255,.95) 75%,rgba(255,255,255,1) 100%);background:-webkit-linear-gradient(top,rgba(255,255,255,0) 0,rgba(255,255,255,.95) 75%,rgba(255,255,255,1) 100%);background:linear-gradient(to bottom,rgba(255,255,255,0) 0,rgba(255,255,255,.95) 75%,rgba(255,255,255,1) 100%);height:250px;margin-top:-235px}.profile-overlay .profile-header{margin-top:-80px}.profile-header h1{font-size:30px}.profile-header h6{letter-spacing:2px;opacity:.5}.profile-header h5{font-size:12px}.profile-header i{margin-right:10px}.profile-header p{font-size:18px}.profile-followers a{float:left;width:33%;color:#1f1f1f;font-size:18px}.profile-followers em{display:block;font-style:normal;font-size:12px}.profile-thumb{margin-top:-50px;width:100px;margin-left:auto;margin-right:auto;display:block;border-radius:100px;border-radius:100px;border:solid 3px #fff}.timeline-1{overflow:hidden;padding:20px}.timeline-1 .timeline-deco{position:absolute;top:0;left:50%;width:1px;bottom:0;background-color:rgba(0,0,0,.15)}.timeline-1 .timeline-icon{width:60px;height:60px;border-radius:60px;line-height:60px;text-align:center;font-size:18px;background-color:#fff;border:solid 1px rgba(0,0,0,.2);margin:0 auto 30px auto}.timeline-1 .container{background-color:#fff;padding:30px 0 1px 0}.timeline-2{overflow:hidden;padding:50px 20px 0 20px}.timeline-2 .timeline-deco{position:absolute;top:0;left:50px;width:1px;bottom:0;background-color:rgba(0,0,0,.15)}.timeline-2 .timeline-icon{width:40px;height:40px;border-radius:40px;line-height:40px;text-align:center;font-size:18px;background-color:#fff;border:solid 1px rgba(0,0,0,.2);margin-left:10px}.timeline-2 .container{background-color:#fff;margin-left:70px;margin-top:-60px;padding-bottom:30px}.news-slider .amp-carousel-button{display:none}.news-slider{margin-bottom:10px}.news-thumbs .news-item{min-height:125px;color:#1f1f1f}.news-thumbs .news-item .responsive-img{width:95px;position:absolute;margin-top:5px}.news-thumbs .news-item h5{margin-left:110px;font-size:15px}.news-thumbs .news-item p{margin-left:110px;line-height:27px;margin-bottom:0;font-size:13px}.news-strip{background-color:#000;padding:20px 0 20px 0;margin-bottom:30px}.news-strip h5{font-weight:800;color:#fff;padding:0 20px 20px 20px}.news-category{margin:0 20px 0 20px}.news-category p{display:inline-block;padding:5px 25px 0 25px;font-size:13px;margin:0}.news-category div{height:5px;width:100%}.news-blocks .news-item{min-height:125px;color:#1f1f1f}.news-blocks .news-item h5{font-size:18px;padding:15px 0 5px 0}.news-full .news-item{margin-top:1px}.news-full .news-item h6{position:absolute;background-color:rgba(0,0,0,.8);bottom:0;width:100%;color:#fff;padding:10px 10px 10px 10px}.news-full .titles{position:absolute;background-color:#fff;width:250px;height:65px;margin-top:-65px}.news-full h5{font-size:13px;padding:10px 20px 0 20px;color:#000}.news-full em a{display:inline}.news-full em{font-size:10px;padding-left:20px;display:block}.news-full p{padding:10px 20px 0 20px}.news-full .read-more{padding-right:20px;text-align:right;font-size:12px;padding-bottom:30px}.news-post-info{font-style:normal;font-size:12px;padding:5px 0 15px 0;display:block}.news-post-info a{display:inline}.contactField{font-family:Roboto,sans-serif;height:40px;line-height:40px;line-height:100%;width:100%;display:block;border:solid 1px rgba(0,0,0,.1);text-indent:10px;font-size:13px;transition:all 250ms ease;margin-bottom:20px}.contactField:focus{border:solid 1px #27ae60;transition:all 250ms ease}.contactTextarea{font-family:Roboto,sans-serif;padding-top:10px;min-height:80px;line-height:40px;line-height:100%;width:100%;display:block;border:solid 1px rgba(0,0,0,.1);text-indent:10px;font-size:13px;transition:all 250ms ease;margin-bottom:30px}.contactTextarea:focus{transition:all 250ms ease;border:solid 1px #27ae60}.field-title{font-size:13px;margin-bottom:5px}.field-title span{font-size:10px;color:#cacaca;position:absolute;right:0;margin-top:2px}.buttonWrap{width:100%;display:block;text-align:center;margin-bottom:30px;appearance:none;-webkit-appearance:none}.contact-icon{color:#666;line-height:30px}.contact-icon i{color:#1f1f1f;width:30px}.cover-clear{height:40px}.cover-1{padding:30px 40px 0 40px;border-top:solid 1px rgba(255,255,255,.1)}.cover-1,.cover-2,.cover-4,.cover-5{border-top:solid 1px rgba(255,255,255,.1)}.cover-1 h1{font-size:30px;color:#fff}.cover-1 h6{font-size:16px;color:#fff;padding:10px 0 30px 0;font-weight:300}.cover-1 p{font-size:15px;font-weight:300;color:#a7a7a7;line-height:35px;margin-bottom:50px}.cover-1 .button{border:solid 1px rgba(255,255,255,.2);color:#fff;margin-right:10px}.cover-2{padding-top:0}.cover-2 h1{color:#fff;font-size:30px;font-weight:300;text-align:center;padding-top:30px}.cover-2 h6{color:#c1c1c1;font-style:italic;font-size:13px;font-weight:300;text-align:center;padding:0 0 20px 0}.cover-2 p{font-size:15px;text-align:center;line-height:36px;color:#c1c1c1;padding:30px 35px 40px 35px;font-weight:300}.cover-3{padding:20px 20px 20px 20px;background-color:#fff;margin:20px;border-radius:7px}.cover-3 amp-img{margin:0 auto;display:block}.cover-3 h1{font-size:24px;text-align:center;padding:20px 0 0 0}.cover-3 em{font-size:12px;display:block;text-align:center;margin-bottom:20px}.cover-3 p{text-align:center;font-size:16px;font-weight:300;padding:0 5px 0 5px}.cover-3 .socials{transform:scale(.8,.8);width:210px;margin:0 auto}.cover-3 .socials a{margin-left:4px;margin-right:4px}.cover-4 h1{color:#fff;text-align:center;font-weight:300;font-size:36px;margin:30px 0 10px 0}.cover-4 em{color:#fff;text-align:center;font-style:normal;display:block;font-size:12px;text-transform:uppercase;letter-spacing:1px;font-weight:300}.cover-4 strong{color:#969696;text-transform:uppercase;font-weight:800;font-size:12px;text-align:center;display:block;margin:20px 0 30px 0}.cover-4 strong i{padding-right:10px}.cover-4 p{padding:40px 30px 40px 30px;color:#fff;line-height:36px;text-align:center;font-weight:300;font-size:16px}.cover-4 a{font-size:13px;width:170px;margin:0 auto}.cover-5 h1{color:#fff;font-size:40px;font-weight:300;text-align:center;padding-top:30px}.cover-5 em{display:block;text-align:center;color:#fff;font-size:12px;margin-top:10px;font-style:normal}.cover-5 p{padding:20px 30px 20px 30px;color:#a7a7a7;font-weight:300;text-align:center;font-size:16px;line-height:40px}.cover-socials a{width:35px;height:35px;border-radius:35px;float:left;text-align:center;line-height:35px;font-size:12px;margin:0 5px 0 5px}.cover-socials{width:230px;margin:40px auto 30px auto}.container{max-width:400px;width:100%;margin:0 auto;position:relative}#contact button[type=submit],#contact input[type=email],#contact input[type=tel],#contact input[type=text],#contact input[type=url],#contact textarea{font:400 12px/16px Roboto,Helvetica,Arial,sans-serif}#contact{background:#f9f9f9;padding:25px;margin:10px 0;box-shadow:0 0 20px 0 rgba(0,0,0,.2),0 5px 5px 0 rgba(0,0,0,.24)}#contact h3{display:block;font-size:20px;font-weight:300;margin-bottom:10px}#contact h4{margin:5px 0 15px;display:block;font-size:13px;font-weight:400}fieldset{border:medium none!important;margin:0 0 7px;min-width:100%;padding:0;width:100%}#contact input[type=email],#contact input[type=tel],#contact input[type=text],#contact input[type=url],#contact textarea{width:95%;border:1px solid #ccc;background:#fff;margin-bottom:20px;padding:10px}#contact input[type=email]:hover,#contact input[type=tel]:hover,#contact input[type=text]:hover,#contact input[type=url]:hover,#contact textarea:hover{-webkit-transition:border-color .3s ease-in-out;-moz-transition:border-color .3s ease-in-out;transition:border-color .3s ease-in-out;border:1px solid #aaa}#contact textarea{height:100px;max-width:100%;resize:none}#contact button[type=submit]{cursor:pointer;width:100%;border:none;background:#4caf50;color:#fff;margin:0 0 5px;padding:10px;font-size:15px}#contact button[type=submit]:hover{background:#43a047;-webkit-transition:background .3s ease-in-out;-moz-transition:background .3s ease-in-out;transition:background-color .3s ease-in-out}#contact button[type=submit]:active{box-shadow:inset 0 1px 3px rgba(0,0,0,.5)}.copyright{text-align:center}#contact input:focus,#contact textarea:focus{outline:0;border:1px solid #aaa}::-webkit-input-placeholder{color:#888}:-moz-placeholder{color:#888}::-moz-placeholder{color:#888}:-ms-input-placeholder{color:#888}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}*{margin:0;padding:0;box-sizing:border-box}body{width:100%;height:100vh;display:flex;position:relative;background:#eeeeec;align-items:center;flex-direction:column;justify-content:center;font-family:'Open Sans',sans-serif;font-size:100%}.customSlider{opacity:0;display:none}.customSlider+label{position:relative;top:35px;left:15px;cursor:pointer}.customSlider+label:after,.customSlider+label:before{background:#fff;position:absolute;content:''}.customSlider+label:before{top:-90px;left:-60px;width:80px;height:40px;background:#ccc;border-radius:20px;transition:background .75s}.customSlider+label:after{height:34px;width:34px;top:-87px;left:-55px;border-radius:50%;transition:all .5s}.customSlider:checked+label:after{height:34px;width:34px;top:-87px;left:-19px;border-radius:50%}.customSlider:checked~.wrapper{color:#fff;transition:background .6s ease;background:#31394d}.customSlider:checked~.wrapper .top-icons i{color:#fff}.customSlider:checked~.wrapper .profile .name{color:#fdfeff}.customSlider:checked~.wrapper .profile .title{color:#7c8097}.customSlider:checked~.wrapper .profile .description{color:#fdfeff}.customSlider:checked~.wrapper .icon h4{color:#fff}.customSlider:checked~.wrapper .icon p{color:#666b7d}.wrapper{width:320px;height:540px;background:#fdfeff;transition:background .6s ease;border-radius:10px;padding:5px 5px 5px 5px;box-shadow:0 8px 40px rgba(0,0,0,.2)}.wrapper .top-icons i{color:#080911}.wrapper .top-icons i:nth-of-type(1){float:left}.wrapper .top-icons i:nth-of-type(2){float:right}.wrapper .top-icons i:nth-of-type(3){float:right;padding-right:.8em}.wrapper .profile{margin-top:.1em;position:relative}.wrapper .profile .check{position:absolute;right:5em;bottom:12.7em}.wrapper .profile .check i{color:#fff;width:20px;height:20px;font-size:12px;line-height:20px;text-align:center;border-radius:100%;background:linear-gradient(to bottom right,#c90a6d,#ff48a0)}.wrapper .profile .thumbnail{width:124px;height:124px;display:flex;margin-left:auto;margin-right:auto;margin-bottom:1.5em;border-radius:100%;box-shadow:0 13px 26px rgba(0,0,0,.2),0 3px 6px rgba(0,0,0,.2)}.wrapper .profile .name{color:#2d354a;font-size:24px;font-weight:600;text-align:center}.wrapper .profile .title{color:#7c8097;font-size:.75em;font-weight:300;text-align:center;padding-top:.5em;padding-bottom:.7em;letter-spacing:1.5px;text-transform:uppercase}.wrapper .profile .description{color:#080911;font-size:14px;font-weight:300;text-align:center;margin-bottom:1.3em}.wrapper .profile .btn{color:#fff;width:130px;height:42px;outline:0;border:none;display:block;cursor:pointer;font-weight:300;margin-left:auto;margin-right:auto;border-radius:70px;box-shadow:0 13px 26px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.16);background:linear-gradient(to bottom right,#c90a6d,#ff48a0)}.wrapper .social-icons{display:flex;margin-top:1.2em;justify-content:space-between}.wrapper .social-icons .icon{display:flex;align-items:center;flex-direction:column}.wrapper .social-icons .icon a{color:#fff;width:60px;height:60px;font-size:28px;line-height:60px;text-align:center;border-radius:30px;box-shadow:0 13px 26px rgba(0,0,0,.2),0 3px 6px rgba(0,0,0,.2)}.wrapper .social-icons .icon:nth-of-type(1) a{background:linear-gradient(to bottom right,#c90a6d,#ff48a0)}.wrapper .social-icons .icon:nth-of-type(2) a{background:linear-gradient(to bottom right,#5e5aec,#3f9efc)}.wrapper .social-icons .icon:nth-of-type(3) a{background:linear-gradient(to bottom right,#6452e9,#639ff9)}.wrapper .social-icons .icon h4{color:#080911;font-size:1em;margin-top:1.3em;margin-bottom:.2em}.wrapper .social-icons .icon p{color:#666b7d;font-size:12px}.concept{position:absolute;bottom:25px;color:#aab0c4;font-size:.9em;font-weight:400}.concept a{color:#ac1966;text-decoration:none}.center{display:block;margin-left:auto;margin-right:auto;width:50%}.kontak-button{margin-top:20px}.sk-folding-cube{position:absolute;top:40px;left:0;margin:20px auto;width:40px;height:40px;position:relative;-webkit-transform:rotateZ(45deg);transform:rotateZ(45deg)}.sk-folding-cube .sk-cube{float:left;width:50%;height:50%;position:relative;-webkit-transform:scale(1.1);-ms-transform:scale(1.1);transform:scale(1.1)}.sk-folding-cube .sk-cube:before{content:'';position:absolute;top:0;left:0;width:100%;height:100%;background-color:#0069cb;-webkit-animation:sk-foldCubeAngle 2.4s infinite linear both;animation:sk-foldCubeAngle 2.4s infinite linear both;-webkit-transform-origin:100% 100%;-ms-transform-origin:100% 100%;transform-origin:100% 100%}.sk-folding-cube .sk-cube2{-webkit-transform:scale(1.1) rotateZ(90deg);transform:scale(1.1) rotateZ(90deg)}.sk-folding-cube .sk-cube3{-webkit-transform:scale(1.1) rotateZ(180deg);transform:scale(1.1) rotateZ(180deg)}.sk-folding-cube .sk-cube4{-webkit-transform:scale(1.1) rotateZ(270deg);transform:scale(1.1) rotateZ(270deg)}.sk-folding-cube .sk-cube2:before{-webkit-animation-delay:.3s;animation-delay:.3s}.sk-folding-cube .sk-cube3:before{-webkit-animation-delay:.6s;animation-delay:.6s}.sk-folding-cube .sk-cube4:before{-webkit-animation-delay:.9s;animation-delay:.9s}@-webkit-keyframes sk-foldCubeAngle{0%,10%{-webkit-transform:perspective(140px) rotateX(-180deg);transform:perspective(140px) rotateX(-180deg);opacity:0}25%,75%{-webkit-transform:perspective(140px) rotateX(0);transform:perspective(140px) rotateX(0);opacity:1}100%,90%{-webkit-transform:perspective(140px) rotateY(180deg);transform:perspective(140px) rotateY(180deg);opacity:0}}@keyframes sk-foldCubeAngle{0%,10%{-webkit-transform:perspective(140px) rotateX(-180deg);transform:perspective(140px) rotateX(-180deg);opacity:0}25%,75%{-webkit-transform:perspective(140px) rotateX(0);transform:perspective(140px) rotateX(0);opacity:1}100%,90%{-webkit-transform:perspective(140px) rotateY(180deg);transform:perspective(140px) rotateY(180deg);opacity:0}}
  </style>
  <noscript>
    <style amp-boilerplate>
      body {
        -webkit-animation: none;
        -moz-animation: none;
        -ms-animation: none;
        animation: none
      }
    </style>
  </noscript>
  <title>Contact US</title>
</head>

<body>
  <div class="page-content" style="margin-top:20px;">
    <div class="wrapper">
      <div class="sk-folding-cube">
        <div class="sk-cube1 sk-cube"></div>
        <div class="sk-cube2 sk-cube"></div>
        <div class="sk-cube4 sk-cube"></div>
        <div class="sk-cube3 sk-cube"></div>
      </div>

      <div class="profile" style="margin-top: 100px;">
        <div>

          <h3 class="name" style="font-size: 16px;"><?=$cs?></h3>
          <a href="<?=$wa_url?>?phone=<?=$cs?>&text=<?=$msg?>">
            <img src="https://gass.co.id/img/button-wa.jpg?i=1" class="center kontak-button" width="170" height="38">
          </a>
          <div style="text-align: center; margin-top: 40px;">

            <p style="margin:10px;">
              Anda akan segera terhubung dengan customer service kami <br /> Atau klik tombol di atas untuk menghubungi customer service kami
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script>
    function isInstagramInAppBrowserOnIOS() {
      const ua = navigator.userAgent || navigator.vendor || window.opera;

      // Check for Instagram on iOS (iPhone or iPad)
      const isInstagram = ua.includes('Instagram');
      const isIOS = /iPhone|iPad|iPod/i.test(ua); // Detect iOS devices

      // Return true if it's both Instagram and iOS
      return isInstagram && isIOS;
    }
    function ready(callback) {
      // in case the document is already rendered
      if (document.readyState != 'loading') callback();
      // modern browsers
      else if (document.addEventListener) document.addEventListener('DOMContentLoaded', callback);
      // IE <= 8
      else document.attachEvent('onreadystatechange', function() {
        if (document.readyState == 'complete') callback();
      });
    }
    ready(function() {

      sleep(2000).then(() => {
        setTimeout(function() {
          //var a = document.getElementsByTagName("a");
          //            for(var i=0; i<a.length; i++) {
          //                a[i].onclick= function() {window.open('<?=$wa_url?>?phone=<?=$phone?>&text=<?=$msg?>', "_self")}
          //                a[i].click();
          //            }
          //window.open('<?=$wa_url?>?phone=<?=$phone?>&text=<?=$msg?>', "_self");
          //window.location.replace("<?=$wa_url?>?phone=<?=$phone?>&text=<?=$msg?>");
          //window.location.replace("<?=$wa_url?>?phone=<?=$phone?>&text=<?=$msg?>");
          if(isInstagramInAppBrowserOnIOS()){
              window.location.replace("<?=$wa_url?>?phone=<?=$phone?>&text=<?=$msg?>");
          }else{
              window.location.replace("<?=$wa_url?>?phone=<?=$phone?>&text=<?=$msg?>);
          }  
        }, 500); // timeout
      });
    });
  </script>
</body>

</html>
<?php  
 }else{
?>
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contact Us</title>
  <style>
*,::after,::before{box-sizing:border-box}body{font-family:Roboto,sans-serif;background-color:rgba(0,0,0,.5);margin:0;padding:0;display:flex;justify-content:center;align-items:center;height:100vh;position:relative}.form-container{background:#fff;border-radius:8px;box-shadow:0 0 20px rgba(0,0,0,.2);padding:30px;width:100%;max-width:400px;position:relative;z-index:10}.form-field input,.submit-button{padding:10px;width:100%;text-align:center}h3{text-align:center;margin-bottom:20px;color:#333}.form-field{margin-bottom:15px}.form-field label{display:block;margin-bottom:5px;font-weight:500;color:#555}.form-field input{border:1px solid #ccc;border-radius:4px;font-size:14px;transition:border-color .3s;margin:0}.form-field input:focus{border-color:#27ae60;outline:0}.submit-button{background-color:#4caf50;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:16px;transition:background-color .3s}.submit-button:hover{background-color:#43a047}
  </style>
</head>

<body>
  <div class="form-container">
    <div id="divContactForm">
        <h3>Contact Us</h3>
        <form id="ContactForm" name="ContactForm" action="https://<?=$_SERVER['HTTP_HOST']?>/api.html" method="POST">
            <input type="hidden" name="act" value="v_form">
            <input type="hidden" name="visitor_id" value="<?=$visitor_id?>">
            <input type="hidden" name="project_key" value="<?=$_GET["p"]?>">
            <input type="hidden" name="divisi" value="<?=$_GET["d"]?>">
          <div class="form-field">
            <label for="name">Name</label>
            <input type="text" id="name" name="firstname" required placeholder="Enter your name">
          </div>
          <div class="form-field">
            <label for="phone">Phone Number</label>
            <input type="tel" id="phone" name="phone" required placeholder="Enter your phone number">
          </div>
          <button type="submit" class="submit-button">Submit</button>
        </form>
    </div>
    <div id="divMessage">
    
    </div>
  </div>
<script> 
function showAlertForm(e,t){const o=document.createElement("div");o.style.position="fixed",o.style.top="0",o.style.left="0",o.style.width="100%",o.style.height="100%",o.style.backgroundColor="rgba(0, 0, 0, 0.5)",o.style.display="flex",o.style.alignItems="center",o.style.justifyContent="center",o.style.zIndex="99";const n=document.createElement("div");n.style.width="300px",n.style.padding="20px",n.style.backgroundColor="#fff",n.style.borderRadius="5px",n.style.boxShadow="0 4px 8px rgba(0, 0, 0, 0.2)",n.style.textAlign="center",n.style.position="relative";const s=document.createElement("div");s.innerHTML=e,s.style.margin="0",s.style.fontSize="1rem",s.style.color="#333",s.style.userSelect="text";const d=document.createElement("button");d.innerText="Close",d.style.marginTop="20px",d.style.padding="10px 20px",d.style.fontSize="1rem",d.style.color="#fff",d.style.backgroundColor="#c36",d.style.border="none",d.style.borderRadius="3px",d.style.cursor="pointer",d.onclick=function(){if(document.body.removeChild(o),t){var e=document.getElementById("popupForm");e&&(e.style.display="flex")}},n.appendChild(s),n.appendChild(d),o.appendChild(n),document.body.appendChild(o)}function request_post(e,t,o){var n=new XMLHttpRequest;n.open("POST",e,!0),n.onreadystatechange=function(){if(n.readyState===XMLHttpRequest.DONE)if(n.status>=200&&n.status<400){var e=n.responseText;if(e)try{o(JSON.parse(e))}catch(t){o(e)}}else{o({code:0,msg:"Request failed"})}},n.onerror=function(){o({code:0,msg:"Request Error"})},n.send(t)}function setCookie(e,t,o){let n=new Date;n.setTime(n.getTime()+24*o*60*60*1e3);const s="expires="+n.toUTCString();document.cookie=e+"="+t+"; "+s+"; path=/"}function getCookie(e){const t=document.cookie.split(";");for(let o of t){const[t,n]=o.trim().split("=");if(t===e)return decodeURIComponent(n)}return null}function base64_encode(e){const t=(new TextEncoder).encode(e);return btoa(String.fromCharCode(...t))}function base64_decode(e){const t=atob(e);return(new TextDecoder).decode(Uint8Array.from(t,(e=>e.charCodeAt(0))))}document.addEventListener("DOMContentLoaded",(function(){const e=getCookie("submit_form");null!=e&&(document.getElementById("divContactForm").style.display="none",document.getElementById("divMessage").style.display="block",document.getElementById("divMessage").innerHTML=base64_decode(e))}));
    document.getElementById('ContactForm').addEventListener('submit', function(event) { 
        event.preventDefault(); 
        const formData = new FormData(event.target); 
        request_post('https://<?=$_SERVER['HTTP_HOST']?>/api.html?act=v_form', formData, function(response){
            if(response.code==1){
                setCookie("submit_form", base64_encode(response.result.msg), 1);
                document.getElementById('divContactForm').style.display = "none";
                document.getElementById('divMessage').style.display = "block";
                document.getElementById('divMessage').innerHTML = response.result.msg;
            }else{  
                showAlertForm(response.result.msg, 1);
            }                        
        });
    });
    
</script>
</body>
</html>

<?php
}
ob_end_flush();
?>