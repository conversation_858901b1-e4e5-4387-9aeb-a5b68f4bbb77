<?php

namespace DynamicWebhook\TriggerProcessor;

use DynamicWebhook\Abstracts\AbstractTriggerProcessor;

/**
 * Purchase trigger processor
 */
class PurchaseTriggerProcessor extends AbstractTriggerProcessor
{
    private $normalizer;

    public function __construct($metaService, $dbService, $normalizer = null)
    {
        parent::__construct($metaService, $dbService);
        $this->normalizer = $normalizer;
    }

    /**
     * Get trigger type name
     */
    public function getTriggerType(): string
    {
        return 'purchase';
    }

    /**
     * Check if trigger should be processed for given message type
     */
    public function shouldProcess(string $messageType): bool
    {
        // Purchase trigger only processes outgoing messages
        return $messageType === 'message_out';
    }

    /**
     * Process specific trigger logic
     */
    protected function processSpecificTrigger(array $data, array $context): bool
    {
        $message = $data['message'] ?? '';
        if (!$message) {
            return false;
        }

        $metaResult = $this->getMeta("format_purchase");
        $metaResult2 = $this->getMeta("format_purchase_value");
        
        if ($metaResult["code"] != 1 || $metaResult2["code"] != 1) {
            return false;
        }

        // Check purchase format
        $formatPurchase = $this->cleanString($metaResult["result"]["data"]);
        $formatPurchase = preg_quote($formatPurchase, '/');
        $strregex = str_replace('%ID%', '(\d+)', $formatPurchase);
        $regex = '/' . $strregex . '/';
        $result = preg_match($regex, $message, $matches);
        
        if ($result === false || $result == 0) {
            return false;
        }

        // Check value format
        $formatValuePurchase = $this->cleanString($metaResult2["result"]["data"]);
        $formatValuePurchase = preg_quote($formatValuePurchase, '/');
        $strregex = str_replace('%VALUE%', '([0-9,.]*)', $formatValuePurchase);
        $regex = '/' . $strregex . '/';
        $result2 = preg_match($regex, $message, $matches);
        
        if ($result2 === false || $result2 == 0) {
            return false;
        }

        // Extract value and additional purchase data
        $value = preg_replace('/[.,]/', '', $matches[1]);
        $context['purchase_value'] = $value;
        $context['purchase_data'] = $this->extractPurchaseData($message);
        
        return true;
    }

    /**
     * Extract additional purchase data
     */
    private function extractPurchaseData(string $message): array
    {
        $orderData = [];
        
        // Extract nama
        $formatNama = $this->getMeta("format_purchase_nama")["result"]["data"] ?? "";
        if ($formatNama != "" && $this->normalizer) {
            $nama = $this->normalizer->extractTextWithFormat($message, $formatNama, '%NAMA%');
            if ($nama) {
                $orderData['nama'] = $nama;
            }
        }
        
        // Extract kota
        $formatKota = $this->getMeta("format_purchase_kota")["result"]["data"] ?? "";
        if ($formatKota != "" && $this->normalizer) {
            $kota = $this->normalizer->extractTextWithFormat($message, $formatKota, '%KOTA%');
            if ($kota) {
                $orderData['kota'] = $kota;
            }
        }
        
        // Extract alamat
        $formatAlamat = $this->getMeta("format_purchase_alamat")["result"]["data"] ?? "";
        if ($formatAlamat != "" && $this->normalizer) {
            $alamat = $this->normalizer->extractTextWithFormat($message, $formatAlamat, '%ALAMAT%');
            if ($alamat) {
                $orderData['alamat'] = $alamat;
            }
        }
        
        // Extract provinsi
        $formatProv = $this->getMeta("format_purchase_provinsi")["result"]["data"] ?? "";
        if ($formatProv != "" && $this->normalizer) {
            $provinsi = $this->normalizer->extractTextWithFormat($message, $formatProv, '%PROVINSI%');
            if ($provinsi) {
                $orderData['provinsi'] = $provinsi;
            }
        }
        
        // Extract items
        $orderData['items'] = $this->extractItems($message);
        
        return $orderData;
    }

    /**
     * Extract items from message
     */
    private function extractItems(string $message): array
    {
        $items = [];
        $formatQty = $this->getMeta("format_purchase_qty_sku")["result"]["data"] ?? "";
        
        if ($formatQty != "" && $this->normalizer) {
            $formatQty = $this->normalizer->sanitizeForRegex($formatQty);
            $strregex = str_replace('%QTY%', '(.+)', $formatQty);
            $strregex = str_replace('%SKU%', '(.+)', $strregex);
            $regex = '/' . $strregex . '/';
            
            $messageLines = explode("\n", $message);
            foreach ($messageLines as $line) {
                $result = preg_match($regex, $line, $matches);
                if ($result !== false && $result > 0) {
                    $items[] = [
                        'qty' => trim($matches[1]),
                        'sku' => trim($matches[2])
                    ];
                }
            }
        }
        
        return $items;
    }
}
