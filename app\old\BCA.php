<?php
/****
CLASS Bank BCA
********: ada field baru dalam form login bca
<input type="hidden" name="value(CurNum)" value="4rlc6l9YOHqRiBjSXvX04gMvbP29g32f"> <== ka<PERSON><PERSON>ya bener2 random
<input type="hidden" name="value(user_ip)" value="ip_address_yg_konek_ke_bca">
<input type="hidden" name="value(browser_info)" value="Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.135 Safari/537.36">
***/
define('BCA_PARSER_DEBUG', false);

class BCA{
	private $username;
	private $password;
	private $rekening;
	public $isLoggedIn = false;
	var $login_url = "https://ibank.klikbca.com";
	var $login_process_url="https://ibank.klikbca.com/authentication.do";
	var $login_success="authentication.do?value(actions)=welcome";
	var $menu_url="https://ibank.klikbca.com/nav_bar_indo/menu_bar.htm";
	var $info_rekening="https://ibank.klikbca.com/nav_bar_indo/account_information_menu.htm";
	var $mutasi_form_url="https://ibank.klikbca.com/accountstmt.do?value(actions)=acct_stmt";
	var $mutasi_url = "https://ibank.klikbca.com/accountstmt.do?value(actions)=acctstmtview";
	var $logout_url = "https://ibank.klikbca.com/authentication.do?value(actions)=logout";
	var $ch;
	var $dom;
	
	public function __construct($username, $password, $rekening){
		$this->dom = new DOMDocument();
		if( BCA_PARSER_DEBUG == true ) error_reporting(E_ALL);
		$this->username = $username;
		$this->password = $password;
		$this->rekening = $rekening;
		if($this->login()){
			$this->isLoggedIn = true;
		}
	}
	
	private function openCurl(){
		$this->ch = curl_init();
		
		curl_setopt($this->ch, CURLOPT_FOLLOWLOCATION, 1);
		curl_setopt($this->ch, CURLOPT_RETURNTRANSFER,1);
		curl_setopt($this->ch, CURLOPT_BINARYTRANSFER,1);
		curl_setopt($this->ch, CURLOPT_USERAGENT,"Mozilla/5.0 (Windows; U; Windows NT 5.1; en-GB; rv:*******) Gecko/******** Firefox/*******");
		curl_setopt($this->ch, CURLOPT_COOKIEJAR, '.cookieBCA-'.$this->username);
		curl_setopt($this->ch, CURLOPT_COOKIEFILE, '.cookieBCA-'.$this->username);
		curl_setopt($this->ch, CURLOPT_CAINFO, "cert/cacert.pem");
		curl_setopt($this->ch, CURLOPT_SSL_VERIFYPEER, 0);
		curl_setopt($this->ch, CURLOPT_SSL_VERIFYHOST, 0);
	}

	private function closeCurl(){
		curl_close($this->ch);
	}

	private function browse($url,$post=false,$follow=false,$reffer=false){
		$this->openCurl();
		curl_setopt($this->ch, CURLOPT_URL, $url);

		if($post){
			curl_setopt($this->ch, CURLOPT_POST, 1 );
			curl_setopt($this->ch, CURLOPT_POSTFIELDS, $post);
		}

		if($follow){
			curl_setopt($this->ch, CURLOPT_FOLLOWLOCATION, 1);
		}

		if($reffer){
			curl_setopt($this->ch, CURLOPT_REFERER,$reffer);
		}

		$result = array("data"=>curl_exec($this->ch),"info"=>curl_getinfo($this->ch));
		$result['headers'] = substr($result['data'], 0, $result['info']['header_size']);

		if( BCA_PARSER_DEBUG == true ) {
			echo $result['data'];
		}
		$this->closeCurl();
		return $result;
	}

	function login(){
/*
		$this->browse($this->login_url);
		$params = 'value%28actions%29=login&value%28user_id%29=' . $this->userid . '&value%28user_ip%29=ip.server&value%28pswd%29=' . $this->password . '&value%28Submit%29=LOGIN';
*/
		$result = $this->browse($this->login_url);
        $str = $result['data'];
        $str = preg_replace("/\n+/","",$str);
        $str = preg_replace("/<br>/"," ",$str);
        $str = preg_replace("/\s+/"," ",$str);
        $str = preg_replace("/,/","",$str);
		/* 20160606: lenyap lagi
		preg_match('/<input type="hidden" name="value\(CurNum\)" value="(.*?)">/si', $str, $match);
        $CurNum = $match[1];
		*/
        preg_match('/<input type="hidden" name="value\(user_ip\)" value="(.*?)">/si', $str, $match);
        $user_ip = $match[1];
        $params = array();
        $params[] = 'value%28actions%29=login';
        $params[] = 'value%28user_id%29=' . $this->username;
        $params[] = 'value%28pswd%29=' . $this->password;
        $params[] = 'value%28user_ip%29=' . $user_ip;
        //$params[] = 'value%28CurNum%29=' . $CurNum;
		$params[] = 'value%28browser_info%29=Mozilla/5.0+(Windows;+U;+Windows+NT****;+en-GB;+rv:*******)+Gecko/********+Firefox/*******';
        $params[] = 'value%28mobile%29=false';
        $params[] = 'value%28Submit%29=LOGIN';
        $params = implode( '&', $params );

		$result = $this->browse($this->login_process_url,$params,false,$this->login_url);
		return $isLogin = strpos($result['data'],$this->login_success);
		/*
		if(strpos($result['data'],$this->login_success))
			return false;
		return $result['data'];
		*/
	}

	public function getListTransaksi($from, $to)
	{
		$result = $this->getMutasiRekening($from, $to);
		$result = $this->getArrayValues($result);
		return $result;
	}
	
	function getMutasiRekening($dari,$sampai){
		$page = $this->browse($this->menu_url,false,false,$this->login_process_url);
		$page = $this->browse($this->info_rekening,false,false,$this->login_process_url);
		$page = $this->browse($this->mutasi_form_url,false,false,$this->info_rekening);

		$params = array();
		$t1 = explode( '-', $sampai );
		$t0 = explode( '-', $dari );

		$params[] = 'value%28startDt%29=' . $t0[2];
		$params[] = 'value%28startMt%29=' . $t0[1];
		$params[] = 'value%28startYr%29=' . $t0[0];
		$params[] = 'value%28endDt%29=' . $t1[2];
		$params[] = 'value%28endMt%29=' . $t1[1];
		$params[] = 'value%28endYr%29=' . $t1[0];
		$params[] = 'value%28D1%29=0';
		$params[] = 'value%28r1%29=1';
		$params[] = 'value%28fDt%29=';
		$params[] = 'value%28tDt%29=';
		$params[] = 'value%28submit1%29=Lihat+Mutasi+Rekening';
		$params = implode( '&', $params );

		$result = $this->browse($this->mutasi_url,$params,false,$this->info_rekening);
		$mydata = $result['data'];
		unset($result);
		return $this->getMutasiRekeningTable($mydata);
	}

	function getMutasiRekeningTable($html)
	{
		$dom = new DOMDocument();
	
		if ( BCA_PARSER_DEBUG ) {
			$dom->loadHTML($html);	
		} else {
			@$dom->loadHTML($html);	
		}
		
		$dom->getElementById('pagebody');
		
		$table = $dom->getElementsByTagName('table');
		$table = $table->item(4);
		return $dom->saveHTML($table);
	}
	
	private function getArrayValues($html)
	{
		$dom = new DOMDocument();
		$dom->loadHTML($html);
		$table = $dom->getElementsByTagName('table');
		$rows = $dom->getElementsByTagName('tr');
		//print_r($html);
		$datas = [];
		for ($i = 0; $i < $rows->length; $i++) {
			if($i== 0 ) continue;
		    $cols = $rows->item($i)->getElementsbyTagName("td");

		    $date = $this->hapusstr($cols->item(0)->nodeValue);
			if(strpos($date, 'PEND') === false){
				$date = explode('/', $date);
		    	$date = date('Y') . '-' . $date[1] . '-' . $date[0];
			}
	
		    $description = $this->hapusstr($cols->item(1)->nodeValue);
			$type = $this->hapusstr($cols->item(4)->nodeValue);
			$nominal = $this->hapusstr($cols->item(3)->nodeValue);
		    $data = compact('date','description', 'type', 'nominal');
		    $datas[] = $data;
		}
		return $datas;
	}
	
	function hapusstr($str){
		$str = preg_replace("/\n+/","",$str);
		$str = preg_replace("/<br>/"," ",$str);
		$str = preg_replace("/\s+/"," ",$str);
		$str = preg_replace("/,/","",$str);
		return $str;
	}
		
	function logout(){
		$this->browse($this->logout_url);
	}
}

?>