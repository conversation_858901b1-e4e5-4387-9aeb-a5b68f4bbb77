<?php 
$verifyToken = 'ezbotxxxx123456';

// Verify the webhook request
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['hub_challenge'])) {
    $challenge = $_GET['hub_challenge'];
    $mode = $_GET['hub_mode'];

    if ($mode === 'subscribe' && $_GET['hub_verify_token'] === $verifyToken) {
        echo $challenge;
        http_response_code(200);
        exit;
    } else {
        http_response_code(403);
        exit;
    }
}
$input = file_get_contents('php://input');


file_put_contents('log/hooklog-waba-'.$_GET["cs"].'.txt', '[' . date('Y-m-d H:i:s') . "]\n", FILE_APPEND);	
file_put_contents('log/hooklog-waba-'.$_GET["cs"].'.txt', $input, FILE_APPEND);	
file_put_contents('log/hooklog-waba-'.$_GET["cs"].'.txt', "\n\n", FILE_APPEND);
file_put_contents('log/hooklog-waba-'.$_GET["cs"].'.txt', json_encode($_GET), FILE_APPEND);	
file_put_contents('log/hooklog-waba-'.$_GET["cs"].'.txt', "\n\n", FILE_APPEND);


if(!isset($_GET["p"])){echo "invalid link";die();}
if(!isset($_GET["cs"])){echo "invalid link";die();}
if(!isset($input)){echo "invalid link";die();}

//////////////////////////////////////////////////////////////////////// setting //////////////////////
$trigger_kontak = false;
$trigger_mql = false;
$trigger_prospek = false;
$trigger_purchase = false;
$visitor_id = NULL;

if(!isset($_GET["p"])){
    return false;
}
if(!isset($_GET["cs"])){
    return false;
}
if(!isset($input)){
    return false;
}

$project_key = $_GET["p"];
$nope_cs = $_GET["cs"];
$webhookData = json_decode($input, true);

if (isset($webhookData['entry'][0]['changes'][0]['value']['messages'])) {
    $messages = $webhookData['entry'][0]['changes'][0]['value']['messages'];

    foreach ($messages as $message) {
        if(isset($message['to'])) {
            $phone = $message['to'];
            $msg_type = "message_out";
        } else {
            $phone = $message['from'];
            $msg_type = "message_in";
        }
        //$from = $message['from']; // Nomor pengirim
        //$to = $message['to'];
        $type = $message['type']; // Jenis pesan (text, image, audio, dll.)

        if ($type === 'text') {
            $pesan = $message['text']['body'];
            file_put_contents("log/log-waba-messages.txt", "Pesan dari $phone: $pesan\n", FILE_APPEND);
        } elseif ($type === 'image') {
            $imageId = $message['image']['id']; // ID file gambar
            file_put_contents("log/log-waba-messages.txt", "Pesan gambar dari $phone: $imageId\n", FILE_APPEND);
        } elseif ($type === 'audio') {
            $audioId = $message['audio']['id']; // ID file audio
            file_put_contents("log/log-waba-messages.txt", "Pesan audio dari $phone: $audioId\n", FILE_APPEND);
        }
    }
}else{
    if (isset($webhookData['entry'][0]['changes'][0]['value']['message_echoes'])) {
        $messages = $webhookData['entry'][0]['changes'][0]['value']['message_echoes'];
    
        foreach ($messages as $message) {
            if(isset($message['to'])) {
                $phone = $message['to'];
                $msg_type = "message_out";
            } else {
                $phone = $message['from'];
                $msg_type = "message_in";
            }
            //$from = $message['from']; // Nomor pengirim
            //$to = $message['to'];
            $type = $message['type']; // Jenis pesan (text, image, audio, dll.)
    
            if ($type === 'text') {
                $pesan = $message['text']['body'];
                file_put_contents("log/log-waba-messages.txt", "Pesan dari $phone: $pesan\n", FILE_APPEND);
            } elseif ($type === 'image') {
                $imageId = $message['image']['id']; // ID file gambar
                file_put_contents("log/log-waba-messages.txt", "Pesan gambar dari $phone: $imageId\n", FILE_APPEND);
            } elseif ($type === 'audio') {
                $audioId = $message['audio']['id']; // ID file audio
                file_put_contents("log/log-waba-messages.txt", "Pesan audio dari $phone: $audioId\n", FILE_APPEND);
            }
        }
    }else{
        die();
    }
}


$msg = $input;

if(isset($pesan) && $pesan==null){
    $pesan = '';
}
if(!isset($pesan)){
    die();
}
$post_archive = [
    'act' => 'archive_add',
    'pesan' => $pesan,
    'nope_cs' =>  $nope_cs,
    'phone' => $phone,
    'msg_type' => $msg_type,
];
$forward_to =array("http://10.104.0.56/api.html");
//print_r($post_archive);
///////////////////////////////////////////////////////////////////////// end setting //////////////////////
global $app;
$db = $app->db;
$db->where("project_key = UNHEX(?)",[$project_key]);
$project = $db->getone("project");

if($project == NULL){echo "invalid link";die();}

assign_child_db($project["project_id"]);

$t = new track();
$m = new meta();
global $app;
$db2 = $app->db2;

$db2->where("cs_key = UNHEX(?)",[md5($nope_cs)]);
$cs = $db2->getone("cs");

if($cs == NULL){echo "invalid link";die();}

 ///////////////////  cek new kontak //////////////////////
$is_new_kontak = false;
$new_kontak["phone"] = $phone;
$new_kontak["phone_hash"] = $db2->func("UNHEX(?)", [md5($phone) ]);
$new_kontak["created"] = date("Y-m-d H:i:s");
if ($db2->setQueryOption('IGNORE')->insert("new_kontak", $new_kontak)){
    $is_new_kontak = true;
}
 ///////////////////  cek new kontak //////////////////////
 ///////////////////////////////////// kontak masuk
$format_id = $m->get_meta("format_id");
if($format_id["code"] == 0){
    preg_match("/ID \[(.*?)\]/s", $msg, $match);
    if (count($match) > 1){
        $visitor_id = $match[1];
        $trigger_kontak = true;
    }else{
        if($is_new_kontak){
            $trigger_kontak = true;
        }
    }
}else{
    $format_id =  clean_string($format_id["result"]["data"]);
    $format_id = preg_quote(trim($format_id));
    $format_id = "/(?<=".$format_id." )\S+\b/is";
    $format_id = preg_replace('/\s+/', ' ', $format_id);
    $msg = preg_replace('/\s+/', ' ', $msg);
    $msg = str_replace('\n'," ",$msg);
    preg_match($format_id, $msg, $match);
    if (count($match) > 0){
        $visitor_id = trim($match[0]);
        $trigger_kontak = true;
    }else{

        preg_match("/ID \[(.*?)\]/s", $msg, $match);
        if (count($match) > 1)
        {
            $visitor_id = $match[1];
            $trigger_kontak = true;
        
        }else{
            if($is_new_kontak){
                $trigger_kontak = true;
            }
        }
    }
}
 ///////////////////////////////////// end kontak masuk /////////////////////////////////////



if ($msg_type == "message_in"){
    //////////////////////////////////// mql //////////////////////////////////
    $data["count"] = 1;
    $data["phone"] = $phone;
    $data["created"] = date("Y-m-d H:i:s");
    $data["phone_hash"] = $db2->func("UNHEX(?)", [md5($phone) ]);
    $db2->setQueryOption(["IGNORE"])->insert("mql", $data);
    $res = $m->get_meta("mql");
    if ($res["code"] == 1){
        $mql_limit = $res["result"]["data"] - 1;
    }
    $db2->where("phone_hash = UNHEX(?)", [md5($phone) ]);
    $mql_data = $db2->getone("mql");
    $inc = true;
    if ($mql_data != null){
        if ($mql_data["count"] == $mql_limit){
            $trigger_mql = true;
        }
        if ($mql_data["count"] > $mql_limit ){
            $inc = false;
        }
    }
    if ($inc){
        $data = [];
        $data_insert = [];
        $data["phone_hash"] = $db2->func("UNHEX(?)", [md5($phone)]);
        $data["count"] = 1;
        $data_insert["count"] = $db2->inc(1);
        $db2->onDuplicate($data_insert);
        $db2->insert("mql", $data);
    }
}
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
if ($msg_type == "message_out"){
    ///////////////////////// prospek
    $res = $m->get_meta("format_prospek");
    if ($res["code"] == 1){
        $rgxFormatCheckout = clean_string($res["result"]["data"]);
        $rgxFormatCheckout = preg_quote($rgxFormatCheckout , '/');
        $strregex = str_replace('%ID%', '(\d+)', $rgxFormatCheckout);
        $rgx = '/' . $strregex . '/';
        $res = preg_match($rgx, $pesan, $matches);
        if ($res !== false && $res > 0){
            $trigger_prospek = true;
        }
    }
    //////////////////////// end prospek
    ///////////////////////// purchase
    $value = 0;
    $meta_result = $m->get_meta("format_purchase");
    $meta_result2 = $m->get_meta("format_purchase_value");
    if ($meta_result["code"] == 1 && $meta_result2["code"] == 1){
        $rgxFormatPurchase = clean_string($meta_result["result"]["data"]);
        $rgxFormatPurchase = preg_quote($rgxFormatPurchase, '/');
        $strregex = str_replace('%ID%', '(\d+)', $rgxFormatPurchase);
        $rgx = '/' . $strregex . '/';
        $res = preg_match($rgx, $pesan, $matches);
        if ($res !== false && $res > 0){
            $rgxFormatValuePurchase = clean_string($meta_result2["result"]["data"]);
            $rgxFormatValuePurchase = preg_quote($rgxFormatValuePurchase, '/');
            $strregex = str_replace('%VALUE%', '([0-9,.]*)', $rgxFormatValuePurchase);
            $rgx = '/' . $strregex . '/';
            $res2 = preg_match($rgx, $pesan, $matches);
            if ($res2 !== false && $res2 > 0){
                $value = preg_replace('/[.,]/', '', $matches[1]);
                $trigger_purchase = true;
                
                
                
                $data_order['value'] = $value;

                ////////////////////// get alamat dll

                $data_order['nama'] = null;
                $format_prov = $m->get_meta("format_purchase_nama")["result"]["data"] ?? "";
                if ($format_prov != "") {
                    $rgxFormat = preg_quote($format_prov, '/');
                    $strregex = str_replace('%NAMA%', '(.+)', $rgxFormat);
                    $rgx = '/' . $strregex . '/';
                    $res = preg_match($rgx, $pesan, $matches);

                    if ($res !== false && $res > 0) {
                        $data_order['nama'] = trim($matches[1]);
                    }
                }

                $data_order['kota'] = null;
                $format_kota = $m->get_meta("format_purchase_kota")["result"]["data"] ?? "";
                if ($format_kota != "") {
                    $rgxFormat = preg_quote($format_kota, '/');
                    $strregex = str_replace('%KOTA%', '(.+)', $rgxFormat);
                    $rgx = '/' . $strregex . '/';
                    $res = preg_match($rgx, $pesan, $matches);

                    if ($res !== false && $res > 0) {
                        $data_order['kota'] = trim($matches[1]);
                    }
                }

                $data_order['alamat'] = null;
                $format_alamat = $m->get_meta("format_purchase_alamat")["result"]["data"] ?? "";
                if ($format_alamat != "") {
                    $rgxFormat = preg_quote($format_alamat, '/');
                    $strregex = str_replace('%ALAMAT%', '(.+)', $rgxFormat);
                    $rgx = '/' . $strregex . '/';
                    $res = preg_match($rgx, $pesan, $matches);

                    if ($res !== false && $res > 0) {
                        $data_order['alamat'] = trim($matches[1]);
                    }
                }

                $data_order['provinsi'] = null;
                $format_prov = $m->get_meta("format_purchase_provinsi")["result"]["data"] ?? "";
                if ($format_prov != "") {
                    $rgxFormat = preg_quote($format_prov, '/');
                    $strregex = str_replace('%PROVINSI%', '(.+)', $rgxFormat);
                    $rgx = '/' . $strregex . '/';
                    $res = preg_match($rgx, $pesan, $matches);

                    if ($res !== false && $res > 0) {
                        $data_order['provinsi'] = trim($matches[1]);
                    }
                }

                $data_order['items'] = array();
                $pesan_line = explode("\n", $pesan);
                $format_qty = $m->get_meta("format_purchase_qty_sku")["result"]["data"] ?? "";
                if ($format_qty != "") {
                    $rgxFormat = preg_quote($format_qty, '/');
                    $strregex = str_replace('%QTY%', '(.+)', $rgxFormat);
                    $strregex = str_replace('%SKU%', '(.+)', $strregex);
                    $rgx = '/' . $strregex . '/';

                    foreach ($pesan_line as $line) {
                        $res = preg_match($rgx, $line, $matches);
                        if ($res !== false && $res > 0) {

                            $item['qty'] = trim($matches[1]);
                            $item['sku'] = trim($matches[2]);
                            array_push($data_order['items'], $item);
                        }
                    }
                }
            }
        }
    }
    //////////////////////// end purchase
}
$x = false;

$visitor = $t->get_visitor($visitor_id, $phone);

if($visitor == false){
    file_put_contents('log/hooklog-waba-'.$_GET["cs"].'.txt', "visitor not found", FILE_APPEND);
}
else{
    file_put_contents('log/hooklog-waba-'.$_GET["cs"].'.txt', "existing visitor", FILE_APPEND);
}
if ($visitor == false) {
    if ($phone != null) {
        if( $is_new_kontak){
            $trigger_kontak = true;
            file_put_contents('log/hooklog-waba-'.$_GET["cs"].'.txt', "set trigger_kontak true", FILE_APPEND);
        }
        $visitor_id = $t->create_visitor($phone,$is_new_kontak);
        $tmp_visitor_id = $visitor_id;
      
        $visitor_id = convBase($visitor_id, "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        $visitor_id = str_split($visitor_id, 4);
        $visitor_id = implode(".", $visitor_id);
        $visitor = $t->get_visitor($visitor_id, $phone);

        //if($trigger_kontak == false){
        file_put_contents("log/insert-cs-".$nope_cs.".txt", '[' . date('Y-m-d H:i:s') . "]\n" . $phone."\n".$msg_type . "\n\n", FILE_APPEND);
            $t->insert_cs($tmp_visitor_id, $nope_cs, $msg_type);
        //}
        
        $x = $t->fbwa_waba($phone, ["raw" => $input]);
        if($x){
            $db2->setQueryOption(array('IGNORE'))->insert("visitor_source", ["visitor_id" => $visitor_id, "source" => "meta"]);
            $visitor = $t->get_visitor($visitor_id, $phone);
        }        
    } 
}

//////////////////////// cek ctwa ///////////////////////////////////////////

if ($x) {
    $trigger_kontak = true;
    $data_tmp["is_new_kontak"] = $is_new_kontak;
    $data_tmp["visitor"] = $visitor;
    $data_tmp["nope_cs"] = $nope_cs;
    $data_tmp["phone"] = $phone;
    $data_tmp = serialize($data_tmp);
    @file_put_contents('log/log_ctwa.txt', '[' . date('Y-m-d H:i:s') . "]\n" . $data_tmp."\n\n", FILE_APPEND);

    $visitor_data = unserialize($visitor["data"]);
    
    // Upsert visitor_ctwa by phone: update adcopy_id, ctwa_clid, data; insert if not exists
    $adcopy_id = null;
    $ctwa_clid = null;
    if (is_array($visitor_data)) {
        if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
            $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];
        }
        if (isset($visitor_data["last_campaign"]["data"]["ctwaClid"])) {
            $ctwa_clid = $visitor_data["last_campaign"]["data"]["ctwaClid"];
        } elseif (isset($visitor_data["last_campaign"]["data"]["ctwa_clid"])) {
            $ctwa_clid = $visitor_data["last_campaign"]["data"]["ctwa_clid"];
        }
    }
    $data_payload = isset($visitor["data"]) ? $visitor["data"] : (is_array($visitor_data) ? serialize($visitor_data) : null);
    if (!empty($phone)) {
        $db2->where("phone", $phone);
        $existing_ctwa = $db2->getOne("visitor_ctwa");
        $row_update = [
            "adcopy_id" => $adcopy_id,
            "ctwa_clid" => $ctwa_clid,
            "data" => $data_payload,
            "phone_cs" => $nope_cs,
            "project_key" => $project_key,
            "project_id" => $project["project_id"]
        ];
        if ($existing_ctwa) {
            $db2->where("phone", $phone);
            $db2->update("visitor_ctwa", $row_update);
        } else {
            $row_insert = $row_update;
            $row_insert["visitor_id"] = $visitor["visitor_id"] ?? null;
            $row_insert["phone"] = $phone;
            $db2->insert("visitor_ctwa", $row_insert);
        }
    }    
}

/////////////////////////////////////////////////////// trigger ////////////////////////

if($trigger_kontak || $trigger_mql || $trigger_prospek || $trigger_purchase){
        
    if($trigger_kontak){
        file_put_contents('log/hooklog-waba-'.$_GET["cs"].'.txt', "trigger lead", FILE_APPEND);
        $t->lead($is_new_kontak,$visitor,$nope_cs,$phone);
    }
    if($trigger_mql){
        $t->mql($nope_cs,$visitor,$phone);
    }

    if($trigger_prospek){
        $t->prospek($nope_cs,$visitor,$phone);
    }

    if($trigger_purchase){
        $t->purchase($nope_cs, $visitor, $phone, $value, $message_id, $data_order, $pesan);
    }
}

$x = forwarder($post_archive, $forward_to, NULL, true);
$save_history = 1;
if(file_exists('log/history/'.$project["project_id"].'/setting.text')){
    $save_history = file_get_contents('log/history/'.$project["project_id"].'/setting.text');
}
if($save_history == 1){ 
    $chatHistory = new ChatHistory($project["project_id"]);   
    if (!empty($phone) && !empty($pesan) && in_array($msg_type, ['message_in', 'message_out'])) {
        $chatHistory->save($phone, $msg_type, $pesan);
    }
}

die();



?>