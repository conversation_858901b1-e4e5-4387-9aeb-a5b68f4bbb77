<?php 

class cs_jadwal{



	function add_jadwal($cs_key,$day,$project_id)
	{
		global $app;
		$db = $app->db;
		$db2 = $app->db2;


		$this->delete_jadwal($cs_key,$project_id);

		foreach ($day as $key => $value) {
			$data["cs_key"] = $db2->func("UNHEX(?)",array($cs_key));
			$data["day"] = $key;
			$data["hour"] = $value["start"];
			$data["task"] = "on";
			$db2->insert("cs_jadwal",$data);

			$data2["cs_key"] = $db2->func("UNHEX(?)",array($cs_key));
			$data2["day"] = $key;
			$data2["hour"] = $value["end"];
			$data2["task"] = "off";
			$db2->insert("cs_jadwal",$data2);

			$data = array();
			$data2 = array();

			$data["cs_key"] = $db->func("UNHEX(?)",array($cs_key));
			$data["day"] = $key;
			$data["project_id"] = $project_id;
			$data["hour"] = $value["start"];
			$data["task"] = "on";
			$db->insert("cs_jadwal",$data);

			$data2["cs_key"] = $db->func("UNHEX(?)",array($cs_key));
			$data2["day"] = $key;
			$data2["project_id"] = $project_id;
			$data2["hour"] = $value["end"];
			$data2["task"] = "off";
			$db->insert("cs_jadwal",$data2);

			$data = array();
			$data2 = array();
		}

		
		$return["code"] = 1;
		$return["result"]["msg"] = "sukses add jadwal";
		return $return;
	}

	function delete_jadwal($cs_key,$project_id)
	{
		global $app;
		$db = $app->db;
		$db2 = $app->db2;

		$db->where("cs_key = UNHEX(?)",array($cs_key));
		$db->where("project_id",$project_id);
		$db->delete("cs_jadwal");

		$db2->where("cs_key = UNHEX(?)",array($cs_key));
		$db2->delete("cs_jadwal");

		$return["code"] = 1;
		$return["result"]["msg"] = "sukses delete jadwal";
		return $return;
	}

	function get_jadwal($cs_key)
	{
		global $app;
		$db2 = $app->db2;

		$db2->where("cs_key = UNHEX(?)",array($cs_key));
	
		$res =  $db2->get("cs_jadwal",NULL,"HEX(cs_key) as cs_key,day,hour,task");
		if(count($res) > 0)
		{
			$ret = array();
			foreach ($res as $key => $value) {
				if($value["task"] == "on")
				{
					$ret[$value["day"]]["start"] = $value["hour"];
				}
				else
				{
					$ret[$value["day"]]["end"] = $value["hour"];
				}
				
			}


			$return["code"] = 1;
			$return["result"]["data"] = $ret;
		
		}
		else
		{
			$return["code"] = 0;
			$return["result"]["msg"] = "gagal get jadwal";
		}


		return $return;
		
	}

	public static function cron_reset()
	{
		global $app;
		$db = $app->db;

		$db->update("cs_jadwal",array("status"=>0));
	}

	public static function cron()
	{
		global $app;
		$db = $app->db;

		$day =  date('N');
		$hour = date('H');

		$db->where("day",$day);
		$db->where("hour",$hour,"<=");
		$db->where("status",0);
		$db->orderBy("hour","asc");
		$jadwal = $db->get("cs_jadwal");


		foreach ($jadwal as $key => $value) {
			assign_child_db($value["project_id"]);
			global $app;
			$db2 = $app->db2;

			$db2->where("cs_key",$value["cs_key"]);
			$user = $db2->getone("cs");

			if($user != NULL)
			{
				$db2->where("cs_key",$value["cs_key"]);
				if($value["task"]== "off")
				{
					$db2->update("cs",array("status"=>0));	
				}
				else
				{
					$db2->update("cs",array("status"=>1));	
				}

				if($value["task"]== "off")
				{
					if($user["status"] == 0)
					{	
						$db->where("id",$value["id"]);
						$db->update("cs_jadwal",array("status"=>1));
					}
				}

				if($value["task"]== "on")
				{
					if($user["status"] == 1)
					{	
						$db->where("id",$value["id"]);
						$db->update("cs_jadwal",array("status"=>1));

					}
				}
			}
		}
	}
}