<?php
class trigger_mgid
{

    private $table_visitor;
    private $table_log;

    public function __construct()
    {

        $this->table_visitor = "visitor";
        $this->table_log = "log_connector";

    }

    public function sent_pixel($connector_key, $value, $endpoint, $vid, $type, $custom = null)
    {

        global $app;
        $db2 = $app->db2;

        $hash["value"] = $value;
        $hash["endpoint"] = $endpoint;
        $hash["source"] = "snack";
        $hash["vid"] = $vid;
        $hash["type"] = $type;
        $hash["custom"] = $custom;
        $hash["waktu"] = date("Y-m-d");
        $data_hash = json_encode($hash);
        //var_dump($hash2);
        $hash = hash("sha256", $data_hash);
        //echo $type;

        $table_visitor = $this->table_visitor;
        $table_log = $this->table_log;

        $waktu = time();

        $db2->where("visitor_id", $vid);
        $visitor = $db2->getone($this->table_visitor);

        if ($visitor == null) {
            return;
        }
        if ($visitor == "") {
            return;
        }

        if ($visitor["data"] == null) {return;}

        $visitor_data = unserialize($visitor["data"]);

        if (!isset($visitor_data["mgid"]["click_id"])) {
            return false;
        }

	//	$type = strtolower($type);
        $postback_url = $endpoint . "?c=" . $visitor_data["mgid"]["click_id"] . "&e=" . $type;

        if(!empty($value)){
            $postback_url.= "&r=". $value;
        }

        // Use cURL to send the request
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $postback_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($ch);
        curl_close($ch);

        $log["endpoint"] = $endpoint;
        $log["type"] = $type;
        $log["custom"] = $custom;

        $data_insertx = array();
        $data_insertx["waktu"] = date("Y-m-d H:i:s");
        $data_insertx["connector_key"] = $db2->func("UNHEX(?)", [$connector_key]);
        $data_insertx["vid"] = $vid;
        $data_insertx["event"] = $type;

        if (curl_errno($ch)) {
            $return = curl_error($ch);
            $log["msg"] = $return;
            $data_insertx["error"] = 1;
            $ddd["input"] = $data_hash;
            $ddd["output"] = $log;

            $data_insertx["result"] = json_encode($ddd);

            $db2->insert($table_log, $data_insertx);

            //echo $db2->getLastQuery();
            $result = $return;

        } else {

            $data_insert["result"] = json_encode($result);
            //$db2->insert($table_log,$data_insert);

            $log["msg"] = $result;
            $data_insertx["error"] = 0;
            $data_insertx["result"] = json_encode($log);
            $db2->insert($table_log, $data_insertx);
            //echo $db2->getLastQuery();
        }
        curl_close($ch);

        return $result;
    }

}
