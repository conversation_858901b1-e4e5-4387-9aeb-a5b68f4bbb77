<?php

class clean
{

    public function reindex()
    {
        global $app;
        $db2 = $app->db2;
        $db3 = $app->db3;

        $q = "OPTIMIZE TABLE `cs_log`; ";
        $db2->rawQuery($q);

        $q = "OPTIMIZE TABLE `log_connector`; ";
        $db2->rawQuery($q);

        $q = "OPTIMIZE TABLE `visitor`; ";
        $db2->rawQuery($q);

        $q = "OPTIMIZE TABLE `visitor_source`; ";
        $db2->rawQuery($q);

        $q = "OPTIMIZE TABLE `visitor_event`; ";
        $db2->rawQuery($q);

        $q = "OPTIMIZE TABLE `visitor_cs`;";
        $db2->rawQuery($q);
    }

    function move_old_cs_log($date_limit,$limit)
    {
        global $app;
        $db2 = $app->db2;
        $db3 = $app->db3;

       
        $q          = "
                CREATE TABLE IF NOT EXISTS `cs_log` (
    `id` bigint(20) NOT NULL,
    `hash` binary(16) NOT NULL,
    `tanggal` date NOT NULL,
    `cs_key` binary(16) DEFAULT NULL,
    `phone` varchar(30) DEFAULT NULL,
    `impression` int(10) UNSIGNED NOT NULL DEFAULT '0',
    `contact` int(10) UNSIGNED NOT NULL DEFAULT '0',
    `inbound` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
    `outbound` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
    `mql` int(10) UNSIGNED NOT NULL DEFAULT '0',
    `prospek` int(10) UNSIGNED NOT NULL DEFAULT '0',
    `purchase` int(10) UNSIGNED NOT NULL DEFAULT '0',
    `value` int(10) UNSIGNED NOT NULL DEFAULT '0'
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4;";
            $db3->rawQuery($q);

            $db2->where("tanggal", $date_limit, "<=");
            $data = $db2->get("cs_log", [0, $limit]);

            foreach ($data as $key => $value) {
                @$db3->insert("cs_log", $value);
                $db2->where("id", $value["id"]);
                $db2->delete("cs_log");
            }
    }

    public function move_old_visitor($date_limit,$limit){
        global $app;
        $db2 = $app->db2;
        $db3 = $app->db3;

        ///////////////////////////// clear visitor
        $db2->where("created", $date_limit, "<=");
        $db2->where("lead = 0");
        $data = $db2->get("visitor");

        foreach ($data as $key => $value) {
            $db3->setQueryOption(['IGNORE'])->insert("visitor", $value);

            $db2->where("visitor_id", $value["visitor_id"]);
            $vs = $db2->get("visitor_source",[0,$limit]);

            foreach ($vs as $key2 => $value2) {

                $db3->setQueryOption(['IGNORE'])->insert("visitor_source", $value2);

                // $db2->where("visitor_id",$value["visitor_id"]);
                //$db2->delete("visitor_source");
            }

            $db2->where("visitor_id", $value["visitor_id"]);
            $db2->delete("visitor");
        }
    }

    function move_old_log_connector($date_limit,$limit){
        global $app;
        $db2 = $app->db2;
        $db3 = $app->db3;

        $db2->where("waktu", $date_limit, "<=");
        $data = $db2->get("log_connector",[0,$limit]);

        foreach ($data as $key => $value) {
            $db3->setQueryOption(['IGNORE'])->insert("log_connector", $value);
            $db2->where("id", $value["id"]);
            $db2->delete("log_connector");
        }
    }
    

    function move_old_visitor_event($date_limit,$limit){
        global $app;
        $db2 = $app->db2;
        $db3 = $app->db3;

        $db2->where("waktu", $date_limit, "<=");
        $data = $db2->get("visitor_event",[0,$limit]);

        foreach ($data as $key => $value) {
            $db3->setQueryOption(['IGNORE'])->insert("visitor_event", $value);
            $db2->where("visitor_event_id", $value["visitor_event_id"]);
            $db2->delete("visitor_event");
        }
    }

    function move_old_visitor_cs($date_limit,$limit){
        global $app;
        $db2 = $app->db2;
        $db3 = $app->db3;       

        $db2->where("created", $date_limit, "<=");
        $data = $db2->get("visitor_cs",[0,$limit]);
        foreach ($data as $key => $value) {
            $db3->setQueryOption(['IGNORE'])->insert("visitor_cs", $value);
            $db2->where("visitor_id", $value["visitor_id"]);
            $db2->where("cs_key", $value["cs_key"]);
            $db2->delete("visitor_cs");
        }
    }

    public function move_old()
    {
        $limit      = 20;
        $date_limit = date('Y-m-d', strtotime("-90 days"));

        $this->move_old_cs_log($date_limit,$limit);
        $this->move_old_visitor($date_limit,$limit);
        $this->move_old_log_connector($date_limit,$limit);
        $this->move_old_visitor_event($date_limit,$limit);
        $this->move_old_visitor_cs($date_limit,$limit);
        

    }

    public function clean_data($data)
    {
        global $app;
        $db  = $app->db;
        $db2 = $app->db2;

        $q = "delete from log_connector_hash where created < now() - interval 90 DAY;";
        $db2->rawQuery($q);
    }
}
