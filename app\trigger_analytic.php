<?php 





class GAnalytic{



	function send($event,$api_secret, $measurement_id,$client_id,$value = 0, $utm = NULL, $gclid = NULL)
	{


		$default_timezone = date_default_timezone_get();
		date_default_timezone_set('UTC');
		$microsec = (int)ceil(microtime(true) * 1000000);

		@date_default_timezone_set($default_timezone);


		if(isset($utm["campaign_id"]))
		{$param["campaign_id"] = $utm["campaign_id"];}

		if(isset($utm["campaign"]))
		{$param["campaign"] = $utm["campaign"];}

		if(isset($utm["source"]))
		{$param["source"] = $utm["source"];}

		if(isset($utm["medium"]))
		{$param["medium"] = $utm["medium"];}

		if(isset($utm["term"]))
		{$param["term"] = $utm["term"];}

		if(isset($utm["content"]))
		{$param["content"] = $utm["content"];}

/*
		if(isset($value["currency"]))
		{$param["currency"] = $value["currency"];}
		*/

		if(isset($value))
		{$param["value"] = $value;}

		//$param["debug_mode"] = 1;

		if(isset($param))
		{
			$data = array(
		    'client_id' => $client_id,
		    'timestamp_micros' => $microsec,
		    'non_personalized_ads' =>  true,
		    'events' => array(
		        array(
		            'name' => $event,
		            'params' => $param,
		        )
		    )
			);
		}else{
			$data = array(
		    'client_id' => $client_id,
		    'timestamp_micros' => $microsec,
		    'non_personalized_ads' =>  true,
		    'events' => array(
		        array(
		            'name' => $event
		        )
		    )
			);
		}

		


		$data_string = json_encode($data);

		$ch = curl_init('https://www.google-analytics.com/mp/collect?measurement_id=' . $measurement_id . '&api_secret=' . $api_secret);
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
		curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array(
		    'Content-Type: application/json',
		    'Content-Length: ' . strlen($data_string)
		));

		$result = curl_exec($ch);

		

		if ($result === false) {
		    $ret = 'Error: ' . curl_error($ch);
		} else {
		    $ret = 'Response: ' . $result;
		}


		curl_close($ch);

		return $ret;
	}
}