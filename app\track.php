<?php
/**
 * Track Class - Handles visitor tracking and event management
 * 
 * Recent Changes (2025):
 * - Improved fbwa_waba() function for better CTWA/WABA detection:
 *   * Fixed scope bug with $entry variable usage
 *   * Enhanced CTWA detection to work with ctwaClid OR adcopy_id (not just adcopy_id)
 *   * Always set waba_id when available for proper CTWA connector detection
 *   * Merge existing visitor data instead of overwriting to preserve other campaign data
 *   * More robust error handling for data serialization
 *   * Added add_report() call to ensure CTWA/WABA data is properly tracked in reporting system
 *   * CTWA/WABA views are now recorded with event type "ctwa_view" for proper analytics
 *   * Campaign data includes ctwa_clid, waba_id, source_url, headline, media_type, and image_url
 * - Enhanced CTWA reporting logic:
 *   * Added handle_ctwa_reporting() helper function to handle CTWA cases without adcopy_id
 *   * Updated all reporting functions (lead, mql, prospek, purchase) to support CTWA reporting
 *   * CTWA can now be reported using ctwa_clid OR adcopy_id OR waba_id
 * - Unknown source reporting:
 *   * Panggil report->ensure_unknown_structure() di semua *_unknown handler
 *     untuk menjamin struktur report Unknown (report_id=1) ada sebelum tulis metrik.
 */
class track
{

    /**
     * Helper function to handle CTWA reporting when adcopy_id is not available
     * CTWA can be reported using ctwa_clid, adcopy_id, waba_id, or source_url
     */
    private function handle_ctwa_reporting($visitor, $visitor_data, $phone, $type) {
        global $app;
        $db2 = $app->db2;
        
        // Check for adcopy_id first (standard case)
        if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
            $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];
            $db2->where("external_key = UNHEX(?)", [sha1($adcopy_id)]);
            $data_report = $db2->getone("report");
            if ($db2->count > 0 && isset($data_report["report_id"])) {
                return $data_report["report_id"];
            }else{
                $db2->where("external_key = UNHEX(?)", [sha1('unknown_'.$adcopy_id)]);
                $data_report = $db2->getone("report");
                if ($db2->count > 0 && isset($data_report["report_id"])) {
                    return $data_report["report_id"];
                }
            }
            
            // If not found in report table, try to use raw adcopy_id
            // This handles cases where report structure hasn't been created yet
            if (is_numeric($adcopy_id) && $adcopy_id > 0) {
                return $adcopy_id;
            }
        }
        
        // Check for source_id (alternative to adcopy_id)
        if (isset($visitor_data["last_campaign"]["data"]["source_id"])) {
            $source_id = $visitor_data["last_campaign"]["data"]["source_id"];
            
            // Try to resolve source_id to report_id via external_key lookup
            $db2->where("external_key = UNHEX(?)", [sha1($source_id)]);
            $data_report = $db2->getone("report");
            if ($db2->count > 0 && isset($data_report["report_id"])) {
                return $data_report["report_id"];
            }else{
                $db2->where("external_key = UNHEX(?)", [sha1('unknown_'.$source_id)]);
                $data_report = $db2->getone("report");
                if ($db2->count > 0 && isset($data_report["report_id"])) {
                    return $data_report["report_id"];
                }
            }
            
            // If not found, try to use raw source_id
            if (is_numeric($source_id) && $source_id > 0) {
                return $source_id;
            }
        }
        
        // Check for waba_id
        if (isset($visitor_data["last_campaign"]["data"]["waba_id"])) {
            $waba_id = $visitor_data["last_campaign"]["data"]["waba_id"];
            // Use waba_id as external_id for reporting
            return $waba_id;
        }

        // No CTWA data found
        return null;
    }

    public function cta($visitor_id = null)
    {
        global $app;
        $db2 = $app->db2;

        $visitor_id = str_replace(".", "", $visitor_id);
        $visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");

        $db2->where("visitor_id", $visitor_id);
        $visitor = $db2->getone("visitor");

        if ($visitor != null) {
            if (isset($visitor["data"])) {
                if ($visitor["data"] != "") {
                    $visitor_data = unserialize($visitor["data"]);
                }
            }
            if (isset($visitor_data["last_campaign"]["source"])) {
                $source = $visitor_data["last_campaign"]["source"];
                if ($source == "meta" || $source == "tiktok" || $source == "meta" || $source == "google" || $source == "organic" || $source == "shortlink") {

                    if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                        $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];
                        $campaign = new report();
                        if (!isset($_COOKIES["cta-" . $source . "-" . $adcopy_id])) {
                            $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                        }
                        $campaign->add_report_data($visitor, $adcopy_id, "cta");
                        setcookie("cta-" . $source . "-" . $adcopy_id, 1, time() + (86400 * 360), "/");
                    }
                }
            }
            $con = new connector();
            $con->trigger($visitor_id, "cta");
        }
    }
////////////////////////////// untuk add ulang //////////////////////
    public function add_report_gass($visitor,$type="lead"){
        global $app, $project;
        $db2 = $app->db2;

        $campaign = new report();
        if (isset($visitor["data"])) {
            if ($visitor["data"] != "") {
                $visitor_data = unserialize($visitor["data"]);
            }
        }
        if (isset($visitor_data["last_campaign"]["source"])) {
            $source = $visitor_data["last_campaign"]["source"];

            if ($source == "meta" || $source == "tiktok" || $source == "google" || $source == "organic" || $source == "shortlink" || $source == "snack" || $source == "mgid") {

                // Use helper function to handle CTWA reporting
                $external_id = $this->handle_ctwa_reporting($visitor, $visitor_data, $visitor["phone"], $type);
                
                if ($external_id !== null) {
                    $key = $visitor["phone"] . ";".$type;
                    $campaign = new report();

                    if ($campaign->get_hash($key) == null) {
                        $campaign->add_report_data($visitor, $external_id, "unik_wa");
                    }
                    $campaign->add_report_data($visitor, $external_id, "wa");
                    $campaign->add_hash($key);
                } else {
                    $this->kontak_unknown(true, $visitor["phone"],null);
                }
            } else {
                $this->kontak_unknown(true, $visitor["phone"],null);
            }
        } else {
            $this->kontak_unknown(true, $visitor["phone"],null);
        }
    }
////////////////////////////// untuk add ulang //////////////////////
    public function retrigger_connector($visitor,$type="lead"){
        global $app, $project;
        $db2 = $app->db2;

        $campaign = new report();

        $key = $visitor["phone"] . ";lead;" . date("Y-m-d");
        if ($campaign->get_hash($key) == null) {
            $con = new connector();
            $con->trigger($visitor["visitor_id"], $type);

        }
        $campaign->add_hash($key);

    }
////////////////////////////// untuk trigger ulang pixel //////////////////////


///////////////////////////////////////////////////////////////////

    public function lead($is_new_kontak, $visitor, $nope_cs, $phone = null, $message_id = '', $msg_type = null, $trigger_kontak=false)
    {

        global $app, $project;
        $db2 = $app->db2;
        $visitor_id = $visitor['visitor_id'];

        if ($message_id != '') {
            $db2->where("event", "lead");
            $db2->where("message_id", $message_id);
            $db2->get("visitor_event");
            if ($db2->count > 0) {
                return;
            }
        }

        $update["lead"] = 1;
        $update["waktu_contact"] = date("Y-m-d H:i:s");
        if ($phone != null) {
            $update["phone"] = $phone;
        } else {
            $phone = $visitor["phone"];
        }

        if ($phone == null || $phone == "") {
            return false;
        }

        $db2->where("visitor_id", $visitor_id);
        $db2->where("lead", 1);
        $tmp = $db2->getone("visitor");
        // $tmp = null;
        if ($tmp != null) {
            return false;
        }

        $campaign = new report();

        $db2->where("visitor_id", $visitor_id);
        $db2->update("visitor", $update);

        $visitor_event["waktu"] = date("Y-m-d H:i:s");
        $visitor_event["visitor_id"] = $visitor_id;
        $visitor_event["event"] = "lead";
        $visitor_event["message_id"] = $message_id;
        $db2->insert("visitor_event", $visitor_event);

        $this->insert_cs($visitor_id, $nope_cs, $msg_type);

        if (isset($visitor["data"])) {
            if ($visitor["data"] != "") {
                $visitor_data = unserialize($visitor["data"]);

            }
        }

        if (isset($visitor_data["last_campaign"]["source"])) {
            $source = $visitor_data["last_campaign"]["source"];

            if ($source == "meta" || $source == "tiktok" || $source == "google" || $source == "organic" || $source == "shortlink" || $source == "snack" || $source == "mgid") {

                // Use helper function to handle CTWA reporting
                $external_id = $this->handle_ctwa_reporting($visitor, $visitor_data, $phone, "lead");
                if ($external_id !== null) {
                    $key = $phone . ";lead";
                    $campaign = new report();
                    ////////////////////////////// unique by adcopy id
                    /*
                    $db2->setLockMethod("WRITE")
                        ->lock(array(
                            "report_hash",
                            "meta",
                            "report_data",
                            "visitor",
                            "connector",
                            "log_connector_hash",
                            "log_connector",
                        ));
                        */
                    if ($campaign->get_hash($key) == null) {
                        $campaign->add_report_data($visitor, $external_id, "unik_wa");
                    }
                    $campaign->add_report_data($visitor, $external_id, "wa");
                    $campaign->add_hash($key);
                    //$db2->unlock();
                } else {
                    $this->kontak_unknown($is_new_kontak, $phone, $nope_cs, $trigger_kontak);
                }
            } else {
                $this->kontak_unknown($is_new_kontak, $phone, $nope_cs, $trigger_kontak);
            }
        } else {
            $this->kontak_unknown($is_new_kontak, $phone, $nope_cs, $trigger_kontak);
        }

        //////////////////////////////
        //////////////////// trigger pixel ////////////////
        $key = $phone . ";lead;" . date("Y-m-d");
        /*
        $db2->setLockMethod("WRITE")
            ->lock(array(
                "report_hash",
                "meta",
                "report_data",
                "visitor",
                "connector",
                "log_connector_hash",
                "log_connector",
                "visitor_source",
            ));
            */
        if ($campaign->get_hash($key) == null) {
            $con = new connector();
            $con->trigger($visitor_id, "lead");

        }
        $campaign->add_hash($key);
       // $db2->unlock();
        //////////////////// trigger pixel ////////////////
    }

    public function mql($nope_cs, $visitor, $phone, $message_id = '')
    {
        global $app, $project;
        $db2 = $app->db2;
        $visitor_id = $visitor['visitor_id'];

        if ($message_id != '') {
            $db2->where("event", "mql");
            $db2->where("message_id", $message_id);
            $db2->get("visitor_event");
            if ($db2->count > 0) {
                return;
            }
        }

        $db2->where("visitor_id", $visitor_id);
        $db2->where("mql", 1);
        $tmp = $db2->getone("visitor");
        if ($tmp != null) {
            return false;
        }

        $db2->where("visitor_id", $visitor_id);
        $db2->update("visitor", ["mql" => 1]);

        $con = new connector();
        $con->trigger($visitor_id, "mql");

        if (isset($visitor["data"])) {
            if ($visitor["data"] != "") {
                $visitor_data = unserialize($visitor["data"]);
            }
        }

        if (isset($visitor_data["last_campaign"]["source"])) {
            $source = $visitor_data["last_campaign"]["source"];

            if ($source == "meta" || $source == "tiktok" || $source == "google" || $source == "organic" || $source == "shortlink" || $source == "snack" || $source == "mgid") {
                // Use helper function to handle CTWA reporting
                $external_id = $this->handle_ctwa_reporting($visitor, $visitor_data, $phone, "mql");
                
                if ($external_id !== null) {
                    $campaign = new report();

                    $key = $phone . ";mql";
                    $db2->setLockMethod("WRITE")
                        ->lock(array(
                            "report_hash",
                            "meta",
                            "report_data",
                            "visitor",
                            "connector",
                            "log_connector_hash",
                            "log_connector",

                        ));

                    if ($campaign->get_hash($key) == null) {

                        $campaign->add_report_data($visitor, $external_id, "mql");
                    }
                    $campaign->add_hash($key);
                    $db2->unlock();

                } else {
                    $this->mql_unknown($phone);
                }
            } else {
                $this->mql_unknown($phone);
            }
        } else {
            $this->mql_unknown($phone);
        }
        $cs_log = new cs_log($nope_cs);
        $cs_log->add_stat("mql");

        $visitor_event["waktu"] = date("Y-m-d H:i:s");
        $visitor_event["visitor_id"] = $visitor_id;
        $visitor_event["event"] = "mql";
        $visitor_event["message_id"] = $message_id;
        $db2->insert("visitor_event", $visitor_event);

    }

    public function prospek($nope_cs, $visitor, $phone, $message_id = '')
    {
        global $app, $project;
        $db2 = $app->db2;
        $visitor_id = $visitor['visitor_id'];

        if ($message_id != '') {
            $db2->where("event", "prospek");
            $db2->where("message_id", $message_id);
            $db2->get("visitor_event");
            if ($db2->count > 0) {
                return;
            }
        }
        

        $db2->where("visitor_id", $visitor_id);
        $db2->where("prospek", 1);
        $tmp = $db2->getone("visitor");
        if ($tmp != null) {
            return false;
        }

        $db2->where("visitor_id", $visitor_id);
        $db2->update("visitor", ["prospek" => 1]);

        if (isset($visitor["data"])) {
            if ($visitor["data"] != "") {
                $visitor_data = unserialize($visitor["data"]);
            }
        }
        $visitor_id = $visitor["visitor_id"];
        $con = new connector();
        $con->trigger($visitor_id, "prospek");

        if (isset($visitor_data["last_campaign"]["source"])) {
            $source = $visitor_data["last_campaign"]["source"];

            if ($source == "meta" || $source == "tiktok" || $source == "google" || $source == "organic" || $source == "shortlink" || $source == "snack" || $source == "mgid") {
                // Use helper function to handle CTWA reporting
                $external_id = $this->handle_ctwa_reporting($visitor, $visitor_data, $phone, "prospek");
                
                if ($external_id !== null) {
                    $campaign = new report();

                    $key = $phone . ";prospek";
                    $db2->setLockMethod("WRITE")
                        ->lock(array(
                            "report_hash",
                            "meta",
                            "report_data",
                            "visitor",
                            "connector",
                            "log_connector_hash",
                            "log_connector",
                        ));
                    if ($campaign->get_hash($key) == null) {

                        $campaign->add_report_data($visitor, $external_id, "prospek");
                    }
                    $campaign->add_hash($key);
                    $db2->unlock();

                } else {
                    $this->prospek_unknown($phone);
                }
            } else {
                $this->prospek_unknown($phone);
            }
        } else {
            $this->prospek_unknown($phone);
        }

        $cs_log = new cs_log($nope_cs);
        $cs_log->add_stat("prospek");

        $visitor_event["waktu"] = date("Y-m-d H:i:s");
        $visitor_event["visitor_id"] = $visitor_id;
        $visitor_event["event"] = "prospek";
        $visitor_event["message_id"] = $message_id;
        $db2->insert("visitor_event", $visitor_event);

        if (isset($project["use_builder"])) {
            if ($project["use_builder"] == 1) {
                $challenge = "DSmAFhZQ4JerWYH";
                $timeSlot = floor(time() / 5);
                $server_token = sha1(crc32($challenge . $timeSlot));

                $data_curl["vid"] = $visitor_id;
                $data_curl["project_key"] = bin2hex($project["project_key"]);
                $data_curl["event"] = "prospek";
                $data_curl["server_token"] = $server_token;
                $data_curl["act"] = "event_update";

                //  echo json_encode($data_curl);
                $url = "https://data-builder.gass.co.id";
                sendPostRequestWithoutWaiting($url, $data_curl);
            }
        }
    }

    public function purchase($nope_cs, $visitor, $phone, $value = 0, $message_id = '', $data_order = null, $msg = "")
    {
        global $app, $project;
        $db2 = $app->db2;
        
        // Pastikan value selalu berupa integer yang valid
        $value = is_numeric($value) ? (int)$value : 0;
        $visitor_id = $visitor['visitor_id'];

        $db2->setLockMethod("WRITE")->lock(array("visitor_event"));   

        if ($message_id != '') {
            $db2->where("event", "purchase");
            $db2->where("message_id", $message_id);
            $db2->get("visitor_event");
            if ($db2->count > 0) {
                $db2->unlock();
                return;
            }
        }
       

        $db2->where("waktu", date("Y-m-d 00:00:00"), ">=");
        $db2->where("waktu", date("Y-m-d 23:59:59"), "<=");
        $db2->where("visitor_id", $visitor_id);
        $db2->where("event", "purchase");
        $tmp = $db2->getone("visitor_event");
        if (!empty($tmp)) {
            $db2->unlock();
            return;
        }
       
        $visitor_event["waktu"] = date("Y-m-d H:i:s");
        $visitor_event["visitor_id"] = $visitor_id;
        $visitor_event["event"] = "purchase";
        $visitor_event["value"] = $value;
        $visitor_event["message_id"] = $message_id;
        $db2->insert("visitor_event", $visitor_event);

        $db2->unlock();
    

        $first_purchase = $visitor["first_purchase"];
        if ($first_purchase == "NULL" || $first_purchase == "" || is_null($first_purchase)) {
            $update["first_purchase"] = date("Y-m-d H:i:s");
        }

        if (isset($visitor["data"])) {
            if ($visitor["data"] != "") {
                $visitor_data = unserialize($visitor["data"]);
            }
        }
        $visitor_id = $visitor["visitor_id"];
        $con = new connector();
        $con->trigger($visitor_id, "purchase", $value);
        if (isset($visitor_data["last_campaign"]["source"])) {
            $source = $visitor_data["last_campaign"]["source"];
            if ($source == "meta" || $source == "tiktok" || $source == "google" || $source == "organic" || $source == "shortlink" || $source == "snack" || $source == "mgid") {
                // Use helper function to handle CTWA reporting
                $external_id = $this->handle_ctwa_reporting($visitor, $visitor_data, $phone, "purchase");
                
                if ($external_id !== null) {
                    $campaign = new report();

                    $key = $phone . ";purchase";
                    $db2->setLockMethod("WRITE")
                        ->lock(array(
                            "report_hash",
                            "meta",
                            "report_data",
                            "visitor",
                            "connector",
                            "log_connector_hash",
                            "log_connector",
                        ));
                    if ($campaign->get_hash($key) == null) {
                        $campaign->add_report_data($visitor, $external_id, "unik_purchase");
                    }
                   // $campaign->add_hash($key);
                    $db2->unlock();

                    $key = $phone . ";purchase;" . date("Y-m-d");
                    $db2->setLockMethod("WRITE")
                        ->lock(array(
                            "report_hash",
                            "meta",
                            "report_data",
                            "visitor",
                            "connector",
                            "log_connector_hash",
                            "log_connector",
                        ));
                    if ($campaign->get_hash($key) == null) {
                        $campaign->add_report_data($visitor, $external_id, "purchase");
                        $campaign->add_report_data($visitor, $external_id, "purchase_value", $value);

                        $update = array();
                        $update["purchase"] = 1;
                        $update["value"] = $db2->inc($value);
                        $db2->where("visitor_id", $visitor_id);
                        $db2->update("visitor", $update);

                    }
                    $campaign->add_hash($key);
                    $db2->unlock();

                } else {
                    $this->purchase_unknown($phone, $value);
                }
            } else {
                $this->purchase_unknown($phone, $value);
            }
        } else {
            $this->purchase_unknown($phone, $value);
        }

        if (isset($visitor_data["last_campaign"]["source"])) {
            $orders["source"] = $visitor_data["last_campaign"]["source"];
        } else {
            $orders["source"] = "unknown";
        }

        $orders["visitor_id"] = $visitor_id;
        $orders["phone"] = $phone;
        $orders["value"] = $value;

        if (!empty($msg)) {
            $orders["msg"] = $msg;
        }

        if (!empty($data_order)) {
            $orders["details"] = json_encode($data_order);
        }
        $orders["created"] = date("Y-m-d H:i:s");
        $db2->insert("orders", $orders);

        $cs_log = new cs_log($nope_cs);
        $cs_log->add_stat("purchase", $value);

       

        if (isset($project["use_builder"])) {
            if ($project["use_builder"] == 1) {
                $challenge = "DSmAFhZQ4JerWYH";
                $timeSlot = floor(time() / 5);
                $server_token = sha1(crc32($challenge . $timeSlot));

                $data_curl["vid"] = $visitor_id;
                $data_curl["project_key"] = bin2hex($project["project_key"]);
                $data_curl["event"] = "purchase";
                $data_curl["server_token"] = $server_token;
                $data_curl["act"] = "event_update";

                //  echo json_encode($data_curl);
                $url = "https://data-builder.gass.co.id";
                sendPostRequestWithoutWaiting($url, $data_curl);
            }
        }
    }

    public function insert_cs($visitor_id, $nope_cs, $msg_type = null)
    {
        global $app;
        $db2 = $app->db2;

        // $db2->where("visitor_id", $visitor_id);
        // $db2->where("cs_key = UNHEX(?)", [md5($nope_cs)]);
        // $tmp = $db2->getone("visitor_cs");

        // if ($tmp == null) {
        $db2->where("cs_key = UNHEX(?)", [md5($nope_cs)]);
        $db2->where("visitor_id", $visitor_id);
        $tmp = $db2->getone("visitor_cs");

        if ($db2->count == 0) {
            $data_kontak["created"] = date("Y-m-d H:i:s");
            $data_kontak["visitor_id"] = $visitor_id;
            $data_kontak["cs_key"] = $db2->func("UNHEX(?)", [md5($nope_cs)]);
            $db2->setQueryOption('IGNORE')
                ->insert('visitor_cs', $data_kontak);

            //  @file_put_contents('log/hooksas_kontak.txt', '[' . date('Y-m-d H:i:s') . "]\n" . $db2->getLastQuery(), FILE_APPEND);
            $cs_log = new cs_log($nope_cs);
            $cs_log->add_stat("contact", 0, $msg_type);

            //if($msg_type == "message_out"){
            //     $cs_log->add_lead_detail("outbound");
            // }else{
            //      $cs_log->add_lead_detail("inbound");
            // }
        }
        // $cs_log = new cs_log($nope_cs);
        // $cs_log->add_stat("contact");
        // if($msg_type == "message_out"){
        //     $cs_log->add_lead_detail("outbound");
        // }else{
        //     $cs_log->add_lead_detail("inbound");
        // }
        // }
    }

    public function kontak_unknown($is_new_kontak, $phone, $nope_cs, $trigger_kontak=false)
    {
        if ($is_new_kontak || $trigger_kontak) {
            global $app;
            $db2 = $app->db2;

            $key = $phone . ";lead";
            $campaign = new report();
            // ensure Unknown campaign exists and get its id
            $unknownId = $campaign->ensure_unknown_structure($phone);
            ////////////////////////////// unique by adcopy id
            /*
            $db2->setLockMethod("WRITE")
                ->lock(array(
                    "report_hash",
                    "meta",
                    "report_data",
                    "visitor",
                    "connector",
                    "log_connector_hash",
                    "log_connector",
                ));
                */
            $db2->where("phone", $phone);
            $visitor = $db2->getone("visitor");
            if ($campaign->get_hash($key) == null) {
                $campaign->add_report_data($visitor, $unknownId, "unik_wa");
            }
            $campaign->add_report_data($visitor, $unknownId, "wa");
            $campaign->add_hash($key);
           // $db2->unlock();
        }
    }

    public function mql_unknown($phone)
    {
        global $app;
        $db2 = $app->db2;

        $campaign = new report();
        // ensure Unknown campaign exists and get its id
        $unknownId = $campaign->ensure_unknown_structure();

        

        $key = $phone . ";mql";
        $db2->setLockMethod("WRITE")
            ->lock(array(
                "report_hash",
                "meta",
                "report_data",
                "visitor",
                "connector",
                "log_connector_hash",
                "log_connector",
            ));

        if ($campaign->get_hash($key) == null) {
            $db2->where("phone", $phone);
            $visitor = $db2->getone("visitor");
            $con = new connector();
            $campaign->add_report_data($visitor, $unknownId, "mql");
        }
        $campaign->add_hash($key);
        $db2->unlock();
    }

    public function prospek_unknown($phone)
    {
        global $app;
        $db2 = $app->db2;
        $campaign = new report();
        // ensure Unknown campaign exists and get its id
        $unknownId = $campaign->ensure_unknown_structure();

        $key = $phone . ";prospek";
        $db2->setLockMethod("WRITE")
            ->lock(array(
                "report_hash",
                "meta",
                "report_data",
                "visitor",
                "connector",
                "log_connector_hash",
                "log_connector",
            ));

        if ($campaign->get_hash($key) == null) {
            $db2->where("phone", $phone);
            $visitor = $db2->getone("visitor");
            $con = new connector();
            $campaign->add_report_data($visitor , $unknownId, "prospek");
        }
        $campaign->add_hash($key);
        $db2->unlock();
    }

    public function purchase_unknown($phone, $value)
    {
        global $app;
        $db2 = $app->db2;

        $campaign = new report();
        // ensure Unknown campaign exists and get its id
        $unknownId = $campaign->ensure_unknown_structure();

        $key = $phone . ";purchase";
        $db2->setLockMethod("WRITE")
            ->lock(array(
                "report_hash",
                "meta",
                "report_data",
                "visitor",
                "connector",
                "log_connector_hash",
                "log_connector",
            ));

        if ($campaign->get_hash($key) == null) {
            $con = new connector();
            $campaign->add_report_data(null, $unknownId, "unik_purchase");
        }

        $campaign->add_hash($key);
        $db2->unlock();

        $db2->where("phone", $phone);
        $visitor = $db2->getone("visitor");

        $key = $phone . ";purchase;" . date("Y-m-d");
        $db2->setLockMethod("WRITE")
            ->lock(array(
                "report_hash",
                "meta",
                "report_data",
                "visitor",
                "connector",
                "log_connector_hash",
                "log_connector",
            ));
        if ($campaign->get_hash($key) == null) {
            $campaign->add_report_data($visitor, $unknownId, "purchase");
            $campaign->add_report_data($visitor, $unknownId, "purchase_value", $value);

            $update = array();
            $update["purchase"] = 1;
            $update["value"] = $db2->inc($value);
            $db2->where("visitor_id", $visitor['visitor_id']);
            $db2->update("visitor", $update);

        }
        $campaign->add_hash($key);
        $db2->unlock();

/*
$update = array();
$update["value"] = $db2->inc($value);
$db2->where("visitor_id", $visitor["visitor_id"]);
$db2->update("visitor", $update);
 */

    }

    public function get_visitor($visitor_id = null, $phone = null)
    {
        global $app;
        $db2 = $app->db2;

         if($visitor_id == '0' || $visitor_id == 0 || $visitor_id == ''){
             $visitor_id = null;
         }
         if($phone == '' || $phone == '0'){
             $phone = null;
         }

        if ($visitor_id == null and $phone == null) {
            return false;
        }

        $visitor = null;
        if ($visitor_id != null) {
            $visitor_id = str_replace(".", "", trim($visitor_id));
            $visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");

            $db2->where("visitor_id", $visitor_id);
            $visitor = $db2->getone("visitor");
        } 
        if($visitor == null){
            if ($phone != null) {
                $db2->where("phone", $phone);
                $db2->orderBy("created_unix","desc");
                $visitor = $db2->getone("visitor");
            }
        } 
        

        if ($visitor != null) {
            return $visitor;
        } else {
            return false;
        }
    }

    public function create_visitor($phone = null,$is_new_kontak = false)
    {
        global $app;
        $db2 = $app->db2;

        $data_visitor["created"] = date("Y-m-d H:i:s");
        $data_visitor["visit"] = date("Y-m-d H:i:s");
        if ($phone != null) {
            if($is_new_kontak){
              //  $data_visitor["lead"] = 1;
            }else{
                
            }
            $data_visitor["lead"] = 0;
            $data_visitor["phone"] = $phone;
            $data_visitor["waktu_contact"] = date("Y-m-d H:i:s");
        }

        $visitor_id = $db2->insert("visitor", $data_visitor);

        return $visitor_id;
    }

    public function fbwa_personal($phone, $param)
    {
        if (!is_array($param)) {
            return false;
        }
        if (!isset($param["raw"])) {
            return false;
        }
        global $app;
        $db2 = $app->db2;
        $fbwa_ret = false;

        $tmp = json_encode($param);
        $data = [];
        extract($param);
        $raw = json_decode_adv($raw, true);

        if ($raw != null) {
            if (count($raw) > 0) {

                $tmp = [];
                foreach ($raw as $key => $value) {
                    $tmp = $value;
                    break;
                }

                if (isset($tmp["key"]["remoteJid"])) {
                    $data["sender"] = $tmp["key"]["remoteJid"];

                    $tmp2 = explode("@", $data["sender"]);
                    if ($tmp2 != null) {
                        if (count($tmp2) > 1) {
                            $data["phone"] = $tmp2[0];
                            $data["phone_key"] = $db2->func("UNHEX(?)", [md5($tmp2[0])]);
                        }
                    }
                }

                if (isset($tmp["message"]["extendedTextMessage"]["contextInfo"]["externalAdReply"]["ctwaClid"])) {
                    $data["ctwaClid"] = $tmp["message"]["extendedTextMessage"]["contextInfo"]["externalAdReply"]["ctwaClid"];
                }
                if (isset($tmp["message"]["extendedTextMessage"]["contextInfo"]["externalAdReply"]["sourceId"])) {
                    $data["adcopy_id"] = $tmp["message"]["extendedTextMessage"]["contextInfo"]["externalAdReply"]["sourceId"];
                    $fbwa_ret = true;
                }
            }

            if (isset($data["adcopy_id"])) {
                $db2->where("external_key = UNHEX(?)", [sha1($data["adcopy_id"])]);
                $data_report = $db2->getone("report");

             
                    $data_visitor_fbwa["last_campaign"]["source"] = "meta";
                    if(isset($data_report["report_id"])){
                        $data_visitor_fbwa["last_campaign"]["data"]["adcopy_id"] = $data_report["report_id"];
                    }
                    if (isset($data["ctwaClid"])) {
                        $data_visitor_fbwa["last_campaign"]["data"]["ctwaClid"] = $data["ctwaClid"];
                    }
                    $db2->where("phone", $phone);
                    $db2->update("visitor", ["data" => serialize($data_visitor_fbwa)]);

                

            }
            
        }

        return $fbwa_ret;
    }


    public function fbwa_waba($phone, $param, $is_new_kontak=false, $force_report=true)
    {
        if (!is_array($param)) {
            return false;
        }
        if (!isset($param["raw"])) {
            return false;
        }
        global $app;
        $db2 = $app->db2;
        $fbwa_ret = false;

        $tmp = json_encode($param);
        $data = [];
        extract($param);
        $raw = json_decode_adv($raw, true);

        // Track WABA ID untuk di-set ke visitor data
        $current_waba_id = null;

        if ($raw != null) {
            // Handle new WhatsApp Business API structure
            if (isset($raw["object"]) && $raw["object"] == "whatsapp_business_account") {
                if (isset($raw["entry"]) && is_array($raw["entry"])) {
                    foreach ($raw["entry"] as $entry) {
                        // Capture WABA ID dari entry
                        if (isset($entry["id"])) {
                            $current_waba_id = $entry["id"];
                        }
                        
                        if (isset($entry["changes"]) && is_array($entry["changes"])) {
                            foreach ($entry["changes"] as $change) {
                                if (isset($change["value"]["messages"]) && is_array($change["value"]["messages"])) {
                                    foreach ($change["value"]["messages"] as $message) {
                                        // Extract phone from contacts
                                        if (isset($change["value"]["contacts"]) && is_array($change["value"]["contacts"])) {
                                            foreach ($change["value"]["contacts"] as $contact) {
                                                if (isset($contact["wa_id"])) {
                                                    $data["phone"] = $contact["wa_id"];
                                                    $data["phone_key"] = $db2->func("UNHEX(?)", [md5($contact["wa_id"])]);
                                                }
                                            }
                                        }
                                        
                                        // Extract referral data (new structure)
                                        if (isset($message["referral"])) {
                                            if (isset($message["referral"]["ctwa_clid"])) {
                                                $data["ctwaClid"] = $message["referral"]["ctwa_clid"];
                                            }
                                            if (isset($message["referral"]["source_id"])) {
                                                $data["adcopy_id"] = $message["referral"]["source_id"];
                                            }
                                            
                                            // Handle additional referral data for better tracking
                                            if (isset($message["referral"]["source_url"])) {
                                                $data["source_url"] = $message["referral"]["source_url"];
                                            }
                                            if (isset($message["referral"]["source_type"])) {
                                                $data["source_type"] = $message["referral"]["source_type"];
                                            }
                                            if (isset($message["referral"]["headline"])) {
                                                $data["headline"] = $message["referral"]["headline"];
                                            }
                                            if (isset($message["referral"]["media_type"])) {
                                                $data["media_type"] = $message["referral"]["media_type"];
                                            }
                                            if (isset($message["referral"]["image_url"])) {
                                                $data["image_url"] = $message["referral"]["image_url"];
                                            }
                                        }
                                        
                                        // Fallback to old structure
                                        if (isset($message["extendedTextMessage"]["contextInfo"]["externalAdReply"]["ctwaClid"])) {
                                            $data["ctwaClid"] = $message["extendedTextMessage"]["contextInfo"]["externalAdReply"]["ctwaClid"];
                                        }
                                        if (isset($message["extendedTextMessage"]["contextInfo"]["externalAdReply"]["sourceId"])) {
                                            $data["adcopy_id"] = $message["extendedTextMessage"]["contextInfo"]["externalAdReply"]["sourceId"];
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                // Handle old structure
                if (count($raw) > 0) {
                    $tmp = [];
                    foreach ($raw as $key => $value) {
                        $tmp = $value;
                        break;
                    }

                    if (isset($tmp["key"]["remoteJid"])) {
                        $data["sender"] = $tmp["key"]["remoteJid"];

                        $tmp2 = explode("@", $data["sender"]);
                        if ($tmp2 != null) {
                            if (count($tmp2) > 1) {
                                $data["phone"] = $tmp2[0];
                                $data["phone_key"] = $db2->func("UNHEX(?)", [md5($tmp2[0])]);
                            }
                        }
                    }

                    if (isset($tmp["message"]["extendedTextMessage"]["contextInfo"]["externalAdReply"]["ctwaClid"])) {
                        $data["ctwaClid"] = $tmp["message"]["extendedTextMessage"]["contextInfo"]["externalAdReply"]["ctwaClid"];
                    }
                    if (isset($tmp["message"]["extendedTextMessage"]["contextInfo"]["externalAdReply"]["sourceId"])) {
                        $data["adcopy_id"] = $tmp["message"]["extendedTextMessage"]["contextInfo"]["externalAdReply"]["sourceId"];
                    }
                }
            }


            // Set flag CTWA jika ada ctwaClid atau adcopy_id
            $fbwa_ret = isset($data["ctwaClid"]) || isset($data["adcopy_id"]);

            // Update visitor data jika ada data CTWA/WABA
            if ($fbwa_ret || $current_waba_id) {
                // Get existing visitor data to merge
                $db2->where("phone", $phone);
                $visitor = $db2->getone("visitor");
                $existing_data = [];
                
                if ($visitor && !empty($visitor["data"])) {
                    $existing_data = @unserialize($visitor["data"]);
                    if (!is_array($existing_data)) {
                        $existing_data = [];
                    }
                }

                // Prepare data to update
                $data_visitor_fbwa = $existing_data;
                $data_visitor_fbwa["last_campaign"]["source"] = "meta";
                
                // Add adcopy_id if available (use report_id if available, otherwise use original adcopy_id)
                if (isset($data["adcopy_id"]) && $force_report==true) {
                    $db2->where("external_key = UNHEX(?)", [sha1($data["adcopy_id"])]);
                    $data_report = $db2->getone("report");
                    if ($db2->count > 0 && isset($data_report["report_id"])) {
                        $data_visitor_fbwa["last_campaign"]["data"]["adcopy_id"] = $data_report["report_id"];
                    } else {
                        //file_put_contents('log/log_fbwa_waba_notfound.txt', '[' . date('Y-m-d H:i:s') . "]\n" . $db2->getLastQuery(), FILE_APPEND);
                        // Fallback: grab insights from Facebook and create report structure, then save parent report_id
                        try {
                            // try all active facebook integrations (desc by id) until one returns data
                            $db2->where("status", 1);
                            $db2->where("platform", "facebook");
                            $db2->orderBy("id", "DESC");
                            $integrations = $db2->get("integration", null, "id,account_id,platform,type,data");
                            $resolved = false;
                            if ($integrations && is_array($integrations) && count($integrations) > 0) {
                                foreach ($integrations as $integration) {
                                    $access_token = null;
                                    $account_id_fb = null;
                                    if (isset($integration["data"])) {
                                        $tmpData = @unserialize($integration["data"]);
                                        if (is_array($tmpData) && isset($tmpData["access_token"])) {
                                            $access_token = $tmpData["access_token"];
                                        }
                                        $account_id_fb = $integration["account_id"];
                                    }
                                    if (!$access_token || !$account_id_fb) {
                                        continue;
                                    }
                                    $today = date('Y-m-d');
                                    $fields = "ad_id,ad_name,adset_id,adset_name,campaign_id,campaign_name";
                                    $url = "https://graph.facebook.com/v23.0/" . $account_id_fb . "/insights?level=ad&fields=" . $fields .
                                        "&filtering=[{'field':'ad.id','operator':'IN','value':['" . $data["adcopy_id"] . "']}]" .
                                        "&access_token=" . $access_token;

                                    $ch = curl_init();
                                    curl_setopt($ch, CURLOPT_URL, $url);
                                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                                    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
                                    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                                        'Accept: application/json'
                                    ]);
                                    $response = curl_exec($ch);
                                    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                                    curl_close($ch);

                                    if ($response !== false && $http_code >= 200 && $http_code < 300) {
                                        $res = json_decode($response, true);
                                        if (isset($res["data"]) && is_array($res["data"]) && count($res["data"]) > 0) {
                                            $source = "meta";
                                            $r = new report();
                                            $campaigns = [];
                                            foreach ($res["data"] as $value) {
                                                $campaigns[$value["campaign_id"]]["name"] = $value["campaign_name"];
                                                $campaigns[$value["campaign_id"]]["adset"][$value["adset_id"]]["name"] = $value["adset_name"];
                                                $campaigns[$value["campaign_id"]]["adset"][$value["adset_id"]]["adcopy"][$value["ad_id"]]["name"] = $value["ad_name"];
                                            }
                                            foreach ($campaigns as $cid => $campaign) {
                                                $campaign_id = $r->add_report_kolom($source, $campaign["name"], "campaign", $cid, null, true)["data"];
                                                if ($campaign_id) {
                                                    foreach ($campaign["adset"] as $aid => $adset) {
                                                        $adset_id = $r->add_report_kolom($source, $adset["name"], "adset", $aid, $campaign_id, true)["data"];
                                                        if ($adset_id) {
                                                            foreach ($adset["adcopy"] as $adid => $adcopy) {
                                                                $r->add_report_kolom($source, $adcopy["name"], "adcopy", $adid, $adset_id, true);
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                            
                                            // after insert, get parent of this adcopy
                                            $db2->where("external_key = UNHEX(?)", [sha1($data["adcopy_id"])]);
                                            $adcopy_report = $db2->getone("report");
                                            if ($db2->count > 0 && isset($adcopy_report["parent_id"])) {
                                                $data_visitor_fbwa["last_campaign"]["data"]["adcopy_id"] = $adcopy_report["parent_id"];
                                            } else {
                                                $data_visitor_fbwa["last_campaign"]["data"]["adcopy_id"] = $data["adcopy_id"]; // fallback raw
                                            }
                                            $resolved = true;
                                            break;
                                        }
                                    }else{
                                        file_put_contents('log/error_get_insights.txt', '[' . date('Y-m-d H:i:s') . "]\n" . $url . "\n" . $response . "\n", FILE_APPEND);
                                    }
                                }
                            }else{

                            }
                            if (!$resolved) {
                                $data_visitor_fbwa["last_campaign"]["data"]["adcopy_id"] = $data["adcopy_id"]; // fallback if none matched
                            }
                        } catch (Exception $e) {
                            $data_visitor_fbwa["last_campaign"]["data"]["adcopy_id"] = $data["adcopy_id"]; // fallback on exception
                        }
                    }
                }
                
                // Add ctwaClid if available
                if (isset($data["ctwaClid"])) {
                    $data_visitor_fbwa["last_campaign"]["data"]["ctwaClid"] = $data["ctwaClid"];
                }
                
                // Add waba_id if available
                if ($current_waba_id) {
                    $data_visitor_fbwa["last_campaign"]["data"]["waba_id"] = $current_waba_id;
                }
                
                // Add additional referral data if available
                if (isset($data["source_url"])) {
                    $data_visitor_fbwa["last_campaign"]["data"]["source_url"] = $data["source_url"];
                }
                if (isset($data["source_type"])) {
                    $data_visitor_fbwa["last_campaign"]["data"]["source_type"] = $data["source_type"];
                }
                if (isset($data["headline"])) {
                    $data_visitor_fbwa["last_campaign"]["data"]["headline"] = $data["headline"];
                }
                if (isset($data["media_type"])) {
                    $data_visitor_fbwa["last_campaign"]["data"]["media_type"] = $data["media_type"];
                }
                if (isset($data["image_url"])) {
                    $data_visitor_fbwa["last_campaign"]["data"]["image_url"] = $data["image_url"];
                }

                if(isset($data_visitor_fbwa["last_campaign"]["data"]["adcopy_id"])){
                    $db2->where("external_key = UNHEX(?)", [sha1($data_visitor_fbwa["last_campaign"]["data"]["adcopy_id"])]);
                    $adcopy_report = $db2->getone("report");
                    if ($db2->count > 0 ) {
                        $data_visitor_fbwa["last_campaign"]["data"]["adcopy_id"] = $adcopy_report["id"];
                    }
                }
                
                // Update visitor data
                $db2->where("phone", $phone);
                $db2->update("visitor", ["data" => serialize($data_visitor_fbwa)]);
            }
        }
        //print_r('report_id '.$report_id);
        return $fbwa_ret;
    }

    /**
     * Handle Facebook WhatsApp tracking for Qontak
     * 
     * @param string $phone The phone number
     * @param array $param The parameters from Qontak webhook
     * @return bool Returns true if adcopy_id is found and processed, false otherwise
     */
    public function fbwa_qontak($phone, $param) {
        if (!is_array($param)) {
            return false;
        }
        
        global $app;
        $db2 = $app->db2;
        $fbwa_ret = false;
        $data = [];
        
        // Check if we have room description with ctwa_clid
        if (isset($param["room"]["description"])) {
            $description = $param["room"]["description"];
            
            // Extract ctwa_clid from description
            if (preg_match('/ctwa_clid=([^;&\s]+)/', $description, $matches)) {
                $data["ctwaClid"] = trim($matches[1]);
            }
            
            // Extract source_id if available (equivalent to adcopy_id)
            if (preg_match('/source_id=([^;&\s]+)/', $description, $matches)) {
                $data["adcopy_id"] = trim($matches[1]);
                $fbwa_ret = true;
            }
            
            // Extract additional campaign data
            if (preg_match('/campaign_id=([^;&\s]+)/', $description, $matches)) {
                $data["campaign_id"] = trim($matches[1]);
            }
            
            if (preg_match('/ad_id=([^;&\s]+)/', $description, $matches)) {
                $data["ad_id"] = trim($matches[1]);
            }
            
            // If we have adcopy_id, update visitor data
            if (isset($data["adcopy_id"]) || isset($data["ctwaClid"])) {
                // Get existing visitor data to merge
                $db2->where("phone", $phone);
                $visitor = $db2->getone("visitor");
                $existing_data = [];
                
                if ($visitor && !empty($visitor["data"])) {
                    $existing_data = @unserialize($visitor["data"]);
                    if (!is_array($existing_data)) {
                        $existing_data = [];
                    }
                }
                
                // Prepare visitor data
                $data_visitor_fbwa = $existing_data;
                $data_visitor_fbwa["last_campaign"]["source"] = "meta";
                
                // Add adcopy_id if available
                if (isset($data["adcopy_id"])) {
                    $db2->where("external_key = UNHEX(?)", [sha1($data["adcopy_id"])]);
                    $data_report = $db2->getone("report");
                    
                    if ($db2->count > 0 && isset($data_report["report_id"])) {
                        $data_visitor_fbwa["last_campaign"]["data"]["adcopy_id"] = $data_report["report_id"];
                    } else {
                        $data_visitor_fbwa["last_campaign"]["data"]["adcopy_id"] = $data["adcopy_id"];
                    }
                }
                
                // Add ctwaClid if available
                if (isset($data["ctwaClid"])) {
                    $data_visitor_fbwa["last_campaign"]["data"]["ctwaClid"] = $data["ctwaClid"];
                }
                
                // Add additional campaign data
                if (isset($data["campaign_id"])) {
                    $data_visitor_fbwa["last_campaign"]["data"]["campaign_id"] = $data["campaign_id"];
                }
                
                if (isset($data["ad_id"])) {
                    $data_visitor_fbwa["last_campaign"]["data"]["ad_id"] = $data["ad_id"];
                }
                
                // Update visitor record
                $db2->where("phone", $phone);
                $db2->update("visitor", ["data" => serialize($data_visitor_fbwa)]);
            }
        }
        
        return $fbwa_ret;
    }
}
