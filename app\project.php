<?php

class project
{
    function AddProjectData($project_id, $param) {
        global $app;
        $db2 = $app->db2;
        $param = json_decode($param,true);

        $ret = array();
        $ret["code"] = 1;
    
        if ($project_id = 0 || $project_id == null) {
            $ret["code"] = 0;
            $ret["msg"] = "invalid project id";
            return $ret;
        }
        $m = new meta();
    
    /////////////////////// project //////////////////////////////
        if (isset($param["project"]["timezone"])) {
            $m->set_meta("timezone", $param["project"]["timezone"]);
        }
        if (isset($param["project"]["currency"])) {
            $m->set_meta("currency", $param["project"]["currency"]);
        }
        if (isset($param["project"]["mql"])) {
            $m->set_meta("mql", $param["project"]["mql"]);
        }
    
        if (isset($param["project"]["subdomain"]) && $param["project"]["subdomain"] != "") {
            $subdomain = $param["project"]["subdomain"];
            $subdomain = strtolower($subdomain);
            $subdomain = str_replace("https://", "", $subdomain);
            $subdomain = str_replace("http://", "", $subdomain);
            $tmp = explode("/", $subdomain);
            if (count($tmp) > 1) {
                $subdomain = $tmp[0];
            }
    
            $d = new subdomain();
            $msg = $d->add_subdomain($subdomain);
    
            if ($msg['code'] == 1) {
                $co = file_get_contents('config/c.gass.co.id.php');
                file_put_contents('config/' . $subdomain . '.php', $co);
            } else {
                $ret["code"] = 0;
                $ret["msg"][] = $msg["msg"];
            }
        }
    /////////////////////// project //////////////////////////////
    
    /////////////////////// cs setting //////////////////////////////
    
        if (isset($param["cs_setting"]["pembagian_cs"])) {
            $m->set_meta("pembagian_cs", $param["cs_setting"]["pembagian_cs"]);
        }
        if (isset($param["cs_setting"]["default_msg"])) {
            $m->set_meta("default_msg", $param["cs_setting"]["default_msg"]);
        }
        if (isset($param["cs_setting"]["format_id"])) {
            $m->set_meta("format_id", $param["cs_setting"]["format_id"]);
        }
        if (isset($param["cs_setting"]["format_prospek"])) {
            $m->set_meta("format_prospek", $param["cs_setting"]["format_prospek"]);
        }
        if (isset($param["cs_setting"]["format_purchase"])) {
            $m->set_meta("format_purchase", $param["cs_setting"]["format_purchase"]);
        }
        if (isset($param["cs_setting"]["format_purchase_value"])) {
            $m->set_meta("format_purchase_value", $param["cs_setting"]["format_purchase_value"]);
        }
    /////////////////////// cs setting //////////////////////////////
    
    ////////////////////// connector ////////////////////////////////
        if (isset($param["connectors"])) {
            $connectors = $param["connectors"];
            if (is_array($connectors)) {
                $con = new connector();
                foreach ($connectors as $key => $connector) {
                    $msg = $con->add($connector["type"], $connector["name"], $connector["data"]);
    
                    if ($msg["code"] == 0) {
                        $ret["code"] = 0;
                        $ret["msg"][] = $msg["msg"];
                    }
                }
            }
        }
    ////////////////////// end connector ////////////////////////////////
    
    ////////////////////// cs ////////////////////////////////
    
        if (isset($param["customer_services"])) {
            $customer_services = $param["customer_services"];
            if (is_array($customer_services)) {
                $cs_class = new cs();
                $con = new connector();
    
                $divisi = $db2->getone("divisi", "HEX(divisi_key) as divisi_key ");
                
                $post_param["act"] = "client_cs_add";
                $post_param["project_key"] = $param['project_key'];
                $post_param["token"] = $param['token'];
                $post_param["autodc"] = 0;
                $post_param["without_scan"] = 0;
                $post_param["divisi"][] = $divisi["divisi_key"];

                
                foreach ($customer_services as $key => $cs) {
                    $post_param["name"] = $cs['name'];
                    $post_param["phone"] = $cs['phone'];

                    $msg = post_x_contents($post_param,"https://n.gass.co.id/api.html");
                    $msg = json_decode($msg,true);
                    /*
                    $msg = $cs_class->add_cs($project_id, $cs['name'], $cs['phone'], true, false, $divisi["divisi_key"]);
    
                    if ($msg["code"] == 0) {
                        $ret["code"] = 0;
                        $ret["msg"][] = $msg["msg"];
                    }
                    */
                }
                
            }
        }
        
    ////////////////////// end cs ////////////////////////////////
    
        return $ret;
    }

    function set_project_setting($param)
    {
        global $app;
        $db2 = $app->db2;

        $m = new meta();


        if (isset($param["convertion_record"]))
        {
            $m->set_meta("convertion_record", $param["convertion_record"]);
        }

        if (isset($param["timezone"]))
        {
            $m->set_meta("timezone", $param["timezone"]);
        }

        if (isset($param["mql"]))
        {
            $m->set_meta("mql", $param["mql"]);
        }

        if (isset($param["currency"]))
        {
            $m->set_meta("currency", $param["currency"]);
        }

        if (isset($param["google_connector"]))
        {
            if($param["google_connector"] == 0 || $param["google_connector"] == false){
                $param["google_connector"] = "false";
            }

            $m->set_meta("google_connector", $param["google_connector"]);
        }

        if (isset($param["unknown_connector"]))
        {
            if($param["unknown_connector"] == 0 || $param["unknown_connector"] == false){
                $param["unknown_connector"] = "false";
            }

            $m->set_meta("unknown_connector", $param["unknown_connector"]);
        }

        if (isset($param["shortlink_connector"]))
        {
            if($param["shortlink_connector"] == 0 || $param["shortlink_connector"] == false){
                $param["shortlink_connector"] = "false";
            }

            $m->set_meta("shortlink_connector", $param["shortlink_connector"]);
        }

        
        if (isset($param["organic_connector"]))
        {
            if($param["organic_connector"] == 0 || $param["organic_connector"] == false){
                $param["organic_connector"] = "false";
            }
           
            $m->set_meta("organic_connector", $param["organic_connector"]);
        }

        return $this->get_project_setting();
    }

    function get_project_setting()
    {
        global $app;
        $db2 = $app->db2;

        $m = new meta();

        $tmp =  $m->get_meta("google_connector");
        if($tmp["code"] == 1){
            $ret["google_connector"] = $tmp["result"]["data"];
        }else{
            $ret["google_connector"] = "";
        }

        $tmp =  $m->get_meta("unknown_connector");
        if($tmp["code"] == 1){
            $ret["unknown_connector"] = $tmp["result"]["data"];
        }else{
            $ret["unknown_connector"] = "";
        }

        $tmp =  $m->get_meta("shortlink_connector");
        if($tmp["code"] == 1){
            $ret["shortlink_connector"] = $tmp["result"]["data"];
        }else{
            $ret["shortlink_connector"] = "";
        }

        $tmp =  $m->get_meta("organic_connector");
        if($tmp["code"] == 1){
            $ret["organic_connector"] = $tmp["result"]["data"];
        }else{
            $ret["organic_connector"] = "";
        }
        
        $ret["convertion_record"] = $m->get_meta("convertion_record")["result"]["data"];
        $ret["timezone"] = $m->get_meta("timezone")["result"]["data"];
        $ret["mql"] = $m->get_meta("mql")["result"]["data"];
        $ret["currency"] = $m->get_meta("currency")["result"]["data"];
        $ret["google_kontak"] = "https://";
        $ret["google_mql"] = "https://";
        $ret["google_prospek"] = "https://";
        $ret["google_purchase"] = "https://";
        //$public_ip = file_get_contents('http://ipecho.net/plain');
        //$ret["ip_arecord"] = $public_ip;
        return $ret;
    }

    function add_project($project_id,$project_key,$project_name)
    {
        global $app;
        $db = $app->db;

        $data["project_id"] = $project_id;
        $data["project_key"] = $db->func("UNHEX(?)",[$project_key]);
        $data["name"] = $project_name;

        if($db->insert("project",$data))
        {
            $ret = $this->create_table($project_id);
            
        }else{
            $ret["code"] = 0;
            $ret["data"] = "error creating project";
        }

        return $ret;
    }

    function create_table($project_id)
    {

        $setDB2 = array(
            'host' => "localhost",
            'username' => 'client_' . $project_id,
            'password' => 'C4J5ai2pCwyxTaNa',
            'db' => 'client_' . $project_id,
            'port' => 3306,
            'charset' => 'utf8mb4'
        );

        $db2 = new db($setDB2);

        include ("default_table.php");

        $statements = $this->extractdump($q);

        $error = array();
       
        foreach ($statements as $statement)
        {

            $statement = trim($statement);
            if (!empty($statement))
            {
                try
                {
                    if (!$db2->rawQuery($statement))
                    {
                        if($db2->getLastError() != ""){
                        $error[] = $db2->getLastError();
                        }
                    }
                }
                catch(customException $e)
                {
                    if($e->errorMessage() != ""){
                    $error[] = $e->errorMessage();
                    }
                }
            }
        }

        ////////////////////////////////////// db3

        $setDB3 = array(
            'host' => "localhost",
            'username' => 'client_' . $project_id."_old",
            'password' => 'C4J5ai2pCwyxTaNa',
            'db' => 'client_' . $project_id."_old",
            'port' => 3306,
            'charset' => 'utf8mb4'
        );

        $db3 = new db($setDB3);

        include ("default_table_old.php");

        $statements = $this->extractdump($q);

        $error = array();
       
        foreach ($statements as $statement)
        {

            $statement = trim($statement);
            if (!empty($statement))
            {
                try
                {
                    if (!$db3->rawQuery($statement))
                    {
                        if($db3->getLastError() != ""){
                        $error[] = $db3->getLastError();
                        }
                    }
                }
                catch(customException $e)
                {
                    if($e->errorMessage() != ""){
                    $error[] = $e->errorMessage();
                    }
                }
            }
        }

        /////////////////////////////////////// end db3

        if(count($error) >= 1){
          $ret["code"] = 0;
          $ret["data"] = $error;
          $ret["msg"] = 'error create table';
          return $ret;
        }else{
          $this->insert_default_data($project_id);
          $ret["code"] = 1;
          $ret["msg"] = 'success create table';
          return $ret;
        }
    }

    function insert_default_data($project_id)
    {
      $setDB2 = array(
            'host' => "localhost",
            'username' => 'client_' . $project_id,
            'password' => 'C4J5ai2pCwyxTaNa',
            'db' => 'client_' . $project_id,
            'port' => 3306,
            'charset' => 'utf8mb4'
        );

      $db2 = new db($setDB2);

      ////////////// data for unknown report
      $q = "INSERT IGNORE INTO `report` (`report_id`, `source`, `parent_id`, `type`, `name`, `external_id`, `external_key`) VALUES ('1', 'Unknown', '0', 'campaign', 'Unknown', '1', UNHEX('356a192b7913b04c54574d18c28d46e6395428ab'));";
      $db2->rawQuery($q);
      


      $q = "INSERT IGNORE INTO `meta` ( `meta_key`, `name`, `value`) VALUES
      (0xb2c6cc48f97ccd71b16d31d88fc177a6, 'timezone', 'Asia/Bangkok');";
      $db2->rawQuery($q);

      assign_child_db($project_id);
      $d = new divisi();
      $d->add_divisi("Lead");

      $m = new meta();
      $m->set_meta("convertion_record", "realtime");       
      $m->set_meta("currency", "IDR");  
      $m->set_meta("timezone", "Asia/Bangkok");
      $m->set_meta("mql", "5");
      $m->set_meta("pembagian_cs", "urut_impression");
      $m->set_meta("default_msg", "Boleh minta info produknya");
    }

    function extractdump($sqlString)
    {
        $createStatements = [];
        $tables = explode("\n", $sqlString);

        $statement = array();

        foreach ($tables as $table)
        {
            $include = true;
            $table = trim(preg_replace('/\s\s+/', ' ', $table));
            if (trim($table) == "" || trim($table) == " " || trim($table) == "\n")
            {
                $include = false;
            }
            elseif (substr($table, 0, 2) == "--")
            {
                $include = false;
            }
            elseif (substr($table, 0, 3) == "SET")
            {
                $include = false;
            }
            elseif (substr($table, 0, 5) == "START")
            {
                $include = false;
            }
            elseif (substr($table, 0, 6) == "COMMIT")
            {
                $include = false;
            }

            if ($include)
            {
                $statement[] = $table;
            }
        }

        $q = implode("\n", $statement);

        $statements = explode(';', $q);

        unset($statements[count($statements) - 1]);

        return $statements;
    }

    function extractCreateAndAlterStatements($sqlDump)
    {
        // Regular expression pattern to match the CREATE TABLE statement for `connector`
        $patternCreate = '/CREATE TABLE `connector` \((.*?)\)(?=;)/ms';

        // Regular expression pattern to match the ALTER TABLE statement for `log_connector`
        $patternAlter = '/ALTER TABLE `log_connector` \((.*?)\)(?=;)/ms';

        // Match the CREATE TABLE statement for `connector`
        preg_match($patternCreate, $sqlDump, $matchesCreate);

        // Match the ALTER TABLE statement for `log_connector`
        preg_match($patternAlter, $sqlDump, $matchesAlter);

        $result = array();

        if (!empty($matchesCreate[0]))
        {
            $result['create_table'] = $matchesCreate[0];
        }
        else
        {
            $result['create_table'] = "CREATE TABLE statement not found for table 'connector'.";
        }

        if (!empty($matchesAlter[0]))
        {
            $result['alter_table'] = $matchesAlter[0];
        }
        else
        {
            $result['alter_table'] = "ALTER TABLE statement not found for table 'log_connector'.";
        }

        return $result;
    }





}
