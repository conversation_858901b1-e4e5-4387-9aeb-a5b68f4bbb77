<?php 


function get_info_by_phone($param){

    
    $msg = array();
    $gump = new GUMP(); 
    $msg['code'] = 0 ;
    $gump->validation_rules(array(
        'project_id'      => 'required',
        'phone'      => 'required'
        ));
    $validated = $gump->run($param);
    if($validated === false) {
        $msg['code'] = 0 ;
        $msg['msg'] = $gump->get_readable_errors(true); 
    }else{

        assign_child_db($param['project_id']);
        global $app;
        $db2 = $app->db2;
        $db2->where("phone",$param['phone']);
        $visitor =$db2->getone("visitor","phone,data");

        if(!empty($visitor["data"])){
            $visitor['data'] = unserialize($visitor['data']);
        }

        return $visitor;
    }

}