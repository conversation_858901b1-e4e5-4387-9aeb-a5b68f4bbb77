<?php


//// data yg di lempar
/*
$post = [
			'act' => 'wa_send',
			'sender' => '628113608550',
			'message' =>  'test',
			'type' => 'text',
			'to' => '628985606632',
		];
		
$forward_to = ["http://xxx.xxx.com/api.html"];
*/

function clean_string($input){
    $input = preg_replace('/[\x00-\x1F\x7F\xC2\xA0]+/', ' ', $input); // Replace unwanted chars with space
    return $input;
}

function mergeAndSumTwoArrays($array1, $array2) {
    // Combine both arrays into one
    $combinedArray = array_merge($array1, $array2);

    // Initialize an empty result array
    $result = [];

    foreach ($combinedArray as $item) {
        $name = $item['name'];
        $key = $item['report_key'];

        // Create a unique key using both name and report_key
        $unique_key = $name . '|' . $key;

        // If the unique key already exists, sum the report_value
        if (isset($result[$unique_key])) {
            $result[$unique_key]['report_value'] += (int) $item['report_value'];
        } else {
            // Otherwise, add the item to the result array
            $result[$unique_key] = $item;
            $result[$unique_key]['report_value'] = (int) $item['report_value']; // Ensure report_value is an integer
        }
    }

    // Reset the result array to be indexed numerically
    return array_values($result);
}

function sendPostRequestWithoutWaiting($url, $data)
{
    // Initialize a cURL session
    $ch = curl_init();

    // Set the URL and other options
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT_MS, 100); // Timeout in milliseconds

    // Execute the cURL session
    $response = curl_exec($ch);

    // Check for errors
    if (curl_errno($ch)) {
        $error_msg = curl_error($ch);
        echo "cURL error: $error_msg";
    } else {
        // Show the result
        echo "Response: $response";
    }

    // Close the cURL session
    curl_close($ch);
}

function containsHuruf($str) {
    // Regular expression to match alphabetic characters
    return preg_match('/[a-zA-Z]/', $str);
}
function vid_to_raw($vid)
{
    $raw_vid = convBase($vid, "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
            $raw_vid = str_split($raw_vid, 4);
            $raw_vid = implode(".", $raw_vid);
    return $raw_vid;
}
function raw_to_vid($raw_vid)
{
    $vid = str_replace(".","",$raw_vid);
    $vid = convBase($vid, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");
          
    return $vid;
}
function forwarder($post,$forward_to,$forwarder_url = NULL, $quee =false)
{
    return false;
	if($post["pesan"] == false)
    {
        return false;
    }
    if($quee == false){
        $msg['act'] = 'post_webhooks';
        $msg['data'] = $post;
        $msg['webhooks'] = $forward_to;

        /////////////////////// add task rabbitmq
        $fields['act'] = 'mq_add_task';
        $fields['msg'] = json_encode($msg);

        $header[0] = "Accept-Language: en";
        $header[]  = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
        $ch        = curl_init();
        if($forwarder_url != NULL)
        {
            curl_setopt($ch, CURLOPT_URL, $forwarder_url);
        }
        else{
            curl_setopt($ch, CURLOPT_URL, 'http://10.104.0.52/api.php');
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;
    }else{
        $url_archive = str_replace("10.104.0.56","128.199.95.122", $forward_to[0]);
        $data = [
            "url" => $url_archive,
            "method" => "POST",
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded"
            ],
            "body" => $post
        ];
        
        $ch = curl_init("https://queue.gass.co.id/add-url-advanced");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Content-Type: application/json"
        ]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        $response = curl_exec($ch);
        curl_close($ch);
        return $response;
    }	

}

function generate_memcached_key($functionName, ...$parameters) {
    $key = $functionName . '_'; // Start with the function name
    foreach ($parameters as $param) {
        if (is_array($param) || is_object($param)) {
            // If the parameter is an array or object, serialize it
            $key .= serialize($param) . '_';
        } else {
            // For other types of parameters, convert to string and append
            $key .= strval($param) . '_';
        }
    }
    return $key;
}

function clean_url_param($url) {
    $list_param = ["gcn", "gadsn", "gadcn", "gcid", "gadsid", "gadcid", "gs", "fbclid", "ttclid", "gclid", "gbraid", "utm_source", "utm_medium", "utm_campaign", "utm_content", "utm_term", "click_id", "CampaignID", "adSETID", "CreativeID", "pixel_id"];
    foreach ($list_param as $key => $value) {
        $url = removeqsvar($url, $value);
    }
    return $url;
}

function removeqsvar($url, $varname) {
    list($urlpart, $qspart) = array_pad(explode('?', $url), 2, '');
    parse_str($qspart, $qsvars);
    unset($qsvars[$varname]);
    $newqs = http_build_query($qsvars);
    if (strlen($newqs) > 0) {
        return $urlpart . '?' . $newqs;
    } else {
        return $urlpart;
    }
}

function get_client_ip() {
    $ipaddress = '';
    if (getenv('HTTP_CLIENT_IP')) {
        $ipaddress = getenv('HTTP_CLIENT_IP');
    } elseif (getenv('HTTP_X_FORWARDED_FOR')) {
        $ipaddress = getenv('HTTP_X_FORWARDED_FOR');
    } elseif (getenv('HTTP_X_FORWARDED')) {
        $ipaddress = getenv('HTTP_X_FORWARDED');
    } elseif (getenv('HTTP_FORWARDED_FOR')) {
        $ipaddress = getenv('HTTP_FORWARDED_FOR');
    } elseif (getenv('HTTP_FORWARDED')) {
        $ipaddress = getenv('HTTP_FORWARDED');
    } elseif (getenv('REMOTE_ADDR')) {
        $ipaddress = getenv('REMOTE_ADDR');
    } else {
        $ipaddress = false;
    }

    return $ipaddress;
}

function json_decode_adv($string, $opt = NULL) { # list from www.json.org: (\b backspace, \f formfeed)


    $string = str_replace("\\\\", "\\", $string);
    $json   = json_decode(preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $string), true);
    
    if($json == NULL)
    {
        $json = fixAndDecodeJson($string);
    }
    
    return $json;
   

  
}

function fixAndDecodeJson($jsonString) {
    // Find the position of the content field
    $contentStart = strpos($jsonString, '"content":"{');
    if ($contentStart !== false) {
        // Find the end of the content field by counting braces
        $braceCount = 0;
        $contentEnd = $contentStart + 11; // Skip past '"content":"{'
        $inString = false;
        $escaped = false;
        
        for ($i = $contentEnd; $i < strlen($jsonString); $i++) {
            $char = $jsonString[$i];
            
            if ($escaped) {
                $escaped = false;
                continue;
            }
            
            if ($char === '\\') {
                $escaped = true;
                continue;
            }
            
            if ($char === '"' && !$escaped) {
                $inString = !$inString;
                continue;
            }
            
            if (!$inString) {
                if ($char === '{') {
                    $braceCount++;
                } elseif ($char === '}') {
                    $braceCount--;
                    if ($braceCount === 0) {
                        $contentEnd = $i + 1;
                        break;
                    }
                }
            }
        }
        
        // Extract the content JSON and escape it properly
        $contentJson = substr($jsonString, $contentStart + 11, $contentEnd - $contentStart - 12);
        $escapedContent = str_replace('"', '\"', $contentJson);
        
        // Replace the content field with properly escaped version
        $fixedString = substr($jsonString, 0, $contentStart + 11) . $escapedContent . substr($jsonString, $contentEnd - 1);
    } else {
        $fixedString = $jsonString;
    }
    
    return json_decode($fixedString, true);
}

function assign_child_db($project_id, $server = "localhost") {
    global $app;
    $app->project_id = $project_id;
    $setDB2 = array('host' => $server, 'username' => 'client_' . $project_id, 'password' => 'C4J5ai2pCwyxTaNa', 'db' => 'client_' . $project_id, 'port' => 3306, 'charset' => 'utf8mb4');

    $app->db2 = new db($setDB2);

    $setDB3   = array('host' => $server, 'username' => 'client_' . $project_id . "_old", 'password' => 'C4J5ai2pCwyxTaNa', 'db' => 'client_' . $project_id . "_old", 'port' => 3306, 'charset' => 'utf8mb4');
    $app->db3 = new db($setDB3);

    $m        = new meta();
    $timezone = $m->get_meta("timezone");
    if ($timezone["code"] == 1) {
        @date_default_timezone_set($timezone["result"]["data"]);
    }
}

function convBase($numberInput, $fromBaseInput, $toBaseInput) {
    if ($fromBaseInput == $toBaseInput) {
        return $numberInput;
    }

    $fromBase  = str_split($fromBaseInput, 1);
    $toBase    = str_split($toBaseInput, 1);
    $number    = str_split($numberInput, 1);
    $fromLen   = strlen($fromBaseInput);
    $toLen     = strlen($toBaseInput);
    $numberLen = strlen($numberInput);
    $retval    = '';
    if ($toBaseInput == '0123456789') {
        $retval = 0;
        for ($i = 1; $i <= $numberLen; $i++) {
            $retval = bcadd($retval, bcmul(array_search($number[$i - 1], $fromBase), bcpow($fromLen, $numberLen - $i)));
        }

        return $retval;
    }
    if ($fromBaseInput != '0123456789') {
        $base10 = convBase($numberInput, $fromBaseInput, '0123456789');
    } else {
        $base10 = $numberInput;
    }

    if ($base10 < strlen($toBaseInput)) {
        return $toBase[$base10];
    }

    while ($base10 != '0') {
        $retval = $toBase[bcmod($base10, $toLen)] . $retval;
        $base10 = bcdiv($base10, $toLen, 0);
    }
    return $retval;
}

function simple_crypt($string, $action = 'e') {
    // you may change these values to your own
    $secret_key = 'GassV2*!';
    $secret_iv  = 'Kerja123!';

    $output         = false;
    $encrypt_method = "AES-256-CBC";
    $key            = hash('sha256', $secret_key);
    $iv             = substr(hash('sha256', $secret_iv), 0, 16);

    if ($action == 'e') {
        $output = base64_encode(openssl_encrypt($string, $encrypt_method, $key, 0, $iv));
    } elseif ($action == 'd') {
        $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $iv);
    }

    return $output;
}

function post_flip($fields) {
    $fields    = http_build_query($fields);
    $header[0] = "Accept-Language: en";
    $header[]  = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
    $header[]  = "Content-Type: application/x-www-form-urlencoded";
    $ch        = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://pay.gass.co.id/api.html');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
    $data = curl_exec($ch);
    curl_close($ch);
    return json_decode($data, true);
}

function getAbbreviation($string) {
    $words        = explode(" ", $string); // Memisahkan kalimat menjadi array kata-kata
    $abbreviation = "";
    $i            = 0;
    foreach ($words as $word) {
        if ($i < 2) {
            $abbreviation .= strtoupper(substr($word, 0, 1)); // Mengambil huruf pertama dari setiap kata dan membuat hurufnya menjadi kapital
        }
        $i++;
    }
    return $abbreviation;
}

function convertToHigherUnit($sizeInBytes) {
    $units         = ['B', 'KB', 'MB', 'GB', 'TB'];
    $convertedSize = $sizeInBytes;
    $unitIndex     = 0;

    while ($convertedSize >= 1024 && $unitIndex < count($units) - 1) {
        $convertedSize /= 1024;
        $unitIndex++;
    }

    return number_format($convertedSize, 2) . ' ' . $units[$unitIndex];
}

function validate_param($rules, $param) {
    $gump        = new GUMP();
    $msg['code'] = 0;
    $gump->validation_rules($rules);
    $validated = $gump->run($param);
    if ($validated === false) {
        $msg['code'] = 0;
        $msg['msg']  = $gump->get_readable_errors(true);
        echo json_encode($msg);
        die();
    }
}

function get_IP_address() {
    foreach (array('HTTP_CLIENT_IP',
        'HTTP_X_FORWARDED_FOR',
        'HTTP_X_FORWARDED',
        'HTTP_X_CLUSTER_CLIENT_IP',
        'HTTP_FORWARDED_FOR',
        'HTTP_FORWARDED',
        'REMOTE_ADDR') as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $IPaddress) {
                $IPaddress = trim($IPaddress); // Just to be safe

                if (filter_var($IPaddress,
                    FILTER_VALIDATE_IP,
                    FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)
                    !== false) {

                    return $IPaddress;
                }
            }
        }
    }
}

function checkTimeDifference($time1, $time2) {
    $datetime1 = new DateTime($time1);
    $datetime2 = new DateTime($time2);
    $interval  = $datetime1->diff($datetime2);
    $minutes   = $interval->format('%i');
    //echo $minutes;
    return $minutes <= 10;
}

function isBot() {
    if (isset($_SERVER['HTTP_USER_AGENT']) && preg_match('/bot|crawl|slurp|spider|mediapartners/i', $_SERVER['HTTP_USER_AGENT'])) {
        return true;
    } else {
        return false;
    }
}

function validDom($url) {
    if (strpos($url, 'http://') === false) {
        $url = 'http://' . $url;
    } elseif (strpos($url, 'https://') === false) {
        $url = 'https://' . $url;
    }

    $domain = parse_url($url, PHP_URL_HOST);
    if (filter_var($domain, FILTER_VALIDATE_DOMAIN)) {
        return $domain;
    } else {
        return false;
    }
}

function request_api($fields) {
    $header[0] = "Accept-Language: en";
    $header[]  = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
    $ch        = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://***********/api.html?' . http_build_query($fields));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    //curl_setopt($ch, CURLOPT_POST, true);
    //curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
    $data = curl_exec($ch);
    curl_close($ch);
    //print_r($data);
    return json_decode($data, true);
}

function request_api_mq($fields) {
    $header[0] = "Accept-Language: en";
    $header[]  = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
    $ch        = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://159.223.32.107/api.php?' . http_build_query($fields));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    //curl_setopt($ch, CURLOPT_POST, true);
    //curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
    $data = curl_exec($ch);
    curl_close($ch);
    return json_decode($data, true);
}


function get_browser_name($user_agent) {
    $t = strtolower($user_agent);
    $t = " " . $t;
    if (strpos($t, 'opera') || strpos($t, 'opr/')) {
        return 'Opera';
    } elseif (strpos($t, 'edge')) {
        return 'Edge';
    } elseif (strpos($t, 'chrome')) {
        return 'Chrome';
    } elseif (strpos($t, 'safari')) {
        return 'Safari';
    } elseif (strpos($t, 'firefox')) {
        return 'Firefox';
    } elseif (strpos($t, 'msie') || strpos($t, 'trident/7')) {
        return 'Internet Explorer';
    }

    return 'Unkown';
}
function isMobile() {
    return preg_match(
        "/(android|avantgo|blackberry|bolt|boost|cricket|docomo|fone|hiptop|mini|mobi|palm|phone|pie|tablet|up\.browser|up\.link|webos|wos)/i",
        $_SERVER["HTTP_USER_AGENT"]
    );
}


function post_gateway($postdata, $url, $customReq = 'POST') {

    $fields    = http_build_query($postdata);
    $header[0] = "Accept-Language: en";
    $header[]  = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
    $header[]  = "Content-Type: application/json";
    $ch        = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
    curl_setopt($ch, CURLOPT_TIMEOUT, 0);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $customReq);
    if ($postdata != '') {
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
    }
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
}
function get_unik($nominal) {
    global $app;
    $db = $app->db;
    $db->where("status", 2, "<");
    $db->where("total", $nominal, ">");
    $res = $db->get("x_order2");
    $db->where("waktu >= current_date - interval 3 day");
    $db->where("total", $nominal, ">");
    $res2   = $db->get("x_order2");
    $ii     = 0;
    $unik   = 0;
    $length = count($res);
    $unik   = $nominal + 1;
    for ($i = 0; $i < 10000; $i++) {
        $dup = false;
        foreach ($res as $key => $value) {
            $total = $value["total"];
            if ($unik == $total) {
                $dup = true;
                break;
            }
        }
        foreach ($res2 as $key2 => $value2) {
            $total = $value2["total"];
            if ($unik == $total) {
                $dup = true;
                break;
            }
        }
        if ($dup == false) {
            break;
        }
        $unik++;
    }
    $ret["angka_unik"] = $unik - $nominal;
    $ret["total"]      = $unik;
    return $ret;
}
function hyphenate($str) {
    return implode("-", str_split($str, 4));
}
//function str_contains($string, $array, $caseSensitive = true)
//{
//    $stripedString = $caseSensitive ? str_replace($array, '', $string) : str_ireplace($array, '', $string);
//    return strlen($stripedString) !== strlen($string);
//}
function hp($nohp) {
    $hp = "";
    if ($nohp[0] != '0' && $nohp[0] != '+' && $nohp[0] != '6') {
        $nohp = "0" . $nohp;
    }
    $nohp = str_replace(" ", "", $nohp);
    $nohp = str_replace("-", "", $nohp);
    $nohp = str_replace(" ", "", $nohp);
    $nohp = str_replace("(", "", $nohp);
    $nohp = str_replace(")", "", $nohp);
    $nohp = str_replace(".", "", $nohp);
    if (!preg_match('/[^+0-9]/', trim($nohp))) {
        if (substr(trim($nohp), 0, 3) == '+62') {
            $hp = trim($nohp);
        }
        if (substr(trim($nohp), 0, 2) == '62') {
            $hp = '+' . trim($nohp);
        } elseif (substr(trim($nohp), 0, 1) == '0') {
            $hp = '+62' . substr(trim($nohp), 1);
        }
    }
    $hp = str_replace("+", "", $hp);
    return $hp;
}
function hp_x($nohp) {
    $hp   = "";
    $nohp = str_replace(" ", "", $nohp);
    $nohp = str_replace("-", "", $nohp);
    $nohp = str_replace(" ", "", $nohp);
    $nohp = str_replace("(", "", $nohp);
    $nohp = str_replace(")", "", $nohp);
    $nohp = str_replace(".", "", $nohp);
    $hp   = str_replace("+", "", $nohp);
    return $hp;
}
function get_contents($url) {
    if (function_exists('curl_exec')) {
        $header[0] = "Accept-Language: en";
        $header[]  = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
        $header[]  = "Pragma: no-cache";
        $header[]  = "Cache-Control: no-cache";
        $header[]  = "Accept-Encoding: gzip,deflate";
        $header[]  = "Content-Encoding: gzip";
        $header[]  = "Content-Encoding: deflate";
        $ch        = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_ENCODING, 'gzip');
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        curl_setopt($ch, CURLOPT_REFERER, '');
        $data = curl_exec($ch);
        curl_close($ch);
    } else {
        $data = @file_get_contents($url);
    }
    return $data;
}
function replaceTXT($txt) {
    $txt = str_replace("WAHANA", "", strtolower($txt));
    $txt = str_replace("wahana", "", strtolower($txt));
    $txt = str_replace("JNE", "", strtolower($txt));
    $txt = str_replace("jne", "", strtolower($txt));
    $txt = str_replace("JNT", "", strtolower($txt));
    $txt = str_replace("jnt", "", strtolower($txt));
    $txt = str_replace("agen wpl", "", strtolower($txt));
    $txt = str_replace("aramex", "", strtolower($txt));
    $txt = str_replace("TNT", "", strtolower($txt));
    $txt = str_replace("tnt", "", strtolower($txt));
    $txt = str_replace("dhl", "", strtolower($txt));
    return ucfirst($txt);
}
function post_contents_api($fields) {
    //$header[0] = "Accept-Language: en";
    // $header[]  = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
    // $header[]  = "Pragma: no-cache";
    //$header[]  = "Cache-Control: no-cache";
    //$header[]  = "Accept-Encoding: gzip,deflate";
    //$header[]  = "Content-Encoding: gzip";
    // $header[]  = "Content-Encoding: deflate";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://hiroexpress.com/apiv2");
    curl_setopt($ch, CURLOPT_POST, true);
    // curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    // curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
}
function post_contents($url, $post, $referer = '', $host = '') {
    $header[0] = "Accept-Language: en";
    $header[]  = "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8";
    $header[]  = "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:70.0) Gecko/20100101 Firefox/70.0";
    if ($host) {
        $header[] = "Host: " . $host;
    }

    $header[] = "Pragma: no-cache";
    $header[] = "Accept-Encoding: gzip,deflate";
    $header[] = "Content-Type: text/html; charset=UTF-8";
    $ch       = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    if ($referer) {
        curl_setopt($ch, CURLOPT_REFERER, $referer);
    }

    curl_setopt($ch, CURLOPT_COOKIEJAR, 'test.dat');
    curl_setopt($ch, CURLOPT_COOKIEFILE, 'test.dat');
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, is_array($post) ? http_build_query($post) : $post);
    $result = curl_exec($ch);
    curl_close($ch);
    print_r($ch);
    return $result;
}
function post_x_contents($post, $url) {
    $header[0] = "Accept-Language: en";
    $header[]  = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.0; de; rv:*******) Gecko/20100401 Firefox/3.6.3";
    $ch        = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, is_array($post) ? http_build_query($post) : $post);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
}

function post_fb($url) {
    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL            => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING       => '',
        CURLOPT_MAXREDIRS      => 10,
        CURLOPT_TIMEOUT        => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST  => 'POST',
    ));
    $response = curl_exec($curl);
    curl_close($curl);
    return $response;
}

function validateEmail($email) {
    return (preg_match("/(@.*@)|(\.\.)|(@\.)|(\.@)|(^\.)/", $email) || !preg_match("/^.+\@(\[?)[a-zA-Z0-9\-\.]+\.([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/", $email)) ? false : true;
}
function ceklogin($run) {
    $xpath = $run['site']["type"];
    if ($run['site']["type"] == 'page') {
        $xpath = $run['tag']["title"];
    }
    if (isset($_COOKIE["token"]) || isset($_SESSION["token"])) {
        if ($xpath == 'login' || $xpath == 'register') {
            header('Location: /panel/dashboard.html');
        }
        if (!isset($run['profile']['uid'])) {
            $_SESSION['redirect'] = $run['config']["url"];
            header('Location: /panel/logout.html');
        }
        //    if (!isset($run['profile']['awb']) && $xpath != 'profile' && $run['profile']['admin'] == true) {
        //             header('Location: /profile.html');
        //        }
    } else {
        if ($xpath !== 'login' && $xpath !== 'register' && $xpath !== 'home') {
            $_SESSION['redirect'] = $run['config']["url"];
            header('Location: /panel/login.html');
        }
    }
}

function logout_user() {
    session_destroy();
    if (isset($_SERVER['HTTP_COOKIE'])) {
        $cookies = explode(';', $_SERVER['HTTP_COOKIE']);
        foreach ($cookies as $cookie) {
            $parts = explode('=', $cookie);
            $name  = trim($parts[0]);
            setcookie($name, '', time() - 1000);
            setcookie($name, '', time() - 1000, '/');
        }
    }
    header('Location: /user/login.html');
    die();
}
function buatrp($angka) {
    $jadi = "Rp " . number_format($angka, 2, ',', '.');
    return $jadi;
}
function debug($object) {
    global $smarty;
    $smarty->assign("debug", nl2br(print_r($object, true)));
}
function trim_all($str, $what = null, $with = ' ') {
    if ($what === null) {
        //  Character      Decimal      Use
        //  "\0"            0           Null Character
        //  "\t"            9           Tab
        //  "\n"           10           New line
        //  "\x0B"         11           Vertical Tab
        //  "\r"           13           New Line in Mac
        //  " "            32           Space
        $what = "\\x00-\\x20"; //all white-spaces and control chars
    }
    $x = trim(preg_replace("/[" . $what . "]+/", $with, $str));
    return $x;
}
function cekp($name) {
    if (isset($_POST[$name])) {return true;} else {return false;}
}
function p($name) {
    return $_POST[$name];
}
function cekg($name) {
    if (isset($_GET[$name])) {return true;} else {return false;}
}
function g($name) {
    return $_GET[$name];
}
function cekf($name) {
    if (isset($_FILES[$name])) {return true;} else {return false;}
}
function f($name) {
    return $_FILES[$name];
}
function calcCrow($lat1, $lon1, $lat2, $lon2) {
    $R    = 6371; // km
    $dLat = toRad($lat2 - $lat1);
    $dLon = toRad($lon2 - $lon1);
    $lat1 = toRad($lat1);
    $lat2 = toRad($lat2);
    $a    = sin($dLat / 2) * sin($dLat / 2) + sin($dLon / 2) * sin($dLon / 2) * cos($lat1) * cos($lat2);
    $c    = 2 * atan2(sqrt($a), sqrt(1 - $a));
    $d    = $R * $c;
    return $d;
}
// Converts numeric degrees to radians
function toRad($Value) {
    return $Value * pi() / 180;
}
//echo distance(32.9697, -96.80322, 29.46786, -98.53506, "M") . " Miles<br>";
//echo distance(32.9697, -96.80322, 29.46786, -98.53506, "K") . " Kilometers<br>";
//echo distance(32.9697, -96.80322, 29.46786, -98.53506, "N") . " Nautical Miles<br>";
function distance($lat1, $lon1, $lat2, $lon2, $unit) {
    if (($lat1 == $lat2) && ($lon1 == $lon2)) {
        return 0;
    } else {
        $theta = $lon1 - $lon2;
        $dist  = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
        $dist  = acos($dist);
        $dist  = rad2deg($dist);
        $miles = $dist * 60 * 1.1515;
        $unit  = strtoupper($unit);
        if ($unit == "K") {
            return ($miles * 1.609344);
        } elseif ($unit == "N") {
            return ($miles * 0.8684);
        } else {
            return $miles;
        }
    }
}