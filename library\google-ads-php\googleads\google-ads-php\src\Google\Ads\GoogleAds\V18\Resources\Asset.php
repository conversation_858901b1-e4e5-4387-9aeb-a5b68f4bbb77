<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/resources/asset.proto

namespace Google\Ads\GoogleAds\V18\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Asset is a part of an ad which can be shared across multiple ads.
 * It can be an image (ImageAsset), a video (YoutubeVideoAsset), etc.
 * Assets are immutable and cannot be removed. To stop an asset from serving,
 * remove the asset from the entity that is using it.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.resources.Asset</code>
 */
class Asset extends \Google\Protobuf\Internal\Message
{
    /**
     * Immutable. The resource name of the asset.
     * Asset resource names have the form:
     * `customers/{customer_id}/assets/{asset_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     */
    protected $resource_name = '';
    /**
     * Output only. The ID of the asset.
     *
     * Generated from protobuf field <code>optional int64 id = 11 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $id = null;
    /**
     * Optional name of the asset.
     *
     * Generated from protobuf field <code>optional string name = 12;</code>
     */
    protected $name = null;
    /**
     * Output only. Type of the asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AssetTypeEnum.AssetType type = 4 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $type = 0;
    /**
     * A list of possible final URLs after all cross domain redirects.
     *
     * Generated from protobuf field <code>repeated string final_urls = 14;</code>
     */
    private $final_urls;
    /**
     * A list of possible final mobile URLs after all cross domain redirects.
     *
     * Generated from protobuf field <code>repeated string final_mobile_urls = 16;</code>
     */
    private $final_mobile_urls;
    /**
     * URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 17;</code>
     */
    protected $tracking_url_template = null;
    /**
     * A list of mappings to be used for substituting URL custom parameter tags in
     * the tracking_url_template, final_urls, and/or final_mobile_urls.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.CustomParameter url_custom_parameters = 18;</code>
     */
    private $url_custom_parameters;
    /**
     * URL template for appending params to landing page URLs served with parallel
     * tracking.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 19;</code>
     */
    protected $final_url_suffix = null;
    /**
     * Output only. Source of the asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AssetSourceEnum.AssetSource source = 38 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $source = 0;
    /**
     * Output only. Policy information for the asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.resources.AssetPolicySummary policy_summary = 13 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $policy_summary = null;
    /**
     * Output only. Policy information for the asset for each FieldType.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.resources.AssetFieldTypePolicySummary field_type_policy_summaries = 40 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    private $field_type_policy_summaries;
    protected $asset_data;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $resource_name
     *           Immutable. The resource name of the asset.
     *           Asset resource names have the form:
     *           `customers/{customer_id}/assets/{asset_id}`
     *     @type int|string $id
     *           Output only. The ID of the asset.
     *     @type string $name
     *           Optional name of the asset.
     *     @type int $type
     *           Output only. Type of the asset.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $final_urls
     *           A list of possible final URLs after all cross domain redirects.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $final_mobile_urls
     *           A list of possible final mobile URLs after all cross domain redirects.
     *     @type string $tracking_url_template
     *           URL template for constructing a tracking URL.
     *     @type array<\Google\Ads\GoogleAds\V18\Common\CustomParameter>|\Google\Protobuf\Internal\RepeatedField $url_custom_parameters
     *           A list of mappings to be used for substituting URL custom parameter tags in
     *           the tracking_url_template, final_urls, and/or final_mobile_urls.
     *     @type string $final_url_suffix
     *           URL template for appending params to landing page URLs served with parallel
     *           tracking.
     *     @type int $source
     *           Output only. Source of the asset.
     *     @type \Google\Ads\GoogleAds\V18\Resources\AssetPolicySummary $policy_summary
     *           Output only. Policy information for the asset.
     *     @type array<\Google\Ads\GoogleAds\V18\Resources\AssetFieldTypePolicySummary>|\Google\Protobuf\Internal\RepeatedField $field_type_policy_summaries
     *           Output only. Policy information for the asset for each FieldType.
     *     @type \Google\Ads\GoogleAds\V18\Common\YoutubeVideoAsset $youtube_video_asset
     *           Immutable. A YouTube video asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\MediaBundleAsset $media_bundle_asset
     *           Immutable. A media bundle asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\ImageAsset $image_asset
     *           Output only. An image asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\TextAsset $text_asset
     *           Immutable. A text asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\LeadFormAsset $lead_form_asset
     *           A lead form asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\BookOnGoogleAsset $book_on_google_asset
     *           A book on google asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\PromotionAsset $promotion_asset
     *           A promotion asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\CalloutAsset $callout_asset
     *           A callout asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\StructuredSnippetAsset $structured_snippet_asset
     *           A structured snippet asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\SitelinkAsset $sitelink_asset
     *           A sitelink asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\PageFeedAsset $page_feed_asset
     *           A page feed asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\DynamicEducationAsset $dynamic_education_asset
     *           A dynamic education asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\MobileAppAsset $mobile_app_asset
     *           A mobile app asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\HotelCalloutAsset $hotel_callout_asset
     *           A hotel callout asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\CallAsset $call_asset
     *           A call asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\PriceAsset $price_asset
     *           A price asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\CallToActionAsset $call_to_action_asset
     *           Immutable. A call to action asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\DynamicRealEstateAsset $dynamic_real_estate_asset
     *           A dynamic real estate asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\DynamicCustomAsset $dynamic_custom_asset
     *           A dynamic custom asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\DynamicHotelsAndRentalsAsset $dynamic_hotels_and_rentals_asset
     *           A dynamic hotels and rentals asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\DynamicFlightsAsset $dynamic_flights_asset
     *           A dynamic flights asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\DemandGenCarouselCardAsset $demand_gen_carousel_card_asset
     *           Immutable. A Demand Gen carousel card asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\DynamicTravelAsset $dynamic_travel_asset
     *           A dynamic travel asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\DynamicLocalAsset $dynamic_local_asset
     *           A dynamic local asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\DynamicJobsAsset $dynamic_jobs_asset
     *           A dynamic jobs asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\LocationAsset $location_asset
     *           Output only. A location asset.
     *     @type \Google\Ads\GoogleAds\V18\Common\HotelPropertyAsset $hotel_property_asset
     *           Immutable. A hotel property asset.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Resources\Asset::initOnce();
        parent::__construct($data);
    }

    /**
     * Immutable. The resource name of the asset.
     * Asset resource names have the form:
     * `customers/{customer_id}/assets/{asset_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getResourceName()
    {
        return $this->resource_name;
    }

    /**
     * Immutable. The resource name of the asset.
     * Asset resource names have the form:
     * `customers/{customer_id}/assets/{asset_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setResourceName($var)
    {
        GPBUtil::checkString($var, True);
        $this->resource_name = $var;

        return $this;
    }

    /**
     * Output only. The ID of the asset.
     *
     * Generated from protobuf field <code>optional int64 id = 11 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int|string
     */
    public function getId()
    {
        return isset($this->id) ? $this->id : 0;
    }

    public function hasId()
    {
        return isset($this->id);
    }

    public function clearId()
    {
        unset($this->id);
    }

    /**
     * Output only. The ID of the asset.
     *
     * Generated from protobuf field <code>optional int64 id = 11 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int|string $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt64($var);
        $this->id = $var;

        return $this;
    }

    /**
     * Optional name of the asset.
     *
     * Generated from protobuf field <code>optional string name = 12;</code>
     * @return string
     */
    public function getName()
    {
        return isset($this->name) ? $this->name : '';
    }

    public function hasName()
    {
        return isset($this->name);
    }

    public function clearName()
    {
        unset($this->name);
    }

    /**
     * Optional name of the asset.
     *
     * Generated from protobuf field <code>optional string name = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * Output only. Type of the asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AssetTypeEnum.AssetType type = 4 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Output only. Type of the asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AssetTypeEnum.AssetType type = 4 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\AssetTypeEnum\AssetType::class);
        $this->type = $var;

        return $this;
    }

    /**
     * A list of possible final URLs after all cross domain redirects.
     *
     * Generated from protobuf field <code>repeated string final_urls = 14;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getFinalUrls()
    {
        return $this->final_urls;
    }

    /**
     * A list of possible final URLs after all cross domain redirects.
     *
     * Generated from protobuf field <code>repeated string final_urls = 14;</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFinalUrls($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->final_urls = $arr;

        return $this;
    }

    /**
     * A list of possible final mobile URLs after all cross domain redirects.
     *
     * Generated from protobuf field <code>repeated string final_mobile_urls = 16;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getFinalMobileUrls()
    {
        return $this->final_mobile_urls;
    }

    /**
     * A list of possible final mobile URLs after all cross domain redirects.
     *
     * Generated from protobuf field <code>repeated string final_mobile_urls = 16;</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFinalMobileUrls($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->final_mobile_urls = $arr;

        return $this;
    }

    /**
     * URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 17;</code>
     * @return string
     */
    public function getTrackingUrlTemplate()
    {
        return isset($this->tracking_url_template) ? $this->tracking_url_template : '';
    }

    public function hasTrackingUrlTemplate()
    {
        return isset($this->tracking_url_template);
    }

    public function clearTrackingUrlTemplate()
    {
        unset($this->tracking_url_template);
    }

    /**
     * URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 17;</code>
     * @param string $var
     * @return $this
     */
    public function setTrackingUrlTemplate($var)
    {
        GPBUtil::checkString($var, True);
        $this->tracking_url_template = $var;

        return $this;
    }

    /**
     * A list of mappings to be used for substituting URL custom parameter tags in
     * the tracking_url_template, final_urls, and/or final_mobile_urls.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.CustomParameter url_custom_parameters = 18;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getUrlCustomParameters()
    {
        return $this->url_custom_parameters;
    }

    /**
     * A list of mappings to be used for substituting URL custom parameter tags in
     * the tracking_url_template, final_urls, and/or final_mobile_urls.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.common.CustomParameter url_custom_parameters = 18;</code>
     * @param array<\Google\Ads\GoogleAds\V18\Common\CustomParameter>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUrlCustomParameters($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Ads\GoogleAds\V18\Common\CustomParameter::class);
        $this->url_custom_parameters = $arr;

        return $this;
    }

    /**
     * URL template for appending params to landing page URLs served with parallel
     * tracking.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 19;</code>
     * @return string
     */
    public function getFinalUrlSuffix()
    {
        return isset($this->final_url_suffix) ? $this->final_url_suffix : '';
    }

    public function hasFinalUrlSuffix()
    {
        return isset($this->final_url_suffix);
    }

    public function clearFinalUrlSuffix()
    {
        unset($this->final_url_suffix);
    }

    /**
     * URL template for appending params to landing page URLs served with parallel
     * tracking.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 19;</code>
     * @param string $var
     * @return $this
     */
    public function setFinalUrlSuffix($var)
    {
        GPBUtil::checkString($var, True);
        $this->final_url_suffix = $var;

        return $this;
    }

    /**
     * Output only. Source of the asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AssetSourceEnum.AssetSource source = 38 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getSource()
    {
        return $this->source;
    }

    /**
     * Output only. Source of the asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.AssetSourceEnum.AssetSource source = 38 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setSource($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\AssetSourceEnum\AssetSource::class);
        $this->source = $var;

        return $this;
    }

    /**
     * Output only. Policy information for the asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.resources.AssetPolicySummary policy_summary = 13 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V18\Resources\AssetPolicySummary|null
     */
    public function getPolicySummary()
    {
        return $this->policy_summary;
    }

    public function hasPolicySummary()
    {
        return isset($this->policy_summary);
    }

    public function clearPolicySummary()
    {
        unset($this->policy_summary);
    }

    /**
     * Output only. Policy information for the asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.resources.AssetPolicySummary policy_summary = 13 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V18\Resources\AssetPolicySummary $var
     * @return $this
     */
    public function setPolicySummary($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Resources\AssetPolicySummary::class);
        $this->policy_summary = $var;

        return $this;
    }

    /**
     * Output only. Policy information for the asset for each FieldType.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.resources.AssetFieldTypePolicySummary field_type_policy_summaries = 40 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getFieldTypePolicySummaries()
    {
        return $this->field_type_policy_summaries;
    }

    /**
     * Output only. Policy information for the asset for each FieldType.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v18.resources.AssetFieldTypePolicySummary field_type_policy_summaries = 40 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param array<\Google\Ads\GoogleAds\V18\Resources\AssetFieldTypePolicySummary>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFieldTypePolicySummaries($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Ads\GoogleAds\V18\Resources\AssetFieldTypePolicySummary::class);
        $this->field_type_policy_summaries = $arr;

        return $this;
    }

    /**
     * Immutable. A YouTube video asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.YoutubeVideoAsset youtube_video_asset = 5 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\YoutubeVideoAsset|null
     */
    public function getYoutubeVideoAsset()
    {
        return $this->readOneof(5);
    }

    public function hasYoutubeVideoAsset()
    {
        return $this->hasOneof(5);
    }

    /**
     * Immutable. A YouTube video asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.YoutubeVideoAsset youtube_video_asset = 5 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\YoutubeVideoAsset $var
     * @return $this
     */
    public function setYoutubeVideoAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\YoutubeVideoAsset::class);
        $this->writeOneof(5, $var);

        return $this;
    }

    /**
     * Immutable. A media bundle asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.MediaBundleAsset media_bundle_asset = 6 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\MediaBundleAsset|null
     */
    public function getMediaBundleAsset()
    {
        return $this->readOneof(6);
    }

    public function hasMediaBundleAsset()
    {
        return $this->hasOneof(6);
    }

    /**
     * Immutable. A media bundle asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.MediaBundleAsset media_bundle_asset = 6 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\MediaBundleAsset $var
     * @return $this
     */
    public function setMediaBundleAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\MediaBundleAsset::class);
        $this->writeOneof(6, $var);

        return $this;
    }

    /**
     * Output only. An image asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ImageAsset image_asset = 7 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ImageAsset|null
     */
    public function getImageAsset()
    {
        return $this->readOneof(7);
    }

    public function hasImageAsset()
    {
        return $this->hasOneof(7);
    }

    /**
     * Output only. An image asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ImageAsset image_asset = 7 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ImageAsset $var
     * @return $this
     */
    public function setImageAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ImageAsset::class);
        $this->writeOneof(7, $var);

        return $this;
    }

    /**
     * Immutable. A text asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.TextAsset text_asset = 8 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\TextAsset|null
     */
    public function getTextAsset()
    {
        return $this->readOneof(8);
    }

    public function hasTextAsset()
    {
        return $this->hasOneof(8);
    }

    /**
     * Immutable. A text asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.TextAsset text_asset = 8 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\TextAsset $var
     * @return $this
     */
    public function setTextAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\TextAsset::class);
        $this->writeOneof(8, $var);

        return $this;
    }

    /**
     * A lead form asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LeadFormAsset lead_form_asset = 9;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\LeadFormAsset|null
     */
    public function getLeadFormAsset()
    {
        return $this->readOneof(9);
    }

    public function hasLeadFormAsset()
    {
        return $this->hasOneof(9);
    }

    /**
     * A lead form asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LeadFormAsset lead_form_asset = 9;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\LeadFormAsset $var
     * @return $this
     */
    public function setLeadFormAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\LeadFormAsset::class);
        $this->writeOneof(9, $var);

        return $this;
    }

    /**
     * A book on google asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.BookOnGoogleAsset book_on_google_asset = 10;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\BookOnGoogleAsset|null
     */
    public function getBookOnGoogleAsset()
    {
        return $this->readOneof(10);
    }

    public function hasBookOnGoogleAsset()
    {
        return $this->hasOneof(10);
    }

    /**
     * A book on google asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.BookOnGoogleAsset book_on_google_asset = 10;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\BookOnGoogleAsset $var
     * @return $this
     */
    public function setBookOnGoogleAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\BookOnGoogleAsset::class);
        $this->writeOneof(10, $var);

        return $this;
    }

    /**
     * A promotion asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.PromotionAsset promotion_asset = 15;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\PromotionAsset|null
     */
    public function getPromotionAsset()
    {
        return $this->readOneof(15);
    }

    public function hasPromotionAsset()
    {
        return $this->hasOneof(15);
    }

    /**
     * A promotion asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.PromotionAsset promotion_asset = 15;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\PromotionAsset $var
     * @return $this
     */
    public function setPromotionAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\PromotionAsset::class);
        $this->writeOneof(15, $var);

        return $this;
    }

    /**
     * A callout asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CalloutAsset callout_asset = 20;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\CalloutAsset|null
     */
    public function getCalloutAsset()
    {
        return $this->readOneof(20);
    }

    public function hasCalloutAsset()
    {
        return $this->hasOneof(20);
    }

    /**
     * A callout asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CalloutAsset callout_asset = 20;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\CalloutAsset $var
     * @return $this
     */
    public function setCalloutAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\CalloutAsset::class);
        $this->writeOneof(20, $var);

        return $this;
    }

    /**
     * A structured snippet asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.StructuredSnippetAsset structured_snippet_asset = 21;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\StructuredSnippetAsset|null
     */
    public function getStructuredSnippetAsset()
    {
        return $this->readOneof(21);
    }

    public function hasStructuredSnippetAsset()
    {
        return $this->hasOneof(21);
    }

    /**
     * A structured snippet asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.StructuredSnippetAsset structured_snippet_asset = 21;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\StructuredSnippetAsset $var
     * @return $this
     */
    public function setStructuredSnippetAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\StructuredSnippetAsset::class);
        $this->writeOneof(21, $var);

        return $this;
    }

    /**
     * A sitelink asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.SitelinkAsset sitelink_asset = 22;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\SitelinkAsset|null
     */
    public function getSitelinkAsset()
    {
        return $this->readOneof(22);
    }

    public function hasSitelinkAsset()
    {
        return $this->hasOneof(22);
    }

    /**
     * A sitelink asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.SitelinkAsset sitelink_asset = 22;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\SitelinkAsset $var
     * @return $this
     */
    public function setSitelinkAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\SitelinkAsset::class);
        $this->writeOneof(22, $var);

        return $this;
    }

    /**
     * A page feed asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.PageFeedAsset page_feed_asset = 23;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\PageFeedAsset|null
     */
    public function getPageFeedAsset()
    {
        return $this->readOneof(23);
    }

    public function hasPageFeedAsset()
    {
        return $this->hasOneof(23);
    }

    /**
     * A page feed asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.PageFeedAsset page_feed_asset = 23;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\PageFeedAsset $var
     * @return $this
     */
    public function setPageFeedAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\PageFeedAsset::class);
        $this->writeOneof(23, $var);

        return $this;
    }

    /**
     * A dynamic education asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicEducationAsset dynamic_education_asset = 24;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DynamicEducationAsset|null
     */
    public function getDynamicEducationAsset()
    {
        return $this->readOneof(24);
    }

    public function hasDynamicEducationAsset()
    {
        return $this->hasOneof(24);
    }

    /**
     * A dynamic education asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicEducationAsset dynamic_education_asset = 24;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DynamicEducationAsset $var
     * @return $this
     */
    public function setDynamicEducationAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DynamicEducationAsset::class);
        $this->writeOneof(24, $var);

        return $this;
    }

    /**
     * A mobile app asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.MobileAppAsset mobile_app_asset = 25;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\MobileAppAsset|null
     */
    public function getMobileAppAsset()
    {
        return $this->readOneof(25);
    }

    public function hasMobileAppAsset()
    {
        return $this->hasOneof(25);
    }

    /**
     * A mobile app asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.MobileAppAsset mobile_app_asset = 25;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\MobileAppAsset $var
     * @return $this
     */
    public function setMobileAppAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\MobileAppAsset::class);
        $this->writeOneof(25, $var);

        return $this;
    }

    /**
     * A hotel callout asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.HotelCalloutAsset hotel_callout_asset = 26;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\HotelCalloutAsset|null
     */
    public function getHotelCalloutAsset()
    {
        return $this->readOneof(26);
    }

    public function hasHotelCalloutAsset()
    {
        return $this->hasOneof(26);
    }

    /**
     * A hotel callout asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.HotelCalloutAsset hotel_callout_asset = 26;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\HotelCalloutAsset $var
     * @return $this
     */
    public function setHotelCalloutAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\HotelCalloutAsset::class);
        $this->writeOneof(26, $var);

        return $this;
    }

    /**
     * A call asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CallAsset call_asset = 27;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\CallAsset|null
     */
    public function getCallAsset()
    {
        return $this->readOneof(27);
    }

    public function hasCallAsset()
    {
        return $this->hasOneof(27);
    }

    /**
     * A call asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CallAsset call_asset = 27;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\CallAsset $var
     * @return $this
     */
    public function setCallAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\CallAsset::class);
        $this->writeOneof(27, $var);

        return $this;
    }

    /**
     * A price asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.PriceAsset price_asset = 28;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\PriceAsset|null
     */
    public function getPriceAsset()
    {
        return $this->readOneof(28);
    }

    public function hasPriceAsset()
    {
        return $this->hasOneof(28);
    }

    /**
     * A price asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.PriceAsset price_asset = 28;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\PriceAsset $var
     * @return $this
     */
    public function setPriceAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\PriceAsset::class);
        $this->writeOneof(28, $var);

        return $this;
    }

    /**
     * Immutable. A call to action asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CallToActionAsset call_to_action_asset = 29 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\CallToActionAsset|null
     */
    public function getCallToActionAsset()
    {
        return $this->readOneof(29);
    }

    public function hasCallToActionAsset()
    {
        return $this->hasOneof(29);
    }

    /**
     * Immutable. A call to action asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CallToActionAsset call_to_action_asset = 29 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\CallToActionAsset $var
     * @return $this
     */
    public function setCallToActionAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\CallToActionAsset::class);
        $this->writeOneof(29, $var);

        return $this;
    }

    /**
     * A dynamic real estate asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicRealEstateAsset dynamic_real_estate_asset = 30;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DynamicRealEstateAsset|null
     */
    public function getDynamicRealEstateAsset()
    {
        return $this->readOneof(30);
    }

    public function hasDynamicRealEstateAsset()
    {
        return $this->hasOneof(30);
    }

    /**
     * A dynamic real estate asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicRealEstateAsset dynamic_real_estate_asset = 30;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DynamicRealEstateAsset $var
     * @return $this
     */
    public function setDynamicRealEstateAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DynamicRealEstateAsset::class);
        $this->writeOneof(30, $var);

        return $this;
    }

    /**
     * A dynamic custom asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicCustomAsset dynamic_custom_asset = 31;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DynamicCustomAsset|null
     */
    public function getDynamicCustomAsset()
    {
        return $this->readOneof(31);
    }

    public function hasDynamicCustomAsset()
    {
        return $this->hasOneof(31);
    }

    /**
     * A dynamic custom asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicCustomAsset dynamic_custom_asset = 31;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DynamicCustomAsset $var
     * @return $this
     */
    public function setDynamicCustomAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DynamicCustomAsset::class);
        $this->writeOneof(31, $var);

        return $this;
    }

    /**
     * A dynamic hotels and rentals asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicHotelsAndRentalsAsset dynamic_hotels_and_rentals_asset = 32;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DynamicHotelsAndRentalsAsset|null
     */
    public function getDynamicHotelsAndRentalsAsset()
    {
        return $this->readOneof(32);
    }

    public function hasDynamicHotelsAndRentalsAsset()
    {
        return $this->hasOneof(32);
    }

    /**
     * A dynamic hotels and rentals asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicHotelsAndRentalsAsset dynamic_hotels_and_rentals_asset = 32;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DynamicHotelsAndRentalsAsset $var
     * @return $this
     */
    public function setDynamicHotelsAndRentalsAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DynamicHotelsAndRentalsAsset::class);
        $this->writeOneof(32, $var);

        return $this;
    }

    /**
     * A dynamic flights asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicFlightsAsset dynamic_flights_asset = 33;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DynamicFlightsAsset|null
     */
    public function getDynamicFlightsAsset()
    {
        return $this->readOneof(33);
    }

    public function hasDynamicFlightsAsset()
    {
        return $this->hasOneof(33);
    }

    /**
     * A dynamic flights asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicFlightsAsset dynamic_flights_asset = 33;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DynamicFlightsAsset $var
     * @return $this
     */
    public function setDynamicFlightsAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DynamicFlightsAsset::class);
        $this->writeOneof(33, $var);

        return $this;
    }

    /**
     * Immutable. A Demand Gen carousel card asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DemandGenCarouselCardAsset demand_gen_carousel_card_asset = 50 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DemandGenCarouselCardAsset|null
     */
    public function getDemandGenCarouselCardAsset()
    {
        return $this->readOneof(50);
    }

    public function hasDemandGenCarouselCardAsset()
    {
        return $this->hasOneof(50);
    }

    /**
     * Immutable. A Demand Gen carousel card asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DemandGenCarouselCardAsset demand_gen_carousel_card_asset = 50 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DemandGenCarouselCardAsset $var
     * @return $this
     */
    public function setDemandGenCarouselCardAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DemandGenCarouselCardAsset::class);
        $this->writeOneof(50, $var);

        return $this;
    }

    /**
     * A dynamic travel asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicTravelAsset dynamic_travel_asset = 35;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DynamicTravelAsset|null
     */
    public function getDynamicTravelAsset()
    {
        return $this->readOneof(35);
    }

    public function hasDynamicTravelAsset()
    {
        return $this->hasOneof(35);
    }

    /**
     * A dynamic travel asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicTravelAsset dynamic_travel_asset = 35;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DynamicTravelAsset $var
     * @return $this
     */
    public function setDynamicTravelAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DynamicTravelAsset::class);
        $this->writeOneof(35, $var);

        return $this;
    }

    /**
     * A dynamic local asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicLocalAsset dynamic_local_asset = 36;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DynamicLocalAsset|null
     */
    public function getDynamicLocalAsset()
    {
        return $this->readOneof(36);
    }

    public function hasDynamicLocalAsset()
    {
        return $this->hasOneof(36);
    }

    /**
     * A dynamic local asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicLocalAsset dynamic_local_asset = 36;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DynamicLocalAsset $var
     * @return $this
     */
    public function setDynamicLocalAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DynamicLocalAsset::class);
        $this->writeOneof(36, $var);

        return $this;
    }

    /**
     * A dynamic jobs asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicJobsAsset dynamic_jobs_asset = 37;</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DynamicJobsAsset|null
     */
    public function getDynamicJobsAsset()
    {
        return $this->readOneof(37);
    }

    public function hasDynamicJobsAsset()
    {
        return $this->hasOneof(37);
    }

    /**
     * A dynamic jobs asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DynamicJobsAsset dynamic_jobs_asset = 37;</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DynamicJobsAsset $var
     * @return $this
     */
    public function setDynamicJobsAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DynamicJobsAsset::class);
        $this->writeOneof(37, $var);

        return $this;
    }

    /**
     * Output only. A location asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LocationAsset location_asset = 39 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\LocationAsset|null
     */
    public function getLocationAsset()
    {
        return $this->readOneof(39);
    }

    public function hasLocationAsset()
    {
        return $this->hasOneof(39);
    }

    /**
     * Output only. A location asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LocationAsset location_asset = 39 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\LocationAsset $var
     * @return $this
     */
    public function setLocationAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\LocationAsset::class);
        $this->writeOneof(39, $var);

        return $this;
    }

    /**
     * Immutable. A hotel property asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.HotelPropertyAsset hotel_property_asset = 41 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\HotelPropertyAsset|null
     */
    public function getHotelPropertyAsset()
    {
        return $this->readOneof(41);
    }

    public function hasHotelPropertyAsset()
    {
        return $this->hasOneof(41);
    }

    /**
     * Immutable. A hotel property asset.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.HotelPropertyAsset hotel_property_asset = 41 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\HotelPropertyAsset $var
     * @return $this
     */
    public function setHotelPropertyAsset($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\HotelPropertyAsset::class);
        $this->writeOneof(41, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getAssetData()
    {
        return $this->whichOneof("asset_data");
    }

}

