<?php
class trigger_linkedin{

    private $table_visitor;
    private $table_log;

    function __construct()
    {
        $this->table_visitor = "visitor";
        $this->table_log = "log_connector";
    }

    function sent_pixel($connector_key, $value, $partner_id, $access_token, $vid, $type, $custom = NULL)
    {
        global $app;
        $db2 = $app->db2;

        $hash = [
            "value" => $value,
            "partner_id" => $partner_id,
            "access_token" => $access_token,
            "source" => "linkedin",
            "vid" => $vid,
            "type" => $type,
            "custom" => $custom,
            "waktu" => date("Y-m-d")
        ];
        $data_hash = json_encode($hash);
        $hash = hash("sha256", $data_hash);

        $table_visitor = $this->table_visitor;
        $table_log = $this->table_log;
        $waktu = time();

        $db2->where("visitor_id", $vid);
        $visitor = $db2->getone($this->table_visitor);

        if ($visitor == NULL || $visitor == "" || $visitor["data"] == NULL) {
            return;
        }

        $visitor_data = unserialize($visitor["data"]);

        // Build LinkedIn Conversion API payload
        $event_id = $type . "-" . $vid;
        $timestamp = date('c'); // ISO 8601
        $payload = [
            "conversion" => [
                "account" => $partner_id, // URN: urn:li:sponsoredAccount:xxxx
                "eventId" => $event_id,
                "eventName" => $type,
                "eventTime" => strtotime($timestamp) * 1000, // in ms
                "eventType" => $type,
                "value" => $value,
                "currency" => "IDR",
                "externalId" => md5($vid),
                "customData" => $custom,
            ]
        ];

        // Optional: enrich context
        if (isset($visitor_data["ip"])) {
            $tmp = explode(",", $visitor_data["ip"]);
            $payload["conversion"]["ipAddress"] = count($tmp) > 1 ? $tmp[0] : $visitor_data["ip"];
        }
        if (isset($visitor_data["useragent"])) {
            $payload["conversion"]["userAgent"] = $visitor_data["useragent"];
        }
        if (isset($visitor_data["phone"])) {
            $payload["conversion"]["hashedPhoneNumber"] = hash('sha256', "+" . preg_replace('/[^0-9.]+/', '', $visitor_data["phone"]));
        } elseif (isset($visitor["phone"]) && $visitor["phone"] != "" && $visitor["phone"] != "0") {
            $payload["conversion"]["hashedPhoneNumber"] = hash('sha256', "+" . preg_replace('/[^0-9.]+/', '', $visitor["phone"]));
        }
        if (isset($visitor_data["current_url"])) {
            $payload["conversion"]["url"] = $visitor_data["current_url"];
        }

        // Send to LinkedIn Conversion API
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.linkedin.com/rest/conversionEvents');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        $headers = [
            'Authorization: Bearer ' . $access_token,
            'Content-Type: application/json',
            'LinkedIn-Version: 202405',
            'X-Restli-Protocol-Version: 2.0.0'
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($ch);

        $log = [
            "partner_id" => $partner_id,
            "access_token" => $access_token,
            "type" => $type,
            "custom" => $custom,
        ];
        $data_insertx = [
            "waktu" => date("Y-m-d H:i:s"),
            "connector_key" => $db2->func("UNHEX(?)", [$connector_key]),
            "vid" => $vid,
            "event" => $type,
        ];

        if (curl_errno($ch)) {
            $return = curl_error($ch);
            $log["msg"] = $return;
            $data_insertx["error"] = 1;
            $ddd["input"] = $data_hash;
            $ddd["output"] = $log;
            $data_insertx["result"] = json_encode($ddd);
            $db2->insert($table_log, $data_insertx);
            $result = $return;
        } else {
            $log["msg"]['result'] = $result;
            $log["msg"]['param'] = $payload;
            $data_insertx["error"] = 0;
            $data_insertx["result"] = json_encode($log);
            $db2->insert($table_log, $data_insertx);
        }
        curl_close($ch);
        return $result;
    }
} 