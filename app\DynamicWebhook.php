<?php

require_once __DIR__ . '/dynamicwebhook/autoload.php';

use DynamicWebhook\PlatformDetector;
use DynamicWebhook\MessageNormalizer;
use DynamicWebhook\CTWAProcessor;
use DynamicWebhook\VisitorManager;
use DynamicWebhook\Config\PlatformConfig;
use DynamicWebhook\DataExtractor\GenericDataExtractor;
use DynamicWebhook\DataExtractor\WABADataExtractor;
use DynamicWebhook\DataExtractor\SmartChatDataExtractor;
use DynamicWebhook\DataExtractor\QontakDataExtractor;
use DynamicWebhook\TriggerProcessor\ContactTriggerProcessor;
use DynamicWebhook\TriggerProcessor\MQLTriggerProcessor;
use DynamicWebhook\TriggerProcessor\ProspectTriggerProcessor;
use DynamicWebhook\TriggerProcessor\PurchaseTriggerProcessor;

/**
 * Refactored Dynamic Webhook Processor
 * Clean OOP structure with dependency injection and single responsibility classes
 */
class DynamicWebhook
{
    // Core services
    private $app;
    private $db;
    private $db2;
    private $trackService;
    private $metaService;
    
    // Project info
    private string $projectKey;
    private int $projectId;
    private string $csPhone;
    private string $input;
    private array $inputData;
    private ?string $provider;
    
    // Helper classes
    private PlatformDetector $platformDetector;
    private MessageNormalizer $normalizer;
    private CTWAProcessor $ctwaProcessor;
    private VisitorManager $visitorManager;
    private array $dataExtractors = [];
    private array $triggerProcessors = [];
    
    // Processing state
    private array $extractedData = [];
    private array $processResult = [];
    private array $triggerStates = [];

    public function __construct($app, string $projectKey, string $csPhone, string $input, ?string $provider = null)
    {
        $this->app = $app;
        $this->db = $app->db ?? null;
        $this->projectKey = $projectKey;
        $this->csPhone = $csPhone;
        $this->input = $input;
        $this->inputData = json_decode($input, true) ?? [];
        $this->provider = $provider;
        
        $this->initializeProject();
        $this->initializeServices();
        $this->initializeHelpers();
    }

    /**
     * Main processing method - orchestrates the entire workflow
     */
    public function process(): array
    {
        $this->processResult = [
            'status' => 'success',
            'steps' => [],
            'summary' => [
                'platform' => null,
                'phone' => null,
                'message_type' => null,
                'is_new_contact' => false,
                'triggers' => [],
                'visitor_id' => null,
                'ctwa_data' => null
            ]
        ];

        try {
            // Step 1: Platform Detection
            $platform = $this->detectPlatform();
            $this->processResult['summary']['platform'] = $platform;
            
            // Step 2: Data Extraction
            $this->extractedData = $this->extractData($platform);
            if (!$this->validateExtractedData()) {
                $this->processResult['status'] = 'failed';
                $this->processResult['error'] = 'Invalid extracted data';
                return $this->processResult;
            }
            
            $this->updateSummaryFromExtractedData();
            
            // Step 3: Process New Contact
            $isNewContact = $this->processNewContact();
            $this->processResult['summary']['is_new_contact'] = $isNewContact;
            
            // Step 4: Process Triggers
            $this->processTriggers();
            
            // Step 5: Process Visitor
            $visitor = $this->processVisitor();
            $this->processResult['summary']['visitor_id'] = $visitor['visitor_id'] ?? null;
            
            // Step 6: Process CTWA
            $ctwaData = $this->processCTWA($platform);
            $this->processResult['summary']['ctwa_data'] = $ctwaData;
            
            // Step 7: Execute Triggers
            if ($visitor) {
                $this->executeTriggers($visitor);
            }
            
        } catch (Exception $e) {
            $this->processResult['status'] = 'error';
            $this->processResult['error'] = $e->getMessage();
        }

        return $this->processResult;
    }

    /**
     * Detect platform using PlatformDetector
     */
    private function detectPlatform(): string
    {
        return $this->platformDetector->detect($this->inputData, $this->provider);
    }

    /**
     * Extract data using appropriate DataExtractor
     */
    private function extractData(string $platform): array
    {
        $extractor = $this->getDataExtractor($platform);
        return $extractor->extract($this->inputData, $platform);
    }

    /**
     * Validate extracted data
     */
    private function validateExtractedData(): bool
    {
        return !empty($this->extractedData['phone']) && isset($this->extractedData['message']);
    }

    /**
     * Process new contact
     */
    private function processNewContact(): bool
    {
        if (!$this->db2 || !$this->extractedData['phone']) {
            return false;
        }

        return $this->visitorManager->processNewContact($this->extractedData['phone']);
    }

    /**
     * Process all triggers
     */
    private function processTriggers(): void
    {
        $context = [
            'input' => $this->input,
            'is_new_contact' => $this->processResult['summary']['is_new_contact'],
            'cs_phone' => $this->csPhone,
            'platform' => $this->processResult['summary']['platform']
        ];

        foreach ($this->triggerProcessors as $processor) {
            $triggerType = $processor->getTriggerType();
            $this->triggerStates[$triggerType] = $processor->process($this->extractedData, $context);
        }

        $this->processResult['summary']['triggers'] = $this->triggerStates;
    }

    /**
     * Process visitor
     */
    private function processVisitor(): ?array
    {
        $context = [
            'visitor_id' => $this->extractedData['visitor_id'] ?? null,
            'is_new_contact' => $this->processResult['summary']['is_new_contact'],
            'cs_phone' => $this->csPhone,
            'platform' => $this->processResult['summary']['platform']
        ];

        return $this->visitorManager->processVisitor($this->extractedData, $context);
    }

    /**
     * Process CTWA data
     */
    private function processCTWA(string $platform): array
    {
        return $this->ctwaProcessor->extractCTWAData($this->inputData, $platform);
    }

    /**
     * Execute triggers
     */
    private function executeTriggers(array $visitor): void
    {
        if (!$this->trackService) {
            return;
        }

        $phone = $this->extractedData['phone'];
        $message = $this->extractedData['message'];

        if ($this->triggerStates['contact'] ?? false) {
            $this->trackService->lead(
                $this->processResult['summary']['is_new_contact'],
                $visitor,
                $this->csPhone,
                $phone,
                null,
                null,
                true
            );
        }

        if ($this->triggerStates['mql'] ?? false) {
            $this->trackService->mql($this->csPhone, $visitor, $phone);
        }

        if ($this->triggerStates['prospect'] ?? false) {
            $this->trackService->prospek($this->csPhone, $visitor, $phone, null, $message);
        }

        if ($this->triggerStates['purchase'] ?? false) {
            $purchaseValue = $this->extractedData['purchase_value'] ?? 0;
            $purchaseData = $this->extractedData['purchase_data'] ?? [];
            $this->trackService->purchase($this->csPhone, $visitor, $phone, $purchaseValue, null, $purchaseData, $message);
        }
    }

    /**
     * Get appropriate data extractor for platform
     */
    private function getDataExtractor(string $platform)
    {
        // Return cached extractor if available
        if (isset($this->dataExtractors[$platform])) {
            return $this->dataExtractors[$platform];
        }

        // Create appropriate extractor based on platform
        $config = PlatformConfig::getAll();
        
        switch ($platform) {
            case 'waba':
            case 'pancake':
                return $this->dataExtractors[$platform] = new WABADataExtractor($this->normalizer, $config);
            
            case 'smartchat':
                return $this->dataExtractors[$platform] = new SmartChatDataExtractor($this->normalizer, $config);
            
            case 'qontak':
                return $this->dataExtractors[$platform] = new QontakDataExtractor($this->normalizer, $config, $this->db2);
            
            default:
                return $this->dataExtractors[$platform] = new GenericDataExtractor($this->normalizer, $config);
        }
    }

    /**
     * Initialize project and database connections
     */
    private function initializeProject(): void
    {
        if ($this->db) {
            $this->db->where("project_key = UNHEX(?)", [$this->projectKey]);
            $project = $this->db->getone("project");
            
            if (!$project) {
                throw new Exception("Invalid project key");
            }
            
            $this->projectId = $project["project_id"];
            
            if (function_exists('assign_child_db')) {
                assign_child_db($this->projectId);
            }
        }

        $this->db2 = $this->app->db2 ?? null;
    }

    /**
     * Initialize core services
     */
    private function initializeServices(): void
    {
        $this->trackService = new track();
        $this->metaService = new meta();
    }

    /**
     * Initialize helper classes
     */
    private function initializeHelpers(): void
    {
        $this->platformDetector = new PlatformDetector();
        $this->normalizer = new MessageNormalizer();
        $this->ctwaProcessor = new CTWAProcessor($this->normalizer);
        $this->visitorManager = new VisitorManager($this->trackService, $this->db2, $this->platformDetector);
        
        // Initialize trigger processors
        $this->triggerProcessors = [
            'contact' => new ContactTriggerProcessor($this->metaService, $this->db2),
            'mql' => new MQLTriggerProcessor($this->metaService, $this->db2),
            'prospect' => new ProspectTriggerProcessor($this->metaService, $this->db2),
            'purchase' => new PurchaseTriggerProcessor($this->metaService, $this->db2, $this->normalizer)
        ];
    }

    /**
     * Update summary from extracted data
     */
    private function updateSummaryFromExtractedData(): void
    {
        $this->processResult['summary']['phone'] = $this->extractedData['phone'];
        $this->processResult['summary']['message_type'] = $this->extractedData['message_type'];
    }

    // Backward compatibility methods
    public function getProcessResult(): array
    {
        return $this->processResult;
    }

    public function getExtractedData(): array
    {
        return $this->extractedData;
    }

    public function getTriggerStates(): array
    {
        return $this->triggerStates;
    }
}
