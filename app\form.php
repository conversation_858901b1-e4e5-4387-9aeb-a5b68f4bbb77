<?php

class form
{

    public function set_setting($param)
    {
        global $app;
        $db2 = $app->db2;
        $success = false;

        if(isset($param["form_contact_default_msg"]))
        {
            $m = new meta();
            $m->set_meta("form_contact_default_msg",$param["form_contact_default_msg"]);

            $success = true;
        }

        if(isset($param["form_auto_msg"]))
        {
            $m = new meta();
            $m->set_meta("form_auto_msg",$param["form_auto_msg"]);
            $success = true;
        }

        if(isset($param["form_contact_default_cs"]))
        {
            $m = new meta();
            $m->set_meta("form_contact_default_cs",$param["form_contact_default_cs"]);
            $success = true;
        }
        
        if($success){
            return["code"=>1,"msg"=>"success"];
        }else{
            return["code"=>0,"msg"=>"error"];
        }
        
    }

    public function get_setting(){
        $m = new meta();
        $tmp= $m->get_meta("form_contact_default_msg");
        if($tmp["code"] == 1){
            $setting["form_contact_default_msg"] =  $tmp["result"]["data"];
        }

        $tmp= $m->get_meta("form_auto_msg");
        if($tmp["code"] == 1){
            $setting["form_auto_msg"] =  $tmp["result"]["data"];
        }
        
        $tmp= $m->get_meta("form_contact_default_cs");
        if($tmp["code"] == 1){
            $setting["form_contact_default_cs"] =  $tmp["result"]["data"];
        }
        return $setting;
    }


    public function add($visitor_id,$phone, $data,$divisi = "")
    {
        global $app;
        $db2 = $app->db2;
        $dt = new DateTime("now", new DateTimeZone('Asia/Jakarta'));
        $timestamp = $dt->format('Y-m-d H:i:s');
        
        $phone = $this->cek_phone($phone);
        if($phone == false){
            $ret["code"] = 0;
            $ret["result"]["msg"] = "invalid phone number, please insert code phone country like (62xxxxxx)";
            return $ret;
        }
        
        $data_insert["visitor_id"] = $visitor_id;
        $data_insert["data"] = serialize($data);
        $data_insert["phone"] = $phone;
        $data_insert["divisi"] = $divisi;
        $data_insert["waktu_contact"] = $timestamp;
        $data_insert["last_cron"] = $timestamp;
        $db2->where("phone",$phone);
        $tmp = $db2->getone("form");

        $update["data"] = serialize($data);
        $update["visitor_id"] = $visitor_id;
        $db2->onDuplicate($update);
        
        $id = $db2->insert("form", $data_insert);
        if($id){
            $con = new connector();
            $con->trigger($visitor_id,"form_submit");
            $return["code"] = 1;
			$return["result"]["msg"] = "sukses add form";
            $return["id"] = $id;
			return $return;
        }else{
			$return["code"] = 0;
			$return["result"]["msg"] = "error add form";
			return $return;
        }
    }


    function cek_phone($phone){
        $countryCodes = [
            '44' => 'UK (+44)',
            '1' => 'USA (+1)',
            '213' => 'Algeria (+213)',
            '376' => 'Andorra (+376)',
            '244' => 'Angola (+244)',
            '1264' => 'Anguilla (+1264)',
            '1268' => 'Antigua & Barbuda (+1268)',
            '54' => 'Argentina (+54)',
            '374' => 'Armenia (+374)',
            '297' => 'Aruba (+297)',
            '61' => 'Australia (+61)',
            '43' => 'Austria (+43)',
            '994' => 'Azerbaijan (+994)',
            '1242' => 'Bahamas (+1242)',
            '973' => 'Bahrain (+973)',
            '880' => 'Bangladesh (+880)',
            '1246' => 'Barbados (+1246)',
            '375' => 'Belarus (+375)',
            '32' => 'Belgium (+32)',
            '501' => 'Belize (+501)',
            '229' => 'Benin (+229)',
            '1441' => 'Bermuda (+1441)',
            '975' => 'Bhutan (+975)',
            '591' => 'Bolivia (+591)',
            '387' => 'Bosnia Herzegovina (+387)',
            '267' => 'Botswana (+267)',
            '55' => 'Brazil (+55)',
            '673' => 'Brunei (+673)',
            '359' => 'Bulgaria (+359)',
            '226' => 'Burkina Faso (+226)',
            '257' => 'Burundi (+257)',
            '855' => 'Cambodia (+855)',
            '237' => 'Cameroon (+237)',
            '1' => 'Canada (+1)',
            '238' => 'Cape Verde Islands (+238)',
            '1345' => 'Cayman Islands (+1345)',
            '236' => 'Central African Republic (+236)',
            '56' => 'Chile (+56)',
            '86' => 'China (+86)',
            '57' => 'Colombia (+57)',
            '269' => 'Comoros (+269)',
            '242' => 'Congo (+242)',
            '682' => 'Cook Islands (+682)',
            '506' => 'Costa Rica (+506)',
            '385' => 'Croatia (+385)',
            '53' => 'Cuba (+53)',
            '90392' => 'Cyprus North (+90392)',
            '357' => 'Cyprus South (+357)',
            '42' => 'Czech Republic (+42)',
            '45' => 'Denmark (+45)',
            '253' => 'Djibouti (+253)',
            '1809' => 'Dominica (+1809)',
            '1809' => 'Dominican Republic (+1809)',
            '593' => 'Ecuador (+593)',
            '20' => 'Egypt (+20)',
            '503' => 'El Salvador (+503)',
            '240' => 'Equatorial Guinea (+240)',
            '291' => 'Eritrea (+291)',
            '372' => 'Estonia (+372)',
            '251' => 'Ethiopia (+251)',
            '500' => 'Falkland Islands (+500)',
            '298' => 'Faroe Islands (+298)',
            '679' => 'Fiji (+679)',
            '358' => 'Finland (+358)',
            '33' => 'France (+33)',
            '594' => 'French Guiana (+594)',
            '689' => 'French Polynesia (+689)',
            '241' => 'Gabon (+241)',
            '220' => 'Gambia (+220)',
            '7880' => 'Georgia (+7880)',
            '49' => 'Germany (+49)',
            '233' => 'Ghana (+233)',
            '350' => 'Gibraltar (+350)',
            '30' => 'Greece (+30)',
            '299' => 'Greenland (+299)',
            '1473' => 'Grenada (+1473)',
            '590' => 'Guadeloupe (+590)',
            '671' => 'Guam (+671)',
            '502' => 'Guatemala (+502)',
            '224' => 'Guinea (+224)',
            '245' => 'Guinea - Bissau (+245)',
            '592' => 'Guyana (+592)',
            '509' => 'Haiti (+509)',
            '504' => 'Honduras (+504)',
            '852' => 'Hong Kong (+852)',
            '36' => 'Hungary (+36)',
            '354' => 'Iceland (+354)',
            '91' => 'India (+91)',
            '62' => 'Indonesia (+62)',
            '98' => 'Iran (+98)',
            '964' => 'Iraq (+964)',
            '353' => 'Ireland (+353)',
            '972' => 'Israel (+972)',
            '39' => 'Italy (+39)',
            '1876' => 'Jamaica (+1876)',
            '81' => 'Japan (+81)',
            '962' => 'Jordan (+962)',
            '7' => 'Kazakhstan (+7)',
            '254' => 'Kenya (+254)',
            '686' => 'Kiribati (+686)',
            '850' => 'Korea North (+850)',
            '82' => 'Korea South (+82)',
            '965' => 'Kuwait (+965)',
            '996' => 'Kyrgyzstan (+996)',
            '856' => 'Laos (+856)',
            '371' => 'Latvia (+371)',
            '961' => 'Lebanon (+961)',
            '266' => 'Lesotho (+266)',
            '231' => 'Liberia (+231)',
            '218' => 'Libya (+218)',
            '417' => 'Liechtenstein (+417)',
            '370' => 'Lithuania (+370)',
            '352' => 'Luxembourg (+352)',
            '853' => 'Macao (+853)',
            '389' => 'Macedonia (+389)',
            '261' => 'Madagascar (+261)',
            '265' => 'Malawi (+265)',
            '60' => 'Malaysia (+60)',
            '960' => 'Maldives (+960)',
            '223' => 'Mali (+223)',
            '356' => 'Malta (+356)',
            '692' => 'Marshall Islands (+692)',
            '596' => 'Martinique (+596)',
            '222' => 'Mauritania (+222)',
            '269' => 'Mayotte (+269)',
            '52' => 'Mexico (+52)',
            '691' => 'Micronesia (+691)',
            '373' => 'Moldova (+373)',
            '377' => 'Monaco (+377)',
            '976' => 'Mongolia (+976)',
            '1664' => 'Montserrat (+1664)',
            '212' => 'Morocco (+212)',
            '258' => 'Mozambique (+258)',
            '95' => 'Myanmar (+95)',
            '264' => 'Namibia (+264)',
            '674' => 'Nauru (+674)',
            '977' => 'Nepal (+977)',
            '31' => 'Netherlands (+31)',
            '687' => 'New Caledonia (+687)',
            '64' => 'New Zealand (+64)',
            '505' => 'Nicaragua (+505)',
            '227' => 'Niger (+227)',
            '234' => 'Nigeria (+234)',
            '683' => 'Niue (+683)',
            '672' => 'Norfolk Islands (+672)',
            '670' => 'Northern Marianas (+670)',
            '47' => 'Norway (+47)',
            '968' => 'Oman (+968)',
            '680' => 'Palau (+680)',
            '507' => 'Panama (+507)',
            '675' => 'Papua New Guinea (+675)',
            '595' => 'Paraguay (+595)',
            '51' => 'Peru (+51)',
            '63' => 'Philippines (+63)',
            '48' => 'Poland (+48)',
            '351' => 'Portugal (+351)',
            '1787' => 'Puerto Rico (+1787)',
            '974' => 'Qatar (+974)',
            '262' => 'Reunion (+262)',
            '40' => 'Romania (+40)',
            '7' => 'Russia (+7)',
            '250' => 'Rwanda (+250)',
            '378' => 'San Marino (+378)',
            '239' => 'Sao Tome & Principe (+239)',
            '966' => 'Saudi Arabia (+966)',
            '221' => 'Senegal (+221)',
            '381' => 'Serbia (+381)',
            '248' => 'Seychelles (+248)',
            '232' => 'Sierra Leone (+232)',
            '65' => 'Singapore (+65)',
            '421' => 'Slovak Republic (+421)',
            '386' => 'Slovenia (+386)',
            '677' => 'Solomon Islands (+677)',
            '252' => 'Somalia (+252)',
            '27' => 'South Africa (+27)',
            '34' => 'Spain (+34)',
            '94' => 'Sri Lanka (+94)',
            '290' => 'St. Helena (+290)',
            '1869' => 'St. Kitts (+1869)',
            '1758' => 'St. Lucia (+1758)',
            '249' => 'Sudan (+249)',
            '597' => 'Suriname (+597)',
            '268' => 'Swaziland (+268)',
            '46' => 'Sweden (+46)',
            '41' => 'Switzerland (+41)',
            '963' => 'Syria (+963)',
            '886' => 'Taiwan (+886)',
            '7' => 'Tajikstan (+7)',
            '66' => 'Thailand (+66)',
            '228' => 'Togo (+228)',
            '676' => 'Tonga (+676)',
            '1868' => 'Trinidad & Tobago (+1868)',
            '216' => 'Tunisia (+216)',
            '90' => 'Turkey (+90)',
            '7' => 'Turkmenistan (+7)',
            '993' => 'Turkmenistan (+993)',
            '1649' => 'Turks & Caicos Islands (+1649)',
            '688' => 'Tuvalu (+688)',
            '256' => 'Uganda (+256)',
            '380' => 'Ukraine (+380)',
            '971' => 'United Arab Emirates (+971)',
            '598' => 'Uruguay (+598)',
            '7' => 'Uzbekistan (+7)',
            '678' => 'Vanuatu (+678)',
            '379' => 'Vatican City (+379)',
            '58' => 'Venezuela (+58)',
            '84' => 'Vietnam (+84)',
            '84' => 'Virgin Islands - British (+1284)',
            '84' => 'Virgin Islands - US (+1340)',
            '681' => 'Wallis & Futuna (+681)',
            '969' => 'Yemen (North)(+969)',
            '967' => 'Yemen (South)(+967)',
            '260' => 'Zambia (+260)',
            '263' => 'Zimbabwe (+263)',
        ];

            // Remove spaces, hyphens, and parentheses for easier matching
        $phone = preg_replace("/[\s\-\(\)]/", "", $phone);
        
        

        // Remove the '+' and check for country code match
        $numberWithoutPlus = str_replace("+","", $phone);
        
      
        
        // Try to match each country code
        foreach ($countryCodes as $code => $country) {
            // Check if the number starts with the current country code
            if (strpos($numberWithoutPlus, (string) $code) === 0) {
                return $phone;
            }
        }
        
        return false; // No valid country code found
    }

    public function get($start='', $end='')
    {
        global $app;
        $db2 = $app->db2;
        if($start !='' && $end !=''){
             $db2->where("DATE(waktu_contact) BETWEEN ? AND ?", array($start, $end));  
        }
        $data_form = $db2->get("form", null, "visitor_id, phone, waktu_contact, data, status");
        foreach ($data_form as $key => $value) {
            $data_form[$key]["data"] = unserialize($value["data"]);
            if (is_array($data_form[$key]["data"])) {
                foreach($data_form[$key]["data"] as $k=> $v){
                    $data_form[$key][$k] = $v;
                }
                //$data_form[$key] = $data_form[$key]["data"];
                unset($data_form[$key]["data"]);
            }
            $visitor_id = convBase($value["visitor_id"], "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
            $visitor_id = str_split($visitor_id, 4);
            $visitor_id = implode(".", $visitor_id);
            $data_form[$key]["visitor_id"] = $visitor_id;
        }
        $res['colum'] = explode(", ", "visitor_id, phone, waktu_contact, name, email, msg, status");
        $res['data'] = $data_form;
        return $res;
    }

     public function send_message($visitor_id)
    {
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
        $visitor_id = str_replace(".", "", $visitor_id);
        $visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");
        $db2->join("cs c","c.cs_key = f.cs_key","LEFT");
        $db2->where("f.visitor_id = ? and f.status = ?", array($visitor_id, 0));  
        $data_form = $db2->getone("form f", "f.visitor_id, f.phone, f.waktu_contact, f.data, HEX(f.cs_key) as cs_key, c.phone as phone_cs");
         //print_r( $data_form);
        if($db2->count >0){
            $data_form["data"] = unserialize($value["data"]);
            if (is_array($data_form["data"])) {
                foreach($data_form["data"] as $k=> $v){
                    $data_form[$k] = $v;
                }
                unset($data_form["data"]);
            }
            $phone_cs = $data_form['phone_cs'];
            $m = new meta();
            $tmp= $m->get_meta("form_contact_default_cs");
            if($tmp["code"] == 1){
                if($tmp["result"]["data"] != 0){
                    $phone_cs = $tmp["result"]["data"];
                }                
            }  
            $tmp= $m->get_meta("form_contact_default_msg");
            if($tmp["code"] == 1){
                 if($tmp["result"]["data"]!=''){
                    //$setting["form_contact_default_msg"] =  $tmp["result"]["data"];
                    $post = array("act" => 'gass_send', "sender"=>$phone_cs, "to" => $data_form["phone"], "message" =>$tmp["result"]["data"]);
                    
                    $res = post_x_contents($post, "https://api.konekwa.com/api.html");
                    $res = json_decode($res, true);
                    if($res['code']==1){
                        $ret["code"] = 1;
                        $ret["msg"] = "Success send message";
                        $db2->where("visitor_id = ?", array($visitor_id));  
                        $db2->update("form", array('status'=> 1));
                    }else{
                        $ret["code"] = 0;
                        $ret["msg"] = "Error send message";
                    }
                }else{
                    $ret["code"] = 0;
                    $ret["msg"] = "Error, default message not set";
                }
            }else{
                $ret["code"] = 0;
                $ret["msg"] = "Error, message default not set.";
            }
            //$ret["data"] = $data_form;
            return $ret;
        }else{
            $ret["code"] = 0;
            $ret["msg"] = "visitor not found";
            return $ret;
        }
    }
    
    public function cron_send_message(){
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
        $db2->join("cs c","c.cs_key = f.cs_key","LEFT");
        $db2->where("f.status = ?", array(0));  
        $data_form = $db2->get("form f", 3, "f.visitor_id, f.phone, f.waktu_contact, f.data, HEX(f.cs_key) as cs_key, c.phone as phone_cs");
         //print_r( $data_form);
        if($db2->count >0){
            $phone_cs = '';
            $m = new meta();
            $tmp= $m->get_meta("form_contact_default_cs");
            if($tmp["code"] == 1){
                if($tmp["result"]["data"] != 0){
                    $phone_cs = $tmp["result"]["data"];
                }                
            }            
            $tmp= $m->get_meta("form_contact_default_msg");
            if($tmp["code"] == 1){
                 if($tmp["result"]["data"]!=''){
                    foreach($data_form as $k=>$v){
                        if($phone_cs==''){
                            $phone_cs = $v['phone_cs'];
                        }
                        $post = array("act" => 'gass_send', "sender"=>$phone_cs, "to" => $v["phone"], "message" =>$tmp["result"]["data"]);
                        $rex = post_x_contents($post, "https://api.konekwa.com/api.html");
                        $res = json_decode($rex, true);
                        if($res['code']==1){
                            $ret["code"] = 1;
                            $ret["msg"] = "Success send message";
                            $db2->where("visitor_id = ?", array($v['visitor_id']));  
                            $db2->update("form", array('status'=> 1));
                        }else{
                            $ret["code"] = 0;
                            $ret["msg"] = "Error send message";
                            $ret["result"] = $rex;
                            $ret["post"] = $post;
                        }
                    }
                    
                }else{
                    $ret["code"] = 0;
                    $ret["msg"] = "Error, default message not set";
                }
            }else{
                $ret["code"] = 0;
                $ret["msg"] = "Error, message default not set.";
            }
            //$ret["data"] = $data_form;
            return $ret;
        }else{
            $ret["code"] = 0;
            $ret["msg"] = "visitor empty";
            return $ret;
        }
    }
    
    
    function delete($form_id)
    {
        global $app;
        $db2 = $app->db2;

        if(empty($form_id)) return false;

        $db2->where("form_id",$form_id);
        $db2->delete("form");
    }
}
