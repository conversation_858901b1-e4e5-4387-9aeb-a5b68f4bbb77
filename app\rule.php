<?php

class rule
{

    public function get_metric()
    {
        global $app;
        $db2 = $app->db2;

        $db2->groupBy("report_key");
        $standart_metric = $db2->get("report_data", null, "report_key");
        $r = new report();
        $custom_metric = $r->get_metric();

        $metric = array();
        $i = 0;
        if (!empty($metric)) {
            foreach ($standart_metric as $key => $value) {
                $metric[$i] = $value;
                $metric[$i]["type"] = "Fix_value";
                $i++;
            }
        }

        if (!empty($custom_metric)) {
            foreach ($custom_metric as $key => $value) {
                $metric[$i] = $value;
                $metric[$i]["type"] = "custom_metric";
                $i++;
            }
        }

        $ret = array();

        $tmp["id"] = "time";
        $tmp["type"] = "time";
        $tmp["name"] = "Time";
        $tmp["option"] = "day";
        $tmp["operator"] = "true";
        $ret[] = $tmp;

        $tmp["id"] = "status";
        $tmp["name"] = "Status";
        $tmp["option"] = "status";
        $tmp["operator"] = "false";
        $ret[] = $tmp;

        foreach ($metric as $key => $value) {

            $key = "";
            if (isset($value["report_key"])) {
                $key = $value["report_key"];
            } elseif (isset($value["metric_name"])) {
                $key = $value["metric_name"];
            }

            if ($key != "") {
                $tmp["id"] = $key;
                $tmp["type"] = "number";
                $tmp["name"] = ucwords(str_replace("_", " ", $key));
                $tmp["option"] = "period";
                $tmp["operator"] = true;

                if ($value["type"] == "metric") {
                    $tmp["metric_type"] = "metric";
                }
                if ($value["type"] == "custom_metric") {
                    $tmp["metric_type"] = "custom_metric";
                }

                $ret[] = $tmp;
            }

        }

        return $ret;
    }

    public function running_rule()
    {
        global $app;
        $db2 = $app->db2;

        $convertion_type = "realtime";
        $db2->where("status", 1);
        $rules = $db2->get("rule_fb");

        

        foreach ($rules as $key => $rule) {

            $freq = !empty($rule["freq"]) ? $rule["freq"] : 0;

            $targets = unserialize($rule["target"]);
            foreach ($targets as $target) {
                $target = unserialize($target["targeting_rule"]);
                $type = $target["level"];
                if ($target["method"] == "specific") {
                    foreach ($target["item"] as $key => $external_id) {
                       $xxx =  $this->cek_rule($rule, $external_id, $type, $convertion_type, $freq);
                       var_dump($xxx);
                    }
                }
            }
        }
    }

    public function cek_rule($rule, $external_id, $type, $convertion_type, $freq = 0)
    {

       

        $parsedRule = preg_replace_callback('/\[([^\]]+)\]/', function ($matches) use ($rule, $external_id, $type) {
            $placeholder = $matches[1];
            if (strpos($placeholder, 'metric:') === 0) {
                
                // Parse the metric and return the appropriate value
                $metricDetails = $this->parseMetric(substr($placeholder, 7), $external_id, $type); // Remove "metric:"
                if ($metricDetails) {
                    return 'getMetricValue("' . $metricDetails . '")'; // Adjusted to reflect parsed metrics
                }
            } elseif (strpos($placeholder, 'day:') === 0) {
                // Extract the day offset
                return 'date("H:i:s")';
            }
            return $matches[0]; // No replacement if the pattern doesn't match
        }, $rule);
      
        $lastExecutionTime = $this->getLastExecutionTime($external_id);
        $currentTime = new DateTime();

        if ($lastExecutionTime) {
            $lastExecutionDateTime = new DateTime($lastExecutionTime);
            // Check if more than a specified time has passed, e.g., 1
            $interval = $currentTime->diff($lastExecutionDateTime);
            if($interval->h >= $freq){
                $this->updateLastExecutionTime($external_id);
                return $parsedRule;
            }
        }
        return false;

    }

    public function getMetricValue($metrics, $external_id, $type)
    {
        // if($metrics['day'] == '0'){
        $now = new DateTime();
        $startOfDay = (clone $now)->setTime(0, 0, 0);
        $endOfDay = (clone $now)->setTime(23, 59, 59);
        // }

        // Example implementation, replace with actual logic
        if ($metrics['type'] == 'simple') {
            $value = $this->getReportData($metrics['table'], $type, $startOfDay, $endOfDay, $external_id);
        } else {
            if ($metrics['table'] == 'fix_value') {
                $value = $this->getReportData($metrics['table'], $type, $startOfDay, $endOfDay, $external_id);
            } else {
                $value = $this->getReportData($metrics['table'], $type, $startOfDay, $endOfDay, $external_id);
            }
        }
        return $value;
    }
    public function isWithinTimeRange($startTime, $endTime)
    {
        $currentTime = time(); // Get the current time
        return $currentTime >= strtotime($startTime) && $currentTime <= strtotime($endTime);
    }
    // Replace logical operators to be PHP-friendly

    public function parseMetric($metricString, $external_id, $type)
    {
        // Split by colon and trim whitespace
        $parts = array_map('trim', explode(':', $metricString));

        // Check the structure of the metric
        if (count($parts) === 2) {
            $column = $parts[0];
            $dayDefinition = null; // No day definition for this structure
            $tableSource = $parts[1];
            $data = [
                'type' => 'simple', // Simple metric with column and table
                'column' => $column,
                'table' => $tableSource,
            ];
            return $this->getMetricValue($data, $external_id, $type);
        } elseif (count($parts) === 3) {
            $column = $parts[0];
            $dayDefinition = $parts[1];
            $tableSource = $parts[2];
            $data = [
                'type' => 'complex', // Complex metric with column, day definition, and table
                'column' => $column,
                'day' => $dayDefinition,
                'table' => $tableSource,
            ];
            return $this->getMetricValue($data, $external_id, $type);
        }
        return null; // Invalid metric format
    }
    public function getReportData($report_key, $type, $start, $end, $external_id = null)
    {
        $start = $start->format('Y-m-d H:i:s');
        $end = $end->format('Y-m-d H:i:s');
        // var_dump($external_id, $type, $start, $end, $report_key);

        global $app;
        $db2 = $app->db2;

        $db2->join("report r3", "r3.report_id = report_data.report_id");
        $db2->join("report r2", "r2.report_id = r3.parent_id");
        $db2->join("report r1", "r1.report_id = r2.parent_id");

        if ($type == "campaign") {
            $db2->where("r1.external_id", $external_id);
            $db2->groupBy("report_data.report_key,r1.report_id");
        }

        if ($type == "adset") {
            $db2->where("r2.external_id", $external_id);
            $db2->groupBy("report_data.report_key,r2.report_id");
        }

        if ($type == "adcopy") {
            $db2->where("r3.external_id", $external_id);
            $db2->groupBy("report_data.report_key,r3.report_id");
        }

        $db2->where('report_key', $report_key);
        $db2->where("date", $start, ">=");
        $db2->where("date", $end, "<=");
        $result = $db2->getone("report_data", "sum(report_data.report_value) as value");

        if (empty($result)) {
            return 0;
        } else {
            return $result["value"];
        }
    }

    public function updateLastExecutionTime($external_id)
    {
        global $app;
        $db2 = $app->db2;

        $currentTime = (new DateTime())->format('Y-m-d H:i:s');

        // Update the last execution time in the database
        $data = [
            "last_execution_time" => $currentTime,
        ];
        $db2->where("external_id", $external_id);
        $db2->update("task_execution_log", $data);
    }

    public function getLastExecutionTime($external_id)
    {
        global $app;
        $db2 = $app->db2;

        // Retrieve the last execution time from the database
        $db2->where("external_id", $external_id);
        $result = $db2->getOne("task_execution_log", "last_execution_time");

        return $result ? $result["last_execution_time"] : null; // Return null if not found
    }

}
