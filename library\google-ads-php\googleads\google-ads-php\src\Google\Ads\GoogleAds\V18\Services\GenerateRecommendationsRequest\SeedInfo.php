<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/services/recommendation_service.proto

namespace Google\Ads\GoogleAds\V18\Services\GenerateRecommendationsRequest;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A keyword seed and a specific url to generate keywords from.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.services.GenerateRecommendationsRequest.SeedInfo</code>
 */
class SeedInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * A specific url to generate ideas from, for example: www.example.com/cars.
     *
     * Generated from protobuf field <code>optional string url_seed = 2;</code>
     */
    protected $url_seed = null;
    /**
     * Optional. Keywords or phrases to generate ideas from, for example: cars
     * or "car dealership near me".
     *
     * Generated from protobuf field <code>repeated string keyword_seeds = 3 [(.google.api.field_behavior) = OPTIONAL];</code>
     */
    private $keyword_seeds;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $url_seed
     *           A specific url to generate ideas from, for example: www.example.com/cars.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $keyword_seeds
     *           Optional. Keywords or phrases to generate ideas from, for example: cars
     *           or "car dealership near me".
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Services\RecommendationService::initOnce();
        parent::__construct($data);
    }

    /**
     * A specific url to generate ideas from, for example: www.example.com/cars.
     *
     * Generated from protobuf field <code>optional string url_seed = 2;</code>
     * @return string
     */
    public function getUrlSeed()
    {
        return isset($this->url_seed) ? $this->url_seed : '';
    }

    public function hasUrlSeed()
    {
        return isset($this->url_seed);
    }

    public function clearUrlSeed()
    {
        unset($this->url_seed);
    }

    /**
     * A specific url to generate ideas from, for example: www.example.com/cars.
     *
     * Generated from protobuf field <code>optional string url_seed = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setUrlSeed($var)
    {
        GPBUtil::checkString($var, True);
        $this->url_seed = $var;

        return $this;
    }

    /**
     * Optional. Keywords or phrases to generate ideas from, for example: cars
     * or "car dealership near me".
     *
     * Generated from protobuf field <code>repeated string keyword_seeds = 3 [(.google.api.field_behavior) = OPTIONAL];</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getKeywordSeeds()
    {
        return $this->keyword_seeds;
    }

    /**
     * Optional. Keywords or phrases to generate ideas from, for example: cars
     * or "car dealership near me".
     *
     * Generated from protobuf field <code>repeated string keyword_seeds = 3 [(.google.api.field_behavior) = OPTIONAL];</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setKeywordSeeds($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->keyword_seeds = $arr;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(SeedInfo::class, \Google\Ads\GoogleAds\V18\Services\GenerateRecommendationsRequest_SeedInfo::class);

