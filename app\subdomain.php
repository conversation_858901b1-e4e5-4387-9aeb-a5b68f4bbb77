<?php 

class subdomain{
    function checkSSLCertificate($domain, $port = 443) {
        $streamContext = stream_context_create([
            "ssl" => [
                "capture_peer_cert" => true,
                "verify_peer" => false,
                "verify_peer_name" => false,
            ]
        ]);
    
        $client = @stream_socket_client("ssl://{$domain}:{$port}", $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $streamContext);
    
        if (!$client) {
            return [
                'status' => 'error',
                'message' => "Koneksi gagal: $errstr ($errno)",
                'domain' => $domain,
            ];
        }
    
        $params = stream_context_get_params($client);
        $cert = $params["options"]["ssl"]["peer_certificate"];
        $certInfo = openssl_x509_parse($cert);
    
        $validTo = $certInfo['validTo_time_t'];
        $validToDate = date('Y-m-d H:i:s', $validTo);
        $now = time();
    
        if ($validTo < $now) {
            $daysExpired = floor(($now - $validTo) / 86400);
            return [
                'status' => 'expired',
                'domain' => $domain,
                'valid_to' => $validToDate,
                'days' => $daysExpired,
                'message' => "SSL expired $daysExpired hari yang lalu pada $validToDate"
            ];
        } else {
            $daysRemaining = floor(($validTo - $now) / 86400);
            return [
                'status' => 'valid',
                'domain' => $domain,
                'valid_to' => $validToDate,
                'days' => $daysRemaining,
                'message' => "SSL masih berlaku sampai $validToDate ($daysRemaining hari lagi)"
            ];
        }
    }
    
    function add_subdomain($subdomain){
        global $app;        
        $db2 = $app->db2;
        $subdomain = strtolower(preg_replace('/\s+/', '', $subdomain));
        $dt = new DateTime("now", new DateTimeZone('Asia/Jakarta'));
        $timestamp = $dt->format('Y-m-d H:i:s');
        $subdomain = str_replace("https://", "", $subdomain);
        $subdomain = str_replace("http://", "", $subdomain);
        $tmp = explode("/", $subdomain); 
        if(count($tmp) > 1){
            $subdomain = $tmp[0];
        }
        
        $db2->where("subdomain_key = UNHEX(?)",[md5($subdomain)]);
        $res = $db2->getone("subdomain", "subdomain_id,HEX(subdomain_key) as subdomain_key,name,status, siteId");
        if($db2->count > 0){
            $return["code"] = 0;
            $return["result"]["msg"] = "subdomain already exists";
            return $return;
        }
        //if($add_sub){
            //$subdomain = strtolower($subdomain);
            
            
            $data["subdomain_key"] = $db2->func("UNHEX(?)",[md5($subdomain)]);
            $data["name"] = $subdomain;
            $data['status'] = 0;
            $data['expired_ssl'] = date('Y-m-d', strtotime($timestamp.' +0 days'));
            if($id = $db2->insert("subdomain",$data)){                
                $return["code"] = 1;
                $return["result"]["data"] = $id;
                $return["result"]["msg"] = "sukses";
            }else{
                $return["code"] = 0;
                $return["result"]["msg"] = "error";
            }
       // }
      
        return $return;
    }	

    function edit_subdomain($subdomain, $data){
        global $app;        
        $db2 = $app->db2;
        //if($add_sub){
            //$subdomain = strtolower($subdomain);
            $subdomain = strtolower(preg_replace('/\s+/', '', $subdomain));
            $subdomain = str_replace("https://", "", $subdomain);
            $subdomain = str_replace("http://", "", $subdomain);
            $tmp = explode("/", $subdomain);
            if(count($tmp) > 1){
                $subdomain = $tmp[0];
            }
            $db2->where("subdomain_key = UNHEX(?)",[md5($subdomain)]);
            if($id = $db2->update("subdomain",$data)){                
                $return["code"] = 1;
                $return["result"]["data"] = $id;
                $return["result"]["msg"] = "sukses edit";
            }else{
                $return["code"] = 0;
                $return["result"]["msg"] = "error edit";
            }
       // }
      
        return $return;
    }
    
    function renew_ssl($subdomain){
        global $app;        
        $db2 = $app->db2;
        $subdomain = strtolower(preg_replace('/\s+/', '', $subdomain));
        $subdomain = str_replace("https://", "", $subdomain);
        $subdomain = str_replace("http://", "", $subdomain);
        $tmp = explode("/", $subdomain);
        if(count($tmp) > 1){
            $subdomain = $tmp[0];
        }
        
        // Cek status SSL terlebih dahulu
        $ssl_info = $this->checkSSLCertificate($subdomain);
        $db2->where("subdomain_key = UNHEX(?) ",[md5($subdomain)]);
        $res = $db2->getone("subdomain s", "s.*, DATEDIFF(expired_ssl, NOW()) AS days_left");
        if($db2->count >0){   
            // Hanya lakukan renew jika SSL expired dan kurang dari 30 hari
            if($ssl_info['status'] == 'expired' || $ssl_info['days'] <= 30){
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, "http://localhost:3434/renew-ssl");
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
                $payload = json_encode(array("domain" => $subdomain));
                curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
                $response = curl_exec($ch);
                $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                $msg = json_decode($response, true);
                
                if(isset($msg['code']) && $msg['code'] == 1){
                    $dt = new DateTime("now", new DateTimeZone('Asia/Jakarta'));
                    $timestamp = $dt->format('Y-m-d H:i:s');
                    $dd['expired_ssl'] = date('Y-m-d', strtotime($timestamp.' +90 days'));
                    $db2->where("subdomain_key = UNHEX(?) ",[md5($subdomain)]);
                    $db2->update("subdomain", $dd);
                    
                    // Cek SSL ulang setelah renew
                    $ssl_check_after = $this->checkSSLCertificate($subdomain);
                    if($ssl_check_after['status'] == 'expired' && $ssl_check_after['days'] <= 30){
                        $return["code"] = 0;
                        $return["result"]["msg"] = "SSL masih expired setelah renew: " . $ssl_check_after['message'].". Please contact support.";
                    }else{
                        $return["code"] = 1;
                        $return["result"]["msg"] = $msg['msg'];
                    }
                }else{
                    $bt      = new bt_api();
                    $rex = $bt->get_ssl($subdomain); 
                    if($rex['status']==true){
                        if($rex['index']==null){
                            $rex = $bt->new_ssl($res['siteId'], $subdomain); 
                        }else{
                            $rex = $bt->renew_ssl($rex['index']); 
                        }
                    }
                    $msg = $rex;
                    if(isset($msg['code']) && $msg['code'] == 1){
                        $dt = new DateTime("now", new DateTimeZone('Asia/Jakarta'));
                        $timestamp = $dt->format('Y-m-d H:i:s');
                        $dd['expired_ssl'] = date('Y-m-d', strtotime($timestamp.' +90 days'));
                        $db2->where("subdomain_key = UNHEX(?) ",[md5($subdomain)]);
                        $db2->update("subdomain", $dd);
                        
                        // Cek SSL ulang setelah renew
                        $ssl_check_after = $this->checkSSLCertificate($subdomain);
                        if($ssl_check_after['status'] == 'expired' && $ssl_check_after['days'] <= 30){
                            $return["code"] = 0;
                            $return["result"]["msg"] = "SSL masih expired setelah renew: " . $ssl_check_after['message']." Please contact support.";
                        }else{
                            $return["code"] = 1;
                            $return["result"]["msg"] = $msg['msg'];
                        }
                    }else{
                        $return = $msg;
                        $return["result"]["msg"] = "error renew ssl ".$msg['msg'];
                    }
                }
            }else{
                $return["code"] = 0;
                $return["result"]["msg"] = "expired ssl masih panjang.";
            }
        }else{
            $return["code"] = 0;
            $return["result"]["msg"] = "error renew ssl";
        }
      
        return $return;
    }
    
    function delete_subdomain($subdomain_key){
        global $app;
        $db2 = $app->db2;
        $db2->where("subdomain_key = UNHEX(?)",[$subdomain_key]);
        $sub = $db2->getone("subdomain", "subdomain_id,HEX(subdomain_key) as subdomain_key,name,status, siteId");
        if($db2->count> 0){
            $db2->where("subdomain_key = UNHEX(?)",[$subdomain_key]);
            if($db2->delete("subdomain")){
                if (file_exists('config/'.$sub['name'].'.php')) {
                    unlink('config/'.$sub['name'].'.php');
                }
                $bt = new bt_api();
                $bt->delete_site($sub['siteId'], $sub['name']);
                $return["code"] = 1;
                $return["result"]["msg"] = "success, delete subdomain";
            }else{
                $return["code"] = 0;
                $return["result"]["msg"] = "error, delete subdomain";
            }
        }else{
            $return["code"] = 0;
            $return["result"]["msg"] = "error, subdomain not found";
        }        
        return $return;
    }

    public static function get_subdomain($subdomain = NULL){
        global $app;
        $db2 = $app->db2;
        if($subdomain != NULL){
            $subdomain = strtolower($subdomain);
            $subdomain = str_replace("https://", "", $subdomain);
            $subdomain = str_replace("http://", "", $subdomain);
            $tmp = explode("/", $subdomain);
            if(count($tmp) > 1){
                $subdomain = $tmp[0];
            }
            $db2->where("subdomain_key = UNHEX(?)",[md5($subdomain)]);
        }
        $res = $db2->get("subdomain",NULL,"subdomain_id,HEX(subdomain_key) as subdomain_key,name,status,expired_ssl,siteId");
        // Tambahkan info SSL untuk setiap subdomain
        if ($res && is_array($res)) {
            foreach ($res as $k => $row) {
                $ssl_info = (new self())->checkSSLCertificate($row['name']);
                $res[$k]['ssl_info'] = $ssl_info;
                // Ganti expired_ssl dengan valid_to dari ssl_info
                $res[$k]['expired_ssl'] = isset($ssl_info['valid_to']) ? $ssl_info['valid_to'] : 'unresolved';
                if($ssl_info['status'] == 'expired'){
                    $res[$k]['status'] = 0;
                }else{
                    $res[$k]['status'] = 1;
                }
            }
        }
        $return["code"] = 1;
        $return["result"]["data"] = $res;
        $return["result"]["msg"] = "sukses";
        if($res == NULL) {
            $return["code"] = 0;
            $return["result"]["msg"] = "subdomain not found";
        }
        return $return;
    }

}