<?php

function add($param)
{
    global $app;
    $rules = [
        "code" => "required",
        "url" => "required",
    ];
    validate_param($rules, $param);
    extract($param);
    $db = $app->db;
    $po['code'] = $code;
    $po['url'] = $url;
    $db->where("code", $code);
    $db->getone("cron");
    if ($db->count == 0) {
        $id = $db->insert("cron", $po);
    } else {
        $id = 0;
    }
    return $id;
}

function run()
{
    //return false;
    global $app;
    $db = $app->db;
    $dt = new DateTime("now", new DateTimeZone('Asia/Jakarta'));
    $timestamp = $dt->format('Y-m-d H:i:s');
    // $db->where("waktu < ?", array($timestamp));
    // $db->orderby('waktu', 'Asc');
    // $res = $db->getone("cron");
    // $msg = '';
    // if ($db->count > 0) {
    //     $dt->modify('+1 day');
    //     $newtimestamp = $dt->format('Y-m-d H:i:s');
    //     $msg = file_get_contents($res['url']);
    //     $db->where('id', $res["id"]);
    //     $db->update("cron", array('waktu' => $newtimestamp));
    // }

    $res = $db->getone("cron");
    if ($db->count > 0) {
        $parsedUrl = parse_url($res['url']);
        $ip = $parsedUrl['host'];
    }else{
        $ip = $_SERVER['SERVER_ADDR'];
    }

    //return $msg;
    // send rabbit mq fb grab
    if($ip !== '***********'){
        $db->where("project_id > ?", array(6));
    }    
    $db->orderby("project_id", "asc");
    $projects = $db->get("project");
    foreach($projects as $y=>$z){
        $project_id = $z['project_id'];
        assign_child_db($project_id);
        $db2 = $app->db2;
        $acc = new account($project_id);
        $db2->where("project_id = ?", array($project_id));
        $integration = $db2->get("integration");
        if($db2->count >0){
            foreach($integration as $k=>$v){
                //$v['webhook'] = 'http://'.$_SERVER['SERVER_ADDR'].'/api.html?act=spend_update&project_id='.$project_id;
                $v['data'] = unserialize($v['data']);
                unset($v['account_hash']);
                if($v['platform']=='google'){
                    $v['data']['google_clientId'] = $app->config->google_clientId;
                    $v['data']['google_clientSecret'] = $app->config->google_clientSecret;
                    $v['data']['google_developerToken'] = $app->config->google_developerToken;
                    $rex['webhook'] = 'http://'.$ip.'/api.html?act=spend_update_new&project_id='.$project_id.'&source=google';
                }elseif($v['platform']=='facebook'){
                    $v['data']['fb_app_id'] = $app->config->fb_app_id;
                    $v['data']['fb_app_secret'] = $app->config->fb_app_secret;
                    $rex['webhook'] = 'http://'.$ip.'/api.html?act=spend_update_new&project_id='.$project_id.'&source=meta';
                }        
                $rex['type'] = 'report';        
                $rex['res'] = $v;
                $pos['msg'] = json_encode($rex);
                $res = post_x_contents($pos, 'https://que-fb1.gass.co.id/add-task');
                print_r($res);
            }
        }
        // $rex['res'] = [];
        // $rex['type'] = 'cron';
        // $rex['webhook'] = 'http://'.$ip.'/api.html?act=project_grab_data_stat&project_id='.$project_id ;
        // $pos['msg'] = json_encode($rex);
        // $res = post_x_contents($pos, 'https://que-fb1.gass.co.id/add-task');
    }
    return $msg;
}


function resend($param)
{
    
    $project_id = $param["project_id"];
        assign_child_db($project_id);

        global $app;
        $db2 = $app->db2;

        $t = new track();

       
        echo "retriger add to report";
        echo "<hr/>";

        ////////////////////////// add to report
        $res = $db2->rawQuery("SELECT * FROM `visitor` WHERE waktu_contact >= '" . date("Y-m-d") . " 00:00:00' and waktu_contact <= '" . date("Y-m-d") . " 23:59:59'");
        $campaign = new report();
        foreach ($res as $key => $value) {
            $key = $value["phone"] . ";lead";
            if ($campaign->get_hash($key) == null) {
                echo $value["phone"] . "<br/>";
                $t->add_report_gass($value, "lead");
            }

        }

        echo "<hr/>";
        echo "retriger connector";
        echo "<hr/>";
        ////////////////////////// retriger connector
        foreach ($res as $key => $value) {
            $key = $value["phone"] . ";lead;" . date("Y-m-d");

            if ($campaign->get_hash($key) == null) {
                $t->retrigger_connector($value, "lead");

                echo $key . "<br/>";
            }
        }

        echo "<hr/>";
        echo "retriger bug visit";
        echo "<hr/>";

        $con = new connector();
        $visitors = $db2->rawQuery("select * from (SELECT * FROM `visitor` WHERE `lead` = 1 and waktu_contact >= '2024-10-30 18:00:00' and waktu_contact <= '2024-10-31 10:36:00') as x where x.visitor_id not in ( select log_connector.vid from log_connector where log_connector.waktu >= '2024-10-30 17:00:00') ");
        foreach ($visitors as $visitor) {
            $vid = $visitor["visitor_id"];

            $con->trigger($vid, "visit");
            $con->trigger($vid, "cta");
            $con->trigger($vid, "lead");

            echo $vid."<br/>";
           
        }

        die();
}

function get_usage_range_new($project_id, $start, $end, $user){
    global $app;
    assign_child_db($project_id);
    $db = $app->db2;
    $last_bill =  $start;  
    $yesterday =  $end;
    $db->where("DATE(created) BETWEEN ? AND ?", array($last_bill, $yesterday));  
    $site = $db->get("visitor", NULL, "site, DATE(created) as waktu");
    $sites = []; 
    foreach ($site as $item) {
        $sit = $item["site"];
        $sit = str_replace("www.","", $sit);
        if($sit!='' && $sit != 'b-cdn.net'){   ///// disable ctwa
            $waktu = $item["waktu"];
            if($sit==''){
                $sit = 'ctwa';
            }
            if (!isset($sites[$sit]['date'][$waktu])) {
                $sites[$sit]['date'][$waktu] = 0;
                $sites[$sit]['total']++;
            }
            $sites[$sit]['date'][$waktu]++;        
        }      
    }
    $total_day_site = 0;$total_day_ctwa = 0;
    foreach ($sites as $k => $v) {
        if($k != 'ctwa'){
            $total_day_site = $v['total'] + $total_day_site;
        }else{
            $total_day_ctwa = $v['total'] + $total_day_ctwa;
        }            
    }
    $total_bill_site = $user['setting_bill']['site'] * $total_day_site;
    $total_bill_ctwa = $user['setting_bill']['ctwa'] * $total_day_ctwa;
    // hitung bill cs
    $db->where("DATE(tanggal) BETWEEN ? AND ?", array($last_bill, $yesterday));  
    $cs = $db->get("cs_log", NULL, "phone, DATE(tanggal) as waktu, impression, contact, mql, prospek, purchase, value");
    $nocs = []; 
    foreach ($cs as $item) {
        $c = $item["phone"];
        $waktu = $item["waktu"];
        if (!isset($nocs[$c]['date'][$waktu])) {
            $nocs[$c]['date'][$waktu] = 0;
            $nocs[$c]['total']++;
        }
        $nocs[$c]['date'][$waktu]++;        
    }
    $total_day_cs = 0;
    foreach ($nocs as $k => $v) {
        $total_day_cs = $v['total'] + $total_day_cs;
    }
    $total_bill_cs = $user['setting_bill']['cs'] * $total_day_cs;
    // hitung cta
    $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'unik_cta'", array($last_bill, $yesterday));  
    $db->orderBy("date","Asc");
    $cta = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
    $tcta = []; $total_click_cta = 0;$tcta_date = [];
    $last_cta = ''; $last_cta_value = 0;
    foreach ($cta as $item) {
        $c = $item["report_id"];
        $waktu = $item["waktu"];
        $last_cta = $waktu;
        if (!isset($tcta[$c]['date'][$waktu])) {
            $tcta[$c]['date'][$waktu] = 0;
            $tcta[$c]['total']++;
        }
        if (!isset($tcta_date[$waktu])) {
            $tcta_date[$waktu] = 0;
        }
        $tcta_date[$waktu] += $item["report_value"]; 
        $tcta[$c]['date'][$waktu] += $item["report_value"];    
        $total_click_cta += $item["report_value"];  
        $last_cta_value = $tcta[$c]['date'][$waktu];
    }
    $total_day_cta = 0;
    foreach ($tcta as $k => $v) {
        $total_day_cta = $v['total'] + $total_day_cta;
    }

    // hitung lp_view
    $db->where("DATE(date) BETWEEN ? AND ? and report_key = 'lp_view'", array($last_bill, $yesterday));  
    $lp_view = $db->get("report_data", NULL, "report_id, DATE(date) as waktu, report_value");
    $tlp_view = []; $total_lp_view = 0;$tlp_view_date = [];
    foreach ($lp_view as $item) {
        $c = $item["report_id"];
        $waktu = $item["waktu"];
        if (!isset($tlp_view[$c]['date'][$waktu])) {
            $tlp_view[$c]['date'][$waktu] = 0;
            $tlp_view[$c]['total']++;
        }
        if (!isset($tlp_view_date[$waktu])) {
            $tlp_view_date[$waktu] = 0;
        }
        $tlp_view_date[$waktu] += $item["report_value"];  
        $tlp_view[$c]['date'][$waktu] += $item["report_value"];  
        $total_lp_view += $item["report_value"];  
    }
    $total_day_lp_view = 0;
    foreach ($tlp_view as $k => $v) {
        $total_day_lp_view= $v['total'] + $total_day_lp_view;
    }

    $total_bill_cta = $user['setting_bill']['lead'] * $total_click_cta;
    $total['total_bill_site'] = $total_bill_site;
    $total['total_bill_ctwa'] = $total_bill_ctwa;
    $total['total_bill_cs'] = $total_bill_cs;
    $total['total_bill_cta'] = $total_bill_cta;
    $total['total_day_site'] = $total_day_site;
    $total['total_day_ctwa'] = $total_day_ctwa;
    $total['total_day_cs'] = $total_day_cs;
    $total['total_day_cta'] = $total_day_cta;
    $total['total_click_cta'] = $total_click_cta;
    $total['total'] = $total_bill_ctwa + $total_bill_site + $total_bill_cs + $total_bill_cta;
    $alert_nominal = $user['data']['billing_alert'] - 5000;
    $create_bill = false;
    // cek minimal billing
    $msg['code']=1;        
    //$msg['data']['user'] = $user;

    $msg['data']['tcta_date'] = $tcta_date;
    $msg['data']['tlp_view_date'] = $tlp_view_date;

    $msg['data']['total'] = $total;
    $msg['last_cta'] = $last_cta;
    $msg['last_cta_value'] = $last_cta_value;

    return $msg;
    
}