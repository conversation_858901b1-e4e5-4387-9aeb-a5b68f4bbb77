<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v18/resources/campaign_criterion.proto

namespace Google\Ads\GoogleAds\V18\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A campaign criterion.
 *
 * Generated from protobuf message <code>google.ads.googleads.v18.resources.CampaignCriterion</code>
 */
class CampaignCriterion extends \Google\Protobuf\Internal\Message
{
    /**
     * Immutable. The resource name of the campaign criterion.
     * Campaign criterion resource names have the form:
     * `customers/{customer_id}/campaignCriteria/{campaign_id}~{criterion_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     */
    protected $resource_name = '';
    /**
     * Immutable. The campaign to which the criterion belongs.
     *
     * Generated from protobuf field <code>optional string campaign = 37 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     */
    protected $campaign = null;
    /**
     * Output only. The ID of the criterion.
     * This field is ignored during mutate.
     *
     * Generated from protobuf field <code>optional int64 criterion_id = 38 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $criterion_id = null;
    /**
     * Output only. The display name of the criterion.
     * This field is ignored for mutates.
     *
     * Generated from protobuf field <code>string display_name = 43 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $display_name = '';
    /**
     * The modifier for the bids when the criterion matches. The modifier must be
     * in the range: 0.1 - 10.0. Most targetable criteria types support modifiers.
     * Use 0 to opt out of a Device type.
     *
     * Generated from protobuf field <code>optional float bid_modifier = 39;</code>
     */
    protected $bid_modifier = null;
    /**
     * Immutable. Whether to target (`false`) or exclude (`true`) the criterion.
     *
     * Generated from protobuf field <code>optional bool negative = 40 [(.google.api.field_behavior) = IMMUTABLE];</code>
     */
    protected $negative = null;
    /**
     * Output only. The type of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.CriterionTypeEnum.CriterionType type = 6 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $type = 0;
    /**
     * The status of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.CampaignCriterionStatusEnum.CampaignCriterionStatus status = 35;</code>
     */
    protected $status = 0;
    protected $criterion;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $resource_name
     *           Immutable. The resource name of the campaign criterion.
     *           Campaign criterion resource names have the form:
     *           `customers/{customer_id}/campaignCriteria/{campaign_id}~{criterion_id}`
     *     @type string $campaign
     *           Immutable. The campaign to which the criterion belongs.
     *     @type int|string $criterion_id
     *           Output only. The ID of the criterion.
     *           This field is ignored during mutate.
     *     @type string $display_name
     *           Output only. The display name of the criterion.
     *           This field is ignored for mutates.
     *     @type float $bid_modifier
     *           The modifier for the bids when the criterion matches. The modifier must be
     *           in the range: 0.1 - 10.0. Most targetable criteria types support modifiers.
     *           Use 0 to opt out of a Device type.
     *     @type bool $negative
     *           Immutable. Whether to target (`false`) or exclude (`true`) the criterion.
     *     @type int $type
     *           Output only. The type of the criterion.
     *     @type int $status
     *           The status of the criterion.
     *     @type \Google\Ads\GoogleAds\V18\Common\KeywordInfo $keyword
     *           Immutable. Keyword.
     *     @type \Google\Ads\GoogleAds\V18\Common\PlacementInfo $placement
     *           Immutable. Placement.
     *     @type \Google\Ads\GoogleAds\V18\Common\MobileAppCategoryInfo $mobile_app_category
     *           Immutable. Mobile app category.
     *     @type \Google\Ads\GoogleAds\V18\Common\MobileApplicationInfo $mobile_application
     *           Immutable. Mobile application.
     *     @type \Google\Ads\GoogleAds\V18\Common\LocationInfo $location
     *           Immutable. Location.
     *     @type \Google\Ads\GoogleAds\V18\Common\DeviceInfo $device
     *           Immutable. Device.
     *     @type \Google\Ads\GoogleAds\V18\Common\AdScheduleInfo $ad_schedule
     *           Immutable. Ad Schedule.
     *     @type \Google\Ads\GoogleAds\V18\Common\AgeRangeInfo $age_range
     *           Immutable. Age range.
     *     @type \Google\Ads\GoogleAds\V18\Common\GenderInfo $gender
     *           Immutable. Gender.
     *     @type \Google\Ads\GoogleAds\V18\Common\IncomeRangeInfo $income_range
     *           Immutable. Income range.
     *     @type \Google\Ads\GoogleAds\V18\Common\ParentalStatusInfo $parental_status
     *           Immutable. Parental status.
     *     @type \Google\Ads\GoogleAds\V18\Common\UserListInfo $user_list
     *           Immutable. User List.
     *     @type \Google\Ads\GoogleAds\V18\Common\YouTubeVideoInfo $youtube_video
     *           Immutable. YouTube Video.
     *     @type \Google\Ads\GoogleAds\V18\Common\YouTubeChannelInfo $youtube_channel
     *           Immutable. YouTube Channel.
     *     @type \Google\Ads\GoogleAds\V18\Common\ProximityInfo $proximity
     *           Immutable. Proximity.
     *     @type \Google\Ads\GoogleAds\V18\Common\TopicInfo $topic
     *           Immutable. Topic.
     *     @type \Google\Ads\GoogleAds\V18\Common\ListingScopeInfo $listing_scope
     *           Immutable. Listing scope.
     *     @type \Google\Ads\GoogleAds\V18\Common\LanguageInfo $language
     *           Immutable. Language.
     *     @type \Google\Ads\GoogleAds\V18\Common\IpBlockInfo $ip_block
     *           Immutable. IpBlock.
     *     @type \Google\Ads\GoogleAds\V18\Common\ContentLabelInfo $content_label
     *           Immutable. ContentLabel.
     *     @type \Google\Ads\GoogleAds\V18\Common\CarrierInfo $carrier
     *           Immutable. Carrier.
     *     @type \Google\Ads\GoogleAds\V18\Common\UserInterestInfo $user_interest
     *           Immutable. User Interest.
     *     @type \Google\Ads\GoogleAds\V18\Common\WebpageInfo $webpage
     *           Immutable. Webpage.
     *     @type \Google\Ads\GoogleAds\V18\Common\OperatingSystemVersionInfo $operating_system_version
     *           Immutable. Operating system version.
     *     @type \Google\Ads\GoogleAds\V18\Common\MobileDeviceInfo $mobile_device
     *           Immutable. Mobile Device.
     *     @type \Google\Ads\GoogleAds\V18\Common\LocationGroupInfo $location_group
     *           Immutable. Location Group
     *     @type \Google\Ads\GoogleAds\V18\Common\CustomAffinityInfo $custom_affinity
     *           Immutable. Custom Affinity.
     *     @type \Google\Ads\GoogleAds\V18\Common\CustomAudienceInfo $custom_audience
     *           Immutable. Custom Audience
     *     @type \Google\Ads\GoogleAds\V18\Common\CombinedAudienceInfo $combined_audience
     *           Immutable. Combined Audience.
     *     @type \Google\Ads\GoogleAds\V18\Common\KeywordThemeInfo $keyword_theme
     *           Immutable. Smart Campaign Keyword Theme.
     *     @type \Google\Ads\GoogleAds\V18\Common\LocalServiceIdInfo $local_service_id
     *           Immutable. GLS service campaign criterion.
     *     @type \Google\Ads\GoogleAds\V18\Common\BrandListInfo $brand_list
     *           Immutable. Brand list campaign criterion.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V18\Resources\CampaignCriterion::initOnce();
        parent::__construct($data);
    }

    /**
     * Immutable. The resource name of the campaign criterion.
     * Campaign criterion resource names have the form:
     * `customers/{customer_id}/campaignCriteria/{campaign_id}~{criterion_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getResourceName()
    {
        return $this->resource_name;
    }

    /**
     * Immutable. The resource name of the campaign criterion.
     * Campaign criterion resource names have the form:
     * `customers/{customer_id}/campaignCriteria/{campaign_id}~{criterion_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setResourceName($var)
    {
        GPBUtil::checkString($var, True);
        $this->resource_name = $var;

        return $this;
    }

    /**
     * Immutable. The campaign to which the criterion belongs.
     *
     * Generated from protobuf field <code>optional string campaign = 37 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getCampaign()
    {
        return isset($this->campaign) ? $this->campaign : '';
    }

    public function hasCampaign()
    {
        return isset($this->campaign);
    }

    public function clearCampaign()
    {
        unset($this->campaign);
    }

    /**
     * Immutable. The campaign to which the criterion belongs.
     *
     * Generated from protobuf field <code>optional string campaign = 37 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setCampaign($var)
    {
        GPBUtil::checkString($var, True);
        $this->campaign = $var;

        return $this;
    }

    /**
     * Output only. The ID of the criterion.
     * This field is ignored during mutate.
     *
     * Generated from protobuf field <code>optional int64 criterion_id = 38 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int|string
     */
    public function getCriterionId()
    {
        return isset($this->criterion_id) ? $this->criterion_id : 0;
    }

    public function hasCriterionId()
    {
        return isset($this->criterion_id);
    }

    public function clearCriterionId()
    {
        unset($this->criterion_id);
    }

    /**
     * Output only. The ID of the criterion.
     * This field is ignored during mutate.
     *
     * Generated from protobuf field <code>optional int64 criterion_id = 38 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int|string $var
     * @return $this
     */
    public function setCriterionId($var)
    {
        GPBUtil::checkInt64($var);
        $this->criterion_id = $var;

        return $this;
    }

    /**
     * Output only. The display name of the criterion.
     * This field is ignored for mutates.
     *
     * Generated from protobuf field <code>string display_name = 43 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return string
     */
    public function getDisplayName()
    {
        return $this->display_name;
    }

    /**
     * Output only. The display name of the criterion.
     * This field is ignored for mutates.
     *
     * Generated from protobuf field <code>string display_name = 43 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param string $var
     * @return $this
     */
    public function setDisplayName($var)
    {
        GPBUtil::checkString($var, True);
        $this->display_name = $var;

        return $this;
    }

    /**
     * The modifier for the bids when the criterion matches. The modifier must be
     * in the range: 0.1 - 10.0. Most targetable criteria types support modifiers.
     * Use 0 to opt out of a Device type.
     *
     * Generated from protobuf field <code>optional float bid_modifier = 39;</code>
     * @return float
     */
    public function getBidModifier()
    {
        return isset($this->bid_modifier) ? $this->bid_modifier : 0.0;
    }

    public function hasBidModifier()
    {
        return isset($this->bid_modifier);
    }

    public function clearBidModifier()
    {
        unset($this->bid_modifier);
    }

    /**
     * The modifier for the bids when the criterion matches. The modifier must be
     * in the range: 0.1 - 10.0. Most targetable criteria types support modifiers.
     * Use 0 to opt out of a Device type.
     *
     * Generated from protobuf field <code>optional float bid_modifier = 39;</code>
     * @param float $var
     * @return $this
     */
    public function setBidModifier($var)
    {
        GPBUtil::checkFloat($var);
        $this->bid_modifier = $var;

        return $this;
    }

    /**
     * Immutable. Whether to target (`false`) or exclude (`true`) the criterion.
     *
     * Generated from protobuf field <code>optional bool negative = 40 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return bool
     */
    public function getNegative()
    {
        return isset($this->negative) ? $this->negative : false;
    }

    public function hasNegative()
    {
        return isset($this->negative);
    }

    public function clearNegative()
    {
        unset($this->negative);
    }

    /**
     * Immutable. Whether to target (`false`) or exclude (`true`) the criterion.
     *
     * Generated from protobuf field <code>optional bool negative = 40 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param bool $var
     * @return $this
     */
    public function setNegative($var)
    {
        GPBUtil::checkBool($var);
        $this->negative = $var;

        return $this;
    }

    /**
     * Output only. The type of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.CriterionTypeEnum.CriterionType type = 6 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Output only. The type of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.CriterionTypeEnum.CriterionType type = 6 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\CriterionTypeEnum\CriterionType::class);
        $this->type = $var;

        return $this;
    }

    /**
     * The status of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.CampaignCriterionStatusEnum.CampaignCriterionStatus status = 35;</code>
     * @return int
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * The status of the criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.enums.CampaignCriterionStatusEnum.CampaignCriterionStatus status = 35;</code>
     * @param int $var
     * @return $this
     */
    public function setStatus($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V18\Enums\CampaignCriterionStatusEnum\CampaignCriterionStatus::class);
        $this->status = $var;

        return $this;
    }

    /**
     * Immutable. Keyword.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.KeywordInfo keyword = 8 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\KeywordInfo|null
     */
    public function getKeyword()
    {
        return $this->readOneof(8);
    }

    public function hasKeyword()
    {
        return $this->hasOneof(8);
    }

    /**
     * Immutable. Keyword.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.KeywordInfo keyword = 8 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\KeywordInfo $var
     * @return $this
     */
    public function setKeyword($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\KeywordInfo::class);
        $this->writeOneof(8, $var);

        return $this;
    }

    /**
     * Immutable. Placement.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.PlacementInfo placement = 9 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\PlacementInfo|null
     */
    public function getPlacement()
    {
        return $this->readOneof(9);
    }

    public function hasPlacement()
    {
        return $this->hasOneof(9);
    }

    /**
     * Immutable. Placement.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.PlacementInfo placement = 9 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\PlacementInfo $var
     * @return $this
     */
    public function setPlacement($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\PlacementInfo::class);
        $this->writeOneof(9, $var);

        return $this;
    }

    /**
     * Immutable. Mobile app category.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.MobileAppCategoryInfo mobile_app_category = 10 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\MobileAppCategoryInfo|null
     */
    public function getMobileAppCategory()
    {
        return $this->readOneof(10);
    }

    public function hasMobileAppCategory()
    {
        return $this->hasOneof(10);
    }

    /**
     * Immutable. Mobile app category.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.MobileAppCategoryInfo mobile_app_category = 10 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\MobileAppCategoryInfo $var
     * @return $this
     */
    public function setMobileAppCategory($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\MobileAppCategoryInfo::class);
        $this->writeOneof(10, $var);

        return $this;
    }

    /**
     * Immutable. Mobile application.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.MobileApplicationInfo mobile_application = 11 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\MobileApplicationInfo|null
     */
    public function getMobileApplication()
    {
        return $this->readOneof(11);
    }

    public function hasMobileApplication()
    {
        return $this->hasOneof(11);
    }

    /**
     * Immutable. Mobile application.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.MobileApplicationInfo mobile_application = 11 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\MobileApplicationInfo $var
     * @return $this
     */
    public function setMobileApplication($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\MobileApplicationInfo::class);
        $this->writeOneof(11, $var);

        return $this;
    }

    /**
     * Immutable. Location.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LocationInfo location = 12 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\LocationInfo|null
     */
    public function getLocation()
    {
        return $this->readOneof(12);
    }

    public function hasLocation()
    {
        return $this->hasOneof(12);
    }

    /**
     * Immutable. Location.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LocationInfo location = 12 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\LocationInfo $var
     * @return $this
     */
    public function setLocation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\LocationInfo::class);
        $this->writeOneof(12, $var);

        return $this;
    }

    /**
     * Immutable. Device.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DeviceInfo device = 13 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\DeviceInfo|null
     */
    public function getDevice()
    {
        return $this->readOneof(13);
    }

    public function hasDevice()
    {
        return $this->hasOneof(13);
    }

    /**
     * Immutable. Device.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.DeviceInfo device = 13 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\DeviceInfo $var
     * @return $this
     */
    public function setDevice($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\DeviceInfo::class);
        $this->writeOneof(13, $var);

        return $this;
    }

    /**
     * Immutable. Ad Schedule.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AdScheduleInfo ad_schedule = 15 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\AdScheduleInfo|null
     */
    public function getAdSchedule()
    {
        return $this->readOneof(15);
    }

    public function hasAdSchedule()
    {
        return $this->hasOneof(15);
    }

    /**
     * Immutable. Ad Schedule.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AdScheduleInfo ad_schedule = 15 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\AdScheduleInfo $var
     * @return $this
     */
    public function setAdSchedule($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\AdScheduleInfo::class);
        $this->writeOneof(15, $var);

        return $this;
    }

    /**
     * Immutable. Age range.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AgeRangeInfo age_range = 16 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\AgeRangeInfo|null
     */
    public function getAgeRange()
    {
        return $this->readOneof(16);
    }

    public function hasAgeRange()
    {
        return $this->hasOneof(16);
    }

    /**
     * Immutable. Age range.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.AgeRangeInfo age_range = 16 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\AgeRangeInfo $var
     * @return $this
     */
    public function setAgeRange($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\AgeRangeInfo::class);
        $this->writeOneof(16, $var);

        return $this;
    }

    /**
     * Immutable. Gender.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.GenderInfo gender = 17 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\GenderInfo|null
     */
    public function getGender()
    {
        return $this->readOneof(17);
    }

    public function hasGender()
    {
        return $this->hasOneof(17);
    }

    /**
     * Immutable. Gender.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.GenderInfo gender = 17 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\GenderInfo $var
     * @return $this
     */
    public function setGender($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\GenderInfo::class);
        $this->writeOneof(17, $var);

        return $this;
    }

    /**
     * Immutable. Income range.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.IncomeRangeInfo income_range = 18 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\IncomeRangeInfo|null
     */
    public function getIncomeRange()
    {
        return $this->readOneof(18);
    }

    public function hasIncomeRange()
    {
        return $this->hasOneof(18);
    }

    /**
     * Immutable. Income range.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.IncomeRangeInfo income_range = 18 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\IncomeRangeInfo $var
     * @return $this
     */
    public function setIncomeRange($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\IncomeRangeInfo::class);
        $this->writeOneof(18, $var);

        return $this;
    }

    /**
     * Immutable. Parental status.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ParentalStatusInfo parental_status = 19 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ParentalStatusInfo|null
     */
    public function getParentalStatus()
    {
        return $this->readOneof(19);
    }

    public function hasParentalStatus()
    {
        return $this->hasOneof(19);
    }

    /**
     * Immutable. Parental status.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ParentalStatusInfo parental_status = 19 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ParentalStatusInfo $var
     * @return $this
     */
    public function setParentalStatus($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ParentalStatusInfo::class);
        $this->writeOneof(19, $var);

        return $this;
    }

    /**
     * Immutable. User List.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.UserListInfo user_list = 22 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\UserListInfo|null
     */
    public function getUserList()
    {
        return $this->readOneof(22);
    }

    public function hasUserList()
    {
        return $this->hasOneof(22);
    }

    /**
     * Immutable. User List.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.UserListInfo user_list = 22 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\UserListInfo $var
     * @return $this
     */
    public function setUserList($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\UserListInfo::class);
        $this->writeOneof(22, $var);

        return $this;
    }

    /**
     * Immutable. YouTube Video.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.YouTubeVideoInfo youtube_video = 20 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\YouTubeVideoInfo|null
     */
    public function getYoutubeVideo()
    {
        return $this->readOneof(20);
    }

    public function hasYoutubeVideo()
    {
        return $this->hasOneof(20);
    }

    /**
     * Immutable. YouTube Video.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.YouTubeVideoInfo youtube_video = 20 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\YouTubeVideoInfo $var
     * @return $this
     */
    public function setYoutubeVideo($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\YouTubeVideoInfo::class);
        $this->writeOneof(20, $var);

        return $this;
    }

    /**
     * Immutable. YouTube Channel.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.YouTubeChannelInfo youtube_channel = 21 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\YouTubeChannelInfo|null
     */
    public function getYoutubeChannel()
    {
        return $this->readOneof(21);
    }

    public function hasYoutubeChannel()
    {
        return $this->hasOneof(21);
    }

    /**
     * Immutable. YouTube Channel.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.YouTubeChannelInfo youtube_channel = 21 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\YouTubeChannelInfo $var
     * @return $this
     */
    public function setYoutubeChannel($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\YouTubeChannelInfo::class);
        $this->writeOneof(21, $var);

        return $this;
    }

    /**
     * Immutable. Proximity.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ProximityInfo proximity = 23 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ProximityInfo|null
     */
    public function getProximity()
    {
        return $this->readOneof(23);
    }

    public function hasProximity()
    {
        return $this->hasOneof(23);
    }

    /**
     * Immutable. Proximity.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ProximityInfo proximity = 23 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ProximityInfo $var
     * @return $this
     */
    public function setProximity($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ProximityInfo::class);
        $this->writeOneof(23, $var);

        return $this;
    }

    /**
     * Immutable. Topic.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.TopicInfo topic = 24 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\TopicInfo|null
     */
    public function getTopic()
    {
        return $this->readOneof(24);
    }

    public function hasTopic()
    {
        return $this->hasOneof(24);
    }

    /**
     * Immutable. Topic.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.TopicInfo topic = 24 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\TopicInfo $var
     * @return $this
     */
    public function setTopic($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\TopicInfo::class);
        $this->writeOneof(24, $var);

        return $this;
    }

    /**
     * Immutable. Listing scope.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ListingScopeInfo listing_scope = 25 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ListingScopeInfo|null
     */
    public function getListingScope()
    {
        return $this->readOneof(25);
    }

    public function hasListingScope()
    {
        return $this->hasOneof(25);
    }

    /**
     * Immutable. Listing scope.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ListingScopeInfo listing_scope = 25 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ListingScopeInfo $var
     * @return $this
     */
    public function setListingScope($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ListingScopeInfo::class);
        $this->writeOneof(25, $var);

        return $this;
    }

    /**
     * Immutable. Language.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LanguageInfo language = 26 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\LanguageInfo|null
     */
    public function getLanguage()
    {
        return $this->readOneof(26);
    }

    public function hasLanguage()
    {
        return $this->hasOneof(26);
    }

    /**
     * Immutable. Language.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LanguageInfo language = 26 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\LanguageInfo $var
     * @return $this
     */
    public function setLanguage($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\LanguageInfo::class);
        $this->writeOneof(26, $var);

        return $this;
    }

    /**
     * Immutable. IpBlock.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.IpBlockInfo ip_block = 27 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\IpBlockInfo|null
     */
    public function getIpBlock()
    {
        return $this->readOneof(27);
    }

    public function hasIpBlock()
    {
        return $this->hasOneof(27);
    }

    /**
     * Immutable. IpBlock.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.IpBlockInfo ip_block = 27 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\IpBlockInfo $var
     * @return $this
     */
    public function setIpBlock($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\IpBlockInfo::class);
        $this->writeOneof(27, $var);

        return $this;
    }

    /**
     * Immutable. ContentLabel.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ContentLabelInfo content_label = 28 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\ContentLabelInfo|null
     */
    public function getContentLabel()
    {
        return $this->readOneof(28);
    }

    public function hasContentLabel()
    {
        return $this->hasOneof(28);
    }

    /**
     * Immutable. ContentLabel.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.ContentLabelInfo content_label = 28 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\ContentLabelInfo $var
     * @return $this
     */
    public function setContentLabel($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\ContentLabelInfo::class);
        $this->writeOneof(28, $var);

        return $this;
    }

    /**
     * Immutable. Carrier.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CarrierInfo carrier = 29 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\CarrierInfo|null
     */
    public function getCarrier()
    {
        return $this->readOneof(29);
    }

    public function hasCarrier()
    {
        return $this->hasOneof(29);
    }

    /**
     * Immutable. Carrier.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CarrierInfo carrier = 29 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\CarrierInfo $var
     * @return $this
     */
    public function setCarrier($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\CarrierInfo::class);
        $this->writeOneof(29, $var);

        return $this;
    }

    /**
     * Immutable. User Interest.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.UserInterestInfo user_interest = 30 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\UserInterestInfo|null
     */
    public function getUserInterest()
    {
        return $this->readOneof(30);
    }

    public function hasUserInterest()
    {
        return $this->hasOneof(30);
    }

    /**
     * Immutable. User Interest.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.UserInterestInfo user_interest = 30 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\UserInterestInfo $var
     * @return $this
     */
    public function setUserInterest($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\UserInterestInfo::class);
        $this->writeOneof(30, $var);

        return $this;
    }

    /**
     * Immutable. Webpage.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.WebpageInfo webpage = 31 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\WebpageInfo|null
     */
    public function getWebpage()
    {
        return $this->readOneof(31);
    }

    public function hasWebpage()
    {
        return $this->hasOneof(31);
    }

    /**
     * Immutable. Webpage.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.WebpageInfo webpage = 31 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\WebpageInfo $var
     * @return $this
     */
    public function setWebpage($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\WebpageInfo::class);
        $this->writeOneof(31, $var);

        return $this;
    }

    /**
     * Immutable. Operating system version.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.OperatingSystemVersionInfo operating_system_version = 32 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\OperatingSystemVersionInfo|null
     */
    public function getOperatingSystemVersion()
    {
        return $this->readOneof(32);
    }

    public function hasOperatingSystemVersion()
    {
        return $this->hasOneof(32);
    }

    /**
     * Immutable. Operating system version.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.OperatingSystemVersionInfo operating_system_version = 32 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\OperatingSystemVersionInfo $var
     * @return $this
     */
    public function setOperatingSystemVersion($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\OperatingSystemVersionInfo::class);
        $this->writeOneof(32, $var);

        return $this;
    }

    /**
     * Immutable. Mobile Device.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.MobileDeviceInfo mobile_device = 33 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\MobileDeviceInfo|null
     */
    public function getMobileDevice()
    {
        return $this->readOneof(33);
    }

    public function hasMobileDevice()
    {
        return $this->hasOneof(33);
    }

    /**
     * Immutable. Mobile Device.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.MobileDeviceInfo mobile_device = 33 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\MobileDeviceInfo $var
     * @return $this
     */
    public function setMobileDevice($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\MobileDeviceInfo::class);
        $this->writeOneof(33, $var);

        return $this;
    }

    /**
     * Immutable. Location Group
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LocationGroupInfo location_group = 34 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\LocationGroupInfo|null
     */
    public function getLocationGroup()
    {
        return $this->readOneof(34);
    }

    public function hasLocationGroup()
    {
        return $this->hasOneof(34);
    }

    /**
     * Immutable. Location Group
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LocationGroupInfo location_group = 34 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\LocationGroupInfo $var
     * @return $this
     */
    public function setLocationGroup($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\LocationGroupInfo::class);
        $this->writeOneof(34, $var);

        return $this;
    }

    /**
     * Immutable. Custom Affinity.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CustomAffinityInfo custom_affinity = 36 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\CustomAffinityInfo|null
     */
    public function getCustomAffinity()
    {
        return $this->readOneof(36);
    }

    public function hasCustomAffinity()
    {
        return $this->hasOneof(36);
    }

    /**
     * Immutable. Custom Affinity.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CustomAffinityInfo custom_affinity = 36 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\CustomAffinityInfo $var
     * @return $this
     */
    public function setCustomAffinity($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\CustomAffinityInfo::class);
        $this->writeOneof(36, $var);

        return $this;
    }

    /**
     * Immutable. Custom Audience
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CustomAudienceInfo custom_audience = 41 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\CustomAudienceInfo|null
     */
    public function getCustomAudience()
    {
        return $this->readOneof(41);
    }

    public function hasCustomAudience()
    {
        return $this->hasOneof(41);
    }

    /**
     * Immutable. Custom Audience
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CustomAudienceInfo custom_audience = 41 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\CustomAudienceInfo $var
     * @return $this
     */
    public function setCustomAudience($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\CustomAudienceInfo::class);
        $this->writeOneof(41, $var);

        return $this;
    }

    /**
     * Immutable. Combined Audience.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CombinedAudienceInfo combined_audience = 42 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\CombinedAudienceInfo|null
     */
    public function getCombinedAudience()
    {
        return $this->readOneof(42);
    }

    public function hasCombinedAudience()
    {
        return $this->hasOneof(42);
    }

    /**
     * Immutable. Combined Audience.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.CombinedAudienceInfo combined_audience = 42 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\CombinedAudienceInfo $var
     * @return $this
     */
    public function setCombinedAudience($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\CombinedAudienceInfo::class);
        $this->writeOneof(42, $var);

        return $this;
    }

    /**
     * Immutable. Smart Campaign Keyword Theme.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.KeywordThemeInfo keyword_theme = 45 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\KeywordThemeInfo|null
     */
    public function getKeywordTheme()
    {
        return $this->readOneof(45);
    }

    public function hasKeywordTheme()
    {
        return $this->hasOneof(45);
    }

    /**
     * Immutable. Smart Campaign Keyword Theme.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.KeywordThemeInfo keyword_theme = 45 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\KeywordThemeInfo $var
     * @return $this
     */
    public function setKeywordTheme($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\KeywordThemeInfo::class);
        $this->writeOneof(45, $var);

        return $this;
    }

    /**
     * Immutable. GLS service campaign criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LocalServiceIdInfo local_service_id = 46 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\LocalServiceIdInfo|null
     */
    public function getLocalServiceId()
    {
        return $this->readOneof(46);
    }

    public function hasLocalServiceId()
    {
        return $this->hasOneof(46);
    }

    /**
     * Immutable. GLS service campaign criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.LocalServiceIdInfo local_service_id = 46 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\LocalServiceIdInfo $var
     * @return $this
     */
    public function setLocalServiceId($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\LocalServiceIdInfo::class);
        $this->writeOneof(46, $var);

        return $this;
    }

    /**
     * Immutable. Brand list campaign criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.BrandListInfo brand_list = 47 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V18\Common\BrandListInfo|null
     */
    public function getBrandList()
    {
        return $this->readOneof(47);
    }

    public function hasBrandList()
    {
        return $this->hasOneof(47);
    }

    /**
     * Immutable. Brand list campaign criterion.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v18.common.BrandListInfo brand_list = 47 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V18\Common\BrandListInfo $var
     * @return $this
     */
    public function setBrandList($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V18\Common\BrandListInfo::class);
        $this->writeOneof(47, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getCriterion()
    {
        return $this->whichOneof("criterion");
    }

}

