<?php
function run_sql($param){
    global $app;
    $rules = [
        "project_id" => "required",
        "query" => "required",
    ];

    validate_param($rules, $param);
    extract($param);
    $project_id = $param["project_id"];
    assign_child_db($project_id);
    $db2 = $app->db2;
    $res = $db2->rawQuery($param["query"]);
    return($res);
}


function js_error($param){
    global $app;
    $rules = [
        "project_key" => "required",
        "message" => "required",
        "url" => "required",
    ];

    validate_param($rules, $param);
    extract($param);
    $db = $app->db;
    $db->where("project_key = UNHEX(?)", [$project_key]);
    $project = $db->getone("project");
    
    if ($project != NULL){
        $project_id = $project["project_id"];
        assign_child_db($project_id);
        $db2 = $app->db2;
        if(!isset($stack)){
            $stack = '';
        }
        $waktu = date("Y-m-d");
        $db2->where("waktu = ?", array($waktu));
        $bugs = $db2->get("bug_js");
        if($db2->count < 3){
            $data_insert["waktu"] = $waktu;
            $data_insert["url"] = $url;
            $data_insert["message"] = $url;
            $data_insert["stack"] = $stack;
            $db2->insert("bug_js", $data_insert);
        }    
    }
}