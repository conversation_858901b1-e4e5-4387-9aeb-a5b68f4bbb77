<?php

class RuleFacebook {

	function get_rule(){
		global $app;
		$db2 = $app->db2;

		$rules = $db2->get("rule_fb");

		foreach($rules as $key => $rule)
		{
			$rules[$key]["rule_key"] = bin2hex($rule["rule_key"]);
			$rules[$key]["raw"] = unserialize($rule["raw"]);
			$rules[$key]["target"] = unserialize($rule["target"]);
			foreach($rules[$key]["target"] as $key2=>$value2)
			{
				$rules[$key]["target"][$key2]["targeting_rule"] = unserialize($value2["targeting_rule"]);
			}
		}

		
		return $rules;
	}

	function update_rule($rule_key,$param)
	{
		global $app;
		$db2 = $app->db2;
		$data_update = array();
		if(isset($param["name"])){
			$data_update["name"] = $param["name"];
		}
		if(isset($param["freq"])){
			$data_update["freq"] = $param["freq"];
		}
		if(isset($param["formula"])){
			$data_update["formula"] = $param["formula"];
		}
		if(isset($param["raw"])){
			$data_update["raw"] = serialize($param["raw"]);
		}
		if(isset($param["task"])){
			$data_update["task"] = $param["task"];
		}
		if(isset($param["status"])){
			$data_update["status"] = $param["status"];
		}
		if(isset($param["target"])){
			foreach($param["target"] as $key=>$t){
				$db2->where("rule_target_key = UNHEX(?)",[$t]);
				$ret = $db2->getone("rule_fb_target");
				$ret["rule_target_key"] = bin2hex($ret["rule_target_key"]);
				$tmp_target[] = $ret;
			}
	
			if(count($tmp_target) > 0)
			{
				$data_update["target"] = serialize($tmp_target);
			}
		}

		if(count($data_update) > 0)
		{
			$db2->where("rule_key = UNHEX(?)",[$rule_key]);
			$db2->update("rule_fb",$data_update);
			return true;
		}
		else{
			return false;
		}
	}

	public function add_rule($name,$formula,$raw,$task,$freq,$target) {
		global $app;
		$db2 = $app->db2;

		$data_insert["name"] = $name;
		$data_insert["freq"] = $freq;
		$data_insert["formula"] = $formula;
		$data_insert["raw"] = serialize($raw);
		$data_insert["task"] = $task;
		$data_insert["status"] = 1;

		$tmp_target = array();
		foreach($target as $key=>$t){
			$db2->where("rule_target_key = UNHEX(?)",[$t]);
			$ret = $db2->getone("rule_fb_target");
			$ret["rule_target_key"] = bin2hex($ret["rule_target_key"]);
			$tmp_target[] = $ret;
		}

		if(count($tmp_target) > 0)
		{
			$data_insert["target"] = serialize($tmp_target);
		}
		
		$id = $db2->insert("rule_fb",$data_insert);

		$data_update["rule_key"] = $db2->func("UNHEX(?)",[md5($id.";rule_fb")]) ;

		$db2->where("rule_id",$id);
		$db2->update("rule_fb",$data_update);

		return true;
	}

	public function get_target($term = NULL) {
		global $app;
		$db2 = $app->db2;
		if ($term != NULL) {
			$db2->where("targeting_name LIKE ?", ["%" . trim($term) . "%"]);
		}

		$target = $db2->get("rule_fb_target", NULL, "HEX(rule_target_key) as rule_target_key,targeting_name");

		return $target;
	}

	public function create_target($name, $rule_target) {
		global $app;
		$db2 = $app->db2;

		$data_insert["targeting_name"] = $name;
		$data_insert["targeting_rule"] = serialize($rule_target);

		$id                             = $db2->insert("rule_fb_target", $data_insert);
		$data_update["rule_target_key"] = $db2->func("UNHEX(?)", [md5("rule_target;" . $id)]);
		$db2->where("rule_target_id", $id);
		$db2->update("rule_fb_target", $data_update);

		$ret = $this->get_target();

		return $ret;
	}

	public function delete_target() {
		global $app;
		$db2 = $app->db2;
	}

	public function get_target_fb($level, $name = null) {
		global $app;
		$db2 = $app->db2;

		$db2->where("platform", "facebook");
		$db2->where("type", "report");
		$integration = $db2->get("integration");

		$result = [];

		if ($integration == null) {
			return false;
		}

		$ad_account = array();

		foreach ($integration as $key => $value) {
			$app_id     = "";
			$app_secret = "";
		
			if ($value["type"] == "report") {
				
				$app_id     = $app->config->fb_app_id;
				$app_secret = $app->config->fb_app_secret;
			} else {
				$app_id     = $app->config->fb_app_id_ctwa;
				$app_secret = $app->config->fb_app_secret_ctwa;
			}
			$fb = new \Facebook\Facebook(["app_id" => $app_id, "app_secret" => $app_secret, "default_graph_version" => "v20.0"]);

			$account_id = ltrim($value["account_id"], "act_");

			$ad_account[$account_id] = $value["name"];

			$data_grab = $value["account_id"] . "/$level?fields=account_id,name,id";

			$value["data"] = unserialize($value["data"]);

			

			$response = $fb->get($data_grab, $value["data"]["access_token"]);
			$tmp      = $response->getDecodedBody();

			if (count($tmp["data"]) > 0) {
				foreach ($tmp["data"] as $key => $v) {
					array_push($result, $v);
				}
			}

			if (isset($tmp["paging"]["cursors"]["after"])) {
				$next_page = $tmp["paging"]["cursors"]["after"];
			} else {
				$next_page = false;
			}

			$i = 0;
			/*
		while ($next_page != false) {
		$data_grab = $value["account_id"] . "/$level?fields=account_id,name,id&after=" . $next_page;

		$response = $fb->get($data_grab, $value["data"]["access_token"]);
		$tmp = $response->getDecodedBody();

		if (count($tmp["data"]) > 0) {
		foreach ($tmp["data"] as $key => $v) {
		array_push($result, $v);
		}
		}

		if (!isset($tmp["paging"]["cursors"]["after"])) {
		$next_page = false;
		} else {
		$next_page = $tmp["paging"]["cursors"]["after"];
		}
		}
		 */
		}

		foreach ($result as $key => $value) {

			if (isset($ad_account[$value["account_id"]])) {
				$result[$key]["account_name"] = $ad_account[$value["account_id"]];
			}
			if ($name != null) {
				if (strpos(strtolower($value["name"]), strtolower($name)) === false) {
					unset($result[$key]);
				}
			}
		}

		return $result;
	}
}