<?php

return array(
    'validate_required'                 => 'Field {field} diperlukan',
    'validate_valid_email'              => 'Field {field} harus berupa alamat email yang valid',
    'validate_max_len'                  => 'Field {field} harus {param} karakter atau kurang',
    'validate_min_len'                  => 'Field {field} harus terdiri dari {param} karakter',
    'validate_exact_len'                => 'Field {field} harus persis karakter {param}',
    'validate_alpha'                    => 'Field {field} mungkin hanya berisi huruf',
    'validate_alpha_numeric'            => 'Field {field} hanya boleh berisi huruf dan angka',
    'validate_alpha_numeric_space'      => 'Field {field} hanya berisi huruf, angka, dan spasi',
    'validate_alpha_dash'               => 'Field {field} hanya berisi huruf dan tanda hubung',
    'validate_alpha_space'              => 'Field {field} hanya boleh berisi huruf dan spasi',
    'validate_numeric'                  => 'Field {field} harus berupa angka',
    'validate_integer'                  => 'Field {field} harus berupa bilangan tanpa desimal',
    'validate_boolean'                  => 'Field {field} harus benar atau salah',
    'validate_float'                    => 'Field {field} harus berupa angka dengan titik desimal (float)',
    'validate_valid_url'                => 'Field {field} harus berupa URL',
    'validate_url_exists'               => 'Field {field} tidak ada',
    'validate_valid_ip'                 => 'Field {field} harus berupa alamat IP yang valid',
    'validate_valid_ipv4'               => 'Field {field} harus berisi alamat IPv4 yang valid',
    'validate_valid_ipv6'               => 'Field {field} harus berisi alamat IPv6 yang valid',
    'validate_guidv4'                   => 'Field {field} harus berisi GUID yang valid',
    'validate_valid_cc'                 => 'Field {field} bukan nomor kartu kredit yang valid',
    'validate_valid_name'               => 'Field {field} harus menjadi nama lengkap',
    'validate_contains'                 => 'Field {field} hanya bisa menjadi salah satu Field berikut: {param}',
    'validate_contains_list'            => 'Field {field} bukan pilihan yang valid',
    'validate_doesnt_contain_list'      => 'Field {field} berisi nilai yang tidak diterima',
    'validate_street_address'           => 'Field {field} harus menjadi alamat jalan yang valid',
    'validate_date'                     => 'Field {field} harus tanggal yang valid',
    'validate_min_numeric'              => 'Field {field} harus berupa nilai numerik, sama dengan, atau lebih tinggi dari {param}',
    'validate_max_numeric'              => 'Field {field} harus berupa nilai numerik, sama dengan, atau lebih rendah dari {param}',
    'validate_min_age'                  => 'Field {field} harus memiliki umur lebih dari atau sama dengan {param}',
    'validate_invalid'                  => 'Field {field} tidak valid',
    'validate_starts'                   => 'Field {field} perlu dimulai dengan {param}',
    'validate_extension'                => 'Field {field} hanya dapat memiliki salah satu ekstensi berikut: {param}',
    'validate_required_file'            => 'Field {field} diperlukan',
    'validate_equalsfield'              => 'Field {field} tidak sama dengan {param}',
    'validate_iban'                     => 'Field {field} harus mengandung IBAN yang benar',
    'validate_phone_number'             => 'Field {field} harus berupa Nomor Telepon yang valid',
    'validate_regex'                    => 'Field {field} perlu mengandung nilai dengan format yang valid',
    'validate_valid_json_string'        => 'Field {field} harus berisi string format JSON yang valid',
    'validate_valid_array_size_greater' => 'Field {field} harus berupa array dengan ukuran, sama dengan, atau lebih tinggi dari {param}',
    'validate_valid_array_size_lesser'  => 'Field {field} harus berupa array dengan ukuran, sama dengan, atau lebih rendah dari {param}',
    'validate_valid_array_size_equal'   => 'Field {field} harus berupa array dengan ukuran sama dengan {param}',
    'validate_valid_persian_name'       => 'Field {field} harus merupakan nama Persia / Dari atau Arab yang benar',
	'validate_valid_eng_per_pas_name'   => 'Field {field} harus berupa bahasa Inggris, Persia / Dari / Pashtu atau Arab yang benar',
	'validate_valid_persian_digit'      => 'Field {field} harus menjadi digit yang valid dalam format Persia / Dari atau Arab',
	'validate_valid_persian_text'       => 'Field {field} harus berupa teks yang valid dalam format Persia / Dari atau Arab',
	'validate_valid_pashtu_text'        => 'Field {field} harus berupa teks yang valid dalam format Pashtu',
	'validate_valid_frmtoken'			=> 'Pengamanan token tidak valid',
); 
