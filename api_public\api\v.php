<?php

function custom_event($param)
{
    global $keya, $c_time, $app, $project_id;
    $db = $app->db;

    extract($param);

    if (!isset($type)) {
        return false;
    }
    $project_key = "49C783F982CCB5457AFEAF51423D8E31";
    $db->where("project_key = UNHEX(?)", [$project_key]);
    $project = $db->getone("project");
    $exist_visitor = false;

    if ($project != null) {
        $project_id = $project["project_id"];
        assign_child_db($project_id);

        $connector_key = "28998CED05090BE2F7DBB741EADD8E70";
        $value = 0;
        $pixel_id = "1108997767171192";
        $access_token = "EAAHwVeoaZBPoBOxW0m99fGtv7yjShyAfRVdr7UWX8pHwtSHGc4hZCHFqjMPEqAZAallD9VPcjaCdS1iUX00F3OZBiyOzA0Uh5VVttgP0a3ZAdUI7pxgtAkQQxuExd7mZAjnRYOJGqm9cmn2srLKIA2wgqzdpzL5d05cRFntcZCkmBediDliHBU98Km6HwGHB5GdxQZDZD";

        $vid = str_replace(".", "", $vid);
        $vid = convBase($vid, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");

        $pixel = new trigger_fb();
        return $pixel->sent_pixel($connector_key, $value, $pixel_id, $access_token, $vid, $type);
    }

}

function trigger($param)
{

    global $keya, $c_time, $app, $project_id;
    $db = $app->db;

    extract($param);

    if (!isset($event)) {
        return ["status" => "error", "message" => "event is required"];
    }

    $db->where("project_key = UNHEX(?)", [$project_key]);
    $project = $db->getone("project");
    $exist_visitor = false;

    if ($project != null) {
        $project_id = $project["project_id"];
        assign_child_db($project_id);
        $db2 = $app->db2;

        if (isset($visitor_id)) {
            $visitor_id = str_replace(".", "", $visitor_id);
            $visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");
            $db2->where("visitor_id", $visitor_id);
            $visitor = $db2->getone("visitor");
        } elseif (isset($phone)) {
            $db2->where("phone like ?", ["%" . $phone . "%"]);
            $visitor = $db2->getone("visitor");

        }

        if ($visitor == null) {
            $db2->where("phone like ?", ["%" . $phone . "%"]);
            $visitor = $db2->getone("visitor");
        }

        if ($visitor != null) {
            $t = new track();
            $message_id = NULL;
            $data_order = null;
            $pesan = null;
            $phone = $visitor["phone"];
            if ($event == "mql") {
                $x = $t->mql($nope_cs, $visitor, $phone, $message_id);
                return ["status" => "success", "message" => $x];
            }

            if ($event == "prospek") {
                $x = $t->prospek($nope_cs, $visitor, $phone, $message_id);
                return ["status" => "success", "message" => $x];
            }

            if ($event == "purchase") {
                if (isset($value)) {
                    $x = $t->purchase($nope_cs, $visitor, $phone, $value, $message_id, $data_order, $pesan);
                    return ["status" => "success", "message" => $x];
                } else {
                    return ["status" => "error", "message" => "empty value"];
                }
            }
        } else {
            return ["status" => "error", "message" => "visitor not found"];
        }
    } else {
        return ["status" => "error", "message" => "Project Not Found"];
    }

}








function form($param)
{
    global $keya, $c_time, $app, $project_id;
    $db = $app->db;
    $db2 = $app->db2;

    $memcached = new Memcached();
    $project_key = $param["project_key"];
    $memcached->addServer('127.0.0.1', 11211);

    if (extension_loaded('memcached')) {
        $mem_key = 'GetProjectByKey_' . $project_key;
        $project = $memcached->get($mem_key);
    }
    //file_put_contents('log/memcached_key.log', "act cta \n" .$mem_key."\n", FILE_APPEND);
    if (empty($project)) {
        $db->where("project_key = UNHEX(?)", [$project_key]);
        $project = $db->getone("project");
        $expiration = 3600 * 24 * 30;
        if (extension_loaded('memcached')) {
            $memcached->set($mem_key, $project, $expiration);
        }
    }
    if ($use_deeplink) {
        if (isInstagramInAppBrowserOnIOS()) {
            $wa_url = "https://api.whatsapp.com/send";
        } else {
            if (isInstagramInAppBrowserOnAndroid()) {
                $wa_url = "https://api.whatsapp.com/send";
            } else {
                $wa_url = "whatsapp://send";
            }
        }
    } else {
        $wa_url = "https://api.whatsapp.com/send";
    }
    extract($param);

    if ($project != null) {
        $project_id = $project["project_id"];
        assign_child_db($project_id);
        $db2 = $app->db2;
        $data = array();
        if (isset($param["firstname"])) {
            $data["name"] = $param["firstname"];
        }
        if (isset($param["email"])) {
            $data["email"] = $param["email"];
        }
        if (isset($param["msg"])) {
            $data["msg"] = $param["msg"];
        }

        foreach ($data as $key => $value) {
            $pattern = "/[^a-zA-Z0-9\?\.,!@\n \x{1F600}-\x{1F64F}\x{1F300}-\x{1F5FF}]/u";

            // Use preg_replace to remove unwanted characters
            $cleanedString = preg_replace($pattern, '', $value);

            $data[$key] = $cleanedString;
        }
        $set_divisi = "";
        if (isset($divisi)) {
            $set_divisi = $divisi;
        }
        $visitor_id = str_replace(".", "", $visitor_id);
        $visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");

        $form = new form();
        $res = $form->add($visitor_id, $phone, $data, $set_divisi);
        if ($res["code"] == 1) {
            $divisi_slug = $divisi;
            if (extension_loaded('memcached')) {
                $mem_key = $project["project_id"] . '_GetDivisiByName_' . $divisi_slug;
                $divisi = $memcached->get($mem_key);
            }
            if (empty($divisi)) {
                $db2->where("divisi_key = UNHEX(?)", [md5($divisi_slug)]);
                $divisi = $db2->getone("divisi");
                $expiration = 3600;
                if (extension_loaded('memcached')) {
                    $memcached->set($mem_key, $divisi, $expiration);
                }
            }
            $pembagian = new cs_pembagian();
            if ($divisi != null) {
                $cs = $pembagian->get_cs($divisi["divisi_id"]);
            } else {
                $all_cs = true;
            }
            if ($all_cs) {
                $cs = $pembagian->random();
            }
            $res["nope_cs"] = $cs;
            $cs_key = md5($cs);
            $db2->where("form_id", $res["id"]);
            $db2->update("form", array("cs_key" => $db2->func("UNHEX(?)", [$cs_key])));
            unset($res["id"]);
            $ret["wa_url"] = $wa_url . "?phone=" . htmlspecialchars($cs, ENT_QUOTES, 'UTF-8') . "&text=" . $data["msg"];
            $res["result"]["cs"] = $cs;
            $res["result"]["msg"] = '<h1>Terima Kasih!</h1>
    <p>Tim kami akan segera menghubungi Anda sesuai jadwal yang Anda pilih.</p>
    <p>Untuk respon yang lebih cepat, Anda juga bisa langsung menghubungi Customer Service kami di nomor:</p>
    <p><a href="' . $ret["wa_url"] . '" class="contact-number" target="_blank" rel="noopener noreferrer">+' . htmlspecialchars($cs, ENT_QUOTES, 'UTF-8') . '</a></p>';
            /////////// send message to cs //////////
            $data_setting = $form->get_setting();
            if(isset($data_setting["form_auto_msg"]) && $data_setting["form_auto_msg"] == 1){
                $form = new form();                
                try {
                    $visitor_id_str = (string)$param["visitor_id"];
                    if (strlen($visitor_id_str) > 20) {
                        $res["result"]["data_error"] = "visitor_id terlalu panjang (lebih dari 32 karakter)";
                    } else {
                        $res["result"]["data"] = $form->send_message($param["visitor_id"]);
                    }
                } catch (\Throwable $e) {
                    $res["result"]["data_error"] = $e->getMessage();
                }
            }
            //$res["result"]["form_setting"] = $data_setting;
        }

        return $res;
    }

}

function isInstagramInAppBrowserOnIOS()
{
    // Get the user agent from the request
    $userAgent = $_SERVER['HTTP_USER_AGENT'];

    // Check if Instagram's in-app browser is being used and if the device is iOS (iPhone, iPad, or iPod)
    $isInstagram = strpos($userAgent, 'Instagram') !== false;
    $isIOS = preg_match('/iPhone|iPad|iPod/i', $userAgent);

    // Return true if both Instagram and iOS are detected
    return $isInstagram && $isIOS;
}

function isInstagramInAppBrowserOnAndroid()
{
    // Get the user agent from the request
    $userAgent = $_SERVER['HTTP_USER_AGENT'];

    // Check if Instagram's in-app browser is being used and if the device is iOS (iPhone, iPad, or iPod)
    $isInstagram = strpos($userAgent, 'Instagram') !== false;
    $isIOS = preg_match('/android/i', $userAgent);

    // Return true if both Instagram and iOS are detected
    return $isInstagram && $isIOS;
}

function purchase($param)
{

    global $keya, $c_time, $app, $project_id;
    $db = $app->db;

    extract($param);

    $db->where("project_key = UNHEX(?)", [$project_key]);
    $project = $db->getone("project");
    $exist_visitor = false;

    if ($project != null) {
        $project_id = $project["project_id"];
        assign_child_db($project_id);
        $db2 = $app->db2;

        if (isset($visitor_id)) {
            $visitor_id = str_replace(".", "", $visitor_id);
            $visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");
            $db2->where("visitor_id", $visitor_id);
            $visitor = $db2->getone("visitor");
        } elseif (isset($phone)) {
            $db2->where("phone like ?", ["%" . $phone . "%"]);
            $visitor = $db2->getone("visitor");

        }

        if ($visitor == null) {
            $db2->where("phone like ?", ["%" . $phone . "%"]);
            $visitor = $db2->getone("visitor");
        }

        if ($visitor != null) {
            $visitor_id = $visitor["visitor_id"];
            $exist_visitor = true;
            $tmp["purchase"] = 1;
            $tmp["value"] = $db2->inc($value);
            $db2->where("visitor_id", $visitor_id);
            $db2->update("visitor", $tmp);

            $first_purchase = $visitor["first_purchase"];
            if ($first_purchase == "NULL" || $first_purchase == "" || is_null($first_purchase)) {
                $db2->where("visitor_id", $visitor_id);
                $db2->update("visitor", ["first_purchase" => date("Y-m-d H:i:s")]);
            }

            $visitor_data = unserialize($visitor["data"]);

            if (isset($visitor_data["last_campaign"])) {

                if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                    $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];
                    $key = $visitor["phone"] . ";purchase";
                    $campaign = new report();
                    ////////////////////////////// unique by adcopy id
                    $db2->setLockMethod("WRITE")->lock(array("report_hash", "meta", "report_data"));
                    if ($campaign->get_hash($key) == null) {
                        $campaign->add_report_data($visitor, $adcopy_id, "unik_purchase");
                    }
                    $campaign->add_report_data($visitor, $adcopy_id, "purchase");
                    $campaign->add_report_data($visitor, $adcopy_id, "purchase_value", $value);
                    $campaign->add_hash($key);
                    $db2->unlock();
                } else {
                    $campaign = new report();

                    $key = $param["phone"] . ";purchase";
                    $db2->setLockMethod("WRITE")
                        ->lock(array(
                            "report_hash",
                            "meta",
                            "report_data",
                            "visitor",
                            "connector",
                            "log_connector_hash",
                            "log_connector",
                        ));

                    if ($campaign->get_hash($key) == null) {
                        $con = new connector();
                        $campaign->add_report_data(null, 1, "unik_purchase");
                    }
                    $campaign->add_report_data($visitor, 1, "purchase");
                    $campaign->add_report_data($visitor, 1, "purchase_value", $value);
                    $campaign->add_hash($key);
                    $db2->unlock();
                }

            } else {
                $campaign = new report();

                $key = $param["phone"] . ";purchase";
                $db2->setLockMethod("WRITE")
                    ->lock(array(
                        "report_hash",
                        "meta",
                        "report_data",
                        "visitor",
                        "connector",
                        "log_connector_hash",
                        "log_connector",
                    ));

                if ($campaign->get_hash($key) == null) {
                    $con = new connector();
                    $campaign->add_report_data(null, 1, "unik_purchase");
                }
                $campaign->add_report_data($visitor, 1, "purchase");
                $campaign->add_report_data($visitor, 1, "purchase_value", $value);
                $campaign->add_hash($key);
                $db2->unlock();
            }

            $con = new connector();
            $con->trigger($visitor_id, "purchase", $value);

            //setcookie("cta_lifetime", 1, time() + (86400 * 360), "/");

        } else {
            if (!$exist_visitor) {
                ///////////// create visitor //////////////
                $visitor_id = hook_create_visitor($param["phone"]);
            }
            $campaign = new report();

            $key = $param["phone"] . ";purchase";
            $db2->setLockMethod("WRITE")
                ->lock(array(
                    "report_hash",
                    "meta",
                    "report_data",
                    "visitor",
                    "connector",
                    "log_connector_hash",
                    "log_connector",
                ));

            if ($campaign->get_hash($key) == null) {
                $con = new connector();
                $campaign->add_report_data(null, 1, "unik_purchase");
            }
            $campaign->add_report_data(null, 1, "purchase");
            $campaign->add_report_data(null, 1, "purchase_value", $value);
            $campaign->add_hash($key);
            $db2->unlock();

            $tmp["purchase"] = 1;
            $tmp["value"] = $db2->inc($value);
            $db2->where("visitor_id", $visitor_id);
            $db2->update("visitor", $tmp);

            $con = new connector();
            $con->trigger($visitor_id, "purchase", $value);
        }

        if ($exist_visitor) {
            $orders["visitor_id"] = $visitor["visitor_id"];
        } else {
            $orders["visitor_id"] = $visitor_id;
        }

        if (isset($param["phone"])) {
            $orders["phone"] = $param["phone"];
        } else {
            $orders["phone"] = 0;
        }

        if (isset($value)) {
            $orders["value"] = $value;
        } else {
            $orders["value"] = 0;
        }
        $orders["created"] = date("Y-m-d H:i:s");
        if ($exist_visitor) {
            $data_visitor = unserialize($visitor["data"]);
            if (isset($data_visitor["last_campaign"]["source"])) {
                $orders["source"] = $data_visitor["last_campaign"]["source"];
            } else {
                $orders["source"] = "unknown";
            }
        } else {
            $orders["source"] = "unknown";
        }

        $db2->insert("orders", $orders);

    } else {
        return "Project Not Found";
    }

}

function update($param)
{
    ////@file_put_contents('param2.txt', '[' . date('Y-m-d H:i:s') . "]\n" . json_encode($param), FILE_APPEND);
    global $keya, $c_time, $app, $project_id;
    $db = $app->db;

    if (isset($param["_ttp"])) {
        $ttp = $param["_ttp"];
    }

    extract($param);
    if (isset($vid)) {
        $visitor_id = $vid;
    }

    if (isset($visitor_id)) {
        $visitor_id = str_replace(".", "", $visitor_id);
        $visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");

        $memcached = new Memcached();
        $project_key = $param["project_key"];
        $memcached->addServer('127.0.0.1', 11211);

        if (extension_loaded('memcached')) {
            $mem_key = 'GetProjectByKey_' . $project_key;
            $project = $memcached->get($mem_key);
        }
        //file_put_contents('log/memcached_key.log', "act update \n".$mem_key."\n", FILE_APPEND);
        if (empty($project)) {
            $db->where("project_key = UNHEX(?)", [$project_key]);
            $project = $db->getone("project");
            $expiration = 3600 * 24 * 30;
            if (extension_loaded('memcached')) {
                $memcached->set($mem_key, $project, $expiration);
            }
        }

        if ($project != null) {
            $project_id = $project["project_id"];
            assign_child_db($project_id);
        }
        if (isset($clientId)) {
            if ($clientId != "undefined") {
                $param['google_analytic']['clientId'] = $clientId;
            }
        }
        if (isset($fbp)) {
            if ($fbp != "undefined") {
                $param['fb']['fbp'] = $fbp;
            }
        }
        if (isset($fbc)) {
            if ($fbc != "undefined") {
                $param['fb']['fbc'] = $fbc;
            }
        }
        if (isset($ttp)) {
            if ($ttp != "ttp") {
                $param['tiktok']['ttp'] = $ttp;
            }
        }

        if (count($param) > 0) {
            $v_class = new visitor();
            $v_class->add($param, $visitor_id);
        }
    }
    return $param;
}

function visit($param)
{

    //// @file_put_contents('param.txt', '[' . date('Y-m-d H:i:s') . "]\n\n" . json_encode($param), FILE_APPEND);
    ////@file_put_contents('server.txt', '[' . date('Y-m-d H:i:s') . "]\n" . json_encode($_SERVER), FILE_APPEND);
    ///////////////////// param list //////////////////
    /*
    project_key
    utm_campaign
    utm_source
    current_url
    array connector (optional)

    ////////////////// visitor data //////////////////////
    ip
    browser agent
    ////////////////////////////////////////////
    /////////facebook////////////////////
    cookies fbp (delay 2 sec)
    cookies fbc (delay 2 sec)
    GET fbclid
    //////////////////////////////////////////

    ///////////// tiktok /////////////////
    GET ttclid
    /////////////////////////////////////////

    //////////////// google ads ///////////////
    GET glid
    GET wbraid
    GET gbraid
    ////////////////////////////////////////

    //////////// google analytic ///////////
    cookie clientId
    ///////////////////////////////

    ///////////// snackvideo ////////////////
    GET click_id
    //////////////////////////////////////////
    /
     */

    //////////////////////////////////////////////////
    global $keya, $c_time, $app, $project_id;
    $db = $app->db;

    if (isset($param["vid"])) {
        $param["visitor_id"] = $param["vid"];
    }
    extract($param);

    $memcached = new Memcached();
    $project_key = $param["project_key"];
    $memcached->addServer('127.0.0.1', 11211);

    if (extension_loaded('memcached')) {
        $mem_key = 'GetProjectByKey_' . $project_key;
        $project = $memcached->get($mem_key);
    }
    //file_put_contents('log/memcached_key.log', "act visit \n".$mem_key."\n", FILE_APPEND);
    if (empty($project)) {
        $db->where("project_key = UNHEX(?)", [$project_key]);
        $project = $db->getone("project");
        $expiration = 3600 * 24 * 30;
        if (extension_loaded('memcached')) {
            $memcached->set($mem_key, $project, $expiration);
        }
    }

    if ($project != null) {
        $project_id = $project["project_id"];
        assign_child_db($project_id);
        $organic = true;

        if (isset($utm_campaign) && isset($utm_source)) {
            $organic = false;
        }

        $domain = "undefined";
        if (isset($page_url)) {

            $parsedUrl = parse_url($page_url);
            if (isset($parsedUrl["host"])) {
                $domain = $parsedUrl["host"];
            }

            if (isset($clientId)) {
                $param['google_analytic']['clientId'] = $clientId;
            }

            list($milliPart, $secondPart) = explode(' ', microtime());
            $milliPart = substr($milliPart, 2, 3);
            $milisec = $secondPart . $milliPart;

            $creationTime = round(microtime(true) * 1000);
            $randomNumber = mt_rand(1000000000, 9999999999) . mt_rand(100000, 999999);

            if (isset($fbp)) {
                $param['fb']['fbp'] = $fbp;
            } else {
                $param['fb']['fbp'] = "fb.1." . $creationTime . "." . $randomNumber . rand(100, 900);
            }


            if (isset($fbc)) {
                $param['fb']['fbc'] = $fbc;
            } else {
                if (isset($fbclid)) {
                    $param['fb']['fbc'] = "fb.1." . $creationTime . "." . $fbclid;
                }
            }



            if (isset($utm_campaign)) {
                $param["utm"]["utm_campaign"] = $utm_campaign;
            }
            if (isset($utm_source)) {
                $param["utm"]["utm_source"] = $utm_source;
            }
            if (isset($utm_medium)) {
                $param["utm"]["utm_medium"] = $utm_medium;
            }
            if (isset($utm_content)) {
                $param["utm"]["utm_content"] = $utm_content;
            }
            if (isset($utm_term)) {
                $param["utm"]["utm_term"] = $utm_term;
            }

            if (isset($param["visitor_id"])) {
                $visitor_id = $param["visitor_id"];
                $visitor_id = str_replace(".", "", $visitor_id);
                $visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");
            }

            $visitor_id_str = (string)$param["visitor_id"];
            if (strlen($visitor_id_str) > 20) {
                $res["result"]["data_error"] = "visitor_id terlalu panjang";
                $res["code"] = 0;
                echo json_encode($res);
                exit;
            }
            ///////////////// add to report
            $c = new report();
            if (isset($gs)) {
                if ($gs == "meta") {
                    if (isset($fbclid)) {
                        $param["fb"]["fbclid"] = $fbclid;
                    }

                    if (isset($gcn) && isset($gadsn) && isset($gadcn) && isset($gcid) && isset($gadsid) && isset($gadcid)) {
                        $data_campaign["page_url"] = clean_url_param($page_url);
                        $data_campaign["page_url"] = str_replace("https://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = str_replace("http://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = rtrim($data_campaign["page_url"], '/');

                        $data_campaign["gcn"] = $gcn;
                        $data_campaign["gadsn"] = $gadsn;
                        $data_campaign["gadcn"] = $gadcn;
                        $data_campaign["gcid"] = $gcid;
                        $data_campaign["gadsid"] = $gadsid;
                        $data_campaign["gadcid"] = $gadcid;

                        $adcopy_id = $c->add_report("meta", "lp_view", $gadcid, $data_campaign);

                        $param["last_campaign"]["source"] = $gs;
                        $param["last_campaign"]["data"]["adcopy_id"] = $adcopy_id;
                        $param["last_campaign"]["data"]["gcn"] = $gcn;
                        $param["last_campaign"]["data"]["gadsn"] = $gadsn;
                        $param["last_campaign"]["data"]["gadcn"] = $gadcn;
                        $param["last_campaign"]["data"]["gcid"] = $gcid;
                        $param["last_campaign"]["data"]["gadsid"] = $gadsid;
                        $param["last_campaign"]["data"]["gadcid"] = $gadcid;
                    } else {
                        $gs = "organic";
                        $data_campaign["page_url"] = clean_url_param($page_url);
                        $data_campaign["page_url"] = str_replace("https://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = str_replace("http://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = rtrim($data_campaign["page_url"], '/');

                        if (isset($param["ref"])) {
                            $data_campaign["ref"] = $param["ref"];
                        } else {
                            $data_campaign["ref"] = "Unknown";
                        }
                        $external_id = $data_campaign["page_url"] . "-ref-" . $data_campaign["ref"];
                        $adcopy_id = $c->add_report("organic", "lp_view", $external_id, $data_campaign);

                        $param["last_campaign"]["source"] = "organic";
                        $param["last_campaign"]["data"]["adcopy_id"] = $adcopy_id;
                    }
                } elseif ($gs == "tiktok") {
                    if (isset($ttclid)) {
                        $param["tiktok"]["ttclid"] = $ttclid;
                    }
                    if (isset($gcn) && isset($gadsn) && isset($gadcn) && isset($gcid) && isset($gadsid) && isset($gadcid)) {
                        $data_campaign["page_url"] = clean_url_param($page_url);
                        $data_campaign["page_url"] = str_replace("https://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = str_replace("http://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = rtrim($data_campaign["page_url"], '/');

                        $data_campaign["gcn"] = $gcn;
                        $data_campaign["gadsn"] = $gadsn;
                        $data_campaign["gadcn"] = $gadcn;
                        $data_campaign["gcid"] = $gcid;
                        $data_campaign["gadsid"] = $gadsid;
                        $data_campaign["gadcid"] = $gadcid;

                        $adcopy_id = $c->add_report("tiktok", "lp_view", $gadcid, $data_campaign);

                        $param["last_campaign"]["source"] = $gs;
                        $param["last_campaign"]["data"]["adcopy_id"] = $adcopy_id;
                        $param["last_campaign"]["data"]["gcn"] = $gcn;
                        $param["last_campaign"]["data"]["gadsn"] = $gadsn;
                        $param["last_campaign"]["data"]["gadcn"] = $gadcn;
                        $param["last_campaign"]["data"]["gcid"] = $gcid;
                        $param["last_campaign"]["data"]["gadsid"] = $gadsid;
                        $param["last_campaign"]["data"]["gadcid"] = $gadcid;
                    } else {
                        $gs = "organic";
                        $data_campaign["page_url"] = clean_url_param($page_url);
                        $data_campaign["page_url"] = str_replace("https://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = str_replace("http://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = rtrim($data_campaign["page_url"], '/');

                        if (isset($param["ref"])) {
                            $data_campaign["ref"] = $param["ref"];
                        } else {
                            $data_campaign["ref"] = "Unknown";
                        }
                        $external_id = $data_campaign["page_url"] . "-ref-" . $data_campaign["ref"];
                        $adcopy_id = $c->add_report("organic", "lp_view", $external_id, $data_campaign);

                        $param["last_campaign"]["source"] = "organic";
                        $param["last_campaign"]["data"]["adcopy_id"] = $adcopy_id;
                    }
                } elseif ($gs == "google") {
                    if (isset($gclid)) {
                        $param["google_ads"]["gclid"] = $gclid;
                    }
                    if (isset($wbraid)) {
                        $param["google_ads"]["wbraid"] = $wbraid;
                    }
                    if (isset($gbraid)) {
                        $param["google_ads"]["gbraid"] = $gbraid;
                    }
                    if (isset($gcn) && isset($gadsn) && isset($gadcn) && isset($gcid) && isset($gadsid) && isset($gadcid)) {

                        $data_campaign["page_url"] = clean_url_param($page_url);
                        $data_campaign["page_url"] = str_replace("https://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = str_replace("http://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = rtrim($data_campaign["page_url"], '/');

                        $data_campaign["gcn"] = $gcn;
                        $data_campaign["gadsn"] = $gadsn;
                        $data_campaign["gadcn"] = $gadcn;

                        $data_campaign["gcid"] = $gcid;
                        $data_campaign["gadsid"] = $gadsid;
                        $data_campaign["gadcid"] = $gadsid . ";" . $gadcid;

                        $adcopy_id = $c->add_report("google", "lp_view", $gadcid, $data_campaign);

                        $param["last_campaign"]["source"] = $gs;
                        $param["last_campaign"]["data"]["adcopy_id"] = $adcopy_id;
                        $param["last_campaign"]["data"]["gcn"] = $gcn;
                        $param["last_campaign"]["data"]["gadsn"] = $gadsn;
                        $param["last_campaign"]["data"]["gadcn"] = $gadcn;
                        $param["last_campaign"]["data"]["gcid"] = $gcid;
                        $param["last_campaign"]["data"]["gadsid"] = $gadsid;
                        $param["last_campaign"]["data"]["gadcid"] = $gadcid;
                    } else {
                        $gs = "organic";
                        $data_campaign["page_url"] = clean_url_param($page_url);
                        $data_campaign["page_url"] = str_replace("https://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = str_replace("http://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = rtrim($data_campaign["page_url"], '/');

                        if (isset($param["ref"])) {
                            $data_campaign["ref"] = $param["ref"];
                        } else {
                            $data_campaign["ref"] = "Unknown";
                        }
                        $external_id = $data_campaign["page_url"] . "-ref-" . $data_campaign["ref"];
                        $adcopy_id = $c->add_report("organic", "lp_view", $external_id, $data_campaign);

                        $param["last_campaign"]["source"] = "organic";
                        $param["last_campaign"]["data"]["adcopy_id"] = $adcopy_id;
                    }
                } elseif ($gs == "snack") {
                    if (isset($click_id)) {
                        $param["snack_video"]["click_id"] = $click_id;
                    }

                    $gcn = $CampaignID;
                    $gadsn = $adSETID;
                    $gadcn = $CreativeID;

                    $gcid = $CampaignID;
                    $gadsid = $adSETID;
                    $gadcid = $CreativeID;

                    if (isset($gcn) && isset($gadsn) && isset($gadcn) && isset($gcid) && isset($gadsid) && isset($gadcid)) {
                        $data_campaign["page_url"] = clean_url_param($page_url);
                        $data_campaign["page_url"] = str_replace("https://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = str_replace("http://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = rtrim($data_campaign["page_url"], '/');

                        $data_campaign["gcn"] = $gcn;
                        $data_campaign["gadsn"] = $gadsn;
                        $data_campaign["gadcn"] = $gadcn;
                        $data_campaign["gcid"] = $gcid;
                        $data_campaign["gadsid"] = $gadsid;
                        $data_campaign["gadcid"] = $gadcid;

                        $adcopy_id = $c->add_report("snack", "lp_view", $gadcid, $data_campaign);

                        $param["last_campaign"]["source"] = $gs;
                        $param["last_campaign"]["data"]["adcopy_id"] = $adcopy_id;
                        $param["last_campaign"]["data"]["gcn"] = $gcn;
                        $param["last_campaign"]["data"]["gadsn"] = $gadsn;
                        $param["last_campaign"]["data"]["gadcn"] = $gadcn;
                        $param["last_campaign"]["data"]["gcid"] = $gcid;
                        $param["last_campaign"]["data"]["gadsid"] = $gadsid;
                        $param["last_campaign"]["data"]["gadcid"] = $gadcid;
                    } else {
                        $gs = "organic";
                        $data_campaign["page_url"] = clean_url_param($page_url);
                        $data_campaign["page_url"] = str_replace("https://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = str_replace("http://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = rtrim($data_campaign["page_url"], '/');

                        if (isset($param["ref"])) {
                            $data_campaign["ref"] = $param["ref"];
                        } else {
                            $data_campaign["ref"] = "Unknown";
                        }
                        $external_id = $data_campaign["page_url"] . "-ref-" . $data_campaign["ref"];
                        $adcopy_id = $c->add_report("organic", "lp_view", $external_id, $data_campaign);

                        $param["last_campaign"]["source"] = "organic";
                        $param["last_campaign"]["data"]["adcopy_id"] = $adcopy_id;
                    }
                } elseif ($gs == "mgid") {
                    if (isset($click_id)) {
                        $param["mgid"]["click_id"] = $click_id;
                    }

                    if (isset($gcn) && isset($gadsn) && isset($gadcn) && isset($gcid) && isset($gadsid) && isset($gadcid)) {
                        $data_campaign["page_url"] = clean_url_param($page_url);
                        $data_campaign["page_url"] = str_replace("https://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = str_replace("http://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = rtrim($data_campaign["page_url"], '/');

                        $data_campaign["gcn"] = $gcn;
                        $data_campaign["gadsn"] = $gadsn;
                        $data_campaign["gadcn"] = $gadcn;
                        $data_campaign["gcid"] = $gcid;
                        $data_campaign["gadsid"] = $gadsid;
                        $data_campaign["gadcid"] = $gadcid;

                        $adcopy_id = $c->add_report("mgid", "lp_view", $gadcid, $data_campaign);

                        $param["last_campaign"]["source"] = $gs;
                        $param["last_campaign"]["data"]["adcopy_id"] = $adcopy_id;
                        $param["last_campaign"]["data"]["gcn"] = $gcn;
                        $param["last_campaign"]["data"]["gadsn"] = $gadsn;
                        $param["last_campaign"]["data"]["gadcn"] = $gadcn;
                        $param["last_campaign"]["data"]["gcid"] = $gcid;
                        $param["last_campaign"]["data"]["gadsid"] = $gadsid;
                        $param["last_campaign"]["data"]["gadcid"] = $gadcid;
                    } else {
                        $gs = "organic";
                        $data_campaign["page_url"] = clean_url_param($page_url);
                        $data_campaign["page_url"] = str_replace("https://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = str_replace("http://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = rtrim($data_campaign["page_url"], '/');

                        if (isset($param["ref"])) {
                            $data_campaign["ref"] = $param["ref"];
                        } else {
                            $data_campaign["ref"] = "Unknown";
                        }
                        $external_id = $data_campaign["page_url"] . "-ref-" . $data_campaign["ref"];
                        $adcopy_id = $c->add_report("organic", "lp_view", $external_id, $data_campaign);

                        $param["last_campaign"]["source"] = "organic";
                        $param["last_campaign"]["data"]["adcopy_id"] = $adcopy_id;
                    }
                } elseif ($gs == "linkedin") {
                    if (isset($li_fat_id)) {
                        $param["linkedin"]["li_fat_id"] = $li_fat_id;
                    }

                    if (isset($gcn) && isset($gadsn) && isset($gadcn) && isset($gcid) && isset($gadsid) && isset($gadcid)) {
                        $data_campaign["page_url"] = clean_url_param($page_url);
                        $data_campaign["page_url"] = str_replace("https://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = str_replace("http://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = rtrim($data_campaign["page_url"], '/');

                        $data_campaign["gcn"] = $gcn;
                        $data_campaign["gadsn"] = $gadsn;
                        $data_campaign["gadcn"] = $gadcn;
                        $data_campaign["gcid"] = $gcid;
                        $data_campaign["gadsid"] = $gadsid;
                        $data_campaign["gadcid"] = $gadcid;

                        $adcopy_id = $c->add_report("linkedin", "lp_view", $gadcid, $data_campaign);

                        $param["last_campaign"]["source"] = $gs;
                        $param["last_campaign"]["data"]["adcopy_id"] = $adcopy_id;
                        $param["last_campaign"]["data"]["gcn"] = $gcn;
                        $param["last_campaign"]["data"]["gadsn"] = $gadsn;
                        $param["last_campaign"]["data"]["gadcn"] = $gadcn;
                        $param["last_campaign"]["data"]["gcid"] = $gcid;
                        $param["last_campaign"]["data"]["gadsid"] = $gadsid;
                        $param["last_campaign"]["data"]["gadcid"] = $gadcid;
                    } else {
                        $gs = "organic";
                        $data_campaign["page_url"] = clean_url_param($page_url);
                        $data_campaign["page_url"] = str_replace("https://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = str_replace("http://", "", $data_campaign["page_url"]);
                        $data_campaign["page_url"] = rtrim($data_campaign["page_url"], '/');

                        if (isset($param["ref"])) {
                            $data_campaign["ref"] = $param["ref"];
                        } else {
                            $data_campaign["ref"] = "Unknown";
                        }
                        $external_id = $data_campaign["page_url"] . "-ref-" . $data_campaign["ref"];
                        $adcopy_id = $c->add_report("organic", "lp_view", $external_id, $data_campaign);

                        $param["last_campaign"]["source"] = "organic";
                        $param["last_campaign"]["data"]["adcopy_id"] = $adcopy_id;
                    }
                }
            } else {
                if (isset($param["visitor_id"])) {
                    global $app;
                    $db2 = $app->db2;
                    $visitor_id = $param["visitor_id"];
                    $visitor_id = str_replace(".", "", $visitor_id);
                    $visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");
                    $organic = false;
                    $db2->where("visitor_id", $visitor_id);
                    $visitor = $db2->getone("visitor");
                    if ($visitor == null) {
                        $organic = true;
                    }
                }

                if ($organic) {
                    $gs = "organic";
                    $data_campaign["page_url"] = clean_url_param($page_url);
                    $data_campaign["page_url"] = str_replace("https://", "", $data_campaign["page_url"]);
                    $data_campaign["page_url"] = str_replace("http://", "", $data_campaign["page_url"]);
                    $data_campaign["page_url"] = rtrim($data_campaign["page_url"], '/');

                    if (isset($param["ref"])) {
                        $data_campaign["ref"] = $param["ref"];
                    } else {
                        $data_campaign["ref"] = "Unknown";
                    }
                    $external_id = $data_campaign["page_url"] . "-ref-" . $data_campaign["ref"];
                    $adcopy_id = $c->add_report("organic", "lp_view", $external_id, $data_campaign);

                    $param["last_campaign"]["source"] = "organic";
                    $param["last_campaign"]["data"]["adcopy_id"] = $adcopy_id;
                }

            }

            /////////////////// save visitor data
            $v_class = new visitor();

            $param["site"] = $domain;
            $visitor_id = $v_class->add($param, $visitor_id);
            $ret['visitor_id'] = convBase($visitor_id, "0123456789", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
            $ret['visitor_id'] = str_split($ret['visitor_id'], 4);
            $ret['visitor_id'] = implode(".", $ret['visitor_id']);
            ///////////////////////////////
            global $app;
            $db2 = $app->db2;

            // $db2->where('visitor_id', $visitor_id);
            // $v_source = $db2->getone("visitor_source");

            //if($v_source == NULL)
            // {
            $db2->setQueryOption(array('IGNORE'))->insert("visitor_source", ["visitor_id" => $visitor_id, "source" => strtolower($gs)]);
            // }

            /*
            $source = "none";
            $campaign_name = "no name";
            if(isset($utm_source)){
            $source = $utm_source;
            }
            if(isset($utm_campaign)){
            $campaign_name = $utm_campaign;
            }
            $c = new campaign();
            $c->add_campaign($campaign_name,$source,"impression");
             */
            /////////////////////////////////////
            /////////////////// get connector
            ///////////////////// if google anal connector availabel /////////////////// return google anal connector

            $data_insert["visitor_id"] = $visitor_id;
            $data_insert["type"] = "visit";
            $db2->setQueryOption(["IGNORE"])->insert("quee_trigger_view", $data_insert);

            $con = new connector();
            // $con->trigger($visitor_id, "visit");
            $connector_post = null;
            if (isset($connector)) {
                $connector_post = $connector;
            }

            $connector_data = $con->get($connector_post);

            if ($connector_data["code"] == 1) {
                $ret["connector"] = $connector_data["result"]["data"];
                foreach ($ret["connector"] as $key => $value) {
                    unset($ret["connector"][$key]["connector_key"]);
                    unset($ret["connector"][$key]["created"]);
                    if (isset($ret["connector"][$key]["data"]["access_token"])) {
                        unset($ret["connector"][$key]["data"]["access_token"]);
                    }
                }

            } else {
                $ret["code"] = 0;
                $ret["msg"] = "no connector available";
            }
            //////////////////////////////////////
            return $ret;
        }

    }

}

function cron($param)
{
    if (empty($param["project_key"])) {
        return ["code" => 0, "msg" => "no project_key"];
    }

    global $app, $project_id;
    $db = $app->db;

    $memcached = new Memcached();
    $project_key = $param["project_key"];
    $memcached->addServer('127.0.0.1', 11211);

    if (extension_loaded('memcached')) {
        $mem_key = 'GetProjectByKey_' . $project_key;
        $project = $memcached->get($mem_key);
    }

    //file_put_contents('log/memcached_key.log', "act cron \n".$mem_key."\n", FILE_APPEND);
    if (empty($project)) {
        $db->where("project_key = UNHEX(?)", [$project_key]);
        $project = $db->getone("project");
        $expiration = 3600 * 24 * 30;
        if (extension_loaded('memcached')) {
            $memcached->set($mem_key, $project, $expiration);
        }
    }

    if ($project != null) {

        $projectId = $project["project_id"];
        $project_id = $projectId;
        assign_child_db($projectId);

    }

    $db2 = $app->db2;
    $con = new connector();
    $db2->orderBy("rand()");
    $queue = $db2->get("quee_trigger_view", [0, 10]);

    foreach ($queue as $key => $value) {
        try {

            $res = $con->trigger($value["visitor_id"], $value["type"]);
            $db2->where("visitor_id", $value["visitor_id"])->delete("quee_trigger_view");

            $ret = ["code" => 1, "result" => "empty"];
        } catch (\Throwable $th) {
            return ["code" => 0, "result" => "empty"];
        } finally {

        }
    }
    return $ret;
}

function cta($param)
{
    $all_cs = false;
    if (!isset($param["divisi"])) {
        $all_cs = true;
    }
    if (!isset($param["project_key"])) {
        return ["code" => 0, "msg" => "no project_key"];
    }

    if (!isset($param["use_deeplink"])) {
        return ["code" => 0, "msg" => "no use_deeplink"];
    }
    if (!isset($param["msg"])) {
        return ["code" => 0, "msg" => "no msg"];
    }

    global $keya, $c_time, $app, $project_id;
    $db = $app->db;

    extract($param);

    $memcached = new Memcached();
    $project_key = $param["project_key"];
    $memcached->addServer('127.0.0.1', 11211);

    if (extension_loaded('memcached')) {
        $mem_key = 'GetProjectByKey_' . $project_key;
        $project = $memcached->get($mem_key);
    }
    //file_put_contents('log/memcached_key.log', "act cta \n" .$mem_key."\n", FILE_APPEND);
    if (empty($project)) {
        $db->where("project_key = UNHEX(?)", [$project_key]);
        $project = $db->getone("project");
        $expiration = 3600 * 24 * 30;
        if (extension_loaded('memcached')) {
            $memcached->set($mem_key, $project, $expiration);
        }
    }

    if ($project == null) {
        return ["code" => 0, "msg" => "Project Not Found"];
    }
    global $app;
    assign_child_db($project["project_id"]);
    $project_id = $project["project_id"];
    $db2 = $app->db2;

    $divisi_slug = $divisi;
    unset($divisi);

    if (extension_loaded('memcached')) {
        $mem_key = $project["project_id"] . '_GetDivisiByName_' . $divisi_slug;
        $divisi = $memcached->get($mem_key);
    }
    //file_put_contents('log/memcached_key.log', $mem_key."\n", FILE_APPEND);
    if (empty($divisi)) {
        $db2->where("divisi_key = UNHEX(?)", [md5($divisi_slug)]);
        $divisi = $db2->getone("divisi");
        $expiration = 3600;
        if (extension_loaded('memcached')) {
            $memcached->set($mem_key, $divisi, $expiration);
        }
    }

    if ($use_deeplink) {
        if (isInstagramInAppBrowserOnIOS()) {
            $wa_url = "https://api.whatsapp.com/send";
        } else {
            if (isInstagramInAppBrowserOnAndroid()) {
                $wa_url = "https://api.whatsapp.com/send";
            } else {
                $wa_url = "whatsapp://send";
            }
        }
    } else {
        $wa_url = "https://api.whatsapp.com/send";
    }

    if (!empty($msg)) {
        $msg = urlencode($param["msg"]);
        $msg = str_replace('%25break%25', '%0A', $msg);
    } else {
        $msg = "Halo ada yang mau ditanyakan ?";
    }

    //$divisi = null;

    $pembagian = new cs_pembagian();

    if ($divisi != null) {

        $cs = $pembagian->get_cs($divisi["divisi_id"]);

    } else {
        $all_cs = true;
    }

    if ($all_cs) {
        $cs = $pembagian->random();
    }

    $phone = $cs;

    if (!empty($visitor_id)) {

        if (strpos($msg, '_gid_') !== false) {
            $msg = str_replace("_gid_", " " . $visitor_id . " ", $msg);
        } else {
            $msg = "ID+%5B" . " " . $visitor_id . " " . "%5D%0A%0A" . $msg;
        }

        $visitor_id = str_replace(".", "", $visitor_id);
        $visitor_id = convBase($visitor_id, "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", "0123456789");

        $db2->where("visitor_id", $visitor_id);
        $visitor = $db2->getone("visitor");

        if ($visitor != null) {

            $visitor_data = unserialize($visitor["data"]);

            if (isset($visitor_data["last_campaign"])) {
                if ($visitor_data["last_campaign"]["source"] == "meta") {
                    if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                        $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];

                        $key = $visitor_id . ";cta;meta";
                        $campaign = new report();
                        $db2->setLockMethod("WRITE")->lock(array("report_hash", "meta", "report_data"));
                        if ($campaign->get_hash($key) == null) {
                            $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                        }
                        /*
                        if (!isset($_COOKIES["cta-meta-" . $adcopy_id])) {
                        $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                        }*/
                        $campaign->add_report_data($visitor, $adcopy_id, "cta");
                        // setcookie("cta-meta-" . $adcopy_id, 1, time() + (86400 * 360), "/");
                        $campaign->add_hash($key);
                        $db2->unlock();
                    }
                }

                if ($visitor_data["last_campaign"]["source"] == "tiktok") {
                    if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                        $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];
                        $key = $visitor_id . ";cta;tiktok";
                        $campaign = new report();
                        $db2->setLockMethod("WRITE")->lock(array("report_hash", "meta", "report_data"));
                        if ($campaign->get_hash($key) == null) {
                            $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                        }
                        $campaign->add_report_data($visitor, $adcopy_id, "cta");
                        $campaign->add_hash($key);
                        $db2->unlock();
                        /*
                    if (!isset($_COOKIES["cta-tiktok-" . $adcopy_id])) {
                    $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                    }
                    $campaign->add_report_data($visitor, $adcopy_id, "cta");
                    setcookie("cta-tiktok-" . $adcopy_id, 1, time() + (86400 * 360), "/");
                     */
                    }
                }

                if ($visitor_data["last_campaign"]["source"] == "snack") {
                    if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                        $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];

                        $key = $visitor_id . ";cta;snack";
                        $campaign = new report();
                        $db2->setLockMethod("WRITE")->lock(array("report_hash", "meta", "report_data"));
                        if ($campaign->get_hash($key) == null) {
                            $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                        }
                        $campaign->add_report_data($visitor, $adcopy_id, "cta");
                        $campaign->add_hash($key);
                        $db2->unlock();
                    }
                }

                if ($visitor_data["last_campaign"]["source"] == "mgid") {
                    if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                        $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];

                        $key = $visitor_id . ";cta;mgid";
                        $campaign = new report();
                        $db2->setLockMethod("WRITE")->lock(array("report_hash", "meta", "report_data"));
                        if ($campaign->get_hash($key) == null) {
                            $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                        }
                        $campaign->add_report_data($visitor, $adcopy_id, "cta");
                        $campaign->add_hash($key);
                        $db2->unlock();
                    }
                }

                if ($visitor_data["last_campaign"]["source"] == "mgid") {
                    if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                        $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];

                        $key = $visitor_id . ";cta;mgid";
                        $campaign = new report();
                        $db2->setLockMethod("WRITE")->lock(array("report_hash", "meta", "report_data"));
                        if ($campaign->get_hash($key) == null) {
                            $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                        }
                        $campaign->add_report_data($visitor, $adcopy_id, "cta");
                        $campaign->add_hash($key);
                        $db2->unlock();
                    }
                }

                if ($visitor_data["last_campaign"]["source"] == "google") {
                    if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                        $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];

                        $key = $visitor_id . ";cta;google";
                        $campaign = new report();
                        $db2->setLockMethod("WRITE")->lock(array("report_hash", "meta", "report_data"));
                        if ($campaign->get_hash($key) == null) {
                            $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                        }
                        $campaign->add_report_data($visitor, $adcopy_id, "cta");
                        $campaign->add_hash($key);
                        $db2->unlock();
                    }
                }

                if ($visitor_data["last_campaign"]["source"] == "organic") {
                    if (isset($visitor_data["last_campaign"]["data"]["adcopy_id"])) {
                        $adcopy_id = $visitor_data["last_campaign"]["data"]["adcopy_id"];

                        $key = $visitor_id . ";cta;organic";
                        $campaign = new report();
                        $db2->setLockMethod("WRITE")->lock(array("report_hash", "meta", "report_data"));
                        if ($campaign->get_hash($key) == null) {
                            $campaign->add_report_data($visitor, $adcopy_id, "unik_cta");
                        }
                        $campaign->add_report_data($visitor, $adcopy_id, "cta");
                        $campaign->add_hash($key);
                        $db2->unlock();
                    }
                }

            }
            $con = new connector();

            $data_insert["visitor_id"] = $visitor_id;
            $data_insert["type"] = "cta";
            $db2->setQueryOption(["IGNORE"])->insert("quee_trigger_view", $data_insert);

            $visitor_event["waktu"] = date("Y-m-d H:i:s");
            $visitor_event["visitor_id"] = $visitor_id;
            $visitor_event["event"] = "cta";
            $visitor_event["message_id"] = !empty($param["divisi"]) ? $param["divisi"] : "";
            $db2->insert("visitor_event", $visitor_event);

        }
    }

    $ret["wa_url"] = $wa_url . "?phone=" . $cs . "&text=" . $msg;
    $ret["msg"] = $msg;
    $ret["phone"] = $phone;
    return $ret;

}

function hook_create_visitor($phone)
{
    global $app;
    $db2 = $app->db2;

    $data_visitor["phone"] = $phone;
    $data_visitor["created"] = date("Y-m-d H:i:s");
    $data_visitor["visit"] = date("Y-m-d H:i:s");
    $data_visitor["waktu_contact"] = date("Y-m-d H:i:s");
    $data_visitor["lead"] = 1;

    $visitor_id = $db2->insert("visitor", $data_visitor);

    return $visitor_id;
}

/*
function contact($param){
global $keya, $c_time, $app;
$db = $app->db;
$db2 = $app->db2;
$site_id = $param["site_id"];
$db->where("id",$site_id);
$site = $db->getone("x_site");
if($db->count==0){
die();
}

$log["site_id"] = $site_id;
$log["param"] = @json_encode($param);
@$db->insert("x_log_contact2",$log);

print_r($param);
$param["pixel"] = $site["fb_pixel"];
$param["apikey"] = $site["fb_token"];
$param["adw_global_tag_id"] = $site["adw_tag"];
$param["adw_conv_id"] = $site["adw_conv_id"];
$param["gtm"] = $site["gtm"];
$param["tiktok"] = $site["tiktok_pixel"];
$param["vid"] =  preg_replace('/[^0-9]/', '', $param["vid"]);
if(isset($param["phone"])){
$custom["phone"] = $param["phone"];
}
if(isset($param["email"])){
$custom["email"] = $param["email"];
}
if(isset($param["debug"])){
$custom["debug"] = 1;
}
$et = new eztrack($site_id);
if(isset($custom)){
$res = $et->contact($site,$param,$custom);
}else{
$res = $et->contact($site,$param);
}
echo 1;
die();
}

function checkout($param){
global $keya, $c_time, $app;
$db = $app->db;
$db2 = $app->db2;
$site_id = $param["site_id"];
$db->where("id",$site_id);
$site = $db->getone("x_site");
if($db->count==0){
die();
}
print_r($param);
if($param["vid"] == "" || $param["vid"] == "undefined"){
$table_visitor = $site_id . '_visitor';
$db2->where("phone",$param["phone"]);
$tmp = $db2->getone($table_visitor);
$param["vid"] = $tmp["vid"];
}
$param["pixel"] = $site["fb_pixel"];
$param["apikey"] = $site["fb_token"];
$param["adw_global_tag_id"] = $site["adw_tag"];
$param["adw_conv_id"] = $site["adw_conv_id"];
$param["gtm"] = $site["gtm"];
$param["tiktok"] = $site["tiktok_pixel"];
$param["vid"] =  preg_replace('/[^0-9]/', '', $param["vid"]);
if(isset($param["phone"])){
$custom["phone"] = $param["phone"];
}
if(isset($param["email"])){
$custom["email"] = $param["email"];
}
if(isset($param["debug"])){
$custom["debug"] = 1;
}
if(isset($param["pesan"])){
$custom["pesan"] = $param["pesan"];
}
$et = new eztrack($site_id);
if(isset($custom)){
$res = $et->checkout($site,$param,$custom);
}else{
$res = $et->checkout($site,$param);
}
echo 1;
die();
}

function purchase($param){
global $keya, $c_time, $app;
$db = $app->db;
$db2 = $app->db2;
$site_id = $param["site_id"];
$db->where("id",$site_id);
$site  = $db->getone("x_site");
if($db->count==0){
die();
}

$log["site_id"] = $site_id;
$log["param"] = @json_encode($param);
@$db->insert("x_log_purchase",$log);

if($param["vid"] == "" || $param["vid"] == "undefined"){
$table_visitor = $site_id . '_visitor';
$db2->where("phone",$param["phone"]);
$tmp = $db2->getone($table_visitor);
$param["vid"] = $tmp["vid"];
//echo $db2->getLastQuery();
}
$param["pixel"] = $site["fb_pixel"];
$param["apikey"] = $site["fb_token"];
$param["adw_global_tag_id"] = $site["adw_tag"];
$param["adw_conv_id"] = $site["adw_conv_id"];
$param["gtm"] = $site["gtm"];
$param["tiktok"] = $site["tiktok_pixel"];
$param["vid"] =  preg_replace('/[^0-9]/', '', $param["vid"]);
if(isset($param["phone"])){
$custom["phone"] = $param["phone"];
}
if(isset($param["email"])){
$custom["email"] = $param["email"];
}
if(isset($param["currency"])){
$custom["currency"] = $param["currency"];
}
if(isset($param["value"])){
$custom["value"] = $param["value"];
}
if(isset($param["pesan"])){
$custom["pesan"] = $param["pesan"];
}
$et = new eztrack($site_id);
if(isset($custom)){
$res = $et->purchase($site, $param, $custom);
}else{
$res = $et->purchase($site,$param);
}
echo $res;
die();
}
 */
