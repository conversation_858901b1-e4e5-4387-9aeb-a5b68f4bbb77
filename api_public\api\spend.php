<?php
 
function update($param){
    
    //file_put_contents("log/spend.txt",json_encode($param));die();
    
    
    //$param = file_get_contents("log/spend.txt");
    
   //$param = json_decode($param,true);
  
   $param["data"] = json_decode($param["data"],true);
   
   
   
   extract($param);
   
   if(isset($project_id)){
        assign_child_db($project_id);
        
        $r = new report();
        $source = "meta";
        $data_campaign = $data;
        
        foreach ($data_campaign as $key => $value){
                $cid = $value["campaign_id"];
                $adset_id = $value["adset_id"];
                $ad_id = $value["ad_id"];
                $campaigns[$cid]["name"] = $value["campaign_name"];
                $campaigns[$cid]["adset"][$adset_id]["name"] = $value["adset_name"];
                $campaigns[$cid]["adset"][$adset_id]["adcopy"][$ad_id]["name"] = $value["ad_name"];
            }
            foreach ($campaigns as $key => $campaign){
                $campaign_id = $r->add_report_kolom($source, $campaign["name"], "campaign", $key, null, true) ["data"];
                if (count($campaign["adset"]) > 0){
                    if ($campaign_id != NULL){
                        foreach ($campaign["adset"] as $key2 => $adset){
                            $adset_id = $r->add_report_kolom($source, $adset["name"], "adset", $key2, $campaign_id, true) ["data"];
                            if ($adset_id != NULL)
                            {

                                foreach ($adset["adcopy"] as $key3 => $adcopy)
                                {
                                    $adcopy_id = $r->add_report_kolom($source, $adcopy["name"], "adcopy", $key3, $adset_id, true) ["data"];
                                }
                            }
                        }
                    }
                }
            }

           
            foreach ($data_campaign as $key => $data_campaign_child){
                $tmp = [];
                $date = $data_campaign_child["date_start"];
                foreach ($data_campaign_child as $key => $value){
                    if ($key == "spend" || $key == "impressions") {
                        $tmp[$key] = $value;
                    }elseif ($key == "outbound_clicks"){
                        $tmp[$key] = (int)$value[0]["value"];
                    }
                }

                foreach ($tmp as $key2 => $value2){
                    $report_id = $r->get_report_id($data_campaign_child["ad_id"]);
                    if ($report_id){
                        $r->update_report_data($report_id, $key2, $value2, $date);
                    }
                }
            }
        
      //  echo "x";
       
   }
   
  
   
   return $param;
    
    
}