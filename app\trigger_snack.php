<?php
class trigger_snack{

	private $table_visitor;
	private $table_log;

	function __construct()
    {
  
    	$this->table_visitor = "visitor";
    	$this->table_log = "log_connector";
    	
    }
 
    function test_event($access_token,$pixel,$clickid,$event)
    {
    	// API Endpoint URL
		$url = 'http://www.adsnebula.com/log/common/api';

		// Data to be sent in the POST request
		$data = array(
		    'access_token' => $access_token,
		    'clickid' => $clickid,
		    'event_name' => $event,
		    'is_attributed' => 1,
		    'mmpcode' => 'PL',
		    'pixelId' => $pixel,
		    'pixelSdkVersion' => '9.9.9',
		    'properties' => '{"content_id":"18711551","content_type":"product","content_name":"Pacote de Presente de Eletrodomésticos"}',
		    'testFlag' => false,
		    'third_party' => 'gass.co.id',
		    'trackFlag' => true
		);

		// Convert data to JSON format
		$postData = json_encode($data);

		// Initialize cURL session
		$curl = curl_init($url);

		// Set cURL options
		curl_setopt($curl, CURLOPT_POST, true);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HTTPHEADER, array(
		    'accept: application/json;charset=utf-8',
		    'Content-Type: application/json'
		));
		curl_setopt($curl, CURLOPT_POSTFIELDS, $postData);

		// Execute cURL request and get the response
		$response = curl_exec($curl);

		// Check for cURL errors
		if ($response === false) {
		    $error = curl_error($curl);
		   // echo 'cURL Error: ' . $error;
		} else {
		    // Handle the API response
		   // echo 'Response: ' . $response;
		}

		// Close cURL session
		curl_close($curl);
    }
	     
	function sent_pixel($connector_key,$value,$pixel_id,$access_token,$vid,$type,$custom=NULL)
	{

		global $app;
		$db2 = $app->db2;

		$hash["value"] = $value;
		$hash["pixel_id"] = $pixel_id;
		$hash["access_token"] = $access_token;
		$hash["source"] = "snack";
		$hash["vid"] = $vid;
		$hash["type"] = $type;
		$hash["custom"] = $custom;
		$hash["waktu"] = date("Y-m-d");
		$data_hash = json_encode($hash);
		//var_dump($hash2);
		$hash = hash("sha256", $data_hash);
		//echo $type;
		

		$table_visitor = $this->table_visitor;
		$table_log = $this->table_log;

		$waktu = time();

		$db2->where("visitor_id",$vid);
		$visitor = $db2->getone($this->table_visitor);

		if($visitor == NULL)
		{
				return;
		}
		if($visitor == "")
		{
				return;
		}

		if($visitor["data"] == NULL){return;}

		$visitor_data = unserialize($visitor["data"]);

		if(!isset($visitor_data["snack_video"]["click_id"])){
			return false;
		}

			
			$timestamp = date('Y-m-d\TH:i:s.Z\Z', time());
			$post = array();
			$post["access_token"] = $access_token;
			$post["clickid"] = $visitor_data["snack_video"]["click_id"];
			$post["event_name"] = $type;
			$post["is_attributed"] = 1;
			$post["mmpcode"] = "PL";
			$post["pixelId"] = $pixel_id;
			$post["pixelSdkVersion"] = "9.9.9";
			//$post["properties"]["event_timestamp"] = $timestamp;
			if($value != 0)
			{
				$m = new meta();
				$cur = $m->get_meta("currency");
				if(isset($cur["result"]["data"])){
					$post["properties"]["currency"] = $cur["result"]["data"];
				}else{
					$post["properties"]["currency"] = "IDR";
				}
				
				$post["properties"]["value"] = $value;
			}
			$post["testFlag"] = false;
			$post["third_party"] = "gass.co.id";
			$post["trackFlag"] = false;
			
			
			// Convert data to JSON format
			$postData = $post;
			$postData["properties"] = json_encode($postData["properties"]);
			$postData = json_encode($postData);

			// API Endpoint URL
			$url = 'http://www.adsnebula.com/log/common/api';
 
			// Initialize cURL session
			$curl = curl_init($url);

			// Set cURL options
			curl_setopt($curl, CURLOPT_POST, true);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
    'accept: application/json;charset=utf-8',
    'Content-Type: application/json'
));
		
			curl_setopt($curl, CURLOPT_POSTFIELDS, $postData);

			// Execute cURL request and get the response
			$result = curl_exec($curl);
			
           





			$log["pixel_id"] = $pixel_id;
			$log["access_token"] = $access_token;

			
			$log["type"] = $type;
			$log["custom"] = $custom;
			

			$data_insertx = array();
			$data_insertx["waktu"] = date("Y-m-d H:i:s");
			$data_insertx["connector_key"] = $db2->func("UNHEX(?)",[$connector_key]);
			$data_insertx["vid"] = $vid;
			$data_insertx["event"] = $type;
			

			if (curl_errno($curl)) {
				$return = curl_error($curl);

			  	$log["msg"] = $return;
			  	$data_insertx["error"] = 1;
			  	$ddd["input"] = $data_hash;
				$ddd["output"] = $log;

		  		$data_insertx["result"] = json_encode($ddd);

		  		$db2->insert($table_log,$data_insertx);

		  		//echo $db2->getLastQuery();
				$result = $return;

			}else
			{

				$data_insert["result"] = json_encode($result);
				//$db2->insert($table_log,$data_insert);

				$log["msg"] = $result;
				$data_insertx["error"] = 0;
				$data_insertx["result"] = json_encode($log);
				$db2->insert($table_log,$data_insertx);
				//echo $db2->getLastQuery();
			}
			curl_close($curl);
		
		return $result;
	}



}