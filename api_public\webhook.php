<?php
global $app;  
$db = $app->db;
header('Access-Control-Allow-Origin: *'); 
header('Access-Control-Allow-Methods: GET, POST');
//header("Access-Control-Allow-Headers: X-Requested-With");
set_time_limit(0);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
//file_put_contents('server.txt', json_encode($_SERVER));
if (count($_GET) > 0) {
    $_POST = array_merge($_POST,$_GET);
}

if(isset($_GET["type"]))
{
    $_GET["provider"] = $_GET["type"];
}

if(isset($_GET["provider"])){
    if($_GET["provider"] == "waba" || $_GET["provider"] == "qontak" || $_GET["provider"] == "sleekflow"){
        include("webhook-v2.php");
        die();
    }
}

if(isset($_GET["p"]))
{
    /// konekwa hasbi 3ECQYSAH6L0
    if($_GET["p"] == "FA9F3F818BDD56CA8AA9DFBAAB2636F6")
    {
        include("webhook-v2.php");
        die();
    }

    /// qontak muslim 3MFEZNJ0SS8  159.65.4.14
    if($_GET["p"] == "F9D31A51B8E60023BDA507278E3053B3")
    {
        include("webhook-v2.php");
        die();
    }

    // /// waba Andrion Series 3OOY201223Z	6289654929455
    // if($_GET["p"] == "E3F02BD9E312135255A641E85B0FF78C")
    // {
    //     include("webhook-v2.php");
    //     die();
    // }

    /// test
    if($_GET["p"] == "2235BA132AA6C92005626FF2A329301B")
    {
        include("webhook-v2.php");
        die();
    }
}
if(isset($_GET["provider"])){
    $file = "api_public/webhook/".$_GET["provider"].".php";
    if (file_exists($file)) {
        include "webhook/".$_GET["provider"].".php";
    } 
}

if(isset($_GET["pv"])){
    $file = "api_public/webhook/".$_GET["pv"].".php";
    if (file_exists($file)) {
        include "webhook/".$_GET["pv"].".php";
    }
}