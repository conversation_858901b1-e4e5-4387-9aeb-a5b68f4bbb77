<?php

if(!isset($_GET["p"])){echo "invalid link";die();}
if(!isset($_GET["cs"])){echo "invalid link";die();}
if(!isset($input)){echo "invalid link";die();}

$input = file_get_contents('php://input');

file_put_contents('log/hooklog-botsailor-'.$_GET["cs"].'.txt', '[' . date('Y-m-d H:i:s') . "]\n", FILE_APPEND);	
file_put_contents('log/hooklog-botsailor-'.$_GET["cs"].'.txt', $input, FILE_APPEND);	
file_put_contents('log/hooklog-botsailor-'.$_GET["cs"].'.txt', "\n\n", FILE_APPEND);