<?php 
class sites{

    function delete_campaign($global_campaign_id)
    {
        global $app;
        $db = $app->db;
        $db2 = $app->db2;        
        $db->where("id",$global_campaign_id);
        $campaign = $db->getone("x_campaign");
        if($db->count >0){
            $db->where("campaign_id",$global_campaign_id);
            $db->delete("x_shortlink");

            $campaign_id = $campaign["campaign_id"];
            $site_id = $campaign["site_id"];
            $table_campaign = $site_id."_campaign";
            $db->where("campaign_id",$campaign_id);
            $db->delete($table_campaign);
            $table_campaign_cs = $site_id."_campaign_cs";
            $db->where("campaign_id",$campaign_id);
            $db->delete($table_campaign_cs);
    
            $table_campaign_detail = $site_id."_campaign_detail";
            $db->where("campaign_id",$campaign_id);
            $db->delete($table_campaign_detail);
    
            $table_visitor = $site_id."_visitor";
            $db->where("campaign_id",$campaign_id);
            $db->delete($table_visitor);
            return true;
        }else{
            return false;
        }        
    }
    
    function delete_sites($site_id)
    {
        global $app;
        $db = $app->db;
        $db2 = $app->db2;
        $db->where("site_id",$site_id);
        $campaign = $db->get("x_campaign");
        foreach ($campaign as $key => $value) {
            $db->where("campaign_id",$value["id"]);
            $db->delete("x_shortlink");
        }
        $db->where("site_id",$site_id);
        $db->delete("x_campaign");
        $db->where("site_id",$site_id);
        $db->delete("x_cs");
        $db->where("id",$site_id);
        $db->delete("x_site");
        $q = "DROP TABLE `{$site_id}_campaign`";
        $db2->rawQuery($q);
        $q = "DROP TABLE `{$site_id}_campaign_cs`";
        $db2->rawQuery($q);
        $q = "DROP TABLE `{$site_id}_campaign_detail`";
        $db2->rawQuery($q);
        $q = "DROP TABLE `{$site_id}_cs`";
        $db2->rawQuery($q);
        $q = "DROP TABLE `{$site_id}_cs_log`";
        $db2->rawQuery($q);
        $q = "DROP TABLE `{$site_id}_log`";
        $db2->rawQuery($q);
        $q = "DROP TABLE `{$site_id}_event`";
        $db2->rawQuery($q);
        $q = "DROP TABLE `{$site_id}_hash`";
        $db2->rawQuery($q);
        $q = "DROP TABLE `{$site_id}_landing_page`";
        $db2->rawQuery($q);
        $q = "DROP TABLE `{$site_id}_landing_page_detail`";
        $db2->rawQuery($q);
        $q = "DROP TABLE `{$site_id}_visitor`";
        $db2->rawQuery($q);
        return true;
    }


}