<?php

namespace DynamicWebhook\TriggerProcessor;

use DynamicWebhook\Abstracts\AbstractTriggerProcessor;

/**
 * Prospect trigger processor
 */
class ProspectTriggerProcessor extends AbstractTriggerProcessor
{
    /**
     * Get trigger type name
     */
    public function getTriggerType(): string
    {
        return 'prospect';
    }

    /**
     * Check if trigger should be processed for given message type
     */
    public function shouldProcess(string $messageType): bool
    {
        // Prospect trigger only processes outgoing messages
        return $messageType === 'message_out';
    }

    /**
     * Process specific trigger logic
     */
    protected function processSpecificTrigger(array $data, array $context): bool
    {
        $message = $data['message'] ?? '';
        if (!$message) {
            return false;
        }

        $res = $this->getMeta("format_prospek");
        if ($res["code"] != 1) {
            return false;
        }

        $formatCheckout = $this->cleanString($res["result"]["data"]);
        $formatCheckout = preg_quote($formatCheckout, '/');
        $strregex = str_replace('%ID%', '(\d+)', $formatCheckout);
        $regex = '/' . $strregex . '/';
        
        $result = preg_match($regex, $message, $matches);
        
        return $result !== false && $result > 0;
    }
}
