<?php

function subdomain_get(){
    global $keya, $c_time, $app;
    $db = $app->db;
    $db2 = $app->db2;
    $db->where("subdomain != '' and subdomain_status = ? and status = 1", array(0));
    $site = $db->getone("x_site", 'subdomain');
    if($db->count == 0){
        $site = array();
    }
    return $site;
}

function subdomain_update($param){
    global $keya, $c_time, $app;
    $db = $app->db;
    $db2 = $app->db2;
	$msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
		'subdomain'      => 'required',
		));
	$validated = $gump->run($param);
	if($validated === false) {
		$msg['code'] = 0 ;
		$msg['msg'] = $gump->get_readable_errors(true);	
	}else{
        $db->where("subdomain = ? and subdomain_status = 0",array($param['subdomain']));
        $service = $db->getone("x_site");
        if($db->count > 0){ 
            $db->where("subdomain = ?",array($param['subdomain']));
            if($db->update("x_site", array('subdomain_status'=> 1))){
                $msg['code'] = 1 ;
                $msg["msg"] = 'Sukses mengubah status subdomain.';
            }else{
                $msg['code'] = 0 ;
                $msg["msg"] = 'Gagal mengubah status subdomain.';
            }
        }else{
            $msg['code'] = 0 ;
            $msg["msg"] = 'Site tidak ditemukan';
        }
    }
    return $msg;
}


function webhook($param){
    global $keya, $c_time, $app;
    $db = $app->db;
    $db2 = $app->db2;
    $site_id = $param["site_id"];
    $db->where("id",$site_id);
    $site = $db->getone("x_site");
    if($db->count==0){
        die();
    }
    $param["pixel"] = $site["fb_pixel"];
    $param["apikey"] = $site["fb_token"];
    $param["adw_global_tag_id"] = $site["adw_tag"];
    $param["adw_conv_id"] = $site["adw_conv_id"];
    $param["gtm"] = $site["gtm"];
    $param["tiktok"] = $site["tiktok_pixel"];
    $param["vid"] =  preg_replace('/[^0-9]/', '', $param["vid"]);
    if(isset($param["phone"])){
        $custom["phone"] = $param["phone"];
    }
    if(isset($param["email"])){
        $custom["email"] = $param["email"];
    }
    if(isset($param["debug"])){
        $custom["debug"] = 1;
    }
    $et = new eztrack($site_id);
    if(isset($custom)){
        $res = $et->contact($site,$param,$custom);
    
    }else{
        $res = $et->contact($site,$param);
    }
    echo 1;
    die();
}

function contact($param){
    global $keya, $c_time, $app;
    $db = $app->db;
    $db2 = $app->db2;
    $site_id = $param["site_id"];
    $db->where("id",$site_id);
    $site = $db->getone("x_site");
    if($db->count==0){
        die();
    }
    $param["pixel"] = $site["fb_pixel"];
    $param["apikey"] = $site["fb_token"];
    $param["adw_global_tag_id"] = $site["adw_tag"];
    $param["adw_conv_id"] = $site["adw_conv_id"];
    $param["gtm"] = $site["gtm"];
    $param["tiktok"] = $site["tiktok_pixel"];
    $param["vid"] =  preg_replace('/[^0-9]/', '', $param["vid"]);
    if(isset($param["phone"])){
        $custom["phone"] = $param["phone"];
    }
    if(isset($param["email"])){
        $custom["email"] = $param["email"];
    }
    if(isset($param["debug"])){
        $custom["debug"] = 1;
    }
    $et = new eztrack($site_id);
    if(isset($custom)){
        $res = $et->contact($site,$param,$custom);
    }else{
        $res = $et->contact($site,$param);
    }
    echo 1;
    die();
}

function checkout($param){
    global $keya, $c_time, $app;
    $db = $app->db;
    $db2 = $app->db2;
    $site_id = $param["site_id"];
    $db->where("id",$site_id);
    $site = $db->getone("x_site");
    if($db->count==0){
        die();
    }
    if($param["vid"] == "" || $param["vid"] == "undefined"){
        $table_visitor = $site_id . '_visitor';
        $db2->where("phone",$param["phone"]);
        $tmp = $db2->getone($table_visitor);
        $param["vid"] = $tmp["vid"];
    }
    $param["pixel"] = $site["fb_pixel"];
    $param["apikey"] = $site["fb_token"];
    $param["adw_global_tag_id"] = $site["adw_tag"];
    $param["adw_conv_id"] = $site["adw_conv_id"];
    $param["gtm"] = $site["gtm"];
    $param["tiktok"] = $site["tiktok_pixel"];
    $param["vid"] =  preg_replace('/[^0-9]/', '', $param["vid"]);
    if(isset($param["phone"])){
        $custom["phone"] = $param["phone"];
    }
    if(isset($param["email"])){
        $custom["email"] = $param["email"];
    }
    if(isset($param["debug"])){
        $custom["debug"] = 1;
    }
    if(isset($param["pesan"])){
        $custom["pesan"] = $param["pesan"];
    }
    $et = new eztrack($site_id);
    if(isset($custom)){
        $res = $et->checkout($site,$param,$custom);
    }else{
        $res = $et->checkout($site,$param);
    }
    echo 1;
    die();
}

function purchase($param){
    global $keya, $c_time, $app;
    $db = $app->db;
    $db2 = $app->db2;
    $site_id = $param["site_id"];
    $db->where("id",$site_id);
    $site  = $db->getone("x_site");
    if($db->count==0){
        die();
    }
    if($param["vid"] == "" || $param["vid"] == "undefined"){
        $table_visitor = $site_id . '_visitor';
        $db2->where("phone",$param["phone"]);
        $tmp = $db2->getone($table_visitor);
        $param["vid"] = $tmp["vid"];
    }
    $param["pixel"] = $site["fb_pixel"];
    $param["apikey"] = $site["fb_token"];
    $param["adw_global_tag_id"] = $site["adw_tag"];
    $param["adw_conv_id"] = $site["adw_conv_id"];
    $param["gtm"] = $site["gtm"];
    $param["tiktok"] = $site["tiktok_pixel"];
    $param["vid"] =  preg_replace('/[^0-9]/', '', $param["vid"]);
    if(isset($param["phone"])){
        $custom["phone"] = $param["phone"];
    }
    if(isset($param["email"])){
        $custom["email"] = $param["email"];
    }
    if(isset($param["currency"])){
        $custom["currency"] = $param["currency"];
    }
    if(isset($param["value"])){
        $custom["value"] = $param["value"];
    }
    if(isset($param["pesan"])){
        $custom["pesan"] = $param["pesan"];
    }
    $et = new eztrack($site_id);
    if(isset($custom)){
        $res = $et->purchase($site, $param, $custom);
    }else{
        $res = $et->purchase($site,$param);
    }
    echo $res;
    die();
}

function send($param){
    global $keya, $c_time, $app;
    $db = $app->db;
	$o = new order();
	$msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
		'sender'      => 'required',
        'to'      => 'required',
        'message' => 'required',
		));
	$validated = $gump->run($param);
	if($validated === false) {  
		$msg['code'] = 0 ;
		$msg['msg'] = $gump->get_readable_errors(true);	
	}else{
        $data = [
            'sender' => $param['sender'],
            'to' => $param['to'],
            'message' => $param['message'],
        ];
        $db->where("nope = ? and status = 1", array($param['sender']));
        $cs = $db->getone("x_cs");
        if($db->count>0){
            $msg = json_decode(post_gateway_wa($data, 'http://'.$cs['server'].':8080/send-message'), true);
        }else{
            $msg['code'] = 0 ;
            $msg['msg'] = "Phone not found";
        }
        
    }
    return $msg;
}

function validate($param){ 
    global $keya, $c_time, $app;
    $db = $app->db;
	$msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
		'id'      => 'required',
        'phone'      => 'required',
		));
	$validated = $gump->run($param);
	if($validated === false) {  
		return $msg;
	}else{  
        $db->where("wa_notif = ? and id = ?",array(p('phone'), p('id')));
        $service = $db->getone("x_service");
        if($db->count > 0){
            $dd["wa_notif_status"]   = 1;
            $db->where("wa_notif = ? and id = ?",array(p('phone'), p('id')));
            if($db->update("x_service",$dd)){
                $msg['msg'] = 'Sukses verifikasi nomer whatsapp';
                $msg['code'] = 1 ;
            }else{
                $msg['msg'] = 'Gagal verifikasi nomer whatsapp';
                $msg['code'] = 0 ;
            }
        }else{
            $msg['code'] = 0 ;
            $msg['msg'] = "ID atau phone anda tidak sesuai data.";	
        }
    }
    return $msg;
}

function cs_scan($param){ 
    global $keya, $c_time, $app;
    $db = $app->db;
	$msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
        'phone'      => 'required',
		));
	$validated = $gump->run($param);
	if($validated === false) {  
		return $msg;
	}else{  
        $db->where("c.nope = ? and c.status = 1 and c.followup_scan = 0", array(p('phone')));
        $db->join("x_site s","s.id = c.site_id");
        $db->join("x_user u","u.id = c.user_id");
        $cs = $db->getone("x_cs c","c.nope,c.name,c.id as cs_id,c.cs_id as cs_id2,c.server, u.phone, s.domain");
        if($db->count > 0){
            autodc(p('phone'));
            $wa = new wa();
            $pesan = "Whatsapp cs ini ".$cs['nope']."(".$cs['name'].") harus di Scan di panel https://panel.gass.co.id/wa-scan.html?site=".strtoupper($cs['domain'])."&phone=".$cs['nope'];
            $pesan .= "\n\n( Ini merupakan pesan otomatis )";
            $wa->send('text', $app->config->phone_whatsapp, $cs['phone'], $pesan);
            $db->where("nope = ?",array(p('phone')));
            if($db->update("x_cs", array('followup_scan' => 1))){
                $msg['msg'] = 'Sukses kirim pesan qr';
                $msg['code'] = 1 ;
            }else{
                $msg['msg'] = 'Gagal kirim pesan qr';
                $msg['code'] = 0 ;
            }
        }else{
            $msg['code'] = 0 ;
            $msg['msg'] = "phone cs tidak ditemukan.";	
        }
    }
    return $msg;
}


function get_cs_server($param){
    global $keya, $c_time, $app;
    $db = $app->db;
	$msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
        'server' => 'required',
		));
	$validated = $gump->run($param);
	if($validated === false) {  
		$msg['code'] = 0 ;
		$msg['msg'] = $gump->get_readable_errors(true);	
	}else{   
        $db->where("server = ?",array($param['server']));
        $db->update("x_cs", array('followup_scan' => 0));

        $db->where("status = 1"); 
        $db->where("server = ?",array($param['server']));
        $cs = $db->get("x_cs", NULL, "id, name, nope, license, webhook, status, status_wa, server, formatVisit, formatCheckout, valueCheckout, formatPurchase, valuePurchase");
        foreach($cs as $i=>$v){
            $cs[$i]['webhook'] = str_replace("https://gass.co.id/apisas.html", "http://10.104.0.2/api.html", $cs[$i]['webhook']);
            $cs[$i]['webhook'] = str_replace("https://panel.gass.co.id/apisas.html", "http://10.104.0.2/api.html", $cs[$i]['webhook']);
        }
        //echo $db->getLastQuery();
        if($db->count > 0){ 
            $msg['msg'] = 'Success get cs';
            $msg['code'] = 1 ;
            $msg['data'] = $cs;
        }else{
            $msg['msg'] = 'empty data';
            $msg['code'] = 0 ;
        }
    }
    return $msg;
}

function update_status($param){
    global $keya, $c_time, $app;
    $db = $app->db;
	$msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
		'phone'      => 'required',
        'status' => 'required',
		));
	$validated = $gump->run($param);
	if($validated === false) {  
		$msg['code'] = 0 ;
		$msg['msg'] = $gump->get_readable_errors(true);	
	}else{
        $db->where('nope', $param['phone']);
        $cs = $db->getone("x_cs", "id, name, nope, license, webhook, status, status_wa, server");
        if($db->count > 0){
            $data = array('status_wa' => $param['status']);
            if($param['status'] == 'connected'){
                $dt = new DateTime("now", new DateTimeZone('Asia/Jakarta'));
                $timestamp = $dt->format('Y-m-d H:i:s');
                $data = array('status_wa' => $param['status'], 'followup_scan' => 0, 'last_online' => $timestamp);
            }
            $db->where('nope', $param['phone']);
            if($db->update("x_cs", $data)){
                $msg['msg'] = 'Sukses edit status.';
                $msg['code'] = 1 ;
            }else{
                $msg['msg'] = 'Gagal edit status.';
                $msg['code'] = 0 ;
            }
        }else{
            $msg['msg'] = 'Phone tidak ditemukan';
            $msg['code'] = 0 ;
        }
    }
    return $msg;
}

function check_wa($param){
    global $keya, $c_time, $app;
    $db = $app->db;
	$msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
        'phone' => 'required',
		));
	$validated = $gump->run($param);
	if($validated === false) {  
		$msg['code'] = 0 ;
		$msg['msg'] = $gump->get_readable_errors(true);	
	}else{   
        $db->where("status = 1"); 
        $db->where("nope = ?", array($param['phone']));
        $cs = $db->getone("x_cs", "id, name, nope, license, webhook, status, status_wa, server, formatVisit, formatCheckout, valueCheckout, formatPurchase, valuePurchase");
        //echo $db->getLastQuery();
        if($db->count > 0){ 
            $msg['msg'] = 'Success check cs';
            $msg['code'] = 1 ;
            $msg['data'] = $cs;
        }else{
            $msg['msg'] = 'empty data';
            $msg['code'] = 0 ;
        }
    }
    return $msg;
}

function autodc($phone){
    global $keya, $c_time, $app;
    $db = $app->db;
    $db2 = $app->db2;
    $db->where("nope",$phone);
    $cs  = $db->getone("x_cs");
    $table_cs = $cs["site_id"]."_cs";
    $db2->where("nope",$phone);
    $cs2  = $db2->getone($table_cs);
    if($cs["autodc"] == 1){
        $db2->where("id",$cs2["id"]);
        $db2->update($table_cs,array("status"=>0));
        //echo $db2->getLastQuery();
        $msg['msg'] = 'Success auto dc '.$db2->getLastQuery();;
        $msg['code'] = 1 ;
    }else{
        $msg['msg'] = "auto dc disabled";
        $msg['code'] = 0 ;
    }
    return $msg;
}

function cs_login($param){ 
    global $keya, $c_time, $app;
    $db = $app->db;
	$cs = new cs();
	$msg = array();
	$gump = new GUMP(); 
	$msg['code'] = 0 ;
	$gump->validation_rules(array(
        'phone'      => 'required',
		));
	$validated = $gump->run($param);
	if($validated === false) {  
		return $msg;
	}else{  
        $msg = generate_code($param['phone']);
    }
    return $msg;
}

function generate_code($phone){
    global $keya, $c_time, $app;
    $db = $app->db;
    $wa = new wa();
    $db->where("nope = ? and status = 1", array($phone));
    $users = $db->getone("x_cs");
    if ($app->db->count > 0) {
        $data["verif"]   = $verf = rand(111111,999999);
        $db->where("nope = ? and status = 1", array($phone));
        if ($app->db->update('x_cs', $data)) {
            $msg = "( Ini merupakan pesan otomatis ) \n";
            $msg .= "Kode OTP gass.co.id anda ".$data["verif"] ;
            $rex = $wa->send('text', '628113608550', $phone, $msg);
            $ret["code"] = 1;
            $ret["msg"]  = "Sukses generate.";
        }else {
            $ret["code"] = 0;
            $ret["msg"]  = "gagal generate.";
        }
    } else {
        $msg = "( Ini merupakan pesan otomatis ) \n";
        $msg .= "Nomer whatsapp kamu tidak ditemukan ";
        $wa->send('text', '628113608550', $phone, $msg);
        $ret["code"] = 0;
        $ret["msg"]  = "gagal generate, CS tidak ditemukan.";
    }       
    return $ret;
}

function message_in($param){
    global $keya, $c_time, $app;
    $db = $app->db;
    file_put_contents('pesan_masuk.txt', json_encode($param));
    try {
        if($param != NULL) {
            preg_match('/ID \[(.*?)\]/s', $param["message"], $match);
            if(count($match) > 1){
                $data["vid"] = $match[1];
                $data["nope_cs"] = $param["nope_cs"];
                $data["phone"] = $param["phone"];
                $db->insert('x_log_contact',$data);
            }
        }
        $data2["nope_cs"] = $param["nope_cs"];
        $data2["phone"] = $param["phone"];
        $data2["msg"] = $param["message"];
        $data2["receive"] = 1;
        @$db->insert('x_log_msg',$data2);
        file_put_contents('pesan_masuk.txt', $db->getLastQuery());
    }catch(Exception $e) {
        file_put_contents('log2.txt',$e->getMessage());
    }
    return 1;
}

function message_out($param){
    file_put_contents('pesan_keluar.txt', json_encode($param));
    global $keya, $c_time, $app;
    $db = $app->db;
    $data2["nope_cs"] = $param["nope_cs"];
    $data2["phone"] = $param["phone"];
    $data2["msg"] = $param["message"];
    $data2["receive"] = 0;
    @$db->insert('x_log_msg',$data2);
    if($param != NULL){
        preg_match('/ID \[(.*?)\]/s', $param["message"], $match);
        if(count($match) > 1){
            $data["vid"] = $match[1];
            $domain = strtolower($param["license"]);
            $db->where("domain", $domain);
            $site = $db->getone("x_site");
            if($db->count > 0){
                $param["pixel"] = $site["fb_pixel"];
                $param["apikey"] = $site["fb_token"];
                $param["adw_global_tag_id"] = $site["adw_tag"];
                $param["adw_conv_id"] = $site["adw_conv_id"];
                $param["gtm"] = $site["gtm"];
                $param["tiktok"] = $site["tiktok_pixel"];
                $param["vid"] =  preg_replace('/[^0-9]/', '', $data["vid"]);
                if(isset($param["phone"])){
                    $custom["phone"] = $param["phone"];
                }
                if(isset($param["email"])){
                    $custom["email"] = $param["email"];
                }
                if(isset($param["debug"])){
                    $custom["debug"] = 1;
                }
                $et = new eztrack($site["id"]);
                if(isset($custom)){
                    $res = $et->contact($site,$param,$custom);
                }else{
                    $res = $et->contact($site,$param);
                }
            }
        }
    }
    return 2;
}
