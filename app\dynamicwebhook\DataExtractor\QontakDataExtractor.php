<?php

namespace DynamicWebhook\DataExtractor;

use DynamicWebhook\Abstracts\AbstractDataExtractor;

/**
 * Qontak data extractor with integration support
 */
class QontakDataExtractor extends AbstractDataExtractor
{
    private $db2;

    public function __construct($normalizer, array $platformConfigs = [], $db2 = null)
    {
        parent::__construct($normalizer, $platformConfigs);
        $this->db2 = $db2;
    }

    /**
     * Get supported platforms
     */
    public function getSupportedPlatforms(): array
    {
        return ['qontak'];
    }

    /**
     * Extract platform-specific data
     */
    protected function extractPlatformSpecificData(array $data, array $config, string $platform): array
    {
        $result = $this->getEmptyResult();

        // Extract basic fields
        $result['phone'] = $this->normalizer->extractNestedValue($data, $config['phone_field']);
        $result['message'] = $this->normalizer->extractNestedValue($data, $config['message_field']);
        
        // Determine message type based on sender_type
        $senderType = $this->normalizer->extractNestedValue($data, $config['type_field']);
        if ($senderType === $config['incoming_condition']) {
            $result['message_type'] = 'message_in';
        } else {
            $result['message_type'] = 'message_out';
        }
        
        // Handle integration-specific CS mapping
        $integrationId = $this->normalizer->extractNestedValue($data, $config['integration_field']);
        if ($integrationId) {
            $result['cs_phone'] = $this->getCsFromIntegration($integrationId);
        }
        
        // Clean and normalize
        $result['phone'] = $this->normalizer->cleanPhoneNumber($result['phone']);
        $result['message'] = $this->normalizer->normalizeMessage($result['message']);
        $result['raw_data'] = $data;

        return $result;
    }

    /**
     * Get CS number based on Qontak integration ID
     */
    private function getCsFromIntegration(string $integrationId): ?string
    {
        if (empty($integrationId) || !$this->db2) {
            return null;
        }
        
        $integrationHash = sha1($integrationId);
        $this->db2->where("integration_id_key = UNHEX(?)", [$integrationHash]);
        $integration = $this->db2->getone('qontak_waba');
        
        if ($integration) {
            return $integration["cs_nope"];
        }

        return null;
    }
}
