<?php

namespace DynamicWebhook\TriggerProcessor;

use DynamicWebhook\Abstracts\AbstractTriggerProcessor;

/**
 * <PERSON><PERSON> (Marketing Qualified Lead) trigger processor
 */
class MQLTriggerProcessor extends AbstractTriggerProcessor
{
    /**
     * Get trigger type name
     */
    public function getTriggerType(): string
    {
        return 'mql';
    }

    /**
     * Check if trigger should be processed for given message type
     */
    public function shouldProcess(string $messageType): bool
    {
        // MQL trigger only processes incoming messages
        return $messageType === 'message_in';
    }

    /**
     * Process specific trigger logic
     */
    protected function processSpecificTrigger(array $data, array $context): bool
    {
        $phone = $data['phone'] ?? '';
        if (!$phone || !$this->dbService) {
            return false;
        }

        // Insert or update MQL count
        $mqlData = [
            "count" => 1,
            "phone" => $phone,
            "created" => date("Y-m-d H:i:s"),
            "phone_hash" => $this->dbService->func("UNHEX(?)", [md5($phone)])
        ];
        
        $this->dbService->setQueryOption(["IGNORE"])->insert("mql", $mqlData);
        
        // Get MQL limit from meta
        $res = $this->getMeta("mql");
        $mqlLimit = 1; // Default limit
        if ($res["code"] == 1) {
            $mqlLimit = $res["result"]["data"] - 1;
        }
        
        // Check current MQL count
        $this->dbService->where("phone_hash = UNHEX(?)", [md5($phone)]);
        $mqlRecord = $this->dbService->getone("mql");
        
        $shouldIncrement = true;
        $triggerActivated = false;
        
        if ($mqlRecord != null) {
            if ($mqlRecord["count"] == $mqlLimit) {
                $triggerActivated = true;
            }
            if ($mqlRecord["count"] > $mqlLimit) {
                $shouldIncrement = false;
            }
        }
        
        // Increment count if needed
        if ($shouldIncrement) {
            $updateData = [];
            $insertData = [];
            $updateData["phone_hash"] = $this->dbService->func("UNHEX(?)", [md5($phone)]);
            $updateData["count"] = 1;
            $insertData["count"] = $this->dbService->inc(1);
            $this->dbService->onDuplicate($insertData);
            $this->dbService->insert("mql", $updateData);
        }
        
        return $triggerActivated;
    }
}
