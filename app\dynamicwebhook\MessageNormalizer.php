<?php

namespace DynamicWebhook;

use DynamicWebhook\Interfaces\MessageNormalizerInterface;

/**
 * Message and data normalizer
 */
class MessageNormalizer implements MessageNormalizerInterface
{
    /**
     * Clean and normalize phone number
     */
    public function cleanPhoneNumber(?string $phone): ?string
    {
        if (!$phone) {
            return null;
        }

        // Remove @s.whatsapp.net suffix for WhatsApp numbers
        $phone = str_replace('@s.whatsapp.net', '', $phone);
        
        // Remove non-numeric characters
        $cleaned = preg_replace('/\D/', '', $phone);
        
        // Validate length (reject if too long)
        if (strlen($cleaned) >= 20) {
            return null;
        }
        
        return $cleaned ?: null;
    }

    /**
     * Normalize message content
     */
    public function normalizeMessage(?string $message): string
    {
        if (!$message) {
            return '';
        }

        // Basic normalization - trim whitespace
        $normalized = trim($message);
        
        // Remove excessive whitespace
        $normalized = preg_replace('/\s+/', ' ', $normalized);
        
        return $normalized;
    }

    /**
     * Validate phone number format
     */
    public function isValidPhoneNumber(?string $phone): bool
    {
        if (!$phone) {
            return false;
        }

        $cleaned = $this->cleanPhoneNumber($phone);
        
        // Check if we have a valid cleaned phone number
        if (!$cleaned) {
            return false;
        }

        // Basic validation - should be numeric and reasonable length
        return is_numeric($cleaned) && strlen($cleaned) >= 8 && strlen($cleaned) < 20;
    }

    /**
     * Extract nested value from array using dot notation
     */
    public function extractNestedValue(array $array, ?string $path)
    {
        if (!$path) {
            return null;
        }

        $keys = explode('.', $path);
        $value = $array;
        
        foreach ($keys as $key) {
            if (is_array($value) && isset($value[$key])) {
                $value = $value[$key];
            } else {
                return null;
            }
        }
        
        return $value;
    }

    /**
     * Clean string helper (for backward compatibility)
     */
    public function cleanString(string $input): string
    {
        if (function_exists('clean_string')) {
            return clean_string($input);
        }
        
        return trim($input);
    }

    /**
     * Sanitize input for regex usage
     */
    public function sanitizeForRegex(string $input): string
    {
        return preg_quote(trim($input), '/');
    }

    /**
     * Extract ID from message using pattern
     */
    public function extractIdFromMessage(string $message, string $pattern = "/ID \[(.*?)\]/s"): ?string
    {
        preg_match($pattern, $message, $matches);
        return isset($matches[1]) ? trim($matches[1]) : null;
    }

    /**
     * Extract value using custom format pattern
     */
    public function extractValueWithFormat(string $message, string $format, string $placeholder = '%VALUE%'): ?string
    {
        $pattern = $this->sanitizeForRegex($format);
        $pattern = str_replace(preg_quote($placeholder), '([0-9,.]*)', $pattern);
        $regex = '/' . $pattern . '/';
        
        preg_match($regex, $message, $matches);
        return isset($matches[1]) ? preg_replace('/[.,]/', '', $matches[1]) : null;
    }

    /**
     * Extract text using custom format pattern
     */
    public function extractTextWithFormat(string $message, string $format, string $placeholder): ?string
    {
        $pattern = $this->sanitizeForRegex($format);
        $pattern = str_replace(preg_quote($placeholder), '(.+)', $pattern);
        $regex = '/' . $pattern . '/';
        
        preg_match($regex, $message, $matches);
        return isset($matches[1]) ? trim($matches[1]) : null;
    }
}
